package com.fxiaoke.open.erpsyncdata.converter.manager;

import cn.hutool.core.map.multi.RowKeyTable;
import cn.hutool.core.map.multi.SetValueMap;
import cn.hutool.core.map.multi.Table;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil;
import com.fxiaoke.open.erpsyncdata.converter.helpers.AviatorHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectDataExistStatus;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.model.TriggerConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/28
 */
@Slf4j
@Service
public class NodeSyncPreManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncDataMappingService syncDataMappingService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private OverrideOuterService overrideOuterService;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private AviatorHelper aviatorHelper;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private CrmObjAndFieldManager crmRequestManager;
    @Autowired
    private CheckSyncDataMappingManager checkSyncDataMappingManager;
    @Autowired
    private ReSyncDataNodeManager reSyncDataNodeManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;

    public NodeSyncPreManager() {
        super(DataNodeNameEnum.DataTriggerProcess);
    }

    @Override
    protected void preReport(SyncDataContextEvent eventData) {
        eventData.setCurrentDataNodeName(dataNodeNameEnum);
        TimePointRecorderStatic.record("streamBegin");
        //准备参数
        String tenantId = eventData.getTenantId();
        //分发节点日志
        monitorReportManager.sendDispatcherEventMsg(tenantId,
                eventData.getObjectApiName(),
                eventData.getMainObjApiName(),
                eventData.getDataId(),
                eventData.getDataVersion(),
                eventData.getStreamId(),
                System.currentTimeMillis(),
                60,
                i18NStringManager.getByEi(I18NStringEnum.s2031,tenantId),
                eventData.getDataVersionList());
    }

    @Override
    protected void postReport(SyncDataContextEvent eventData) {
        String tenantId = eventData.getTenantId();
        String streamId = eventData.getStreamId();
        ErpTempData erpTempData=ErpTempData.builder().dataId(eventData.getDataId()).dataNumber(eventData.getSourceData().getName()).build();
        setDataVersion(tenantId,eventData,erpTempData);
        String mongoId = eventData.getSourceData().getString("mongo_id");//从mongo获取的erp主数据才有这个字段
        if (eventData.getStop()) {
            //同步中断
            if (!eventData.isIgnoreLogWhenStop()) {
                erpTempData.setRemark(eventData.getMsg());
                syncLogManager.saveErpTempLog(tenantId, SyncLogTypeEnum.DATA_SYNC_FILTER, SyncLogStatusEnum.SYNC_FAIL.getStatus(), null, erpTempData);
                //更新到临时库的数据状态，在数据监控页面可以看到。
                overrideOuterService.updateErpTempDataByIds(tenantId, streamId, mongoId, 10050, erpTempData.getRemark());
            }
            //只记录主数据，因此，crm来源的数据无法记录 从的数据量
            if (eventData.isMainObj()) {
                //CRM来源的为1，
                //ERP来源的，为原始数量
                eventData.addStatusRecord(NodeDataStatus.BLOCKED, eventData.getMainIdIfExist(), eventData.getAllObjCount());
            }
        }
        //这一步，认为触发还未完成，在同步后再记录成功触发的日志
    }

    @Override
    public SyncDataContextEvent processMessage(SyncDataContextEvent ctx) {
        beforeRunIntegrationStream(ctx);
        return ctx;
    }

    @CompareSyncField(syncType = SyncCompareConstant.SYNC_PRE_INVOKE)
    public void beforeRunIntegrationStream(final SyncDataContextEvent eventData) {
        //准备参数
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(eventData.getTenantId(),eventData.getSyncPloyDetailSnapshotId()).getData();
        String erpDataCenterId = eventData.getDcId();
        String sourceObjectApiName = eventData.getSourceData().getApiName();
        ObjectData sourceData = eventData.getSourceData();
        Integer sourceEventType = eventData.getSourceEventType();
        String tenantId = eventData.getSourceTenantId();
        Integer sourceTenantType = eventData.getSourceTenantType();
        OuterService outerService = outerServiceFactory.get(sourceTenantType);

        eventData.setSourceTenantType(syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType());
        eventData.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotData.getId());
        eventData.setDestTenantType(syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestTenantType());


        //针对统计、计算、引用字段（只有落地的才有事件）快速丢弃， 走配置项的。
        if (quickPassSpecialFieldData(tenantId, sourceObjectApiName, sourceEventType, sourceData, syncPloyDetailSnapshotData)) {
            eventData.stop(i18NStringManager.getByEi(I18NStringEnum.s2030,tenantId));
            return;
        }


        ObjectData updateData = null;
        String destObjectApiName = null;

        boolean isMasterObject = syncPloyDetailSnapshotData.getSourceObjectApiName().equals(sourceObjectApiName);
        if (isMasterObject) {//主对象的数据准备
            destObjectApiName = syncPloyDetailSnapshotData.getDestObjectApiName();
        } else {
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectMappings()) {
                //拿到从映射字段集合与目标从对象apiName
                if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                    destObjectApiName = detailObjectMapping.getDestObjectApiName();
                    break;
                }
            }
        }
        //中间表节点-单独对象（可能主，可能从）,主对象或者目标非主从一起更新的从对象才执行
        if((isMasterObject||!configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(syncPloyDetailSnapshotData.getDestObjectApiName()))){
            checkSyncDataMappingManager.processMessage(eventData,syncPloyDetailSnapshotData);
        }
        boolean existMapping = syncDataMappingManager.existByTwoWay(tenantId, sourceObjectApiName, sourceData.getId(), destObjectApiName);
        //检查源事件是否要阻拦
        Result2<Boolean> isSucc = checkSourceDataEventNeedSync(syncPloyDetailSnapshotData, eventData, existMapping);
        if (isSucc.getData()!=null&&!isSucc.getData()) {
            log.info("trace checkSourceDataEventNeedSync discard eventdata ei:{},srcobj:{},dataid:{}",tenantId, sourceObjectApiName, sourceData.getId());
            eventData.stop(isSucc.getErrMsg());
            return;
        }

        /**updateData 和 sourceData的重构处理。
         * updateData从原来分别放到主和从两个分支的逻辑里面，提到最前面，不区分主从。sourceData在主从里面，获取全字段的地方，需要修改为指向全字段对象。
         * 在判断数据范围之前，都会先补齐全字段。
         * 1.erp->crm是全字段
         * 2.crm->erp 源事件是新增时，只带了ID字段
         * 3.crm->erp 源事件是更新时，只有更新的字段。
         **/
        if (!isMasterObject ||
                ((sourceEventType == EventTypeEnum.UPDATE.getType()) && (TenantTypeEnum.CRM.getType() == syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType()))) {
            //从对象，或者主对象的源事件是更新事件，需要先记录sroucedata, 方便后面对比数据是否发生了变化。原来的逻辑。
            updateData = eventData.getSourceData();
        }
        //补齐全字段到sourcedata后，才能进行数据范围校验，因为校验的字段在源数据中必须存在。
        fillFullFieldsToSourceData(syncPloyDetailSnapshotData, eventData, outerService);

        if (isMasterObject) {
            //填充引用字段的值，才能做数据范围检查，在下面的方法补充了
//            fillQuoteFieldValue(syncPloyDetailSnapshotData, syncDependForce, eventData, outerService, syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType());

            //主对象，检查数据范围
            isSucc = checkConditionMasterData(erpDataCenterId, eventData, syncPloyDetailSnapshotData);
            if (isSucc.getData()!=null&&!isSucc.getData()) {
                log.info("trace checkConditionMasterData, discard ei:{},srcobj:{},dataid:{}",tenantId, sourceObjectApiName, sourceData.getId());
                eventData.stop(isSucc.getErrMsg());
                return;
            }

            /**需要补齐从对象数据到 completeEventTriggerArg。一条主数据要能同步，从数据也要满足条件才行。
            注意，最多补充2000条明细。 因为我们假定CRM对象，明细超过2000条现在还不支持。**/
            isSucc = fillDetailData("", eventData, syncPloyDetailSnapshotData, overrideOuterService);
            log.debug("trace fillDetailData, eventdata:{},  ret:{}", eventData, isSucc);
            if (isSucc.getData()!=null&&isSucc.getData()) {
                //对补齐的所有从对象数据校验数据范围，可能是一次补齐多个从对象 这里一定是true，不执行了
//                isSucc = checkConditionALLDetailData(eventData, syncPloyDetailSnapshotData);
//                if (isSucc.getData()!=null&&!isSucc.getData()) {
//                    log.info("trace checkConditionALLDetailData discard ei:{},srcobj:{},dataid:{}",tenantId, sourceObjectApiName, sourceData.getId());
//                    eventData.stop(isSucc.getErrMsg());
//                    return;
//                }
            }
            //新增或者不能单独更新，需要检查从对象的中间表节点
            if(!existMapping||configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(destObjectApiName)){
                //中间表节点-明细
                checkSyncDataMappingManager.processDetailMessage(eventData,syncPloyDetailSnapshotData);
            }
        } else {
            //对当前这个从对象的数据，检查数据范围
            isSucc = detailCheckCondition(tenantId, "", sourceObjectApiName, eventData.getSourceData(), syncPloyDetailSnapshotData);
            if (isSucc.getData()!=null&&!isSucc.getData()) {
                log.info("trace detailCheckCondition discard ei:{},srcobj:{},dataid:{}",tenantId, sourceObjectApiName, sourceData.getId());
                eventData.stop(isSucc.getErrMsg());
                return;
            }

            //从对象事件转为主对象新增事件
            isSucc = detailEventCauseMasterSync(syncPloyDetailSnapshotData, eventData, outerService, syncDataMappingService);
            if (isSucc.getData()!=null&&isSucc.getData()) {
                log.info("trace detailEventCauseMasterSync discard ei:{},srcobj:{},dataid:{}",tenantId, sourceObjectApiName, sourceData.getId());

                eventData.stop(isSucc.getErrMsg());
                return;
            }

            //某些从对象，不能单独新建和编辑。比如订单产品，必须跟随订单主数据一起新建和一起编辑。
            isSucc = notAllowToWriteWithoutMaster(tenantId, sourceEventType, syncPloyDetailSnapshotData.getDestObjectApiName());
            if (isSucc.getData()!=null&&!isSucc.getData()) {
                log.info("trace notAllowToWriteWithoutMaster discard ei:{},srcobj:{},dataid:{}",tenantId, sourceObjectApiName, sourceData.getId());

                eventData.stop(isSucc.getErrMsg());
                return;
            }
        }
        log.debug("SyncPreManager.beforeRunIntegrationStream,sourceData={}", eventData.getSourceData());

        //数据同步前执行
        eventData.setUpdateData(fillReferenceValue(updateData, eventData.getSourceData()));
        eventData.setDestObjectApiName(destObjectApiName);
        eventData.setMasterUpdateEvent(isMasterObject);
    }

    /**
     * @param sourceTenantId
     * @param sourceObjectApiName
     * @param sourceEventType
     * @param sourceData
     * @param syncPloyDetailSnapshotData
     * @return
     * @desc 如果源事件是更新事件，且sourceData除了固定字段之外只有一个没有对接(字段映射和数据范围)的统计计算引用字段，直接过滤掉
     * 注：这里过滤进入了分发框架的大量数据，根据配置来决定是否需要走这个过滤逻辑
     */
    public Boolean quickPassSpecialFieldData(String sourceTenantId, String sourceObjectApiName, Integer sourceEventType, ObjectData sourceData, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        List<String> tenantList = tenantConfigurationManager.getQuickPassSpecialFieldTenantConfig();
        if (!tenantList.contains(sourceTenantId)) {//不包含，不快速过滤
            return false;
        }
        if (TenantType.ERP == syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType()) {//源为erp，不快速过滤
            return false;
        }
        if (sourceEventType != EventTypeEnum.UPDATE.getType()) {//源事件不是更新，不快速过滤
            return false;
        }
        if (sourceData == null) {
            return false;
        }
        if (sourceData.size() > CommonConstant.crmCommonField.size() + 1) {
            return false;
        }
        ObjectData copy = BeanUtil.deepCopy(sourceData, ObjectData.class);
        for (String field : CommonConstant.crmCommonField) {
            copy.remove(field);//去掉固定字段
        }
        if (copy.size() == 1) {
            String field = copy.keySet().iterator().next();
            Boolean isSpecialField = crmRequestManager.objFieldIsCountOrFormulaOrQuoteField(sourceTenantId, sourceObjectApiName, field);
            if (isSpecialField) {//是统计、计算、引用字段
                List<FieldMappingData> fieldMappingDataList = null;
                List<String> filterField = Lists.newArrayList();//数据范围字段
                boolean isMasterDataEvent = syncPloyDetailSnapshotData.getSourceObjectApiName().equals(sourceObjectApiName);
                if (isMasterDataEvent) {
                    fieldMappingDataList = syncPloyDetailSnapshotData.getSyncPloyDetailData().getFieldMappings();
                    if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncConditions() != null
                            && syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncConditions().getFilters() != null) {//数据范围
                        List<List<FilterData>> filters = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncConditions().getFilters();
                        for (List<FilterData> filterDataList : filters) {
                            for (FilterData filterData : filterDataList) {
                                filterField.add(filterData.getFieldApiName());
                            }
                        }
                    }
                } else {
                    Optional<DetailObjectMappingsData.DetailObjectMappingData> curMappingData =
                            syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectMappings().stream().filter(v -> v.getSourceObjectApiName().equals(sourceObjectApiName)).findFirst();
                    if (!curMappingData.isPresent()) {//如果从对象没有配置字段映射关系，但是有数据过来了，这条数据是不用往下同步的。快速过滤
                        return true;
                    }
                    fieldMappingDataList = curMappingData.get().getFieldMappings();
                    if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectSyncConditions() != null) {//数据范围
                        for (SyncConditionsData syncConditionsData : syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectSyncConditions()) {
                            if (syncConditionsData.getApiName().equals(sourceObjectApiName)) {
                                List<List<FilterData>> filters = syncConditionsData.getFilters();
                                if (filters != null) {
                                    for (List<FilterData> filterDataList : filters) {
                                        for (FilterData filterData : filterDataList) {
                                            filterField.add(filterData.getFieldApiName());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                Optional<FieldMappingData> fieldMappingData = fieldMappingDataList.stream().filter(v -> field.equals(v.getSourceApiName())).findFirst();
                if (!fieldMappingData.isPresent() && !filterField.contains(field)) {//没有找到映射字段且没找到数据范围字段，快速过滤
                    return true;
                }
            }
        }
        return false;//默认不快速过滤
    }

    /**
     * 检查源事件是否需要往下传递。
     */
    private Result2<Boolean> checkSourceDataEventNeedSync(SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData,
                                                 SyncDataContextEvent eventData,
                                                          boolean existMapping) {
        String tenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        String sourceObjectApiName = eventData.getSourceData().getApiName();
        String destObjectApiName = null;
        String mongoId = null;//从mongo获取的erp主数据才有这个字段
        Integer sourceEventType = eventData.getSourceEventType();
        ObjectData sourceData = eventData.getSourceData();
        String ployDetailId = syncPloyDetailSnapshotData.getSyncPloyDetailId();
        List<FieldMappingData> fieldMappingDataList = null;
        SyncConditionsData syncConditions = null;
        boolean isMasterDataEvent = syncPloyDetailSnapshotData.getSourceObjectApiName().equals(sourceObjectApiName);
        Result2<Boolean> result=Result2.newSuccess();
        String msg;
        if (isMasterDataEvent) {//主对象的数据准备
            mongoId = eventData.getSourceData().getString("mongo_id");//从mongo获取的erp主数据才有这个字段
            fieldMappingDataList = syncPloyDetailSnapshotData.getSyncPloyDetailData().getFieldMappings();
            syncConditions = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncConditions();
            destObjectApiName = syncPloyDetailSnapshotData.getDestObjectApiName();
        } else {
            Optional<DetailObjectMappingsData.DetailObjectMappingData> curMappingData =
                    syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectMappings().stream().filter(v -> v.getSourceObjectApiName().equals(sourceObjectApiName)).findFirst();
            if (!curMappingData.isPresent()) {//如果从对象没有配置字段映射关系，但是有数据过来了，这条数据是不用往下同步的。
                msg=i18NStringManager.getByEi(I18NStringEnum.s2002,tenantId);
                result.setErrMsg(msg);
                result.setData(false);
                return result;
            }
            fieldMappingDataList = curMappingData.get().getFieldMappings();
            destObjectApiName = curMappingData.get().getDestObjectApiName();
            Optional<SyncConditionsData> curConditionData =
                    syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectSyncConditions().stream().filter(v -> v.getApiName().equals(sourceObjectApiName)).findFirst();
            syncConditions = curConditionData.orElse(null);
        }
        Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
        Set<Integer> eventSet = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncRules().getEvents();

        /**1.根据中间表转换源事件- >目标事件, 删除和作废事件例外。**/
        Integer destEventType = existMapping ? EventTypeEnum.UPDATE.getType() : EventTypeEnum.ADD.getType();
        boolean isDeleteOrRecover = sourceEventType == EventTypeEnum.DELETE_DIRECT.getType() || sourceEventType == EventTypeEnum.INVALID.getType();
        if (isMasterDataEvent) {
            isDeleteOrRecover = isDeleteOrRecover || (sourceEventType == EventTypeEnum.RECOVER.getType());
        }
        if (isDeleteOrRecover) {
            destEventType = sourceEventType;
            //如果是作废或直接删除事件，没有同步记录，丢弃
            if (!existMapping) {
                msg=i18NStringManager.getByEi(I18NStringEnum.s892,tenantId);
                eventData.setDestEventType(destEventType);
                log.info("{}detail mapping is null,invaild event not sync,sourceEventType={},data={}", LogUtil.BREAK_KEY, sourceEventType, sourceData);
//                updateFailSyncLogAndTempStatusMsg(tenantId, ployDetailId, mongoId, erpTempData);
                result.setErrMsg(msg);
                result.setData(false);
                return result;
            }
        }
        eventData.setDestEventType(destEventType);
        /**2.用户勾选的目标系统允许的事件，不包含当前的目标事件。
         这里我忍不住要加个注释： 用户看数据是从主数据去看的，他也不懂底层的接口实现。从数据的删除和作废，要理解成主数据有 "更新"了。
         * 比如用户勾选的允许目标系统 有更新没有作废，实际上表达的是 主数据不需要作废，但是从数据的作废还是要支持的*/
        if (!eventSet.contains(destEventType)) {
            if (!isMasterDataEvent && (sourceEventType == EventTypeEnum.DELETE_DIRECT.getType() || sourceEventType == EventTypeEnum.INVALID.getType())) {
                boolean containsUpdate = eventSet.contains(EventTypeEnum.UPDATE.getType());
                boolean fromErp = sourceTenantType.equals(TenantTypeEnum.ERP.getType());
                //如果是ERP往CRM的明细作废，允许的目标事件包含了更新事件，则允许通过。
                boolean erpDetailSync = fromErp && containsUpdate;
                log.info("trace2 eventData:{}, fromErp：{}, containsUpdate：{}, ret:{}", eventData, fromErp, containsUpdate, erpDetailSync);
                msg=i18NStringManager.getByEi(I18NStringEnum.s2003,tenantId);
                result.setErrMsg(msg);
                result.setData(erpDetailSync);
                return result;
            }
            msg = i18NStringManager.getByEi(I18NStringEnum.s893,tenantId) + EventTypeEnum.getNameByType(i18NStringManager,null,tenantId,destEventType) + "," + i18NStringManager.getByEi(I18NStringEnum.s894,tenantId);
//            updateFailSyncLogAndTempStatusMsg(tenantId, ployDetailId, mongoId, erpTempData);
            result.setErrMsg(msg);
            result.setData(false);
            return result;
        }
        /**3. 是否引起数据风暴的判断。
         * 如果是CRM主数据的更新事件，或者是从数据的事件（ERP不能单独操作单条从数据，所以从数据变化，会当做主数据变化处理）, 还要判断下会不会是CRM侧的数据变化引起数据风暴。
         * 比如crm上修改了计算公式，所有CRM数据被刷了一遍。此时要检查变化的字段是不是映射过的字段，或者存在于数据范围的字段。**/
        //判断1： 为什么这里是检查 更新->新增， 不检查新增->新增？  因为 新增->新增 不会产生数据风暴。
        boolean checkCRMDataStorm = (sourceTenantType == TenantTypeEnum.CRM.getType())
                && sourceEventType == EventTypeEnum.UPDATE.getType();
        if (checkCRMDataStorm) {
            final TriggerConfig triggerConfig = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncRules().getTriggerConfig();
            boolean isCustomTriggerConfig = Objects.nonNull(triggerConfig) && Objects.equals(triggerConfig.getTriggerType(), SyncPloyDetailTriggerEnum.CUSTOM.getStatus());
            final List<String> triggerFields = isCustomTriggerConfig ? MapUtils.emptyIfNull(triggerConfig.getTriggerObjectFields()).computeIfAbsent(sourceObjectApiName, key -> Lists.newArrayList()) : new ArrayList<>();
            I18NStringEnum noChangeI18NStringEnum = matchKeyFieldChange(isCustomTriggerConfig, triggerFields, sourceObjectApiName, fieldMappingDataList, eventData, syncConditions, sourceData, destEventType);
            //判断2： 数据要有实际变化才会同步。
            if (Objects.nonNull(noChangeI18NStringEnum)) {
                log.info("{} trace2 not match master matchUpdateData,sourceDataid={}, sourcedataname:{}", LogUtil.BREAK_KEY, sourceData.getId(),sourceData.getName());
                msg=i18NStringManager.getByEi(noChangeI18NStringEnum,tenantId);
                //  从对象 新增 不记录丢弃的事件
                if (!isMasterDataEvent && destEventType != EventTypeEnum.UPDATE.getType()) {
                    eventData.setIgnoreLogWhenStop(true);
                }
                result.setErrMsg(msg);
                result.setData(false);
                return result;
            }

            //此处原来是通过日志去判断是否全部引用字段且和上次同步数据一致，经讨论，删除
        }
        result.setData(true);
        return result;
    }


    /**
     * 检查关键字段是否有更新
     */
    private I18NStringEnum matchKeyFieldChange(boolean isCustomTriggerConfig, List<String> triggerFields, String sourceObjectApiName,
                                               List<FieldMappingData> fieldMappingDataList, SyncDataContextEvent eventData, SyncConditionsData syncConditions,
                                               ObjectData sourceData, Integer destEventType) {
        if (isCustomTriggerConfig) {
            final boolean matchTriggerData = matchTriggerData(eventData, sourceData, triggerFields);
            if (!matchTriggerData) {
                return I18NStringEnum.s3627;
            }

            eventData.setIsMatchUpdateData(matchUpdateData(eventData, sourceData, fieldMappingDataList, destEventType));
            return null;
        }

        boolean isMatchUpdateField = matchUpdateData(eventData, sourceData, fieldMappingDataList, destEventType);
        eventData.setIsMatchUpdateData(isMatchUpdateField);
        return isMatchUpdateField || matchFilterData(eventData, sourceData, syncConditions) ? null : I18NStringEnum.s2004;
    }

    private static boolean matchTriggerData(SyncDataContextEvent eventData, ObjectData sourceData, List<String> triggerFields) {
        Set<String> SKIP_FILTER_MATCH_UPDATE_FIELD = Sets.newHashSet("_id");
        // 自定义触发字段
        boolean keyFieldChange = false;
        for(String field:triggerFields){
            if(SKIP_FILTER_MATCH_UPDATE_FIELD.contains(field)){
                continue;
            }
            if(sourceData.containsKey(field)){
                eventData.setMatchField(field);
                keyFieldChange= true;
                break;
            }
        }
        if (!keyFieldChange) {
            log.debug("变更的字段不在自定义触发字段里, triggerFields:{}, objectData:{}", triggerFields, sourceData);
        }
        return keyFieldChange;
    }

    /**
     * @return true: 触发了主数据新建（或者丢弃了）, 该条从数据不需要往下同步了。
     * FALSE：没有触发主数据新建， 该条数据继续后面的同步步骤
     */
    private Result2<Boolean> detailEventCauseMasterSync(SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData,
                                               SyncDataContextEvent eventData,
                                               OuterService outerService,
                                               SyncDataMappingService syncDataMappingService) {
        Result2<Boolean> result=Result2.newSuccess();
        String masterFieldApiName = null;
        String tenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        String sourceTenantId = eventData.getSourceData().getTenantId();
        String destTenantId = syncPloyDetailSnapshotData.getDestTenantId();
        String sourceObjectApiName = eventData.getSourceData().getApiName();
        ObjectData sourceData = eventData.getSourceData();
        String dataId = eventData.getDataId();
        Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
        String sourceMasterObjectApiName = syncPloyDetailSnapshotData.getSourceObjectApiName();
        String destMasterObjectApiName = syncPloyDetailSnapshotData.getDestObjectApiName();

        //拿到从对象的字段映射 和目标从对象的objapiname
        List<FieldMappingData> fieldMappingDataList = new ArrayList<>(0);
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailSnapshotData.getSyncPloyDetailData().getDetailObjectMappings()) {
            if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                fieldMappingDataList = detailObjectMapping.getFieldMappings();
                break;
            }
        }
        //拿取主对象apiName(detailObjectMapping会构造主对象的mapping以供使用)
        for (FieldMappingData fieldMapping : fieldMappingDataList) {
            if (fieldMapping.getSourceType().equals(FieldTypeContants.MASTER_DETAIL)) {
                //如规格值的masterFieldApiName值为specification_id，specification_id为从对象数据sourceData中主对象数据id的key
                masterFieldApiName = fieldMapping.getSourceApiName();
                break;
            }
        }

        //主对象未新增，从对象触发主对象依赖，这个逻辑放在最后，只要从数据不符合上面的条件，就不会触发主数据依赖
        //通过从对象的masterFieldApiName值为key，获取主对象数据id
        String masterId = eventData.getSourceData().getString(masterFieldApiName);
        if (StringUtils.isEmpty(masterId)) {
            if (sourceData != null) {
                masterId = sourceData.getString(masterFieldApiName);
            }
        }
        if (StringUtils.isEmpty(masterId)) {
            /**erp->crm的从数据事件是全字段,因此如果直接获取主从字段失败，就是erp对象配置或者erp数据本身出错了。
             * 需要联系实施检查 配置和外部数据。
            **/
            if(sourceTenantType.equals(TenantType.ERP)) {
                log.warn("{}detail data masterId is null,tenantId:{},sourceObjectApiName{}, masterFieldApiName:{}",
                        LogUtil.BREAK_KEY, tenantId,sourceObjectApiName, masterFieldApiName);
                result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s2006,tenantId));
                result.setData(true);
                return result;
            }

            //crm的event可能是部分字段，这部分字段如果没有带主从字段，需要获取对象全字段后把朱字段找出来。
            ObjectData remoteSourceObjectData = outerService.getObjectData(sourceTenantId, sourceTenantType, sourceObjectApiName,
                    dataId, true).getData();
            if (remoteSourceObjectData != null) {
                masterId = remoteSourceObjectData.getString(masterFieldApiName);
            }
            if (StringUtils.isEmpty(masterId)) {
                log.warn("{}detail data masterId is null,masterId:{},data={}", LogUtil.BREAK_KEY, masterId, sourceData);
                result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s2006,tenantId));
                result.setData(true);
                return result;
            }
        }
        //补充源对象的sourceMasterId.这里方便从对象创建的时候，就指定master_data_id.
        eventData.setSourceMasterId(masterId);
        //创建映射后如果isCreated=false，说明目标数据未能创建成功（仅创建映射成功还不足以判断），解决某些场景下主对象不同步，从对象还同步的问题
        SyncDataMappingData syncDataMappingData = syncDataMappingService.getSyncDataMapping(tenantId, sourceTenantId, sourceMasterObjectApiName,
                masterId, destTenantId, destMasterObjectApiName).getData();

        if (syncDataMappingData != null && syncDataMappingData.getIsCreated()) {
            MasterMappingsData masterMappingsData = new MasterMappingsData();
            BeanUtils.copyProperties(syncDataMappingData, masterMappingsData);
            eventData.setMasterMappingsData(masterMappingsData);
        }else{
            if(hasCheckSyncDataMappingNode(syncPloyDetailSnapshotData)){
                //获取主数据
                ObjectData sourceMainObjectData = outerService.getObjectData(sourceTenantId, sourceTenantType, syncPloyDetailSnapshotData.getSourceObjectApiName(),
                        masterId,  true).getData();
                if(sourceMainObjectData!=null){
                    SyncDataContextEvent mainEvenData=new SyncDataContextEvent();
                    mainEvenData.setSourceEventType(EventTypeEnum.ADD.getType());
                    mainEvenData.setSourceData(sourceMainObjectData);
                    //主对象执行中间表组件逻辑，因为主从无序，所以处理从数据时如果主未处理，就先处理主
                    checkSyncDataMappingManager.processMessage(mainEvenData,syncPloyDetailSnapshotData);
                    syncDataMappingData = syncDataMappingService.getSyncDataMapping(tenantId, sourceTenantId, sourceMasterObjectApiName,
                            masterId, destTenantId, destMasterObjectApiName).getData();
                    if (syncDataMappingData != null && syncDataMappingData.getIsCreated()) {
                        MasterMappingsData masterMappingsData = new MasterMappingsData();
                        BeanUtils.copyProperties(syncDataMappingData, masterMappingsData);
                        eventData.setMasterMappingsData(masterMappingsData);
                    }
                }
            }
        }

        if (eventData.getMasterMappingsData() == null) {
            Integer dataReceiveType= sourceTenantType.equals(TenantType.CRM) ?DataReceiveTypeEnum.PAAS_META_EVENT.getType() : DataReceiveTypeEnum
                    .AUTO_POLLING_ERP.getType();
            if (sourceTenantType != null && sourceTenantType.equals(TenantType.CRM)) {
                ObjectData copy = BeanUtil.deepCopy(sourceData, ObjectData.class);
                copy.remove(masterFieldApiName);
                for (String field : CommonConstant.crmCommonField) {
                    copy.remove(field);//去掉固定字段
                }
                if (CollectionUtils.isNotEmpty(copy.keySet())) {
                    Boolean isSpecialField = crmRequestManager.allObjFieldIsCountOrFormulaOrQuoteField(sourceTenantId, sourceObjectApiName, copy.keySet());
                    if (isSpecialField) {//如果去掉固定字段后，只剩统计计算引用字段，不触发主数据依赖
                        result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s2007,tenantId));
                        result.setData(true);
                        return result;
                    }
                }
            }
            if(sourceTenantType==TenantType.CRM){//只有crm才有可能需要这个逻辑(crm可能存在只有从事件的情况)
                //主对象没同步，则将主对象先同步.只发送主数据的ID
                log.info("{}master not sync,masterId={},data={}", LogUtil.BREAK_KEY, masterId, sourceData);
                ObjectData newObjectData = new ObjectData();
                newObjectData.putTenantId(sourceTenantId);
                newObjectData.putApiName(sourceMasterObjectApiName);
                newObjectData.putId(masterId);
                //重新构造主对象eventData(sourceData只有id字段)，将主对象发送到分发框架,
                SyncDataContextEvent newEventData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                newEventData.setSourceData(newObjectData);
                newEventData.setDataReceiveType(dataReceiveType);
                newEventData.setSourceEventType(EventTypeEnum.ADD.getType());//从对象新增依赖主对象新增,如果erp需要发新增事件，需要补齐数据
                eventTriggerService.batchSendEventData2DispatcherMqByContext(Lists.newArrayList(newEventData));
            }
            eventData.setDebugRecordIfDetailCauseMaterSync(true);
            result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s2008,tenantId));
            result.setData(true);
            return result;
        }
        result.setData(false);
        return result;
    }

    private boolean hasCheckSyncDataMappingNode(SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        //检查中间表节点不为空
        if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes() != null
                && syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getCheckSyncDataMappingNode() != null
                && syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getCheckSyncDataMappingNode().getQueryObjectMappingData() != null) {
            return true;
        }
        return false;
    }


    /**
     * 返回全字段sourcedata
     * eventData.sourcedata中的字段，有的情况下只有部分字段，补齐全字段。
     * 比如crm的更新事件中，只带了发生变化的字段。
     */
    private void fillFullFieldsToSourceData(SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData,
                                            SyncDataContextEvent eventData,
                                            OuterService outerService) {
        Integer sourceEventType = eventData.getSourceEventType();
        Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
        String sourceTenantId = eventData.getSourceData().getTenantId();
        String sourceObjectApiName = eventData.getSourceData().getApiName();
        String tenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        final ObjectData oldSourceData = eventData.getSourceData();
        ObjectData sourceData = oldSourceData;
        String oldSourceDataId = sourceData.getId();

        boolean isMasterObject = syncPloyDetailSnapshotData.getSourceObjectApiName().equals(sourceObjectApiName);
        boolean isCrmSource = TenantTypeEnum.CRM.getType() == sourceTenantType;
        if (isMasterObject) {
            if (isCrmSource) {
                //更新事件且源为crm(erp更新事件也是全字段了),获取全字段，sourceEventType为新增sourceData本身就是全字段了
                try {
                    if (sourceEventType == EventTypeEnum.UPDATE.getType() || sourceEventType == EventTypeEnum.RECOVER.getType()) {
                        String id = BomUtils.getBomInstanceId(sourceData.getId(), sourceData.getApiName());
                        ObjectData sourceDataReturn = outerService.getObjectData(tenantId, sourceTenantType, sourceObjectApiName, id, true).getData();
                        if (sourceDataReturn != null) {
                            sourceData = sourceDataReturn;
                        } else {
                            log.warn("sourceEventType == EventTypeEnum.UPDATE outerService.getObjectData error tenantId={}, sourceTenantType={}, sourceObjectApiName={}, id={}", tenantId, sourceTenantType, sourceObjectApiName, sourceData.getId());
                            throw new ErpSyncDataException("Update event, failed to obtain all field data");
                        }
                    }
                    //补齐引用字段，当触发字段已经有了，不再去获取。
                    fillSyncConditionQuoteValueNew(tenantId, sourceData, oldSourceData, syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncConditions()
                    );
                } catch (ErpSyncDataException e) {
                    eventData.stop(e.getErrMsg());
                    saveErrorSyncDataByCache(eventData, syncPloyDetailSnapshotData);
                    throw e;
                }
            }
        } else {
            //获取对象全字段
            // ObjectData remoteSourceObjectData = outerService.getObjectData(sourceTenantId, sourceTenantType, sourceObjectApiName,
            //       sourceData.getId(), TraceContext.get().getTraceId(), true).getData();
            ObjectData remoteSourceObjectData = outerService.getObjectData(sourceTenantId, sourceTenantType, sourceObjectApiName,
                    oldSourceDataId, true).getData();
            if (remoteSourceObjectData != null) {
                sourceData = remoteSourceObjectData;
            }
        }
        //因为要补齐字段，可能sourcedata的值已经变化了。eventdata要记录最新的值。
        eventData.setSourceData(sourceData);
        sourceData.putId(oldSourceDataId);
    }

    private void saveErrorSyncDataByCache(SyncDataContextEvent eventData, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        if (syncPloyDetailSnapshotData == null || syncPloyDetailSnapshotData.getSyncPloyDetailData() == null) {
            return ;
        }
        SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();
        if (syncPloyDetailData.getIntegrationStreamNodes() == null || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode() == null
                || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTimeInterval() == null
                || syncPloyDetailData.getIntegrationStreamNodes().getReSyncErrorDataNode().getReSyncTopLimit() == null) {
            return ;
        }
        String tenantId=eventData.getTenantId();
        String syncPloyDetailSnapshotId=syncPloyDetailSnapshotData.getId();
        Integer dataReceiveType=eventData.getDataReceiveType();
        Integer sourceTenantType=eventData.getSourceTenantType();
        Integer masterDestEventType=null;
        if(syncPloyDetailSnapshotData.getSourceObjectApiName().equals(eventData.getSourceData().getApiName())){
            masterDestEventType=eventData.getDestEventType();
        }
        ObjectData sourceData=eventData.getSourceData();
        //伪造SyncDataEntity
        SyncDataEntity dataEntity = new SyncDataEntity();
        dataEntity.setSourceTenantType(sourceTenantType);
        dataEntity.setDestTenantType(eventData.getDestTenantType());
        dataEntity.setSourceDataId(sourceData.getId());
        dataEntity.setSourceDataName(sourceData.getName());
        dataEntity.setSourceEventType(eventData.getSourceEventType());
        dataEntity.setTenantId(tenantId);
        dataEntity.setSourceTenantId(sourceData.getTenantId());
        dataEntity.setSourceObjectApiName(sourceData.getApiName());
        dataEntity.setSourceData(sourceData);
        dataEntity.setDestTenantId(tenantId);
        dataEntity.setDestObjectApiName(eventData.getDestObjectApiName());
        dataEntity.setDestEventType(eventData.getDestEventType());
        dataEntity.setStatus(SyncDataStatusEnum.TRIGGER_FAILED.getStatus());
        dataEntity.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotId);
        dataEntity.setCreateTime(System.currentTimeMillis());
        dataEntity.setUpdateTime(System.currentTimeMillis());
        dataEntity.setIsDeleted(false);
        dataEntity.setSyncLogId(LogIdUtil.get());
        dataEntity.setOperatorId(sourceData.getLastModifiedBy());
        dataEntity.setDataReceiveType(dataReceiveType);
        reSyncDataNodeManager.saveErrorSyncData(tenantId,sourceTenantType,syncPloyDetailSnapshotId,
                dataReceiveType,masterDestEventType,Lists.newArrayList(dataEntity),false, eventData.getLocale());
    }

    /**
     * 这个方法是在同步从数据的时候调用的，检查该从数据对应的主对象。如果是CRM中要求主从一起写入的(新建，更新).
     * 则阻挡从对象的事件。
     */
    private Result2<Boolean> notAllowToWriteWithoutMaster(final String tenantId, Integer sourceEventType, String destMasterObjectAPIName) {
        Result2<Boolean> result=Result2.newSuccess();
        if (configCenterConfig.getUpdateCrmMasterDetailTogetherObjList(tenantId).contains(destMasterObjectAPIName)
                && EventTypeEnum.INVALID.getType() != sourceEventType) {
            result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s2009,tenantId));
            result.setData(false);
            return result;
        }
        result.setData(true);
        return result;
    }

    private Result2<Boolean> detailCheckCondition(String tenantId, String erpDataCenterId, String sourceObjectApiName, ObjectData sourceAllData, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        Result2<Boolean> result=Result2.newSuccess();
        String msg;
        Boolean isMatch = (Boolean) aviatorHelper.execute(
                syncPloyDetailSnapshotData.getDetailObjectSyncConditionsExpressions().get(sourceObjectApiName),
                sourceAllData,
                tenantId,
                erpDataCenterId,
                sourceObjectApiName,
                syncPloyDetailSnapshotData != null ? syncPloyDetailSnapshotData.getSyncPloyDetailId() : null,
                true);
        if (!isMatch) {
            msg=i18NStringManager.getByEi(I18NStringEnum.s897,tenantId);
            log.info("Not match detailObjectSyncConditionsExpressions={},sourceData={}", syncPloyDetailSnapshotData.getDetailObjectSyncConditionsExpressions(), sourceAllData);
            result.setErrMsg(msg);
            result.setData(false);
            return result;
        }
        result.setData(true);
        return result;
    }



    /**
     * 主对象数据范围检查重构
     */
    private Result2<Boolean> checkConditionMasterData(String dataCenterId,
                                                      SyncDataContextEvent eventData,
                                             SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        String ployDetailId = syncPloyDetailSnapshotData.getSyncPloyDetailId();
        String mongoId = eventData.getSourceData().getString("mongo_id");//从mongo获取的erp主数据才有这个字段
        Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
        String sourceObjectApiName = syncPloyDetailSnapshotData.getSourceObjectApiName();
        String tenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        ObjectData sourceData = eventData.getSourceData();
        Result2<Boolean> result=Result2.newSuccess();
        //在前面补全数据的时候，去补齐引用字段值
//        sourceData = fillSyncConditionQuoteValueNew(sourceData, syncPloyDetailSnapshotData.getSyncPloyDetailData().getSyncConditions(), sourceTenantType, outerService);
        if (syncPloyDetailSnapshotData.getSyncConditionsExpression() == null) {
            //加日志判断是否这两个入参会为空，导致异常
            log.warn("not exist expression or sourceData, syncRuleExpression={},sourceData={}", null, sourceData);
        }
        Boolean isMatch = (Boolean) aviatorHelper.execute(
                syncPloyDetailSnapshotData.getSyncConditionsExpression(),
                sourceData,
                tenantId,
                dataCenterId,
                sourceObjectApiName,
                ployDetailId,
                false);
        Boolean isMatchByQueryCrmData = true;
        if (isMatch) {
            //查询CRM节点的判断
            isMatchByQueryCrmData = isMatchByQueryCrmData(sourceTenantType, tenantId, sourceData, syncPloyDetailSnapshotData);
        }
        if (!isMatch || !isMatchByQueryCrmData) {
            String remark = null;
            if (!isMatch) {
                remark = i18NStringManager.getByEi(I18NStringEnum.s898, tenantId);
            } else {
                remark = i18NStringManager.getByEi(I18NStringEnum.s899, tenantId);
            }
            log.info("Not match syncRuleExpression={},sourceData={},isMatchByQueryCrmData={}", syncPloyDetailSnapshotData.getSyncConditionsExpression(), sourceData, isMatchByQueryCrmData);
            result.setErrMsg(remark);
            result.setData(false);
            return result;
        }
        result.setData(true);
        return result;
    }

    public Boolean isMatchByQueryCrmData(Integer sourceTenantType, String tenantId, ObjectData sourceData, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();
        String crmDcId;
        if (TenantType.CRM.equals(sourceTenantType)) {
            crmDcId = syncPloyDetailData.getSourceDataCenterId();
        } else {
            crmDcId = syncPloyDetailData.getDestDataCenterId();
        }
        if (syncPloyDetailData != null && syncPloyDetailData.getIntegrationStreamNodes() != null
                && syncPloyDetailData.getIntegrationStreamNodes().getSyncConditionsQueryDataNode() != null) {
            IntegrationStreamNodesData.SyncConditionsQueryDataNode node = syncPloyDetailData.getIntegrationStreamNodes().getSyncConditionsQueryDataNode();
            List<QueryObjectMappingData> queryObjectMappingDataList = node.getQueryObjectMappingData();
            if(CollectionUtils.isEmpty(queryObjectMappingDataList)){
                return true;
            }
            Boolean allHaveCrmObjectData=true;
            Boolean allNotHaveCrmObjectData=true;
            for (QueryObjectMappingData queryObjectMappingData : queryObjectMappingDataList) {
                List<List<FilterData>> orFilters = Lists.newArrayList();
                for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData fieldMappingData : queryObjectMappingData.getQueryFieldMappings()) {
                    List<FilterData> andFilters = Lists.newArrayList();
                    for (FilterData filterData : fieldMappingData) {
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterData.getFieldValue())) {
                            FilterData filter = JacksonUtil.fromJson(JacksonUtil.toJson(filterData), FilterData.class);
                            String sourceApiName = filterData.getFieldValue().get(0).toString();
                            Object sourceValue = sourceData.get(sourceApiName);
                            if (sourceValue == null) {
                                filter.setFieldValue(Lists.newArrayList());
                            } else {
                                filter.setFieldValue(Lists.newArrayList(sourceValue));
                            }
                            andFilters.add(filter);
                        }
                    }
                    orFilters.add(andFilters);
                }
                String crmObjApiName = queryObjectMappingData.getSourceObjectApiName();
                ObjectData crmObjectData = outerServiceFactory.get(TenantType.CRM).queryObjectDataByEqFilter(crmDcId, tenantId, sourceTenantType, crmObjApiName, orFilters).getData();
                if(crmObjectData==null){
                    allHaveCrmObjectData=false;
                    if (ObjectDataExistStatus.PRESENCE.equals(node.getSyncCondition())) {//都查到数据-同步
                        break;
                    }
                }else {
                    allNotHaveCrmObjectData=false;
                    if (ObjectDataExistStatus.ABSENT.equals(node.getSyncCondition())) {//查不到数据-同步
                        break;
                    }
                }
            }
            if (ObjectDataExistStatus.PRESENCE.equals(node.getSyncCondition())) {//都查到数据-同步
                return allHaveCrmObjectData;
            } else if (ObjectDataExistStatus.ABSENT.equals(node.getSyncCondition())) {//都查不到数据-同步
                return allNotHaveCrmObjectData;
            }
        }
        return true;
    }

    /**
     * 补齐从数据
     */
    private Result2<Boolean> fillDetailData(String dataCenterId,
                                   SyncDataContextEvent eventData,
                                   SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData,

                                   OverrideOuterService overrideOuterService) {
        Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
        ObjectData sourceData = eventData.getSourceData();
        Result2<Boolean> result=Result2.newSuccess();
        /**无论是设置了 强制同步主，还是主数据通过了数据范围过滤检查，都需要检查从数据。
         * 因为用户可能设置： 没有从数据则不同步主数据这类规则。
         *
         **/
        try {
            Map<String, List<ObjectData>> detailObjectDatasMap = this.getMatchDetailObjectDataNew(sourceData.getId(),
                    syncPloyDetailSnapshotData, sourceTenantType, dataCenterId, overrideOuterService);
            //注意这里的动作，主补齐了从数据。
            eventData.setDetailObjectDatasMap(detailObjectDatasMap);
            result.setData(true);
            return result;
        } catch (Exception e) {
            log.warn("masterCheckCondition Exception eventData =" + eventData, e);
            eventData.setErrCode(ResultCodeEnum.DATA_RULE_EXECUTE_FAILED.getErrCode());
            eventData.setErrMsg(e.getMessage());
            result.setErrCode(com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            result.setErrMsg(e.getMessage());
            result.setData(false);
            return result;
        }
    }


    /**
     * 返回满足从对象数据范围过滤的从数据。
     */
    private Map<String, List<ObjectData>> getMatchDetailObjectDataNew(String masterObjectId,
                                                                      SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData,
                                                                      Integer tenantType,
                                                                      String dataCenterId,
                                                                      OverrideOuterService overrideOuterService) {
        Map<String, List<ObjectData>> detailObjectDatasMap = Maps.newHashMap();
        SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();
        if (syncPloyDetailData == null || CollectionUtils.isEmpty(syncPloyDetailData.getDetailObjectMappings())) {
            return detailObjectDatasMap;
        }
        Integer sourceTenantType = syncPloyDetailData.getSourceTenantType();
        String sourceTenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        String destTenantId = syncPloyDetailSnapshotData.getDestTenantId();
        String sourceMasterObjectApiName = syncPloyDetailData.getSourceObjectApiName();
        //从对象数据范围储存Map方便下面相应对象获取
        Map<String, SyncConditionsData> detailSyncConditionsDataMap = syncPloyDetailData.getDetailObjectSyncConditions().stream()
                .collect(Collectors.toMap(SyncConditionsData::getApiName, syncConditionsData -> syncConditionsData));
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailData.getDetailObjectMappings()) {
            String detailObjectApiName = detailObjectMapping.getSourceObjectApiName();
            detailObjectDatasMap.putIfAbsent(detailObjectApiName, Lists.newArrayList());
            String masterFieldApiName = null;
            for (FieldMappingData fieldMapping : detailObjectMapping.getFieldMappings()) {
                if (fieldMapping.getSourceType().equals(FieldTypeContants.MASTER_DETAIL)) {
                    masterFieldApiName = fieldMapping.getSourceApiName();
                    break;
                }
            }
            if (StringUtils.isEmpty(masterFieldApiName)) {
                log.warn("getMatchDetailObjectData masterFieldApi is null,sourceTenantId={},sourceObjectApiName={},sourceDataId={},syncPloyDetailSnapshotId={}", sourceTenantId,
                        sourceMasterObjectApiName, masterObjectId, syncPloyDetailSnapshotData.getId());
                continue;
            }
            SyncConditionsData detailSyncConditionsData = detailSyncConditionsDataMap.get(detailObjectApiName);
            List<ObjectData> detailDatas = overrideOuterService
                    .listDetailDatasByIdAndFilter(sourceTenantId,
                            destTenantId,
                            sourceTenantType,
                            detailObjectApiName,
                            masterFieldApiName,
                            masterObjectId,
                            detailSyncConditionsData,
                            2000,
                            0,
                            dataCenterId).safeData();
            for (ObjectData objectData : detailDatas) {
                //如果是MULTI_UNIT_RELATED_OBJ(多单位对象) 或 SPU_SKU_SPEC_VALUE_RELATE_OBJ(商品、产品、规格、规格值关联对象) 时，会分别构造对应的对象
                if (detailObjectApiName.equals(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ)) {
                    objectData.putApiName(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ);
                    objectData.put("name", objectData.get("id"));
                    if (objectData.get(SpuSkuConstant.PRODUCT_ID) != null && syncPloyDetailSnapshotData.getSourceObjectApiName().equals(SpuSkuConstant.SPU_OBJ)) {
                        continue;
                    }
                } else if (detailObjectApiName.equals(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ)) {
                    objectData.putApiName(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ);
                    objectData.put("name", objectData.getId());
                    if (objectData.get(SpuSkuConstant.SKU_ID) != null && syncPloyDetailSnapshotData.getSourceObjectApiName().equals(SpuSkuConstant.SPU_OBJ)) {
                        continue;
                    }
                }
                detailObjectDatasMap.get(detailObjectApiName).add(objectData);
            }
        }
        return detailObjectDatasMap;
    }

    private boolean matchUpdateData(SyncDataContextEvent eventData, ObjectData updateData, List<FieldMappingData> fieldMappingDataList, Integer destEventType) {
        log.debug("SyncPreManager.matchUpdateData,updateData={},fieldMappingDataList={}", updateData, JSONObject.toJSONString(fieldMappingDataList));
        //去除一些指定字段的被设置成filedMapping，例如 _id apiName tenantId,目前eventTrigger没有引入ConfigCenter.先过滤_id
        Set<String> SKIP_FILTER_MATCH_UPDATE_FIELD = Sets.newHashSet("_id");
        for (FieldMappingData fieldMappingData : fieldMappingDataList) {
            if ((updateData.containsKey(fieldMappingData.getSourceApiName())) && !SKIP_FILTER_MATCH_UPDATE_FIELD.contains(fieldMappingData.getSourceApiName()) && ( destEventType == EventTypeEnum.ADD.getType() || BooleanUtils.isNotTrue(fieldMappingData.getNotUpdateField()))) {
                log.debug("SyncPreManager.matchUpdateData,return={}", true);
                eventData.setMatchField(fieldMappingData.getSourceApiName());
                return true;
            }
        }
        log.debug("SyncPreManager.matchUpdateData,return={}", false);
        return false;
    }

    /**
     * 是否匹配数据范围的字段。
     */
    private boolean matchFilterData(SyncDataContextEvent eventData,ObjectData updateData, SyncConditionsData syncConditionsData) {
        log.debug("SyncPreManager.matchFilterData,updateData={},syncConditionsData={}", updateData, JSONObject.toJSONString(syncConditionsData));
        if ((null == syncConditionsData) || CollectionUtils.isEmpty(syncConditionsData.getFilters())) {
            /**
             * 字段映射是肯定存在的， 数据范围过滤是可能存在也可能不存在。
             * 数据范围过滤条件 syncConditions 为空的时候，要返回false,
             * 因为最后结果是 先取 ! 然后再 && 的关系，此时只检查修改字段是不是映射过的字段就可以了。*/
            log.debug("SyncPreManager.matchFilterData,return={}", false);
            return false;
        }
        for (List<FilterData> orFilterData : syncConditionsData.getFilters()) {
            for (FilterData andFilterData : orFilterData) {
                if (updateData.containsKey(andFilterData.getFieldApiName())) {
                    eventData.setMatchField(andFilterData.getFieldApiName());
                    log.debug("SyncPreManager.matchFilterData,return={}", true);
                    return true;
                }
            }
        }
        log.debug("SyncPreManager.matchFilterData,return={}", false);
        return false;
    }

    private ObjectData fillReferenceValue(ObjectData updateData, ObjectData objectData) {
        if (updateData == null) {
            return null;
        }
        Map<String, Object> referMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : updateData.entrySet()) {
            String fieldName = entry.getKey();
            Object filedValue = entry.getValue();
            if (filedValue != null) {
                Object referValue = objectData.getReferName(fieldName);
                if (referValue != null) {
                    log.info("trace fillReferenceValue fieldName:{}, referValue:{}, class:{} ", fieldName, referValue, referValue.getClass());
                    referMap.put(fieldName + "__r", referValue);
                }
            }
        }
        updateData.putAll(referMap);
        return updateData;
    }

//    private ObjectData fillSyncConditionQuoteValue(ObjectData objectData, SyncConditionsData syncConditions, Integer tenantType) {
//        List<List<FilterData>> filters = syncConditions.getFilters();
//        if (filters == null || filters.isEmpty()) {
//            return objectData;
//        }
//        for (List<FilterData> filter : filters) {
//            for (FilterData filterData : filter) {
//                if (filterData.getFieldType().equals(FieldTypeContants.QUOTE)) {
//                    String sourceQuoteRealField = filterData.getQuoteRealField();
//                    String sourceQuoteFieldTargetObjectField = filterData.getQuoteFieldTargetObjectField();
//                    String sourceQuoteFieldTargetObjectApiName = filterData.getQuoteFieldTargetObjectApiName();
//                    String quoteFieldValue = objectData.getString(sourceQuoteRealField);
//                    if (StringUtils.isEmpty(quoteFieldValue)) {
//                        continue;
//                    }
//                    ObjectData quoteTargetObjectData = outerServiceFactory.get(tenantType).getObjectData(objectData.getTenantId(), tenantType, sourceQuoteFieldTargetObjectApiName, quoteFieldValue, TraceContext.get().getTraceId(), true)
//                            .getData();
//                    if (quoteTargetObjectData == null) {
//                        return null;
//                    }
//                    Object realValue = quoteTargetObjectData.get(sourceQuoteFieldTargetObjectField);
//                    if (realValue != null) {
//                        objectData.put(filterData.getFieldApiName(), realValue);
//                    }
//                }
//            }
//        }
//        return objectData;
//    }


    //数据范围中的条件如果用到了引用字段，要把引用字段的值替换为目标值。
    private void fillSyncConditionQuoteValueNew(String tenantId, ObjectData objectData, ObjectData oldObjectData, SyncConditionsData syncConditions) {
        List<List<FilterData>> filters = syncConditions.getFilters();
        if (filters == null || filters.isEmpty()) {
            return;
        }
        //先找出所有的引用字段筛选条件
        List<FilterData> quoteFilters = filters.stream().flatMap(v -> v.stream())
                //变更字段已经包含，不再查询
                .filter(v -> FieldTypeContants.QUOTE.equals(v.getFieldType())
                        //触发数据包含则不需要再查询了。大量变更了数据范围字段的值时，就不会查询。
                        //由于前面补齐时，可能把引用字段补充到了sourceData，这里必须查一次，否则引用布尔值会变成字符串，数据范围校验有问题
//                        && !oldObjectData.containsKey(v.getFieldApiName())
                        //引用的对象id为空不用查询
                        && StrUtil.isNotBlank(objectData.getString(v.getQuoteRealField()))
                ).collect(Collectors.toList());


        //准备待查询的数据参数
        //<objApiName,id>
        SetValueMap<String, String> needSearchIds = new SetValueMap<>();
        //<objApiName,fields>
        SetValueMap<String, String> needSearchFields = new SetValueMap<>();
        for (FilterData filterData : quoteFilters) {
            String quoteFieldTargetObjectApiName = filterData.getQuoteFieldTargetObjectApiName();
            String quoteObjDataId = objectData.getString(filterData.getQuoteRealField());
            String quoteFieldTargetObjectField = filterData.getQuoteFieldTargetObjectField();
            needSearchIds.putValue(quoteFieldTargetObjectApiName, quoteObjDataId);
            needSearchFields.putValue(quoteFieldTargetObjectApiName, quoteFieldTargetObjectField);
        }

        //执行查询
        //<obj,id,对象数据（仅引用的字段）>
        Table<String, String, ObjectData> objDataMap = new RowKeyTable<>();
        for (Map.Entry<String, Set<String>> entry : needSearchIds) {
            String objApiName = entry.getKey();
            Set<String> ids = entry.getValue();
            Set<String> fields = needSearchFields.get(objApiName);
            Result<List<ObjectData>> listResult = crmRemoteService.listByIdSelective(tenantId, objApiName, ids, fields);
            if (!listResult.isSuccess()) {
                //crm接口异常,或字段不正确，中断
                log.warn("trace fillSyncConditionQuoteValueNew listByIdSelective,objApiName:{},ids:{},fields:{} error:{}", objApiName, ids, fields, listResult);
                throw new ErpSyncDataException("get crm object data failed");
            }
            listResult.getData().forEach(v -> {
                objDataMap.put(objApiName, v.getId(), v);
            });
        }

        //设置字段值
        for (FilterData filterData : quoteFilters) {
            String quoteFieldTargetObjectApiName = filterData.getQuoteFieldTargetObjectApiName();
            String quoteObjDataId = objectData.getString(filterData.getQuoteRealField());
            String quoteFieldTargetObjectField = filterData.getQuoteFieldTargetObjectField();
            ObjectData quoteObjData = objDataMap.get(quoteFieldTargetObjectApiName, quoteObjDataId);
            if (quoteObjData != null && quoteObjData.get(quoteFieldTargetObjectField) != null) {
                //和原逻辑保持一致，当引用对象或字段值为空时，不设置值
                objectData.put(filterData.getFieldApiName(), quoteObjData.get(quoteFieldTargetObjectField));
            }
        }
    }


    private void setDataVersion(String tenantId,SyncDataContextEvent eventData, ErpTempData erpTempData) {
        ObjectData sourceData = eventData.getSourceData();
        if(eventData.getSourceTenantType()==TenantType.CRM){
            if(sourceData.getVersion()!=null){
                erpTempData.setLastSyncTime(sourceData.getVersion());
            }else{
                Result2<ObjectData> objectDataResult2 = outerServiceFactory.get(TenantType.CRM).getObjectData(tenantId, TenantType.CRM, sourceData.getApiName(), sourceData.getId());
                if(objectDataResult2!=null&&objectDataResult2.isSuccess()&&objectDataResult2.getData()!=null){
                    erpTempData.setLastSyncTime(objectDataResult2.getData().getVersion());
                }
            }
        }else {
            erpTempData.setLastSyncTime(eventData.getDataVersion());
        }
    }
}
