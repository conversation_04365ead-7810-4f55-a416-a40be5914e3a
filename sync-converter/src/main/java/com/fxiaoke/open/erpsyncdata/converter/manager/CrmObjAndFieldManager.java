package com.fxiaoke.open.erpsyncdata.converter.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.map.MapUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @Date: 15:34 2022/11/29
 * @Desc:
 */
@Slf4j
@Component
public class CrmObjAndFieldManager {
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CrmObjAndFieldManager self;

    private static TimedCache<String, ObjectDescribe> tenantObjectDescribeCache = CacheUtil.newTimedCache(1000 * 60 * 3);

    //统计，计算
    private static List<String> countOrFormulaField = Lists.newArrayList(FieldType.COUNT, FieldType.FORMULA);
    //引用
    private static List<String> quoteField = Lists.newArrayList(FieldType.QUOTE);
    //统计，计算，引用
    private static List<String> countOrFormulaOrQuoteField = Lists.newArrayList(FieldType.COUNT, FieldType.FORMULA, FieldType.QUOTE);

    static {
        tenantObjectDescribeCache.schedulePrune(1000 * 60 * 3);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Boolean objFieldIsQuoteField(String tenantId, String objApiName, String fieldApiName) {
        return self.objFieldIsSpecialField(tenantId, objApiName, fieldApiName, quoteField);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Boolean objFieldIsCountOrFormulaField(String tenantId, String objApiName, String fieldApiName) {
        return self.objFieldIsSpecialField(tenantId, objApiName, fieldApiName, countOrFormulaField);
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 600, cacheType = CacheType.LOCAL, localLimit = 5000)
    public Boolean objFieldIsCountOrFormulaOrQuoteField(String tenantId, String objApiName, String fieldApiName) {
        return self.objFieldIsSpecialField(tenantId, objApiName, fieldApiName, countOrFormulaOrQuoteField);
    }


    /**
     * 统计字段、计算字段、非落地引用字段
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Boolean objFieldIsCountOrFormulaOrUnIndexedQuoteField(String tenantId, String objApiName, String fieldApiName) {
        return self.objFieldIsSpecialField(tenantId, objApiName, fieldApiName, countOrFormulaOrQuoteField);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Boolean allObjFieldIsCountOrFormulaOrQuoteField(String tenantId, String objApiName, Collection<String> fieldApiNames) {
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return false;
        }
        Boolean isSpecialField = true;
        for (String fieldApiName : fieldApiNames) {
            isSpecialField = objFieldIsCountOrFormulaOrQuoteField(tenantId, objApiName, fieldApiName);
            if (!isSpecialField) {
                break;
            }
        }
        return isSpecialField;
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(expire = 600, cacheType = CacheType.LOCAL, localLimit = 5000)
    public Boolean objFieldIsSpecialField(String tenantId, String objApiName, String fieldApiName, List<String> specialField) {
        return predicateObjField(tenantId, objApiName, fieldApiName, desc -> specialField.contains(fieldApiName));
    }

    /**
     * 字段条件判断，此处无缓存
     *
     * @param predictedProp and条件，是否满足这些值，key：属性，value：值
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Boolean predicateObjField(String tenantId, String objApiName, String fieldApiName, Predicate<FieldDescribe> fieldPredicate) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(objApiName) || StringUtils.isBlank(fieldApiName)) {
            return false;
        }
        ObjectDescribe describeResult = getObjectDescribeCache(tenantId, objApiName);
        if (describeResult == null) {
            return false;
        }
        FieldDescribe fieldDescribe = describeResult.getFields().get(fieldApiName);
        if (MapUtil.isEmpty(fieldDescribe)) {
            //如果缓存中的描述，没有包含需要查找的字段，再次调用接口，所以不要传对象描述中不存在的字段，否则缓存失效多调用接口
            describeResult = this.getObjectDescribe(tenantId, objApiName);
            if (describeResult == null) {
                return false;
            }
        }
        fieldDescribe = describeResult.getFields().get(fieldApiName);
        if (MapUtil.isNotEmpty(fieldDescribe)) {
            return fieldPredicate.test(fieldDescribe);
        }
        return false;
    }


    public ObjectDescribe getObjectDescribe(String tenantId, String objApiName) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId, i18NStringManager);
        ObjectDescribe describe = objectDescribeService.getDescribe(headerObj, objApiName).getData().getDescribe();
        String cacheKey = tenantId + "_" + objApiName;
        tenantObjectDescribeCache.put(cacheKey, describe);
        return describe;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public ObjectDescribe getObjectDescribeCache(String tenantId, String objApiName) {
        String cacheKey = tenantId + "_" + objApiName;
        ObjectDescribe objectDescribe = tenantObjectDescribeCache.get(cacheKey, false);
        if (objectDescribe == null) {
            this.getObjectDescribe(tenantId, objApiName);
            objectDescribe = tenantObjectDescribeCache.get(cacheKey, false);
        }
        return objectDescribe;
    }

    /**
     * @param tenantId
     * @param objApiName
     * @return
     * @desc 如果找不到描述或者本来就是是主对象，返回自己
     */
    @Cached(expire = 600, cacheType = CacheType.LOCAL, localLimit = 5000)
    public String getCrmObjMasterObjApiName(String tenantId, String objApiName) {
        ObjectDescribe describeResult = getObjectDescribeCache(tenantId, objApiName);
        if (describeResult == null) {
            return objApiName;
        }
        for (FieldDescribe fieldDescribe : describeResult.getFields().values()) {
            if (FieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
                return fieldDescribe.getTargetApiName();
            }
        }
        return objApiName;
    }
}
