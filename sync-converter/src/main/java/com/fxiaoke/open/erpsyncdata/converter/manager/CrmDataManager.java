package com.fxiaoke.open.erpsyncdata.converter.manager;

import cn.hutool.core.collection.ListUtil;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 访问CRM数据
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class CrmDataManager {
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private I18NStringManager i18NStringManager;

    public ObjectData getById(String tenantId, String objApiName, String id, String... selectFields) {
        List<String> fields = Objects.isNull(selectFields) ? null : Lists.newArrayList(selectFields);
        return getByIdSelective(tenantId, objApiName, id, fields);
    }

    public ObjectData getByIdSelective(String tenantId, String objApiName, String id, List<String> fields) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId, i18NStringManager);
        GetByIdArg getByIdArg = new GetByIdArg();
        getByIdArg.setDescribeApiName(objApiName);
        getByIdArg.setDataId(id);
        getByIdArg.setIncludeInvalid(true);
        //实时计算引用和计算字段
        getByIdArg.setCalculateFormula(true);
        getByIdArg.setCalculateQuote(true);
        getByIdArg.setSelectFields(fields);
        final Result<ObjectDataGetByIdV3Result> result = objectDataServiceV3.getById(headerObj, getByIdArg);
        return result.getData().getObjectData();
    }

    /**
     * 根据id获取CRM数据，获取失败会抛异常
     * 仅获取为删除未作废的
     */
    public List<ObjectData> listCRMObjsByIds(String tenantId, String objApiName, List<String> ids, List<String> selectFields) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        searchTemplateQuery.addFilter("_id", ids, "IN");
        searchTemplateQuery.addFilter("is_deleted", ListUtil.of("false"), "EQ");
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> queryAccount;
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchTemplateQuery));
        findV3Arg.setDescribeApiName(objApiName);
        //指定返回字段
        findV3Arg.setSelectFields(selectFields);
        try {
            Result<ObjectDataQueryListResult> listResultResult = objectDataServiceV3.queryList(headerObj, findV3Arg);
            if (listResultResult.isSuccess()) {
                return listResultResult.getData().getQueryResult().getDataList();
            } else {
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s242.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s242.getI18nValue(), listResultResult.getMessage()),
                        Lists.newArrayList(listResultResult.getMessage())),
                        null,
                        null);
            }
        } catch (Exception e) {
            throw ErpSyncDataException.wrap(e, I18NStringEnum.s241.getText());
        }
    }

    public void handleAllData(String tenantId, String objectApiName, Consumer<ObjectData> function, Filter... filters) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId, i18NStringManager);
        final int size = 100;

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setSearchSource("es");
        searchTemplateQuery.setLimit(size);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setOrders(Lists.newArrayList(new SearchTemplateQueryOrderBy(ObjectDescribeContants.ID, true)));

        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);

        boolean hasStatusFilter = false;
        if (Objects.nonNull(filters)) {
            for (Filter filter : filters) {
                searchTemplateQuery.getFilters().add(filter);
            }
            hasStatusFilter = Arrays.stream(filters).anyMatch(filter -> Objects.equals(filter.getFieldName(), "is_deleted"));
        }
        searchTemplateQuery.addFilter(ObjectDescribeContants.TENANT_ID, Lists.newArrayList(tenantId), FilterOperatorEnum.EQ);
        if (!hasStatusFilter) {
            searchTemplateQuery.addFilter("is_deleted", ListUtil.of("false"), FilterOperatorEnum.EQ);
        }

        String maxId = null;
        while (true) {
            if (Objects.nonNull(maxId)) {
                searchTemplateQuery.addFilter(ObjectDescribeContants.ID, Lists.newArrayList(maxId), FilterOperatorEnum.GT);
            }
            findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchTemplateQuery));

            final List<ObjectData> dataList;
            try {
                Result<ObjectDataQueryListResult> listResultResult = objectDataServiceV3.queryList(headerObj, findV3Arg);
                if (listResultResult.isSuccess()) {
                    dataList = listResultResult.getData().getQueryResult().getDataList();
                } else {
                    throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s242.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s242.getI18nValue(), listResultResult.getMessage()),
                            Lists.newArrayList(listResultResult.getMessage())),
                            null,
                            null);
                }
            } catch (Exception e) {
                throw ErpSyncDataException.wrap(e, I18NStringEnum.s241.getText());
            }

            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }

            dataList.forEach(function);

            maxId = dataList.get(dataList.size() - 1).getId();
        }
    }
}
