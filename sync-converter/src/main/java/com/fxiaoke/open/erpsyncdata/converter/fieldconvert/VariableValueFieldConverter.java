package com.fxiaoke.open.erpsyncdata.converter.fieldconvert;

import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.PartnerFieldContants;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FiledVariableValueEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.common.util.FieldTypeUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.GetFsAccountByOuterData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService;
import com.google.common.collect.Lists;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
@Slf4j
@Deprecated
public class VariableValueFieldConverter implements FieldConverter {
    @Autowired
    private OuterServiceFactory outerServiceFactory;

    @Override
    public Object convert(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        OuterService outerService = outerServiceFactory.get(TenantTypeEnum.CRM.getType());
        Integer srcEi = Integer.valueOf(sourceData.getTenantId());
        Integer destEi = Integer.valueOf(destTenantId);
        String value = fieldMappingData.getValue();
        String destType = fieldMappingData.getDestType();
        Object result;
        if (StringUtils.isEmpty(value)){
            return null;
        }
        if (value.equals(FiledVariableValueEnum.PARTNER_OWNER.getValue())) {
            result = this.doConvertPartnerOwner(destEi, srcEi, TenantTypeEnum.CRM.getType());
        } else if (value.equals(FiledVariableValueEnum.PARTNER_SOURCE.getValue())) {
            result = outerService.getMapperObjectId(destEi, srcEi, PartnerFieldContants.API_NAME).getData();
        } else if (value.equals(FiledVariableValueEnum.ACCOUNT_OWNER.getValue())) {
            result = this.doConvertAccountOwner(destEi, srcEi, TenantTypeEnum.CRM.getType());
        } else if (value.equals(FiledVariableValueEnum.ACCOUNT_SOURCE.getValue())) {
            result = outerService.getMapperObjectId(destEi, srcEi, AccountFieldContants.API_NAME).getData();
        } else if (value.equals(FiledVariableValueEnum.PRIORITY_RELATION_OWNER.getValue())) {
            result = this.doConvertAccountOwner(destEi, srcEi, TenantTypeEnum.CRM.getType());
            if (result == null){
                result = this.doConvertPartnerOwner(destEi, srcEi, TenantTypeEnum.CRM.getType());
            }
        } else if (value.equals(FiledVariableValueEnum.PRIORITY_RELATION_SOURCE.getValue())) {
            result = outerService.getMapperObjectId(destEi, srcEi, AccountFieldContants.API_NAME).getData();
            if (result == null){
                result = outerService.getMapperObjectId(destEi, srcEi, PartnerFieldContants.API_NAME).getData();
            }
        } else if (value.equals(FiledVariableValueEnum.OBJECT_DATA_REFLECT_OUTER_OWNER.getValue())) {
            if (CollectionUtils.isEmpty(sourceData.getOwner()) || StringUtils.isEmpty(sourceData.getOwner().get(0))) {
                return null;
            }
            result = outerService.getOuterAccountByFs(destEi, srcEi, Integer.valueOf(sourceData.getOwner().get(0))).getData().getOuterUid();
        } else if (value.equals(FiledVariableValueEnum.OUTER_RELATION_OWNER.getValue())) {
            result = outerService.getDownstreamRelationOwnerOuterUid(destEi, srcEi).getData();
        } else if (value.equals(FiledVariableValueEnum.DOWNSTREAM_RELATION_OWNER.getValue())) {
            result = this.doConvertDownstreamRelationOwner(srcEi, destEi, TenantTypeEnum.CRM.getType());
        } else if (value.equals(FiledVariableValueEnum.PURCHASE_ORDER_SUPPLIER_ID.getValue())) {
            result = this.getDefaultPurchaseOrderSupplierId(String.valueOf(srcEi),destTenantId, TenantTypeEnum.CRM.getType());
        } else if (value.equals(FiledVariableValueEnum.GOODS_RECEIVED_NOTE_WAREHOUSE_ID.getValue())) {
            result = this.getDefaultGoodsReceivedNoteWarehouseId(destTenantId, TenantTypeEnum.CRM.getType());
        } else if (value.equals(FiledVariableValueEnum.INPUT_CURRENT_DATE.getValue()) || value.equals(FiledVariableValueEnum.OUTPUT_CURRENT_DATE.getValue())) {
            result = this.getCurrentDateLong();
        } else if (value.equals(FiledVariableValueEnum.INPUT_CURRENT_DATE_TIME.getValue()) || value.equals(FiledVariableValueEnum.OUTPUT_CURRENT_DATE_TIME.getValue())) {
            result = this.getCurrentDateTimeLong();
        } else if (value.equals(FiledVariableValueEnum.INPUT_CURRENT_TIME.getValue()) ||value.equals(FiledVariableValueEnum.OUTPUT_CURRENT_TIME.getValue())) {
            result = this.getCurrentTimeLong();
        } else {
            throw new SyncDataException(ResultCodeEnum.SYNC_PLOY_TYPE_NOT_EXIST.getErrCode(), ResultCodeEnum.SYNC_PLOY_TYPE_NOT_EXIST.getErrMsg());
        }
        return FieldTypeUtil.convertByFieldType(result, destType);
    }

    @Override
    public Integer getFieldMappingType() {
        return FieldMappingTypeEnum.VARIABLE_VALUE.getType();
    }

    private Object getDefaultPurchaseOrderSupplierId(String sourceTenantId,String destTenantId, Integer tenantType) {
        Map<String, List<String>> fieldValues = new HashedMap();
        fieldValues.put("upstream_ei__c", Lists.newArrayList(sourceTenantId));
        List<ObjectData> detailDatas = outerServiceFactory.get(tenantType).listObjectDatas(destTenantId, 1, "SupplierObj", fieldValues, 1, 0).getData();
        if (CollectionUtils.isEmpty(detailDatas)){
            return null;
        }
        return detailDatas.get(0).getId();
    }

    private Object getDefaultGoodsReceivedNoteWarehouseId(String  destTenantId, Integer tenantType) {
        Map<String, List<String>> fieldValues = new HashedMap();
        fieldValues.put("is_default", Lists.newArrayList("true"));
        List<ObjectData> detailDatas = outerServiceFactory.get(tenantType).listObjectDatas(destTenantId, 1, "WarehouseObj", fieldValues, 1, 0).getData();
        if (CollectionUtils.isEmpty(detailDatas)){
            return null;
        }
        return detailDatas.get(0).getId();

    }

    private Object doConvertDownstreamRelationOwner(Integer upstreamEi, Integer downstreamEi, Integer tenantType) {
        OuterService outerService = outerServiceFactory.get(tenantType);
        Long relationOwnerOuterUid = outerService.getDownstreamRelationOwnerOuterUid(upstreamEi, downstreamEi).getData();
        if (relationOwnerOuterUid == null) {
            return null;
        }
        GetFsAccountByOuterData getFsAccountByOuterData = outerService.getFsAccountByOuter(relationOwnerOuterUid).getData();
        return getFsAccountByOuterData.getEmployeeId();
    }

    private Object doConvertAccountOwner(Integer upstreamEi, Integer downstreamEi, Integer tenantType) {
        OuterService outerService = outerServiceFactory.get(tenantType);
        String accountId = outerService.getMapperObjectId(upstreamEi, downstreamEi, AccountFieldContants.API_NAME).getData();
        return outerService.getOwnerId(upstreamEi, null, AccountFieldContants.API_NAME, accountId).getData();
    }

    private Object doConvertPartnerOwner(Integer upstreamEi, Integer downstreamEi, Integer tenantType) {
        OuterService outerService = outerServiceFactory.get(tenantType);
        String partnerId = outerService.getMapperObjectId(upstreamEi, downstreamEi, PartnerFieldContants.API_NAME).getData();
        return outerService.getOwnerId(upstreamEi, null, PartnerFieldContants.API_NAME, partnerId).getData();
    }

    private Object getCurrentDateLong() {
        Long dateLong = 0L;
        try {
            SimpleDateFormat dateDf = new SimpleDateFormat("yyyy-MM-dd");
            dateLong =  dateDf.parse(dateDf.format(new Date())).getTime();
        } catch (ParseException e) {
            log.warn("getCurrentDateLong is fail");
        }
        return dateLong;
    }

    private Object getCurrentDateTimeLong() {
        Long dateLong = 0L;
        try {
            SimpleDateFormat dateTimeDf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            dateLong =  dateTimeDf.parse(dateTimeDf.format(new Date())).getTime();
        } catch (ParseException e) {
            log.warn("getCurrentDateTimeLong is fail");
        }
        return dateLong;
    }
    private Object getCurrentTimeLong() {
        Long dateLong = 0L;
        try {
            SimpleDateFormat timeDf = new SimpleDateFormat("HH:mm");
            dateLong =  timeDf.parse(timeDf.format(new Date())).getTime();
        } catch (ParseException e) {
            log.warn("getCurrentTimeLong is fail");
        }
        return dateLong;
    }

}
