package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FixedValueFieldConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 从SourceData获取数据查询crm数据赋值给目标
 */
@Slf4j
@Component
public class NodeQueryCrmObject2DestManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private I18NStringManager i18NStringManager;

    public NodeQueryCrmObject2DestManager() {
        super(DataNodeNameEnum.QueryCrmObject2DestNode);
    }

    @Override
    public boolean needProcess(SyncDataContextEvent ctx) {
        if (!super.needProcess(ctx)) {
            return false;
        }
        Integer sourceEventType = ctx.getSourceEventType();
        //保持原代码逻辑，除了作废，删除，恢复，都处理
        return !(
                EventTypeEnum.INVALID.match(sourceEventType)
                        || EventTypeEnum.DELETE_DIRECT.match(sourceEventType)
                        || EventTypeEnum.RECOVER.match(sourceEventType)
        );
    }

    @CompareSyncField(syncType = SyncCompareConstant.SYNC_QUERY_CRM_TO_DEST)
    public SyncDataContextEvent processMessage(SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String dataSourceObjApiName = syncDataContextEvent.getSyncDataData().getSourceObjectApiName();
        String dataDestObjApiName = syncDataContextEvent.getSyncDataData().getDestObjectApiName();
        Integer sourceTenantType = syncDataContextEvent.getSyncDataData().getSourceTenantType();
        String snapId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, snapId).getData();
        Boolean isMaster = dataSourceObjApiName.equals(syncPloyDetailSnapshotData.getSourceObjectApiName());
        String crmDcId;
        if (TenantType.CRM.equals(sourceTenantType)) {
            crmDcId = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceDataCenterId();
        } else {
            crmDcId = syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestDataCenterId();
        }
        if (syncPloyDetailSnapshotData != null && syncPloyDetailSnapshotData.getSyncPloyDetailData() != null
                && syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes() != null) {
            boolean hasQueryCrmObject2DestBySource = false,hasQueryCrmObject2DestByDest = false;
            IntegrationStreamNodesData streamNodesData = syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes();
            try{
                if (streamNodesData.getQueryCrmObject2DestNodeBySource() != null) {//从源获取数据，查询crm赋值给目标
                    hasQueryCrmObject2DestBySource = true;
                    IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode = streamNodesData.getQueryCrmObject2DestNodeBySource();
                    if (isMaster) {//主对象，主从一起处理
                        //主对象
                        if (syncDataContextEvent.getDestData() != null && CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())) {
                            for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                                QueryObjectMappingData queryObjectMappingData = queryObjectToDestObjectData.getQueryObjectMappingData();
                                DetailObjectMappingsData.DetailObjectMappingData source2SyncDataMapping = queryObjectToDestObjectData.getQueryData2DestDataMapping();
                                if (queryObjectMappingData == null || source2SyncDataMapping == null) {
                                    continue;
                                }
                                doQueryCrmObject2Dest(true, crmDcId, tenantId, sourceTenantType, syncDataContextEvent.getSyncDataData().getSourceData(), queryObjectMappingData,
                                        source2SyncDataMapping, syncDataContextEvent.getDestData());
                            }
                        }
                        //从对象
                        //从数据
                        List<SyncDataData> detailSyncDataData = Lists.newArrayList();
                        if (syncDataContextEvent.getSyncDataData() != null && syncDataContextEvent.getSyncDataData().getSourceDetailSyncDataIds() != null) {
                            Map<String, List<String>> sourceDetailSyncDataIds = syncDataContextEvent.getSyncDataData().getSourceDetailSyncDataIds();
                            for (String objApiName : sourceDetailSyncDataIds.keySet()) {
                                List<SyncDataData> detailSyncDatas = syncDataManager.batchGet(tenantId, sourceDetailSyncDataIds.get(objApiName), true);
                                if (CollectionUtils.isNotEmpty(detailSyncDatas)) {
                                    detailSyncDataData.addAll(detailSyncDatas);
                                }
                            }
                        }
                        if (CollectionUtils.isNotEmpty(detailSyncDataData) && CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())) {
                            DetailQueryObjectMappingsData detailQueryData2DestDataMapping = queryCrmObject2DestNode.getDetailQueryData2DestDataMapping();
                            LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
                            for (SyncDataData syncData : detailSyncDataData) {
                                ObjectData destData = destDetailSyncDataIdAndDestDataMap.get(syncData.getId());
                                if (destData == null) {
                                    continue;
                                }
                                String sourceDetailObjApiName = syncData.getSourceObjectApiName();
                                List<QueryObjectToDestObjectData> queryObjectToDestObject = null;
                                for (List<QueryObjectToDestObjectData> detail : detailQueryData2DestDataMapping) {
                                    if (CollectionUtils.isEmpty(detail)) {
                                        continue;
                                    }
                                    if (sourceDetailObjApiName.equals(detail.get(0).getQueryObjectMappingData().getDestObjectApiName())) {
                                        queryObjectToDestObject = detail;
                                        break;
                                    }
                                }
                                if (CollectionUtils.isEmpty(queryObjectToDestObject)) {
                                    continue;
                                }
                                for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryObjectToDestObject) {
                                    QueryObjectMappingData queryObjectMappingData = queryObjectToDestObjectData.getQueryObjectMappingData();
                                    DetailObjectMappingsData.DetailObjectMappingData source2SyncDataMapping = queryObjectToDestObjectData.getQueryData2DestDataMapping();
                                    if (queryObjectMappingData == null || source2SyncDataMapping == null) {
                                        continue;
                                    }
                                    doQueryCrmObject2Dest(true, crmDcId, tenantId, sourceTenantType, syncData.getSourceData(), queryObjectMappingData,
                                            source2SyncDataMapping, destData);
                                }

                            }
                        }
                    } else {
                        //从对象，单独处理
                        DetailQueryObjectMappingsData detailQueryData2DestDataMapping = queryCrmObject2DestNode.getDetailQueryData2DestDataMapping();
                        List<QueryObjectToDestObjectData> queryObjectToDestObject = null;
                        for (List<QueryObjectToDestObjectData> detail : detailQueryData2DestDataMapping) {
                            if (CollectionUtils.isEmpty(detail)) {
                                continue;
                            }
                            if (dataSourceObjApiName.equals(detail.get(0).getQueryObjectMappingData().getDestObjectApiName())) {
                                queryObjectToDestObject = detail;
                                break;
                            }
                        }
                        if (syncDataContextEvent.getDestData() != null && CollectionUtils.isNotEmpty(queryObjectToDestObject)) {
                            for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryObjectToDestObject) {
                                QueryObjectMappingData queryObjectMappingData = queryObjectToDestObjectData.getQueryObjectMappingData();
                                DetailObjectMappingsData.DetailObjectMappingData source2SyncDataMapping = queryObjectToDestObjectData.getQueryData2DestDataMapping();
                                if (queryObjectMappingData == null || source2SyncDataMapping == null || syncDataContextEvent.getDestData() == null) {
                                    continue;
                                }
                                doQueryCrmObject2Dest(true, crmDcId, tenantId, sourceTenantType, syncDataContextEvent.getSyncDataData().getSourceData(), queryObjectMappingData,
                                        source2SyncDataMapping, syncDataContextEvent.getDestData());
                            }
                        }
                    }
                }
                if (streamNodesData.getQueryCrmObject2DestNodeByDest() != null) {//从转换后的destData获取数据，查询crm赋值给目标
                    hasQueryCrmObject2DestByDest = true;
                    IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode = streamNodesData.getQueryCrmObject2DestNodeByDest();
                    if (isMaster) {//主对象，主从一起处理
                        //主对象
                        if (syncDataContextEvent.getDestData() != null && CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())) {
                            for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                                QueryObjectMappingData queryObjectMappingData = queryObjectToDestObjectData.getQueryObjectMappingData();
                                DetailObjectMappingsData.DetailObjectMappingData source2SyncDataMapping = queryObjectToDestObjectData.getQueryData2DestDataMapping();
                                if (queryObjectMappingData == null || source2SyncDataMapping == null) {
                                    continue;
                                }
                                doQueryCrmObject2Dest(false, crmDcId, tenantId, sourceTenantType, syncDataContextEvent.getDestData(), queryObjectMappingData,
                                        source2SyncDataMapping, syncDataContextEvent.getDestData());
                            }

                        }

                        //从对象
                        if (syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap() != null && CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())) {
                            DetailQueryObjectMappingsData detailQueryData2DestDataMapping = queryCrmObject2DestNode.getDetailQueryData2DestDataMapping();
                            LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
                            for (String syncDataId : destDetailSyncDataIdAndDestDataMap.keySet()) {
                                ObjectData destData = destDetailSyncDataIdAndDestDataMap.get(syncDataId);
                                if (destData == null) {
                                    continue;
                                }
                                String sourceDetailObjApiName = destData.getApiName();
                                List<QueryObjectToDestObjectData> queryObjectToDestObject = null;
                                for (List<QueryObjectToDestObjectData> detail : detailQueryData2DestDataMapping) {
                                    if (CollectionUtils.isEmpty(detail)) {
                                        continue;
                                    }
                                    if (sourceDetailObjApiName.equals(detail.get(0).getQueryObjectMappingData().getDestObjectApiName())) {
                                        queryObjectToDestObject = detail;
                                        break;
                                    }
                                }
                                if (CollectionUtils.isEmpty(queryObjectToDestObject)) {
                                    continue;
                                }
                                for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryObjectToDestObject) {
                                    QueryObjectMappingData queryObjectMappingData = queryObjectToDestObjectData.getQueryObjectMappingData();
                                    DetailObjectMappingsData.DetailObjectMappingData source2SyncDataMapping = queryObjectToDestObjectData.getQueryData2DestDataMapping();
                                    if (queryObjectMappingData == null || source2SyncDataMapping == null) {
                                        continue;
                                    }
                                    doQueryCrmObject2Dest(false, crmDcId, tenantId, sourceTenantType, destData, queryObjectMappingData,
                                            source2SyncDataMapping, destData);
                                }
                            }
                        }
                    } else {
                        //从对象，单独处理
                        DetailQueryObjectMappingsData detailQueryData2DestDataMapping = queryCrmObject2DestNode.getDetailQueryData2DestDataMapping();
                        List<QueryObjectToDestObjectData> queryObjectToDestObject = null;
                        for (List<QueryObjectToDestObjectData> detail : detailQueryData2DestDataMapping) {
                            if (CollectionUtils.isEmpty(detail)) {
                                continue;
                            }
                            if (dataDestObjApiName.equals(detail.get(0).getQueryObjectMappingData().getDestObjectApiName())) {
                                queryObjectToDestObject = detail;
                                break;
                            }
                        }
                        if (syncDataContextEvent.getDestData() != null && CollectionUtils.isNotEmpty(queryObjectToDestObject)) {
                            for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryObjectToDestObject) {
                                QueryObjectMappingData queryObjectMappingData = queryObjectToDestObjectData.getQueryObjectMappingData();
                                DetailObjectMappingsData.DetailObjectMappingData source2SyncDataMapping = queryObjectToDestObjectData.getQueryData2DestDataMapping();
                                if (queryObjectMappingData == null || source2SyncDataMapping == null || syncDataContextEvent.getDestData() == null) {
                                    continue;
                                }
                                doQueryCrmObject2Dest(false, crmDcId, tenantId, sourceTenantType, syncDataContextEvent.getDestData(), queryObjectMappingData,
                                        source2SyncDataMapping, syncDataContextEvent.getDestData());
                            }
                        }
                    }
                }
            }catch (Exception e){
                String msg = i18NStringManager.getByEi(I18NStringEnum.s901, tenantId) + (e.getCause() != null ? e.getCause().toString() : e.getMessage());
                setNodeMsg(tenantId,hasQueryCrmObject2DestBySource,hasQueryCrmObject2DestByDest,msg);
                syncDataManager.updateToError(tenantId,syncDataContextEvent.getSyncDataData().getId(),SyncStatusEnum.FAILED.getStatus(), msg, I18NStringEnum.s901.name());
                if(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap()!=null&&CollectionUtils.isNotEmpty(syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap().keySet())){
                    for(String detailSyncDataId:syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap().keySet()){
                        syncDataManager.updateToError(tenantId,detailSyncDataId,SyncStatusEnum.FAILED.getStatus(), msg, I18NStringEnum.s901.name());
                    }
                }
                return syncDataContextEvent.stop(msg);
            }
            setNodeMsg(tenantId,false,hasQueryCrmObject2DestByDest,i18NStringManager.getByEi(I18NStringEnum.s6, tenantId));
        }
        return syncDataContextEvent.next();
    }

    private void setNodeMsg(String tenantId, boolean hasQueryCrmObject2DestBySource, boolean hasQueryCrmObject2DestByDest, String msg) {
        if(hasQueryCrmObject2DestByDest){
            syncDataManager.updateNodeMsg(tenantId, null,PloyDetailNodeEnum.CRM_QUERY_BY_DEST,msg,null);
        }else if(hasQueryCrmObject2DestBySource) {
            syncDataManager.updateNodeMsg(tenantId, null,PloyDetailNodeEnum.CRM_QUERY_BY_SOURCE, msg, null);
        }
    }

    public void doQueryCrmObject2Dest(Boolean bySource, String crmDcId, String tenantId, Integer sourceTenantType, ObjectData sourceData, QueryObjectMappingData queryObjectMappingData,
                                      DetailObjectMappingsData.DetailObjectMappingData source2DestDataMapping, ObjectData destData) {
        List<List<FilterData>> orFilters = Lists.newArrayList();
        for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData fieldMappingData : queryObjectMappingData.getQueryFieldMappings()) {
            List<FilterData> andFilters = Lists.newArrayList();
            for (FilterData filterData : fieldMappingData) {
                if (CollectionUtils.isNotEmpty(filterData.getFieldValue())) {
                    FilterData filter = JacksonUtil.fromJson(JacksonUtil.toJson(filterData), FilterData.class);
                    String sourceApiName = filterData.getFieldValue().get(0).toString();
                    Object sourceValue = sourceData.get(sourceApiName);
                    if (sourceValue == null) {
                        filter.setFieldValue(Lists.newArrayList());
                    } else {
                        if(sourceValue instanceof List){
                            filter.setFieldValue((List)sourceValue);
                        }else{
                            filter.setFieldValue(Lists.newArrayList(sourceValue));
                        }
                    }
                    andFilters.add(filter);
                }
            }
            orFilters.add(andFilters);
        }
        String crmObjApiName = queryObjectMappingData.getSourceObjectApiName();
        ObjectData crmObjectData = outerServiceFactory.get(TenantType.CRM).queryObjectDataByEqFilter(crmDcId, tenantId, sourceTenantType, crmObjApiName, orFilters).getData();
        if (crmObjectData != null) {
            for (FieldMappingData fieldMappingData : source2DestDataMapping.getFieldMappings()) {
                Object value = crmObjectData.get(fieldMappingData.getSourceApiName());
                if (value != null) {
                    if (value instanceof List) {
                        value = FixedValueFieldConverter.getSerializableByList((List) value, fieldMappingData.getDestType(), fieldMappingData);
                    } else {
                        value = FixedValueFieldConverter.getSerializable(value.toString(), fieldMappingData.getDestType(), fieldMappingData);
                    }

                }
                if (bySource) {//通过源查询的，不覆盖字段转换的
                    destData.putIfAbsent(fieldMappingData.getDestApiName(), value);
                } else {
                    destData.put(fieldMappingData.getDestApiName(), value);
                }
            }
        }
    }

    private void updateToError(String tenantId, SyncDataContextEvent message, int status, int newStatus) {
        syncDataManager.updateToError(tenantId, message.getSyncDataId(), newStatus, message.getErrMsg(), String.valueOf(message.getErrCode()));
        Map<String, ObjectData> detailSyncMap = message.getDestDetailSyncDataIdAndDestDataMap();
        if (detailSyncMap != null) {
            for (Entry<String, ObjectData> entry : detailSyncMap.entrySet()) {//value可能为空，不要使用
                syncDataManager.updateToError(tenantId, entry.getKey(), newStatus, message.getErrMsg(), String.valueOf(message.getErrCode()));
            }
        }
    }
}
