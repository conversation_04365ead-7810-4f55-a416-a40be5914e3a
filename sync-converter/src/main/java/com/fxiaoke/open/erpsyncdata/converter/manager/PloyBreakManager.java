package com.fxiaoke.open.erpsyncdata.converter.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.lang.Pair;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/11/19
 */
@Component
@Slf4j
public class PloyBreakManager {
    @Autowired
    private SyncPloyManager syncPloyManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private DataCenterManager dataCenterManager;

    //TimedCache<ei_objapiname,"1">,  6小时内只通知一次
    private static TimedCache<String, String> tenantNoticeTenantAdminCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 6);
    /**
     * 处理轮询结果。
     * 监测连续失败，和发送告警
     *
     * @param timeFilterArg
     */
    public void monitorPollingErpResult(TimeFilterArg timeFilterArg, BaseResult pollErpResult) {
        MonitorUtil.send(Pair.of(timeFilterArg,Result.copy(pollErpResult)), MonitorType.POLLING_ERP);
    }

    public static void main(String[] args) {
        long startTime=100023L;
        long endTime=200023L;
        String formatMessage=String.format("在时间戳区间[%s,%s]内，轮询外部对象%s的API返回失败,第%s页后面的数据没有被正常读取。请按下面步骤排查：\\n\"             \"(1)集成流->运行日志->源系统->读接口调用 查看日志中报错信息,解决接口报错问题。 \\n\"             \"(2)如果第(1)步中的错误无法修复，可以联系纷享集成平台研发值班，调整轮询分页大小。 \\n",startTime, endTime, "testobj", 3);
        System.out.println(formatMessage);

    }

    /**
     * 处理轮询ERP，非第一页失败的
     */
    public void noticeTenantAdminPollingNotFirstError(TimeFilterArg timeFilterArg, String traceId) {
        try {
            String tenantId = timeFilterArg.getTenantId();
            String message = i18NStringManager.getByEi(I18NStringEnum.s5006, tenantId);
            String msgTitle = i18NStringManager.getByEi(I18NStringEnum.s5003, tenantId);
            Integer page = (timeFilterArg.getOffset() / timeFilterArg.getLimit()) + 1;
            if (page <= 1) {
                return;
            }
            String formatMessage = String.format(message, timeFilterArg.getStartTime(), timeFilterArg.getEndTime(), timeFilterArg.getObjAPIName(), page);
            String dcId = dataCenterManager.getDataCenterByObjApiName(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName());
            SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder().tenantId(tenantId).
                    msg(formatMessage).msgTitle(msgTitle).dcId(dcId).alwaysSendSuperAdmin(true).
                    build();
            sendAdminNoticeArg.setTenantId(timeFilterArg.getTenantId());
            if (tenantNoticeTenantAdminCache.containsKey(tenantId + "_" + timeFilterArg.getObjAPIName())) {
                return;
            }
            tenantNoticeTenantAdminCache.put(tenantId + "_" + timeFilterArg.getObjAPIName(), "1");
            notificationService.sendTenantAdminNotice(sendAdminNoticeArg,
                    AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager, null, tenantId),
                    AlarmType.GET_BY_ID_API_BREAK,
                    AlarmLevel.IMPORTANT);
        } catch (Exception e) {
            log.warn("发送通知失败", e);
        }
    }
    /**
     * 处理轮询临时库异常。
     * 直接发送告警到超级管理员
     * 如果出现就需要解决，
     * 暂时每次发生都发送如果想防止消息太多可以考虑增加队列
     *
     * @param timeFilterArg
     * @param result
     */
    public void handelPollingTempFailed(TimeFilterArg timeFilterArg, BaseResult result) {
        MonitorUtil.send(Pair.of(timeFilterArg,Result.copy(result)), MonitorType.POLLING_TEMP);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public void incrFailedSyncDataNum(String tenantId, String syncDataId) {
        try{
            //本地线程数据需要取出来
            if (syncDataId == null) {
                return;
            }
            SyncDataEntity simple = syncDataFixDao.getSimple(tenantId, syncDataId);
            if (simple == null) {
                //未找到快照
                return;
            }
            MonitorUtil.send(Pair.of(tenantId,simple), MonitorType.FAILED_SYNC_DATA);
        }catch (Exception ignore){}
    }


    public void quotaOutBreak(String tenantId){
        //超出配额停用集成流
        String stopMsg = i18NStringManager.getByEi(I18NStringEnum.s902,tenantId);
        syncPloyManager.disableAllStream(tenantId,stopMsg);
        //发送告警
        String msgTitle = i18NStringManager.getByEi(I18NStringEnum.s816,tenantId);
        defaultAlert(tenantId, msgTitle, stopMsg);
    }

    /**
     * 默认告警
     * 发送到企业CRM管理员。纷享CSM、纷享客群
     * @param tenantId
     * @param msgTitle
     * @param stopMsg
     */
    public void defaultAlert(String tenantId, String msgTitle, String stopMsg) {
        SendTextNoticeArg noticeArg = new SendTextNoticeArg();
        noticeArg.setMsg(stopMsg);
        noticeArg.setMsgTitle(msgTitle);
        noticeArg.setTenantId(tenantId);
        //发送给企业内的人
        notificationService.sendNoticeByConfig(noticeArg,
                null,
                Lists.newArrayList("00000000000000000000000000000006"),
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.INTEGRATION_STREAM_BREAK,
                AlarmLevel.URGENT);
        //发送给纷享相关人员,不再发送到超级管理员
        SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                .tenantId(tenantId)
                .msgTitle(msgTitle)
                .msg(stopMsg)
                .build();
        notificationService.sendCSM(sendAdminNoticeArg,
                AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                AlarmType.INTEGRATION_STREAM_BREAK,
                AlarmLevel.URGENT);
    }

}
