package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.SyncDataErrLog;
import com.fxiaoke.open.erpsyncdata.common.util.SandboxUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg.ExecuteCustomFunctionParameterData;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.ps.ProtostuffUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 待数据处理MQ
 */
@Slf4j
@Component
public class NodeCompleteDataProcessManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private MergeJedisCmd jedisSupport;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EIEAConverter eieaConverter;

    public NodeCompleteDataProcessManager() {
        super(DataNodeNameEnum.DataDuringFunc);
    }

    @Override
    public boolean needProcess(SyncDataContextEvent ctx) {
        if (!super.needProcess(ctx)) {
            return false;
        }
        Integer sourceEventType = ctx.getSourceEventType();
        //保持原代码逻辑，除了作废，删除，恢复，都处理
        return !(
                EventTypeEnum.INVALID.match(sourceEventType)
                        || EventTypeEnum.DELETE_DIRECT.match(sourceEventType)
                        || EventTypeEnum.RECOVER.match(sourceEventType)
        );
    }

    @CompareSyncField(syncType = SyncCompareConstant.SYNC_DATA_DURATION)
    @Override
    public SyncDataContextEvent processMessage(final SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String snapId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        //数据同步中执行自定义函数
        executeCustomFunction(syncDataContextEvent);

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName("").build();
            if (syncDataContextEvent.isSuccess()) {
                dumpLog.setErrType(0);
            } else {
                dumpLog.setErrType(2);
                dumpLog.setSyncErrMsg(syncDataContextEvent.getErrMsg());
            }
            if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception e) {
        }
        if (!syncDataContextEvent.isSuccess()) {
            updateToError(tenantId, syncDataContextEvent, SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.PROCESS_FAILED.getStatus());
            syncDataManager.updateNodeMsg(tenantId, null,PloyDetailNodeEnum.MID_FUNCTION,syncDataContextEvent.getErrMsg(),null);
            return syncDataContextEvent.stop(i18NStringManager.getByEi(I18NStringEnum.s924,tenantId));
        }
        Map<String, ObjectData> detailSyncMap = syncDataContextEvent.getDestDetailSyncDataIdAndDestDataMap();
        if (detailSyncMap != null) {
            for (Entry<String, ObjectData> entry : detailSyncMap.entrySet()) {
                syncDataManager.updateDestData(tenantId, entry.getKey(), null, entry.getValue(), SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.BE_PROCESS.getStatus());
            }
        }
        syncDataManager.updateDestData(tenantId, syncDataContextEvent.getSyncDataId(), syncDataContextEvent.getDestDataId(), syncDataContextEvent.getDestData(), SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.BE_PROCESS.getStatus());
        DoWriteMqData doWriteMqData = BeanUtil.deepCopy(syncDataContextEvent, DoWriteMqData.class);
        syncDataManager.fillSyncDataMap(syncDataContextEvent);
        return syncDataContextEvent.next();
        //        dataCenterMqProducerFactory.get(message.getDestTenantType()).sendDoWrite(BeanUtil.deepCopy(message, DoWriteMqData.class));
    }

    private void updateToError(String tenantId, SyncDataContextEvent message, int status, int newStatus) {
        syncDataManager.updateToError(tenantId, message.getSyncDataId(), newStatus, message.getErrMsg(), String.valueOf(message.getErrCode()));
        Map<String, ObjectData> detailSyncMap = message.getDestDetailSyncDataIdAndDestDataMap();
        if (detailSyncMap != null) {
            for (Entry<String, ObjectData> entry : detailSyncMap.entrySet()) {//value可能为空，不要使用
                syncDataManager.updateToError(tenantId, entry.getKey(), newStatus, message.getErrMsg(), String.valueOf(message.getErrCode()));
            }
        }
    }

    private SyncDataContextEvent executeCustomFunction(final SyncDataContextEvent message) {
        if (!message.isSuccess()) {
            return message;
        }
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(message.getSourceTenantId(), message.getSyncPloyDetailSnapshotId()).getData();
        String functionApiName = syncPloyDetailSnapshotData.getSyncPloyDetailData().getDuringFuncApiName();
        if (StringUtils.isNotBlank(functionApiName)) {
            String tenentId = syncPloyDetailSnapshotData.getSourceTenantId();
            Integer destTenentType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestTenantType();
            ExecuteCustomFunctionArg executeCustomFunctionArg = new ExecuteCustomFunctionArg();
            executeCustomFunctionArg.setApiName(functionApiName);
            executeCustomFunctionArg.setNameSpace(CustomFunctionConstant.NAME_SPACE);
            final ObjectData destData = message.getDestData();
            executeCustomFunctionArg.setObjectData(destData);
            //构造出key:apiName,value:list<ObjectData>的details
            Map<String, List<ObjectData>> details = message.getDestDetailSyncDataIdAndDestDataMap().values().stream().collect(Collectors.groupingBy(ObjectData::getApiName));
            Map<String, List<Map<String, Object>>> detailsObjectDataMap = BeanUtil.deepCopy(details, new TypeToken<Map<String, List<Map<String, Object>>>>() {}.getType());
            executeCustomFunctionArg.setDetails(detailsObjectDataMap);
            ExecuteCustomFunctionParameterData executeCustomFunctionParameterData = BeanUtil.deepCopy(message, ExecuteCustomFunctionParameterData.class);
            executeCustomFunctionParameterData.setSourceObjectApiName(syncPloyDetailSnapshotData.getSourceObjectApiName());
            executeCustomFunctionArg.setParameter(executeCustomFunctionParameterData);
            Result2<FunctionServiceExecuteReturnData> result = null;
            try {
                SyncPloyDetailEntity syncPloyDetailEntity = syncPloyDetailManager.getEntryById(tenentId, syncPloyDetailSnapshotData.getSyncPloyDetailId());
                result = outerServiceFactory.get(destTenentType).executeCustomFunction(tenentId,
                        message.getDcId(),
                        executeCustomFunctionArg, null,
                        CustomFunctionTypeEnum.DURING_FUNCTION, message.getSyncPloyDetailSnapshotId(), message.getSourceData().getName());
                if (!result.isSuccess()) {
                    log.warn("during sync execute custom function is fail, destDataId ={}, executeCustomFunctionArg={}", destData.getId(), executeCustomFunctionArg);
                    message.setErrCode(result.getIntErrCode());
                    message.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s925,tenentId) + result.getErrMsg());
                    return message;
                }
                //自定义函数返回主对象数据更新目标主对象
                DuringSyncDataResult resultDataMap = BeanUtil.deepCopy(result.getData(), DuringSyncDataResult.class);
                DuringSyncDataResult.DuringFuncObjectData objectData = resultDataMap.getObjectData();
                if (objectData == null) {
                    return message;
                }

                processMasterObjectData(tenentId, functionApiName, destData, objectData, syncPloyDetailEntity);

                processDetails(message, resultDataMap.getDetails(), resultDataMap.getDetailCover());
            } catch (Exception e) {
                log.warn("during sync execute custom function is fail, executeCustomFunctionArg=" + executeCustomFunctionArg, e);
                message.setErrCode(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
                message.setErrMsg(e.getMessage());
            }
            syncDataManager.updateNodeMsg(syncPloyDetailSnapshotData.getSourceTenantId(), null,PloyDetailNodeEnum.MID_FUNCTION,i18NStringManager.getByEi(I18NStringEnum.s6, syncPloyDetailSnapshotData.getSourceTenantId()),null);
        }
        return message;
    }

    private void processMasterObjectData(final String tenentId, final String functionApiName, final ObjectData destData, final DuringSyncDataResult.DuringFuncObjectData objectData, final SyncPloyDetailEntity syncPloyDetailEntity) {
        // 返回的主对象添加了字段的,报警
        notifyDuringFunctionAddFields(tenentId, objectData, destData, functionApiName, syncPloyDetailEntity);

        for (Entry<String, Object> entry : objectData.entrySet()) {
            if (Objects.equals(entry.getKey(), DuringSyncDataResult.DuringFuncObjectData.erpExtendDataKey) || Objects.equals(entry.getKey(), DuringSyncDataResult.DuringFuncObjectData.erpExtendDataKey2)) {
                continue;
            }
            if ((!"tenant_id".equals(entry.getKey()) || !"object_describe_api_name".equals(entry.getKey()) && destData.get(entry.getKey()) != null)) {
                destData.put(entry.getKey(), entry.getValue());
            }
        }

        processExtendData(destData, objectData.getErpExtendData());
    }

    public void processDetails(final SyncDataContextEvent message, final Map<String, List<DuringSyncDataResult.DuringFuncObjectData>> details, final Boolean detailCover) {
        //自定义函数返回从对象数据更新目标从对象，key为sync_data主键
        LinkedHashMap<String, ObjectData> destObjectDataMap = message.getDestDetailSyncDataIdAndDestDataMap();
        //从对象转换(目标事件类型为新增类型才会存在details)
        if (MapUtils.isEmpty(details) || MapUtils.isEmpty(destObjectDataMap)) {
            return;
        }

        // 从对象覆盖
        if (BooleanUtils.isTrue(detailCover)) {
            coverDetail(message, details);
            return;
        }

        // key为数据id, 有隐患,假如有两个不同的从对象,数据id是一样的会导致问题
        Map<String, DuringSyncDataResult.DuringFuncObjectData> detailObjectDataMap =
                details.values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toMap(
                                DuringSyncDataResult.DuringFuncObjectData::getId,
                                Function.identity(), (o1, o2) -> o2
                        ));
        destObjectDataMap.values().stream()
                .filter(Objects::nonNull)
                .forEach(destObjectData -> {
                    final DuringSyncDataResult.DuringFuncObjectData duringFuncObjectData = detailObjectDataMap.get(destObjectData.getId());
                    if (Objects.isNull(duringFuncObjectData)) {
                        return;
                    }
                    // 替换从对象字段值
                    for (Entry<String, Object> detailData : duringFuncObjectData.entrySet()) {
                        if (!ObjectData.PROTECTED_FIELD.contains(detailData.getKey()) && destObjectData.get(detailData.getKey()) != null) {
                            destObjectData.put(detailData.getKey(), detailData.getValue());
                        }
                    }
                    processExtendData(destObjectData, duringFuncObjectData.getErpExtendData());
                });
    }

    /**
     * 因为现在的结构为map,其中key为中间表映射的id,所以不能新增/删除从对象
     */
    private void coverDetail(final SyncDataContextEvent message, final Map<String, List<DuringSyncDataResult.DuringFuncObjectData>> details) {
        LinkedHashMap<String, ObjectData> destObjectDataMap = message.getDestDetailSyncDataIdAndDestDataMap();

        // 校验没有删除/新增对象
        if (destObjectDataMap.size() != details.values().stream().mapToLong(Collection::size).sum()) {
            log.warn("during function change detail number, destObjectDataMap ={}, details={}", destObjectDataMap, details);
            message.setErrCode(ResultCodeEnum.DURING_FUNC_CHANGE_DETAIL_NUM.getErrCode());
            message.setErrMsg(ResultCodeEnum.DURING_FUNC_CHANGE_DETAIL_NUM.getErrMsg());
            return;
        }

        // key为数据id, 有隐患,假如有两个不同的从对象,数据id是一样的会导致问题
        // key:数据id value:中间表映射id
        final Map<String, String> mappingMap = destObjectDataMap.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getValue().getId(), Entry::getKey));

        // 用于将不允许变的值改回来
        final Map<String, ObjectData> dataMap = destObjectDataMap.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getValue().getId(), Entry::getValue));

        // 校验修改了id值
        final Set<String> ids = mappingMap.keySet();
        if (details.values().stream().flatMap(Collection::stream).anyMatch(data -> Objects.isNull(data) || !ids.contains(data.getId()))) {
            log.warn("during function change/remove detail id, details={}", details);
            message.setErrCode(ResultCodeEnum.DURING_FUNC_CHANGE_DETAIL_NUM.getErrCode());
            message.setErrMsg(ResultCodeEnum.DURING_FUNC_CHANGE_DETAIL_NUM.getErrMsg());
            return;
        }

        final LinkedHashMap<String, ObjectData> map = details.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(
                        data -> mappingMap.get(data.getId()),
                        data -> {
                            // 删除扩展数据
                            data.remove(DuringSyncDataResult.DuringFuncObjectData.erpExtendDataKey);
                            data.remove(DuringSyncDataResult.DuringFuncObjectData.erpExtendDataKey2);
                            final ObjectData resultData = ObjectData.convert(data);

                            final ObjectData objectData = dataMap.get(data.getId());
                            // 不能删除/修改保护字段
                            ObjectData.PROTECTED_FIELD.stream()
                                    .filter(k -> Objects.nonNull(objectData.get(k)))
                                    .forEach(k -> resultData.put(k, objectData.get(k)));

                            return resultData;
                        }, (o1, o2) -> o1, LinkedHashMap::new));

        destObjectDataMap.clear();
        destObjectDataMap.putAll(map);
    }

    private static void processExtendData(final ObjectData objectData, final DuringSyncDataResult.ErpExtendData erpExtendData) {
        if (Objects.isNull(erpExtendData)) {
            return;
        }
        // 删除字段,先删后增,防止多删导致数据难以恢复
        final List<String> removeObjectDataFields = erpExtendData.getRemoveFields();
        if (CollectionUtils.isNotEmpty(removeObjectDataFields)) {
            // 不能删除/修改保护字段
            removeObjectDataFields.stream().filter(field -> !ObjectData.PROTECTED_FIELD.contains(field)).forEach(objectData::remove);
        }

        // 新增字段
        Map<String, Object> addObjectDataData = erpExtendData.getAddData();
        if (MapUtils.isNotEmpty(addObjectDataData)) {
            addObjectDataData.forEach((k,v) -> {
                // 只能新增原来没有的字段
                if (Objects.isNull(objectData.get(k))) {
                    objectData.put(k, v);
                }
            });
        }
    }

    private void notifyDuringFunctionAddFields(final String tenantId, final ObjectData functionData, final ObjectData messageData, final String functionApiName, final SyncPloyDetailEntity syncPloyDetailData) {
        if (!checkNeedNotifyFuncAddFields(tenantId, syncPloyDetailData.getId())) {
            return;
        }

        // 比入参多的字段
        final List<String> keys = functionData.keySet().stream()
                .filter(key -> !messageData.containsKey(key))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }

        try {
            final String qixinMessage = i18NStringManager.getByEi2(I18NStringEnum.s926.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s926.getI18nValue(), syncPloyDetailData.getIntegrationStreamName(),
                            syncPloyDetailData.getSourceObjectApiName(), syncPloyDetailData.getDestObjectApiName(),
                            functionApiName, JSON.toJSONString(keys)),
                    Lists.newArrayList(syncPloyDetailData.getIntegrationStreamName(),
                            syncPloyDetailData.getSourceObjectApiName(), syncPloyDetailData.getDestObjectApiName(),
                            functionApiName, JSON.toJSONString(keys)));
            final String dcId = Objects.equals(syncPloyDetailData.getSourceTenantType(), TenantTypeEnum.CRM.getType()) ? syncPloyDetailData.getSourceDataCenterId() : syncPloyDetailData.getDestDataCenterId();
            SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                    .msg(qixinMessage)
                    .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s927,tenantId))
                    .tenantId(tenantId)
                    .dcId(dcId)
                    .sendSuperAdminIfNoSendTenantAdmin(true)
                    .needFillPreDbName(true)
                    .build();
            arg.addTraceInfo();
            notificationService.sendSuperAdminNotice(arg);
        } catch (Exception e) {
            log.warn("通知同步中函数有新增字段报错", e);
        }
    }

    /**
     * 限制同一个集成流报警间隔
     */
    private boolean checkNeedNotifyFuncAddFields(final String tenantId, final String id) {
        final Integer interval = configCenterConfig.getNotifyDuringFunctionAddFieldsInterval();
        if (Objects.nonNull(interval) && interval < 0) {
            return false;
        }
        String key = "NDFAF_" + tenantId + "_" + id;
        final String set = jedisSupport.set(key, "1", SetParams.setParams().ex(interval).nx());
        return StringUtils.isNotBlank(set);
    }
}
