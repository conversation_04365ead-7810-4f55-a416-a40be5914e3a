package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ChangeToWaitingMqData;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg.EventData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ChangeToWaitingManager {
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private I18NStringManager i18NStringManager;


    /**
     * 需要考虑进入分发框架。
     * @param message
     * @throws Throwable
     */
    public void processMessage(ChangeToWaitingMqData message){
        String tenantId = message.getTenantId();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(i18NStringManager.getByEi(I18NStringEnum.s929,tenantId)+"【");
        for (int i = 0; i < message.getEventDataList().size(); i++) {
            if (i != 0) {
                stringBuffer.append(",");
            }
            EventData eventData = message.getEventDataList().get(i);
            String objectName = outerServiceFactory.get(eventData.getSourceTenantType()).getObjectNameByApiName(eventData.getSourceData().getTenantId(), eventData.getSourceData().getApiName()).getData();
            stringBuffer.append(eventData.getSourceData().getApiName()+"（" + objectName +"）");
        }
        stringBuffer.append("】"+i18NStringManager.getByEi(I18NStringEnum.s930,tenantId));
        syncDataManager.updateStatus(tenantId, message.getSyncDataId(), SyncDataStatusEnum.WAITTING.getStatus(), stringBuffer.toString(), I18NStringEnum.s929.name());
    }
}
