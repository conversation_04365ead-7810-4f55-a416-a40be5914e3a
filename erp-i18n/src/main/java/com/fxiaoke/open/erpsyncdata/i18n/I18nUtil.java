package com.fxiaoke.open.erpsyncdata.i18n;

import cn.hutool.core.util.StrUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import lombok.Getter;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * 不需要spring管理的util
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@UtilityClass
public class I18nUtil {
    //不会自动设置，而是随着I18NStringManager加载,所以需要从这获取都需要判空
    @Getter
    private static I18nClient i18nClientOnlySpring;

    //方便单元测试mock
    public void _setI18ClientOnlySpring(I18nClient i18nClient) {
        i18nClientOnlySpring = i18nClient;
    }

    /**
     * 增加了固定前缀
     */
    public String buildKey(String... suffixKeys) {
        StringBuilder i18nKey = new StringBuilder("erpdss");
        for (String suffixKey : suffixKeys) {
            i18nKey.append(".").append(suffixKey);
        }
        return i18nKey.toString();
    }

    /**
     * 推荐直接使用 {@link com.fxiaoke.open.erpsyncdata.i18n.I18nBase#getNameByTraceLocale()}
     */
    public String getOrDefault(String i18nKey, String defaultValue) {
        if (StrUtil.isBlank(i18nKey)) {
            return defaultValue;
        }
        try {
            String locale = getLocaleFromTrace();
            //这是为了在本地单元测试时，不启动spring可以不获取I18N数据
            I18nClient client = I18nUtil.getI18nClientOnlySpring();
            if (client != null) {
                return client.get(i18nKey, 0, locale, defaultValue);
            } else {
                return defaultValue;
            }
        } catch (Exception e) {
            log.warn("I18nUtil getOrDefault failed", e);
            return defaultValue;
        }
    }

    /**
     * 从traceContext读取当前语言环境
     */
    public static String getLocaleFromTrace() {
        String locale = TraceContext.get().getLocale();
        if (locale == null || locale.isEmpty()) {
            locale = "zh-CN";
        }
        return locale;
    }
}
