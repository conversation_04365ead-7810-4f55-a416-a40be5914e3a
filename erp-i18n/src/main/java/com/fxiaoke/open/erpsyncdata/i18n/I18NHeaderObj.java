package com.fxiaoke.open.erpsyncdata.i18n;

import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;

public class I18NHeaderObj {
    public static HeaderObj getHeader(String tenantId, String userId, String lang) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), Integer.valueOf(userId));
        headerObj.put(I18NStringManager.X_FS_LOCALE,lang);
        return headerObj;
    }

    public static HeaderObj getHeader(String tenantId, String lang) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        headerObj.put(I18NStringManager.X_FS_LOCALE,lang);
        return headerObj;
    }

    public static HeaderObj getHeader(String tenantId, String userId, I18NStringManager i18NStringManager) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), Integer.valueOf(userId));
        String lang = i18NStringManager.getDefaultLang(tenantId);
        headerObj.put(I18NStringManager.X_FS_LOCALE,lang);
        return headerObj;
    }

    public static HeaderObj getHeader(String tenantId) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        headerObj.put(I18NStringManager.X_FS_LOCALE,I18nUtil.getLocaleFromTrace());
        return headerObj;
    }

    public static HeaderObj getHeader(String tenantId, I18NStringManager i18NStringManager) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        String lang = i18NStringManager.getDefaultLang(tenantId);
        headerObj.put(I18NStringManager.X_FS_LOCALE,lang);
        return headerObj;
    }

    public static com.fxiaoke.otherrestapi.function.data.HeaderObj getHeader2(String tenantId, I18NStringManager i18NStringManager) {
        com.fxiaoke.otherrestapi.function.data.HeaderObj headerObj = new com.fxiaoke.otherrestapi.function.data.HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        String lang = i18NStringManager.getDefaultLang(tenantId);
        headerObj.put(I18NStringManager.X_FS_LOCALE,lang);
        return headerObj;
    }
}
