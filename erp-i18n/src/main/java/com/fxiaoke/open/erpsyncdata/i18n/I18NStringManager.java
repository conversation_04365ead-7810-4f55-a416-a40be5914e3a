package com.fxiaoke.open.erpsyncdata.i18n;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.NoCache;
import cn.hutool.cache.impl.TimedCache;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 国际化字符串管理器
 * <AUTHOR>
 * @date 2023-10-13
 */
@Slf4j
@Component
public class I18NStringManager {
    public static final String X_FS_LOCALE = "x-fs-locale";
    @Autowired
    private EnterpriseConfigService enterpriseConfigService;

    private static Cache<String,String> tenantLangCache = CacheUtil.newNoCache();

    public static Function<String,String> funcEiTLocale = v -> "zh-CN";
    @PostConstruct
    private void init() {
        if (tenantLangCache instanceof NoCache) {
            //本地缓存60分钟
            TimedCache<String, String> timedCache = CacheUtil.newTimedCache(TimeUnit.MINUTES.toMillis(60L));
            timedCache.schedulePrune(TimeUnit.MINUTES.toMillis(10L));
            tenantLangCache = timedCache;
        }
        try {
            I18nClient instance = I18nClient.getInstance();
            instance.initWithTags("erpdss", "server","pre_object");//erpdss是集成平台专用国际化标签
            I18nUtil._setI18ClientOnlySpring(instance);
        } catch (Throwable e) {
            log.warn("I18NStringManager.init,I18N init error", e);
        }
    }

    public String getDefaultLang(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            return "zh-CN";
        }

        String lang = tenantLangCache.get(tenantId,false, () -> {
            try {
                String defaultLang = funcEiTLocale.apply(tenantId);
                if(null != defaultLang) {
                    log.debug("I18NStringManager.getDefaultLang from funcEiTLocale ,lang: {}",defaultLang);
                    return defaultLang;
                }

                GetConfigDto.Argument argument = new GetConfigDto.Argument();
                argument.setEnterpriseId(Integer.valueOf(tenantId));
                argument.setEmployeeId(1000);
                argument.setKey("enterprise_default_language");
                GetConfigDto.Result config = enterpriseConfigService.getConfig(argument);
                return config.getValue();
            } catch (Exception e) {
                return "zh-CN";
            }
        });
        return lang;
    }

    public static String getByTraceLang(I18NStringEnum i18nEnum, String... extra) {
        return getByLang(i18nEnum, TraceContext.get().getLocale(), extra);
    }

    private static String getByLang(I18NStringEnum i18nEnum, String lang, String... extra) {
        final String i18NKey = i18nEnum.getI18nKey();
        final String defaultVal = i18nEnum.getI18nValue();
        try {
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s6.getI18nKey())) {
                if(!StringUtils.equalsIgnoreCase(I18NStringEnum.s6.getI18nValue(),defaultVal))
                    return defaultVal;
            }

            String msg = I18nClient.getInstance().get(i18NKey,0, lang, defaultVal);
            if(!StringUtils.containsIgnoreCase(msg,"%s")) {
                return msg;
            }
            return String.format(msg, (Object[]) extra);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String get(String i18NKey, String lang, String tenantId, String defaultVal) {
        try {
            if(StringUtils.isEmpty(i18NKey)) return defaultVal;
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s6.getI18nKey())) {
                if(!StringUtils.equalsIgnoreCase(I18NStringEnum.s6.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            if(StringUtils.isEmpty(lang) && StringUtils.isNotEmpty(tenantId)) {
                lang = getDefaultLang(tenantId);
            }
            lang = StringUtils.isEmpty(lang) ? "zh-CN" : lang;
            return I18nClient.getInstance().get(i18NKey,0, lang, defaultVal);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String get2(I18NStringEnum i18nEnum, String lang, String tenantId, String... extra) {
        return get2(i18nEnum.getI18nKey(), lang, tenantId, i18nEnum.getI18nValue(), Arrays.stream(extra).collect(Collectors.toList()));
    }

    public String get2(String i18NKey, String lang, String tenantId, String defaultVal, List<String> extra) {
        try {
            if(StringUtils.isEmpty(i18NKey)) return defaultVal;
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s6.getI18nKey())) {
                if(!StringUtils.equalsIgnoreCase(I18NStringEnum.s6.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            String msg = get(i18NKey, lang, tenantId, defaultVal);
            if(!StringUtils.containsIgnoreCase(msg,"%s")) {
                return msg;
            }
            Object[] args = extra.size() > 0 ? extra.toArray() : null;
            return String.format(msg, args);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String getByEi(String i18NKey, String ei, String defaultVal) {
        try {
            if(StringUtils.isEmpty(i18NKey) || StringUtils.isEmpty(ei)) {
                return defaultVal;
            }
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s6.getI18nKey())) {
                if(!StringUtils.equalsIgnoreCase(I18NStringEnum.s6.getI18nValue(),defaultVal))
                    return defaultVal;
            }

            String lang = getDefaultLang(ei);

            return I18nClient.getInstance().get(i18NKey,0, lang, defaultVal);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String getByEi2(I18NStringEnum i18nEnum, String tenantId, String... extra) {
        return getByEi2(i18nEnum.getI18nKey(), tenantId, i18nEnum.getI18nValue(), Arrays.stream(extra).collect(Collectors.toList()));
    }

    public String getByEi2(String i18NKey, String ei, String defaultVal, List<String> extra) {
        try {
            if(StringUtils.isEmpty(i18NKey) || StringUtils.isEmpty(ei)) return defaultVal;
            if(StringUtils.equalsIgnoreCase(i18NKey,I18NStringEnum.s6.getI18nKey())) {
                if(!StringUtils.equalsIgnoreCase(I18NStringEnum.s6.getI18nValue(),defaultVal))
                    return defaultVal;
            }
            String msg = getByEi(i18NKey, ei, defaultVal);
            if(!StringUtils.containsIgnoreCase(msg,"%s")) {
                return msg;
            }
            Object[] args = extra.size() > 0 ? extra.toArray() : null;
            return String.format(msg, args);
        } catch (Exception e) {
            return defaultVal;
        }
    }

    public String get(I18NStringEnum i18NStringEnum, String lang, String tenantId) {
        return get(i18NStringEnum.getI18nKey(), lang, tenantId, i18NStringEnum.getI18nValue());
    }

    public String getByEi(I18NStringEnum i18NStringEnum, String ei) {
        return getByEi(i18NStringEnum.getI18nKey(), ei, i18NStringEnum.getI18nValue());
    }



    /**
     * 语言转LCID
     * @param lang
     * @return
     */
    public static int lang2lcid(@NonNull String lang) {
        lang = StringUtils.lowerCase(lang);
        switch (lang) {
            case "en":
            case "en-us":
                return 1033;
            case "zh-tw":
                return 1028;
            case "ja":
            case "ja-jp":
                return 1041;
            case "vi":
            case "vi-vn":
                return 1066;
            case "ru":
            case "ru-ru":
                return 1049;
            case "pl":
            case "pl-pl":
                return 1045;
            case "ar":
                return 1025;
            case "es-es":
                return 1034;
            case "de":
                return 1031;
            case "fr-fr":
                return 1033;
            case "pt":
                return 1036;
            case "it-it":
                return 1040;
            case "id-id":
                return 1057;
            case "ko-kr":
                return 1042;
            default:
                return 2052;
        }
    }
}
