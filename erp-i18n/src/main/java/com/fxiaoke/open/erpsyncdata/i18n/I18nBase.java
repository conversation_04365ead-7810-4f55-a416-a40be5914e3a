package com.fxiaoke.open.erpsyncdata.i18n;

import cn.hutool.core.util.StrUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;

import java.text.MessageFormat;

/**
 * 统一的工具类
 * 都放到一个里面ide加载性能很差
 *
 * <AUTHOR> (^_−)☆
 */
public interface I18nBase {

    /**
     * 国际码
     */
    String getI18nKey();

    /**
     * 默认值
     */
    String getI18nValue();

    /**
     * 获取文本，I18N转换的结果！
     */
    default String getText(){
        return getNameByTraceLocale();
    }

    /**
     * 1. 前端请求没走异步，和是统一的异步Controller的异步可以用
     * 2. 轮询触发的任务（轮询ERP，临时库，历史任务）可以使用
     */
    default String getNameByTraceLocale() {
        return I18nUtil.getOrDefault(getI18nKey(), getI18nValue());
    }

    /**
     * 使用 {@link MessageFormat} 对国际化场景更合理
     * <br/>
     * 请注意，不是使用的%s这种java语法，这个在不同语言翻译难保证顺序！！！<br>
     * 通常使用：当value为"this is {1} for {0}"，format( "a", "b") =》 this is b for a<br>
     *
     * @see java.text.MessageFormat
     */
    default String indexedFormat(Object... args) {
        String pattern = getNameByTraceLocale();
        if (args == null) {
            return pattern;
        }
        try {
            return StrUtil.indexedFormat(pattern, args);
        } catch (Exception e) {
            //异常时可能是格式化错误，尝试直接使用i18nValue进行格式化
            try {
                return StrUtil.indexedFormat(getI18nValue(), args);
            } catch (Exception ignore) {
                //默认值都失败了，不应该，但也不报错，会直接返回i18Value
            }
        }
        return pattern;
    }
}
