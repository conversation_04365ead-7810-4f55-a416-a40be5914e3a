<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-erp-sync-data</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>erp-i18n</artifactId>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alicp.jetcache</groupId>
      <artifactId>jetcache-anno</artifactId>
      <version>2.7.3</version>
      <exclusions>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>org.ow2.asm</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.esotericsoftware.kryo</groupId>
      <artifactId>kryo5</artifactId>
      <version>5.5.0</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
    </dependency>
    <!-- Utils -->
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <!-- GSon -->
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>datapersist</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mybatis-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>i18n-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-adapter-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <!-- dubbo -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>
    <!-- dubbo end -->

    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-spring</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>config-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>2.2.6</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-crm-rest-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-other-rest-api</artifactId>
      <version>1.0.6-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-cache</artifactId>
    </dependency>
  </dependencies>
</project>