package com.fxiaoke.open.erpsyncdata.custom.tenant.Wensli;

import com.fxiaoke.open.erpsyncdata.custom.BaseTest;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.Wensli.WensliUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @createTime 2021/8/23 14:45
 * @description
 */
@Ignore
public class WensliTest extends BaseTest {

    @Autowired
    private WensliUtil wensliUtil;

    @Test
    public void wensliTokenTest(){
        Result<String> token = wensliUtil.getToken("729731", WensliUtil.TokenType.message);
//        Result<String> token = wensliUtil.getToken("729731", WensliUtil.TokenType.user);
        System.out.println(token);
    }

    @Test
    public void wensliCategoryTest(){
        Result<String> stringResult = wensliUtil.saveCategory("{\n" +
                "    \"userId\":\"1000\",\n" +
                "    \"dataCenterId\":\"604028865262256128\",\n" +
                "    \"categoryList\":[\n" +
                "        {\n" +
                "            \"name\":\"ces1\",\n" +
                "            \"number\":\"321\",\n" +
                "            \"parentNumber\":\"654\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"name\":\"ces2\",\n" +
                "            \"number\":\"654\"\n" +
                "        }\n" +
                "    ]\n" +
                "}", "80771");
        System.out.println(stringResult);
    }
}
