<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata"
                            use-default-filters="false">
        <context:include-filter type="assignable" expression="com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder"/>
        <context:include-filter type="assignable" expression="com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3FileManager"/>
        <context:include-filter type="assignable" expression="com.fxiaoke.open.erpsyncdata.apiproxy.manager.FileSpeedLimiter"/>
    </context:component-scan>

</beans>