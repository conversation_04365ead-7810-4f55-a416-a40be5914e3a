package com.fxiaoke.open.erpsyncdata.writer.manager

import cn.hutool.core.collection.CollUtil
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MappingCreatedData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UpdateMapping
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.data.CompleteDataWriteMqData
import com.fxiaoke.open.erpsyncdata.preprocess.data.SimpleSyncData
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import spock.lang.Specification

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-11-1
 */
class NodeCompleteDataWriteManagerTest extends Specification {
    //用于记录更新mapping的参数
    List updateArgs = []
    SyncDataContextEvent message
    SyncDataContextEvent dmessage
    SyncDataFixDao syncDataDao
    PloyBreakManager ployBreakManager
    SyncDataMappingsManager syncDataMappingsManager
    SyncDataMappingsDao syncDataMappingsDao

    NodeSyncWriteMainManager nodeSyncWriteMainManager

    void setup() {
        syncDataDao = Mock()
        ployBreakManager = Mock()
        syncDataMappingsManager = Spy()
        syncDataMappingsDao = Stub()
        syncDataMappingsDao.setTenantId(_) >> syncDataMappingsDao
        syncDataMappingsDao.updateByUniKey(*_) >> {
            tenantId, sourceObjectApiName, destObjectApiName, sourceDataId, destDataId, destDataName, lastSyncStatus, isCreated ->
                updateArgs.add([tenantId, sourceObjectApiName, destObjectApiName, sourceDataId, destDataId, destDataName, lastSyncStatus])
                return 1
        }
        syncDataMappingsDao.updateBySyncDataId(*_) >> {
            tenantId, syncDataId, newStatus, destDataName, remark, updateTime ->
                updateArgs.add([tenantId, syncDataId, newStatus, destDataName, remark])
                return 1
        }
        syncDataMappingsDao.bulkUpdateBySyncDataId(*_) >> {
            tenantId, updateDestName, List<UpdateMapping.BySyncDataIdArg> args ->
                if (args.size()==0){
                    throw new ErpSyncDataException("bulk update empty args")
                }
                for (arg in args) {
                    updateArgs.add([tenantId, arg.getSyncDataId(), arg.getLastSyncStatus(), arg.getDestDataName(), arg.getRemark()])
                }
                return args.size()
        }
        syncDataMappingsDao.bulkUpdateDestBySourceArgs(*_) >> {
            tenantId, _, List<UpdateMapping.BySourceArg> args ->
                if (args.size()==0){
                    throw new ErpSyncDataException("bulk update empty args")
                }
                for (arg in args) {
                    updateArgs.add([tenantId, arg.getSourceObjectApiName(), arg.getDestObjectApiName(), arg.getSourceDataId(), arg.getDestDataId(), arg.getDestDataName(), arg.getLastSyncStatus()])
                }
                return args.size()
        }
        syncDataDao.setTenantId(_) >> syncDataDao
        SyncDataManager syncDataManager = new SyncDataManager(
                syncDataDao: syncDataDao,
                ployBreakManager: ployBreakManager
        )

        nodeSyncWriteMainManager = new NodeSyncWriteMainManager(
                syncDataManager: syncDataManager,
                syncDataMappingsDao: syncDataMappingsDao,
                syncDataFixDao: syncDataDao,
                ployBreakManager: ployBreakManager,
                i18NStringManager: Mock(I18NStringManager) {
                    get(_, _, _) >> {
                        return ((I18NStringEnum) it[0]).getI18nValue()
                    }
                    getByEi(*_) >> {
                        return ((I18NStringEnum) it[0]).getI18nValue()
                    }
                },
                monitorReportManager: Mock(MonitorReportManager),
                speedLimitManager: Mock(SpeedLimitManager) {
                    getTenantTpm(*_) >> 10
                }
        )
        /*先用旧代码测试所有场景，形成测试用例。
        再替换成新代码，看能否跑过测试用例。
        通过后，删除旧代码
        */
        SimpleSyncData simpleSyncData = new SimpleSyncData()
        simpleSyncData.setSyncDataId("sd1")
        simpleSyncData.setSourceObjectApiName("srcObj")
        simpleSyncData.setDestObjectApiName("destObj")
        simpleSyncData.setSourceDataId("s1")
        simpleSyncData.setDestDataName("dn1")
        simpleSyncData.setDestDataId("dddd1")
        SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult()
        writeResult.setSyncDataId("sd1")
        writeResult.setDestDataId("d1")
        writeResult.setSimpleSyncData(simpleSyncData)
        message = new SyncDataContextEvent()
        message.setWriteResult(writeResult)
        message.setTenantId("83952")
        message.setDestEventType(1)
        SimpleSyncData detailSyncData = new SimpleSyncData()
        detailSyncData.setSyncDataId("dsd1")
        detailSyncData.setSourceObjectApiName("dsrcObj")
        detailSyncData.setDestObjectApiName("ddestObj")
        detailSyncData.setSourceDataId("ds1")
        detailSyncData.setDestDataName("ddn1")
        detailSyncData.setDestDataId("dddd122")
        CompleteDataWriteMqData.WriteResult dwriteResult = new CompleteDataWriteMqData.WriteResult()
        dwriteResult.setSyncDataId("dsd1")
        dwriteResult.setDestDataId("dd1")
        dwriteResult.setSimpleSyncData(detailSyncData)
        //构建带从结果的message
        dmessage = BeanUtil.deepCopy(message, SyncDataContextEvent)
        dmessage.setDetailWriteResults([dwriteResult])
    }

    def "主新增成功"() {
        when:
        updateArgs = []
        SyncDataContextEvent message1 = BeanUtil.deepCopy(message, SyncDataContextEvent)
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message1)
        println(updateArgs)
        def exceptUpdateArgs = [
                ['83952', 'srcObj', 'destObj', 's1', 'd1', 'dn1', 6],
        ]
        def exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        def unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //当期望未执行时报错
        exceptSubtract == []
        //出现期望之外执行时也报错
        unExceptSubtract == []
        1 * syncDataDao.updateStatusAndDestDataIdBySuccess("83952", "sd1", "d1", 6, "成功")
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', '成功', null)
        1 * syncDataDao.setTenantId('83952') >> syncDataDao
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._
    }

    def "主更新成功"() {
        when:
        updateArgs.clear()
        SyncDataContextEvent message3 = BeanUtil.deepCopy(message, SyncDataContextEvent)
        message3.setDestEventType(2)
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message3)
        println(updateArgs)
        def exceptUpdateArgs = [
                ['83952', 'sd1', 6, 'dn1', '成功'],
        ]
        def exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        def unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //出现期望之外执行时也报错
        unExceptSubtract == []
        //当期望未执行时报错
        exceptSubtract == []
        1 * syncDataDao.updateStatus('83952', 'sd1', 6, '成功', null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', '成功', null)
        1 * syncDataDao.setTenantId('83952') >> syncDataDao
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._
    }

    def "主新增/更新失败"() {
        when:
        //新增
        updateArgs.clear()
        SyncDataContextEvent message2 = BeanUtil.deepCopy(message, SyncDataContextEvent)
        message2.getWriteResult().setErrCode(-1)
        message2.getWriteResult().setErrMsg("failed")
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message2)
        println(updateArgs)
        def exceptUpdateArgs = [
                ['83952', 'sd1', 5, 'dn1', 'failed'],
        ]
        def exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        def unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //出现期望之外执行时也报错
        unExceptSubtract == []
        //当期望未执行时报错
        exceptSubtract == []
        1 * syncDataDao.updateStatus('83952', 'sd1', 5, 'failed', '-1')
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', 'failed', null)
        1 * syncDataDao.setTenantId('83952') >> syncDataDao
        1 * ployBreakManager.incrFailedSyncDataNum("83952", "sd1")
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._


        when:
        //更新
        updateArgs.clear()
        message2.setDestEventType(2)
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message2)
        println(updateArgs)
        exceptUpdateArgs = [
                ['83952', 'sd1', 5, 'dn1', 'failed'],
        ]
        exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //出现期望之外执行时也报错
        unExceptSubtract == []
        //当期望未执行时报错
        exceptSubtract == []
        1 * syncDataDao.updateStatus('83952', 'sd1', 5, 'failed', '-1')
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', 'failed', null)
        1 * syncDataDao.setTenantId('83952') >> syncDataDao
        1 * ployBreakManager.incrFailedSyncDataNum("83952", "sd1")
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._
    }

    def "主从新增成功"() {
        when:
        updateArgs.clear()
        def message1 = BeanUtil.deepCopy(dmessage, SyncDataContextEvent)
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message1)
        println(updateArgs)
        def exceptUpdateArgs = [
                ['83952', 'srcObj', 'destObj', 's1', 'd1', 'dn1', 6],
                ['83952', 'dsrcObj', 'ddestObj', 'ds1', 'dd1', 'ddn1', 6],
        ]
        def exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        def unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //当期望未执行时报错
        exceptSubtract == []
        //出现期望之外执行时也报错
        unExceptSubtract == []
        1 * syncDataDao.updateStatusAndDestDataIdBySuccess("83952", "sd1", "d1", 6, "成功")
        1 * syncDataDao.updateStatusAndDestDataIdBySuccess('83952', 'dsd1', 'dd1', 6, '成功')
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'dsd1', 'WRITE', '成功', null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', '成功', null)
        2 * syncDataDao.setTenantId('83952') >> syncDataDao
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._
    }

    def "主从更新成功"() {
        when:
        updateArgs.clear()
        SyncDataContextEvent message1 = BeanUtil.deepCopy(dmessage, SyncDataContextEvent)
        message1.setDestEventType(2)
        //明细创建成功
        syncDataMappingsDao.listCreatedBySource(*_) >> {
            tenantId,sourceObjectApiName,destObjectApiName, Collection<String> srcIds ->
                MappingCreatedData data = new MappingCreatedData()
                data.setSourceDataId(srcIds[0])
                data.setIsCreated(true)
                return [data]
        }
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message1)
        println(updateArgs)
        def exceptUpdateArgs = [
                ['83952', 'sd1', 6, 'dn1', '成功'],
                ['83952', 'dsd1', 6, 'ddn1', '成功'],
        ]
        def exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        def unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //当期望未执行时报错
        exceptSubtract == []
        //出现期望之外执行时也报错
        unExceptSubtract == []
        1 * syncDataDao.updateStatus("83952", "sd1", 6, "成功", null)
        1 * syncDataDao.updateStatus("83952", "dsd1", 6, "成功", null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', '成功', null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'dsd1', 'WRITE', '成功', null)
        2 * syncDataDao.setTenantId('83952') >> syncDataDao
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._

        when:
        updateArgs.clear()
        //明细未创建成功
        syncDataMappingsDao.listCreatedBySource(*_) >> {
            tenantId,sourceObjectApiName,destObjectApiName, Collection<String> srcIds ->
                MappingCreatedData data = new MappingCreatedData()
                data.setSourceDataId(srcIds[0])
                data.setIsCreated(false)
                return [data]
        }
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message1)
        println(updateArgs)
        exceptUpdateArgs = [
                ['83952', 'sd1', 6, 'dn1', '成功'],
                //明细会更新destDataId
                ['83952', 'dsrcObj', 'ddestObj', 'ds1', 'dd1', 'ddn1', 6],
        ]
        exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //当期望未执行时报错
        exceptSubtract == []
        //出现期望之外执行时也报错
        unExceptSubtract == []
        1 * syncDataDao.updateStatus("83952", "sd1", 6, "成功", null)
        1 * syncDataDao.updateStatus("83952", "dsd1", 6, "成功", null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', '成功', null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'dsd1', 'WRITE', '成功', null)
        2 * syncDataDao.setTenantId('83952') >> syncDataDao
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._
    }

    def "主从新增/更新失败"() {
        //不考虑主成功，从失败的情况
        when:
        updateArgs.clear()
        SyncDataContextEvent message1 = BeanUtil.deepCopy(dmessage, SyncDataContextEvent)
        message1.getWriteResult().setErrCode(-1)
        message1.getWriteResult().setErrMsg("failed")
        message1.getDetailWriteResults().get(0).setErrCode(-1)
        message1.getDetailWriteResults().get(0).setErrMsg("failed")
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message1)
        println(updateArgs)
        def exceptUpdateArgs = [
                ['83952', 'sd1', 5, 'dn1', 'failed'],
                ['83952', 'dsd1', 5, 'ddn1', 'failed'],
        ]
        def exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        def unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //当期望未执行时报错
        exceptSubtract == []
        //出现期望之外执行时也报错
        unExceptSubtract == []
        1 * syncDataDao.updateStatus("83952", "sd1", 5, "failed", '-1')
        1 * syncDataDao.updateStatus("83952", "dsd1", 5, "failed", '-1')
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'dsd1', 'WRITE', 'failed', null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', 'failed', null)
        2 * syncDataDao.setTenantId('83952') >> syncDataDao
        1 * ployBreakManager.incrFailedSyncDataNum("83952", "sd1")
        //明细不再调用incrFailedSyncDataNum
//        1 * ployBreakManager.incrFailedSyncDataNum("83952", "dsd1")
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._

        when:
        updateArgs.clear()
        message1.setDestEventType(2)
        nodeSyncWriteMainManager.completeWriteUpdateStatus(message1)
        println(updateArgs)
        exceptUpdateArgs = [
                ['83952', 'sd1', 5, 'dn1', 'failed'],
                ['83952', 'dsd1', 5, 'ddn1', 'failed'],
        ]
        exceptSubtract = CollUtil.subtract(exceptUpdateArgs, updateArgs)
        unExceptSubtract = CollUtil.subtract(updateArgs, exceptUpdateArgs)
        then:
        //当期望未执行时报错
        exceptSubtract == []
        //出现期望之外执行时也报错
        unExceptSubtract == []
        1 * syncDataDao.updateStatus("83952", "sd1", 5, "failed", '-1')
        1 * syncDataDao.updateStatus("83952", "dsd1", 5, "failed", '-1')
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'sd1', 'WRITE', 'failed', null)
        1 * syncDataDao.updateNodeMsgBySyncDataId('83952', 'dsd1', 'WRITE', 'failed', null)
        2 * syncDataDao.setTenantId('83952') >> syncDataDao
        1 * ployBreakManager.incrFailedSyncDataNum("83952", "sd1")
        //明细不再调用incrFailedSyncDataNum
//        1 * ployBreakManager.incrFailedSyncDataNum("83952", "dsd1")
        //不允许多余调用
        0 * syncDataDao._
        0 * ployBreakManager._
    }
}
