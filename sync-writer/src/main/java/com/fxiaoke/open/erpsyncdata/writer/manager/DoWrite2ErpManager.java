package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ReverseWrite2CrmSourceFieldKey;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AfterSystemProcessModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3CreateConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpReSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.SyncTrace;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CompleteDataWriteArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.BuriedSitesStatisticsUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2ERP;

/**
 * <AUTHOR>
 * @Date: 14:02 2020/8/26
 * @Desc:
 */
@Slf4j
@Component
public class DoWrite2ErpManager {

    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao syncDataDao;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private ErpReSyncDataMongoDao erpReSyncDataMongoDao;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private BuriedSitesStatisticsUtils buriedSitesStatisticsUtils;
    @Autowired
    private I18NStringManager i18NStringManager;

    //如果超过5分钟，就不拦截
    private static TimedCache<String, String> sourceUniqueKey2DestDataMd5 = CacheUtil.newTimedCache(1000 * 60 * 5);

    static {
        sourceUniqueKey2DestDataMd5.schedulePrune(1000 * 60 * 3);
    }

    protected SyncDataContextEvent writeToERP(final SyncDataContextEvent doWriteData) {
        Double objLimit = tenantConfigurationManager.getDoubleConfig(TENANT_LIMIT_PER_COUNT_SECOND_2ERP,
                doWriteData.getDestTenantId()+"_"+doWriteData.getDestObjectApiName());
        if(objLimit != null) {
            speedLimitManager.countAndCheck(doWriteData.getDestTenantId()+"_"+doWriteData.getDestObjectApiName(),
                    SpeedLimitTypeEnum.TO_ERP, 1L,false);
        } else {
            speedLimitManager.countAndCheck(doWriteData.getDestTenantId(), SpeedLimitTypeEnum.TO_ERP, 1L,false);
        }
        setNeedReturnField(doWriteData);
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(doWriteData.getSyncDataId()).build());
        SyncDataContextEvent result = handleDoWrite2Erp(doWriteData);
        SyncTrace.remove();
        return result;
    }

    private void setNeedReturnField(SyncDataContextEvent doWriteData) {
        String tenantId = doWriteData.getDestTenantId();
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, doWriteData.getSyncPloyDetailSnapshotId()).getData();
        SyncPloyDetailData2 ployDetail = syncPloyDetailSnapshotData.getSyncPloyDetailData();
        String destDcId = ployDetail.getDestDataCenterId();
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, destDcId);
        if (connectInfo != null && ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
            if (ployDetail.getIntegrationStreamNodes() != null && ployDetail.getIntegrationStreamNodes().getReverseWriteNode() != null) {
                IntegrationStreamNodesData.ReverseWriteNode reverseWriteNode = ployDetail.getIntegrationStreamNodes().getReverseWriteNode();
                doWriteData.setNeedReturnField(Maps.newHashMap());
                if (reverseWriteNode.getFieldMappings() != null) {
                    doWriteData.getNeedReturnField().put(doWriteData.getDestObjectApiName(), Lists.newArrayList());
                    for (FieldMappingData fieldMapping : reverseWriteNode.getFieldMappings()) {
                        if (ReverseWrite2CrmSourceFieldKey.SYNC_TIME.equals(fieldMapping.getSourceApiName())
                                || ReverseWrite2CrmSourceFieldKey.ERROR_CODE.equals(fieldMapping.getSourceApiName())
                                || ReverseWrite2CrmSourceFieldKey.ERROR_MSG.equals(fieldMapping.getSourceApiName())
                                || ReverseWrite2CrmSourceFieldKey.DEST_ID.equals(fieldMapping.getSourceApiName())
                                || ReverseWrite2CrmSourceFieldKey.DEST_NAME.equals(fieldMapping.getSourceApiName())
                                || ReverseWrite2CrmSourceFieldKey.ERROR_KEY.equals(fieldMapping.getSourceApiName())
                        ) {
                            continue;
                        }
                        doWriteData.getNeedReturnField().get(doWriteData.getDestObjectApiName()).add(fieldMapping.getSourceApiName());
                    }
                }
                if (reverseWriteNode.getDetailObjectMappings() != null) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : reverseWriteNode.getDetailObjectMappings()) {
                        if(detailObjectMapping.getFieldMappings()!=null){
                            doWriteData.getNeedReturnField().put(detailObjectMapping.getSourceObjectApiName(), Lists.newArrayList());
                            for (FieldMappingData fieldMapping : detailObjectMapping.getFieldMappings()) {
                                if (ReverseWrite2CrmSourceFieldKey.SYNC_TIME.equals(fieldMapping.getSourceApiName())
                                        || ReverseWrite2CrmSourceFieldKey.ERROR_CODE.equals(fieldMapping.getSourceApiName())
                                        || ReverseWrite2CrmSourceFieldKey.ERROR_KEY.equals(fieldMapping.getSourceApiName())
                                        || ReverseWrite2CrmSourceFieldKey.ERROR_MSG.equals(fieldMapping.getSourceApiName())
                                        || ReverseWrite2CrmSourceFieldKey.DEST_ID.equals(fieldMapping.getSourceApiName())
                                        || ReverseWrite2CrmSourceFieldKey.DEST_NAME.equals(fieldMapping.getSourceApiName())
                                ) {
                                    continue;
                                }
                                doWriteData.getNeedReturnField().get(detailObjectMapping.getSourceObjectApiName()).add(fieldMapping.getSourceApiName());
                            }
                        }
                    }
                }
            }
        }
    }

    public SyncDataContextEvent handleDoWrite2Erp(final SyncDataContextEvent doWriteData) {
        Integer counts = 1;
        if (doWriteData.getDestDetailSyncDataIdAndDestDataMap() != null) {//累加明细数量
            counts = counts + doWriteData.getDestDetailSyncDataIdAndDestDataMap().size();
        }
        TimePointRecorderStatic.setCountMsg(doWriteData.getSyncDataId(),counts);
        String tenantId = doWriteData.getDestTenantId();
        String uniqueKey = null, destDataMd5 = null;
        Boolean needInterceptRepeatSync = needInterceptRepeatSync(tenantId) && doWriteData.getDestData() != null;
        if (needInterceptRepeatSync) {
            uniqueKey = new StringBuilder().append(tenantId).append("_").append(doWriteData.getObjectApiName()).append("_")
                    .append(doWriteData.getDataId()).append(doWriteData.getSyncPloyDetailId())
                    .toString();//通过源的dataId是因为目标的dataId新增与更新不一样
            destDataMd5 = getDestDataMd5(uniqueKey, doWriteData);
            if (EventTypeEnum.UPDATE.getType() == doWriteData.getDestEventType() && isInterceptRepeatSync(uniqueKey, destDataMd5)) {//是否重复拦截
                return doInterceptRepeatSync(doWriteData);//拦截重复的写erp,直接到完成写
            }
        }

        boolean isIntercept = isIntercept(doWriteData);//是否上一次超时拦截，true为拦截
        if (!isIntercept) {
            SyncDataContextEvent doWriteArgResult = doWrite2Erp(doWriteData);
            if (doWriteArgResult != null) {
//                CompleteDataWriteMqData completeDataWriteArg = doWriteArgResult.getCompleteDataWriteMqData();
                if (EventTypeEnum.ADD.getType() == doWriteData.getDestEventType() || EventTypeEnum.UPDATE.getType() == doWriteData.getDestEventType()) {
                    if (tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NEED_DO_SOCKET_TIME_OUT_TENANT)) {
                        if (CompleteDataWriteArg.SOCKET_TIME_OUT_CODE == doWriteArgResult.getWriteResult().getErrCode()) {
                            ErpReSyncData reSyncDataEntity = erpReSyncDataMongoDao.getByDataId(doWriteData.getDestTenantId(), doWriteData.getDestObjectApiName(), doWriteData.getDestDataId(), null);
                            if (reSyncDataEntity == null) {
                                ErpReSyncData entity = new ErpReSyncData();
                                entity.setId(new ObjectId());
                                entity.setTenantId(tenantId);
                                entity.setObjApiName(doWriteData.getDestObjectApiName());
                                entity.setDataId(doWriteData.getDestDataId());
                                entity.setSyncDataId(doWriteData.getSyncDataId());
                                CheckMessageListData checkMessageData = new CheckMessageListData();
                                CheckMessageData data = new CheckMessageData();
                                data.setDoWriteDataJson(JSONObject.toJSONString(doWriteData));
                                checkMessageData.add(data);
                                entity.setCheckMessage(checkMessageData);
                                entity.setType(ErpReSyncDataType.SOCKETTIMEOUTREDO);
                                entity.setLocale(doWriteData.getLocale());
                                entity.setStatus(ErpReSyncDataStatus.WAIT);
                                entity.setCreateTime(System.currentTimeMillis());
                                entity.setUpdateTime(entity.getCreateTime());
                                int insert = erpReSyncDataMongoDao.insert(tenantId, entity);
                            }
                        } else {
                            ErpReSyncData reSyncDataEntity = erpReSyncDataMongoDao.getByDataId(doWriteData.getDestTenantId(), doWriteData.getDestObjectApiName(), doWriteData.getDestDataId(), null);
                            if (reSyncDataEntity != null) {
                                if (CollectionUtils.isNotEmpty(reSyncDataEntity.getCheckMessage()) && reSyncDataEntity.getCheckMessage().get(0).getNeedReSync()) {//重新发起
                                    //重新发起一次同步
                                    HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
                                    SyncDataEntity syncDataEntity = syncDataDao.setTenantId(tenantId).getById(tenantId, doWriteData.getSyncDataId());
                                    if (syncDataEntity != null) {
                                        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> objectDataResult = objectDataService.getById(headerObj, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getSourceData().getId(), false);
                                        if (objectDataResult != null && objectDataResult.isSuccess() && objectDataResult.getData() != null
                                                && objectDataResult.getData().getObjectData() != null) {
                                            BatchSendEventDataArg.EventData eventData = new BatchSendEventDataArg.EventData();
                                            ObjectData sourceData = new ObjectData();
                                            sourceData.putAll(objectDataResult.getData().getObjectData());
                                            eventData.setSourceData(sourceData);
                                            //一定是修改类型
                                            eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                                            eventData.setSourceTenantType(TenantType.CRM);
                                            //不能设置快照Id，不然可能会修改了策略不生效。
                                            //先临时保留syncmain的后面的结构体。这里做个转换
                                            SyncDataContextEvent syncDataContextEvent = SyncDataContextUtils.convertEventByBatchSendEventDataArg(eventData);
                                            syncDataContextEvent.setLocale(reSyncDataEntity.getLocale());
                                            Result2<Void> voidResult = eventTriggerService.batchSendEventData2DispatcherMqByContext(Lists.newArrayList(syncDataContextEvent));
                                            log.info("send mq,result:{}", voidResult);
                                        }
                                    }
                                }
                                erpReSyncDataMongoDao.deleteById(tenantId, reSyncDataEntity.getId());//删除
                            }
                        }
                    }
                }
                if (needInterceptRepeatSync && doWriteArgResult.getWriteResult() != null
                        && doWriteArgResult.getWriteResult().isSuccess()) {//同步成功，记录数据md5
                    sourceUniqueKey2DestDataMd5.put(uniqueKey, destDataMd5);
                }
            }
        }
        return doWriteData.next();
    }

    private boolean needInterceptRepeatSync(String tenantId) {
        Set<String> tenants = tenantConfigurationManager.getNeedInterceptRepeatSyncTenants();
        return tenants.contains("*") || tenants.contains(tenantId);
    }

    private String getDestDataMd5(String uniqueKey, SyncDataContextEvent doWriteData) {
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(doWriteData.getTenantId(),doWriteData.getSyncPloyDetailSnapshotId()).getData();
        Map<String,List<String>> objApiName2NotUpdateFieldApiName=Maps.newHashMap();
        if(syncPloyDetailSnapshotData.getSyncPloyDetailData()!=null&&syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes()!=null
        &&syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getObjApiName2NotUpdateFieldApiName()!=null){
            objApiName2NotUpdateFieldApiName= syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getObjApiName2NotUpdateFieldApiName();
        }
        String md5;
        Map<String,Object> masterFieldValue=Maps.newHashMap();
        String masterId = doWriteData.getDestData().getId();
        masterFieldValue.put("_id",masterId);
        doWriteData.getDestData().remove("_id");//id新增更新不一样，去掉得到md5后再加上
        if(objApiName2NotUpdateFieldApiName.containsKey(doWriteData.getDestData().getApiName())
                &&CollectionUtils.isNotEmpty(objApiName2NotUpdateFieldApiName.get(doWriteData.getDestData().getApiName()))){
            for(String field:objApiName2NotUpdateFieldApiName.get(doWriteData.getDestData().getApiName())){
                if(doWriteData.getDestData().containsKey(field)){
                    masterFieldValue.put(field,doWriteData.getDestData().get(field));
                    doWriteData.getDestData().remove(field);//更新不传值，去掉得到md5后再加上
                }
            }
        }
        if (doWriteData.getDestDetailSyncDataIdAndDestDataMap() != null && CollectionUtils.isNotEmpty(doWriteData.getDestDetailSyncDataIdAndDestDataMap().values())) {
            Map<String, Map<String,Object>> syncDataId2ObjectData = Maps.newHashMap();
            for (String syncDataId : doWriteData.getDestDetailSyncDataIdAndDestDataMap().keySet()) {//去掉_id,主从字段
                Map<String,Object> detailFieldValue=Maps.newHashMap();
                syncDataId2ObjectData.put(syncDataId, detailFieldValue);
                ObjectData objectData = doWriteData.getDestDetailSyncDataIdAndDestDataMap().get(syncDataId);
                syncDataId2ObjectData.get(syncDataId).put("_id",objectData.getId());
                objectData.remove("_id");
                if (doWriteData.getDestDetailObjMasterDetailFieldApiName() != null) {
                    String masterField = doWriteData.getDestDetailObjMasterDetailFieldApiName().get(objectData.getApiName());
                    if (masterField != null) {
                        syncDataId2ObjectData.get(syncDataId).put(masterField,masterId);
                        objectData.remove(masterField);
                    }
                }
                if(objApiName2NotUpdateFieldApiName.containsKey(objectData.getApiName())
                        &&CollectionUtils.isNotEmpty(objApiName2NotUpdateFieldApiName.get(objectData.getApiName()))){
                    for(String field:objApiName2NotUpdateFieldApiName.get(objectData.getApiName())){
                        if(objectData.containsKey(field)){
                            detailFieldValue.put(field,objectData.get(field));
                            objectData.remove(field);//更新不传值，去掉得到md5后再加上
                        }
                    }
                }
            }
            md5 = md5(uniqueKey, doWriteData.getDestData(), doWriteData.getDestDetailSyncDataIdAndDestDataMap().values());
            for (String syncDataId : syncDataId2ObjectData.keySet()) {//恢复_id,主从字段
                ObjectData objectData = doWriteData.getDestDetailSyncDataIdAndDestDataMap().get(syncDataId);
                if(syncDataId2ObjectData.get(syncDataId)!=null){
                    objectData.putAll(syncDataId2ObjectData.get(syncDataId));
                }

            }
        } else {
            md5 = md5(uniqueKey, doWriteData.getDestData());
        }
        doWriteData.getDestData().putAll(masterFieldValue);
        return md5;
    }

    private SyncDataContextEvent doInterceptRepeatSync(SyncDataContextEvent doWriteData) {
        String msg = i18NStringManager.getByEi(I18NStringEnum.s945,doWriteData.getTenantId());

        SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
        writeResult.setSyncDataId(doWriteData.getSyncDataId());
        writeResult.setDestDataId(doWriteData.getDestDataId());
        writeResult.setErrMsg(msg);
        List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
        if (doWriteData.getDestDetailSyncDataIdAndDestDataMap() != null) {
            for (String detailSyncDataId : doWriteData.getDestDetailSyncDataIdAndDestDataMap().keySet()) {
                SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                detailWriteResult.setSyncDataId(detailSyncDataId);
                detailWriteResult.setErrMsg(msg);
                detailWriteResults.add(detailWriteResult);
            }
        }
        writeResult.setDestDetailSyncDataIdAndDestDataMap(doWriteData.getDestDetailSyncDataIdAndDestDataMap());
        doWriteData.setWriteResult(writeResult);
        doWriteData.setDetailWriteResults(detailWriteResults);
        syncDataManager.fillSimpleSyncData(doWriteData.getSyncDataMap(), doWriteData);
        doWriteData.setInterceptRepeatSync(true);
        return doWriteData.stop(msg);
    }

    private boolean isInterceptRepeatSync(String uniqueKey, String destDataMd5) {
        if (destDataMd5 != null && destDataMd5.equals(sourceUniqueKey2DestDataMd5.get(uniqueKey, false))) {
            return true;
        }
        return false;
    }

    private boolean isIntercept(SyncDataContextEvent doWriteData) {
        if (!tenantConfigurationManager.inWhiteList(doWriteData.getDestTenantId(), TenantConfigurationTypeEnum.NEED_DO_SOCKET_TIME_OUT_TENANT)) {
//不配置的企业直接过掉
            return false;
        }
        if (StringUtils.isNotBlank(doWriteData.getRequestId())) {//不为空说明是重试数据
            return false;
        }
        if (EventTypeEnum.ADD.getType() != doWriteData.getDestEventType() && EventTypeEnum.UPDATE.getType() != doWriteData.getDestEventType()) {
            return false;
        }
        String requestId;
        if (doWriteData.getDestDetailSyncDataIdAndDestDataMap() != null && CollectionUtils.isNotEmpty(doWriteData.getDestDetailSyncDataIdAndDestDataMap().values())) {
            requestId = md5(doWriteData.getTenantId(), doWriteData.getDestObjectApiName(), doWriteData.getDestDataId(), doWriteData.getDestData(), doWriteData.getDestDetailSyncDataIdAndDestDataMap().values());
        } else {
            requestId = md5(doWriteData.getTenantId(), doWriteData.getDestObjectApiName(), doWriteData.getDestDataId(), doWriteData.getDestData());
        }

        doWriteData.setRequestId(requestId);
        doWriteData.getDestData().put(CommonConstant.REQUEST_ID_KEY, requestId);
        ErpReSyncData reSyncDataEntity = erpReSyncDataMongoDao.getByDataId(doWriteData.getDestTenantId(), doWriteData.getDestObjectApiName(), doWriteData.getDestDataId(), null);
        if (reSyncDataEntity != null) {//拦截
            reSyncDataEntity.getCheckMessage().get(0).setNeedReSync(true);
            reSyncDataEntity.setLocale(StringUtils.isBlank(doWriteData.getLocale()) ? reSyncDataEntity.getLocale() : doWriteData.getLocale());
            //更新需要重试
            erpReSyncDataMongoDao.updateErpReSyncData(doWriteData.getDestTenantId(), reSyncDataEntity);


            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            writeResult.setSyncDataId(doWriteData.getSyncDataId());
            writeResult.setErrCode(CompleteDataWriteArg.INTERCEPT_CODE);
            writeResult.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s946,doWriteData.getTenantId()));
            Map<String, ObjectData> detailMaps = doWriteData.getDestDetailSyncDataIdAndDestDataMap();
            if (detailMaps != null) {
                for (String detailSyncDataId : detailMaps.keySet()) {
                    SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                    detailWriteResult.setSyncDataId(detailSyncDataId);
                    detailWriteResult.setErrCode(CompleteDataWriteArg.INTERCEPT_CODE);
                    detailWriteResult.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s946,doWriteData.getTenantId()));
                    detailWriteResults.add(detailWriteResult);
                }
            }
            writeResult.setDestDetailSyncDataIdAndDestDataMap(doWriteData.getDestDetailSyncDataIdAndDestDataMap());
            doWriteData.setWriteResult(writeResult);
            doWriteData.setDetailWriteResults(detailWriteResults);

            doWriteData.setDestTenantType(TenantType.ERP);

            syncDataManager.fillSimpleSyncData(doWriteData.getSyncDataMap(), doWriteData);
            doWriteData.next();
            return true;
        }
        return false;
    }

    public String md5(Object... values) {
        return Hashing.md5().hashString(Stream.of(values).map(Objects::toString).collect(Collectors.joining("^")), StandardCharsets.UTF_8).toString();
    }

     public SyncDataContextEvent doWrite2Erp(SyncDataContextEvent doWriteData) {
        String syncDataId = doWriteData.getSyncDataId();
        String tenantId = doWriteData.getSourceTenantId();
        String callOutSystemNameErrMsg =  "Call "+erpConnectInfoManager.getOutSystemName(tenantId, doWriteData.getSyncPloyDetailSnapshotId())+" : ";
        log.debug("trace tenantId:{} doWrite2Erp callOutSystemNameErrMsg:{}", tenantId, callOutSystemNameErrMsg);

        if (doWriteData.getDestEventType() == EventTypeEnum.ADD.getType()) {
            Map<String, ObjectData> detailMaps = doWriteData.getDestDetailSyncDataIdAndDestDataMap();
            Result<ErpIdResult> result = erpDataPreprocessService.createErpObjData(doWriteData);//调预处理
            SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            if (result.isSuccess()) {
                doProcessSuccessResult(tenantId, writeResult, detailWriteResults, result, doWriteData);
            } else if (ResultCodeEnum.DETAIL_MODIFY_NO_SUPPORT_NOW.getErrCode().equals(result.getErrCode())) {
                //如果主数据不正在同步，则触发一次主对象的上次更新
                erpDataPreprocessService.processErpDetailEvent(doWriteData);
                //不需要执行complete了，因为已经删掉这一条同步记录
                return doWriteData.stop(i18NStringManager.getByEi(I18NStringEnum.s947,doWriteData.getTenantId()));
            } else {
                if(result.getData()!=null){
                    writeResult.setDestReturnData(result.getData().getMasterReturnData());
                }
                writeResult.setSyncDataId(syncDataId);
                if (ResultCodeEnum.SOCKETTIMEOUT.getErrCode().equals(result.getErrCode())) {
                    writeResult.setErrCode(CompleteDataWriteArg.SOCKET_TIME_OUT_CODE);
                } else {
                    writeResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                }
                writeResult.setErrMsg(callOutSystemNameErrMsg + i18NStringManager.getByEi2(result.getI18nKey(),tenantId,result.getErrMsg(),result.getI18nExtra()));
                if (detailMaps != null) {
                    for (String detailSyncDataId : detailMaps.keySet()) {
                        SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                        detailWriteResult.setSyncDataId(detailSyncDataId);
                        detailWriteResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                        detailWriteResult.setErrMsg(callOutSystemNameErrMsg + i18NStringManager.getByEi2(result.getI18nKey(),tenantId,result.getErrMsg(),result.getI18nExtra()));
                        detailWriteResults.add(detailWriteResult);
                    }
                }
                if (ignoreSubmitAuditFail(doWriteData, result)) {
                    doProcessSuccessResult(tenantId, writeResult, detailWriteResults, result, doWriteData, true);
                }
            }
            writeResult.setDestDetailSyncDataIdAndDestDataMap(doWriteData.getDestDetailSyncDataIdAndDestDataMap());
            doWriteData.setWriteResult(writeResult);
            doWriteData.setDetailWriteResults(detailWriteResults);
        } else if (doWriteData.getDestEventType() == EventTypeEnum.INVALID.getType()
                || doWriteData.getDestEventType() == EventTypeEnum.RECOVER.getType()
                || doWriteData.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {
            Result result = Result.newSuccess();
            try {
                if (doWriteData.getDestEventType() == EventTypeEnum.INVALID.getType()) {//作废
                    Result<String> invalidResult = erpDataPreprocessService.invalidErpObjData(doWriteData);
                    result = invalidResult;
                } else if (doWriteData.getDestEventType() == EventTypeEnum.RECOVER.getType()) {//恢复
                    Result<String> recoverResult = erpDataPreprocessService.recoverErpObjData(doWriteData);
                    result = recoverResult;
                } else if (doWriteData.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {//删除
                    Result<String> deleteResult = erpDataPreprocessService.deleteErpObjData(doWriteData);
                    result = deleteResult;
                }
            } catch (Exception e) {
                result = Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                result.setErrMsg(result.getErrMsg() + ":" + e.getMessage());
                log.warn(e.getMessage(), e);
            }
            log.info("DoWrite2ErpMqConsumer.processMessage(),invalid or recover or delete result={}", result);
            if (result != null) {
                if (result.isSuccess()) {
                     doWriteData.newSuccess(doWriteData.getDestEventType(), syncDataId, doWriteData.getDestDataId());
                } else {
                     doWriteData.newError(doWriteData.getDestEventType(), syncDataId, callOutSystemNameErrMsg + i18NStringManager.getByEi2(result.getI18nKey(),tenantId,result.getErrMsg(),result.getI18nExtra()));
                }
            }
        } else if (doWriteData.getDestEventType() == EventTypeEnum.UPDATE.getType()) {
            com.fxiaoke.crmrestapi.common.data.ObjectData objectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
            objectData.putAll(doWriteData.getDestData());
            Result<ErpIdResult> result = erpDataPreprocessService.updateErpObjData(doWriteData);
            List<SyncDataContextEvent.WriteResult> detailWriteResults = new ArrayList<>();
            Map<String, ObjectData> detailMaps = doWriteData.getDestDetailSyncDataIdAndDestDataMap();
            if (result.isSuccess()) {
                // arg = CompleteDataWriteArg.newSuccess(doWriteData.getDestEventType(), syncDataId, doWriteData.getDestDataId());
                SyncDataContextEvent.WriteResult writeResult = new SyncDataContextEvent.WriteResult();

                doProcessSuccessResult(tenantId, writeResult, detailWriteResults, result, doWriteData);
                doWriteData.setWriteResult(writeResult);
                //更新也要带上明细的结果
                doWriteData.setDetailWriteResults(detailWriteResults);
            } else if (ResultCodeEnum.DETAIL_MODIFY_NO_SUPPORT_NOW.getErrCode().equals(result.getErrCode())) {
                //如果主数据不正在同步，则触发一次主对象的上次更新
                erpDataPreprocessService.processErpDetailEvent(doWriteData);
                //不需要执行complete了，因为已经删掉这一条同步记录
                return doWriteData.stop(i18NStringManager.getByEi(I18NStringEnum.s947,doWriteData.getTenantId()));
            } else {
                String errMsg = callOutSystemNameErrMsg + i18NStringManager.getByEi2(result.getI18nKey(),tenantId,result.getErrMsg(),result.getI18nExtra());
                if (detailMaps != null) {
                    for (String detailSyncDataId : detailMaps.keySet()) {
                        SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                        detailWriteResult.setSyncDataId(detailSyncDataId);
                        detailWriteResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                        detailWriteResult.setErrMsg(errMsg);
                        detailWriteResults.add(detailWriteResult);
                    }
                }
                doWriteData.newError(doWriteData.getDestEventType(), syncDataId, errMsg);
                if(result.getData()!=null){
                    doWriteData.getWriteResult().setDestReturnData(result.getData().getMasterReturnData());
                }
                if (ResultCodeEnum.SOCKETTIMEOUT.getErrCode().equals(result.getErrCode())) {
                    doWriteData.getWriteResult().setErrCode(CompleteDataWriteArg.SOCKET_TIME_OUT_CODE);
                } else {
                    doWriteData.getWriteResult().setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                }
                doWriteData.setDetailWriteResults(detailWriteResults);
            }
        } else {
            doWriteData.newError(doWriteData.getDestEventType(), syncDataId, CompleteDataWriteArg.UNSUPPORT_EVENT_TYPE, "");
        }
        doWriteData.setTenantId(tenantId);
        doWriteData.setDestTenantType(TenantType.ERP);
        doWriteData.setSyncPloyDetailSnapshotId(doWriteData.getSyncPloyDetailSnapshotId());

        syncDataManager.fillSimpleSyncData(doWriteData.getSyncDataMap(), doWriteData);
        return doWriteData.next();
    }

    private boolean ignoreSubmitAuditFail(SyncDataContextEvent doWriteData, Result<ErpIdResult> result) {
        if (!ResultCodeEnum.K3C_BILL_AUDIT_FAILED.getErrCode().equals(result.getErrCode()) && !ResultCodeEnum.K3C_BILL_SUBMIT_FAILED.getErrCode().equals(result.getErrCode())) {
            return false;
        }

        String objApiName = doWriteData.getDestObjectApiName();
        final String tenantId = doWriteData.getTenantId();
        String dataCenterId = idFieldConvertManager.getDataCenterId(tenantId, objApiName);
        String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, objApiName);

        final K3CreateConfig k3CreateConfig = configCenterConfig.getK3CreateConfig(tenantId, dataCenterId, realApiName);
//        删除的话不需要写中间表,否则需要处理中间表
        return ResultCodeEnum.K3C_BILL_AUDIT_FAILED.getErrCode().equals(result.getErrCode()) ?
                !k3CreateConfig.isDeleteByAuditFail() :
                !k3CreateConfig.isDeleteBySubmitFail();
    }

    public Result<Void> updateErpDataMapping(String tenantId, List<SyncDataContextEvent.WriteResult> writeResults, boolean ignoreSubmitAuditFail) {
        log.info("DoWrite2ErpManager.updateErpDataMapping,writeResults={}", writeResults);
        Result<Void> result = Result.newSuccess();
        for (SyncDataContextEvent.WriteResult writeResult : writeResults) {
            String dataId = writeResult.getSyncDataId();
            String destDataId = writeResult.getDestDataId();
            SyncDataEntity syncDataEntity = syncDataDao.setTenantId(tenantId).getById(tenantId, dataId);
            log.info("DoWrite2ErpManager.updateErpDataMapping,syncDataEntity={}", syncDataEntity);
            if (StringUtils.isEmpty(destDataId)) {
                continue;
            }
            syncDataEntity.getDestData().putId(destDataId);
            log.info("DoWrite2ErpManager.updateErpDataMapping,syncDataEntity2={}", syncDataEntity);
            syncDataDao.setTenantId(tenantId).updateDestDataById(tenantId, syncDataEntity.getId(), syncDataEntity.getDestData());
            SyncDataMappingsEntity entry = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getDestObjectApiName(), syncDataEntity.getSourceDataId());
            log.info("DoWrite2ErpManager.updateErpDataMapping,entry={}", entry);
            if (entry != null && !entry.getIsCreated()) {
                int i = 0;
                try {
//                    如果ignoreSubmitAuditFail=true,将isCreated设置为true
                    i = syncDataMappingsDao.setTenantId(tenantId).updateDestDataIdIgnore(tenantId, entry.getId(), destDataId,ignoreSubmitAuditFail, System.currentTimeMillis());
                } catch (DataIntegrityViolationException duplicateKeyException) {
                    log.warn("update dest data id failed,duplicateKeyException:{},{},cause:{}", entry, destDataId, duplicateKeyException.getCause());
                } catch (Exception e) {
                    log.warn("update dest data id error,e:", e);
                }
                if (i != 1) {
                    log.info("syncDataMappingsDao.updateDestDataId failed entry={} destDataId={}", entry, destDataId);

                    String formatErrMsg = i18NStringManager.getByEi(I18NStringEnum.s948,tenantId) + destDataId;
                    result = Result.newError(ResultCodeEnum.PARAM_ERROR,formatErrMsg);
                    result.setErrMsg(formatErrMsg);
                    writeResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                    writeResult.setErrMsg(formatErrMsg);
                }
            }
        }
        return result;
    }

    private Map<String, List<String>> getDetailSyncDataIdsMap(SyncDataContextEvent doWriteData) {
        Map<String, ObjectData> detailMaps = doWriteData.getDestDetailSyncDataIdAndDestDataMap();
        Map<String, List<String>> detailSyncDataIdsMap = new HashMap<>();
        Map<String, List<com.fxiaoke.crmrestapi.common.data.ObjectData>> detailDatasMap = new HashMap<>();
        if (detailMaps != null) {
            for (Map.Entry<String, ObjectData> detailDataEntry : detailMaps.entrySet()) {
                ObjectData detailData = detailDataEntry.getValue();
                String detailObjectApiName = detailData.getApiName();
                List<String> detailSyncDataIds = detailSyncDataIdsMap.computeIfAbsent(detailObjectApiName, k -> new ArrayList<>());
                detailSyncDataIds.add(detailDataEntry.getKey());
                List<com.fxiaoke.crmrestapi.common.data.ObjectData> detailDataList = detailDatasMap.computeIfAbsent(detailObjectApiName, k -> new ArrayList<>());
                com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
                fsObjectData.putAll(detailDataEntry.getValue());
                detailDataList.add(fsObjectData);
            }
        }
        return detailSyncDataIdsMap;
    }

    private void doProcessSuccessResult(String tenantId, SyncDataContextEvent.WriteResult writeResult, List<SyncDataContextEvent.WriteResult> detailWriteResults, Result<ErpIdResult> erpIdResult, SyncDataContextEvent doWriteData) {
        doProcessSuccessResult(tenantId, writeResult, detailWriteResults, erpIdResult, doWriteData, false);
    }

    private void doProcessSuccessResult(String tenantId, SyncDataContextEvent.WriteResult writeResult, List<SyncDataContextEvent.WriteResult> detailWriteResults, Result<ErpIdResult> erpIdResult, SyncDataContextEvent doWriteData, boolean ignoreSubmitAuditFail) {
        if (erpIdResult.getData() == null) return;
        List<SyncDataContextEvent.WriteResult> allWriteResults = Lists.newArrayList();
        Map<String, List<String>> detailSyncDataIdsMap = getDetailSyncDataIdsMap(doWriteData);
        String id = erpIdResult.getData().getMasterDataId();//主对象
        String name = erpIdResult.getData().getMasterDataName();//主对象
        writeResult.setSyncDataId(doWriteData.getSyncDataId());
        writeResult.setDestDataId(id);
        writeResult.setDestReturnData(erpIdResult.getData().getMasterReturnData());
        if (doWriteData.getSyncDataMap() != null && doWriteData.getSyncDataMap().get(doWriteData.getSyncDataId()) != null && StringUtils.isNotBlank(name)) {
            doWriteData.getSyncDataMap().get(doWriteData.getSyncDataId()).setDestDataName(name);
        }
        String msg = erpIdResult.getErrMsg();
        if (StringUtils.isNotBlank(msg)) {
            if (Objects.equals(msg, ResultCodeEnum.SUCCESS.getErrMsg())) {
                // 中间表提示success, 直接改动Result和ResultCodeEnum.SUCCESS会影响前端返回
                msg = I18NStringManager.getByTraceLang(I18NStringEnum.s6);
            }
            writeResult.setErrMsg(msg);
        }
        allWriteResults.add(writeResult);
        Map<String, List<String>> detailDataResultsMap = erpIdResult.getData().getDetailDataIds();
        Map<String, Map<String, Map<String, Object>>> detailReturnData=erpIdResult.getData().getDetailReturnData();
        if (detailDataResultsMap != null) {
            for (Map.Entry<String, List<String>> detailEntry : detailDataResultsMap.entrySet()) {
                String detailObjectApiName = detailEntry.getKey();
                List<String> detailObjectResults = detailEntry.getValue();
                Map<String, Map<String, Object>> detailReturnDataMap = null;
                if(detailReturnData!=null){
                    detailReturnDataMap=detailReturnData.get(detailObjectApiName);
                }
                log.info("DoWrite2ErpMqConsumer.doProcessSuccessResult.detailObjectResults={}", detailObjectResults);
                List<String> syncDataIds = detailSyncDataIdsMap.getOrDefault(detailObjectApiName, new ArrayList<>());
                //如果编辑销售订单明细数据，会通过销售订单新变更单来变更订单，需要对已删除的明细数据做特殊处理
                if (StringUtils.containsIgnoreCase(detailObjectApiName, "SAL_SaleOrder.SaleOrderEntry")
                        && doWriteData.getDestEventType() == EventTypeEnum.UPDATE.getType()
                        && detailObjectResults.size() != syncDataIds.size()) {
                    //fixme: 如果明细的数量已经是一致的了，就不做这段逻辑了，将就方案。因为暂时不敢删除这段
                    Iterator<String> iterator = detailObjectResults.iterator();
                    while (iterator.hasNext()) {
                        String next = iterator.next();
                        log.info("DoWrite2ErpMqConsumer.doProcessSuccessResult.next={}", next);
                        String newDataId = next.split("_")[0];
                        log.info("DoWrite2ErpMqConsumer.doProcessSuccessResult.newDataId={}", newDataId);
                        boolean exist = false;
                        for (String key : doWriteData.getDestDetailSyncDataIdAndDestDataMap().keySet()) {
                            String dataId = doWriteData.getDestDetailSyncDataIdAndDestDataMap().get(key).getId();
                            if (StringUtils.equalsIgnoreCase(newDataId, dataId)) {
                                exist = true;
                                break;
                            }
                        }
                        //如果明细被删除，这里的记录也需要被删除
                        if (!exist && !next.contains("_add")) {
                            iterator.remove();
                        }
                    }
                }
                if (detailObjectResults.size() != syncDataIds.size()) {
                    for (String syncDataId : syncDataIds) {
                        SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
                        detailWriteResult.setErrCode(CompleteDataWriteArg.OUT_ERROR_CODE);
                        detailWriteResult.setErrMsg(i18NStringManager.getByEi2(I18NStringEnum.s949.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s949.getI18nValue(), syncDataIds.size(),detailObjectResults.size()),
                                Lists.newArrayList(syncDataIds.size()+"",detailObjectResults.size()+"")));
                        detailWriteResult.setSyncDataId(syncDataId);
                        detailWriteResults.add(detailWriteResult);
                        allWriteResults.add(detailWriteResult);
                    }
                    AfterSystemProcessModel afterSystemProcessModel = AfterSystemProcessModel.builder()
                            .afterProcessType(AfterSystemProcessEnum.DETAIL_COUNT_NOT_SAME.name())
                            .processReason(i18NStringManager.getByEi(I18NStringEnum.s1153,tenantId))
                            .build();
                    syncLogManager.saveLog(tenantId, SyncLogTypeEnum.AFTER_SYSTEM_PROCESS, SyncLogStatusEnum.SYNC_FAIL.getStatus(), afterSystemProcessModel);
                    log.warn("detailObjectResults.size !=syncDataIds.size,detailObjectResults={},syncDataIds={}", detailObjectResults, syncDataIds);
                } else {
                    fillWriteResult(tenantId, detailWriteResults, allWriteResults, detailObjectResults, syncDataIds,detailReturnDataMap);
                }
            }
        }
        if (doWriteData.getDestEventType() == EventTypeEnum.ADD.getType() || doWriteData.getDestEventType() == EventTypeEnum.UPDATE.getType()) {
            updateErpDataMapping(doWriteData.getDestTenantId(), allWriteResults, ignoreSubmitAuditFail);
            if (doWriteData.getDestEventType() == EventTypeEnum.ADD.getType()) {
                buriedSitesStatisticsUtils.uploadBuriedStitesLogBySnap(doWriteData.getSourceTenantId(),
                        doWriteData.getDestObjectApiName(),
                        1,
                        doWriteData.getSyncPloyDetailSnapshotId(),doWriteData.getDataReceiveType());
            }
        }
    }

    /**
     * 填充明细的writeResult,
     * 为避免顺序影响，会先填充已经映射好的，剩余的按顺序映射
     *
     * @param tenantId
     * @param detailWriteResults
     * @param allWriteResults
     * @param detailObjectResults
     * @param syncDataIds
     */
    private void fillWriteResult(String tenantId,
                                 List<SyncDataContextEvent.WriteResult> detailWriteResults,
                                 List<SyncDataContextEvent.WriteResult> allWriteResults,
                                 List<String> detailObjectResults,
                                 List<String> syncDataIds,
                                 Map<String, Map<String, Object>> detailReturnDataMap) {
        log.info("DoWrite2ErpManager.fillWriteResult,detailWriteResults={},allWriteResults={},syncDataIds={},detailObjectResults={},",
                detailWriteResults,
                allWriteResults,
                syncDataIds,
                detailObjectResults);
        if (syncDataIds.isEmpty()) return;
        //这个不是syncDataIds的顺序
        List<SyncDataData> syncDatum = syncDataManager.batchGet(tenantId, syncDataIds, true);
        log.info("DoWrite2ErpManager.fillWriteResult,syncDatum={}", syncDatum);
        //这个替换是支持订单新变更单加的
        LinkedList<String> destDetailDataIds = detailObjectResults.stream().map(v -> v.replace("_add", "")).collect(Collectors.toCollection(LinkedList::new));
        log.info("DoWrite2ErpManager.fillWriteResult,destDetailDataIds={}", destDetailDataIds);

        Set<String> erpDetailIdSet = new HashSet<>(destDetailDataIds);
        List<SyncDataData> createdSyncDatum = new ArrayList<>();
        syncDatum.forEach(v -> {
            //这个时候，SyncDataEntity 的destDataId字段还是null,需要使用v.getDestData().getId()获取destDataId
            String destDataId = v.getDestData().getId();
            if (v.getDestEventType().equals(EventTypeEnum.UPDATE.getType()) && erpDetailIdSet.contains(destDataId)) {
                //更新数据，并且目标ERP明细id中包含这条的destDataId，直接把这个syncData和destDataId匹配
                createdSyncDatum.add(v);
                //只删除一次，防止返回了重复明细Id的情况
                erpDetailIdSet.remove(destDataId);
                destDetailDataIds.remove(destDataId);
                syncDataIds.remove(v.getId());
            }
        });
        log.info("DoWrite2ErpManager.fillWriteResult,createdSyncDatum={}", createdSyncDatum);
        //已创建的映射
        for (SyncDataData syncDataEntity : createdSyncDatum) {
            SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
            if (StringUtils.isNotEmpty(syncDataEntity.getDestDataId())) {
                detailWriteResult.setDestDataId(syncDataEntity.getDestDataId());
            } else {
                detailWriteResult.setDestDataId(syncDataEntity.getDestData().getId());
            }
            detailWriteResult.setSyncDataId(syncDataEntity.getId());
            if(detailReturnDataMap!=null&&detailReturnDataMap.containsKey(detailWriteResult.getDestDataId())){
                detailWriteResult.setDestReturnData(detailReturnDataMap.get(detailWriteResult.getDestDataId()));
            }
            detailWriteResults.add(detailWriteResult);
            allWriteResults.add(detailWriteResult);
        }
        log.info("DoWrite2ErpManager.fillWriteResult,allWriteResults={}", allWriteResults);
        //未创建的映射
        for (int i = 0; i < destDetailDataIds.size() && i < syncDataIds.size(); i++) {
            SyncDataContextEvent.WriteResult detailWriteResult = new SyncDataContextEvent.WriteResult();
            detailWriteResult.setDestDataId(destDetailDataIds.get(i));
            detailWriteResult.setSyncDataId(syncDataIds.get(i));
            if(detailReturnDataMap!=null&&detailReturnDataMap.containsKey(detailWriteResult.getDestDataId())){
                detailWriteResult.setDestReturnData(detailReturnDataMap.get(detailWriteResult.getDestDataId()));
            }
            detailWriteResults.add(detailWriteResult);
            log.info("DoWrite2ErpManager.fillWriteResult,detailWriteResult={}", detailWriteResult);
            allWriteResults.add(detailWriteResult);
        }
        log.info("DoWrite2ErpManager.fillWriteResult,allWriteResults={}", allWriteResults);
    }
}
