package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.core.util.StrUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.SyncDataErrLog;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.common.util.SandboxUtil;
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg.ExecuteCustomFunctionParameterData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AuditLogI18nConstant;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AuditLogService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 待数据传输MQ
 */
@Slf4j
@Component
public class NodeCompleteDataWriteManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private CrmCycleSyncCheckManager crmCycleSyncCheckManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private PloyBreakManager ployBreakManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private AuditLogService auditLogService;
    @Autowired
    private EIEAConverter eieaConverter;
    public NodeCompleteDataWriteManager() {
        super(DataNodeNameEnum.DataAfterFunc);
    }

    @Override
    public boolean needProcess(SyncDataContextEvent ctx) {
        return super.needProcess(ctx);
    }

    @CompareSyncField(syncType = SyncCompareConstant.AFTER_WRITE)
    @DataMonitorScreen(tenantId = "#syncDataContextEvent.tenantId", dataCenterId = "#syncDataContextEvent.sourceDataCenterId", crmObjApiName = "#syncDataContextEvent.crmObjApiName",outSideObjApiName = "#syncDataContextEvent.outSideObjApiName",sourceSystemType = "#syncDataContextEvent.sourceTenantType==1?1:2",operationType = CommonConstant.OPERATE_DATA_MAPPING,historyDataType = "#syncDataContextEvent.dataReceiveType==3",operateStatus="#syncDataContextEvent.writeResult.isSuccess()?1:2",skipSend = "#syncDataContextEvent.writeResult.isSuccess()?false:true")
    public SyncDataContextEvent processMessage(SyncDataContextEvent syncDataContextEvent) {
        String tenantId = syncDataContextEvent.getTenantId();
        String objectApiName = syncDataContextEvent.getObjectApiName();
        String mainObjApiName = syncDataContextEvent.getMainObjApiName();
        String dataId = syncDataContextEvent.getDataId();
        Long version = syncDataContextEvent.getVersion();
        String syncPloyDetailId = syncDataContextEvent.getSyncPloyDetailId();
        log.debug("CompleteDataWriteMqConsumer debug , msg :{}", syncDataContextEvent);
//        monitorReportManager.sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum.process, DataNodeNameEnum.DataIdMapping, tenantId, objectApiName, mainObjApiName, dataId, version, syncPloyDetailId, System.currentTimeMillis(), 160, "开始完成写");
        monitorReportManager.sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum.process, DataNodeNameEnum.DataAfterFunc, tenantId, objectApiName, mainObjApiName, dataId, version, syncPloyDetailId, System.currentTimeMillis(), 180, i18NStringManager.getByEi(I18NStringEnum.s3717, tenantId));
        executeCustomFunction(syncDataContextEvent);
        if (syncDataContextEvent.getDestEventType() == EventTypeEnum.DELETE_DIRECT.getType()) {
            //删除双向中间表（不管调目标是否成功），记录日志
            deleteMappingsAndLog(syncDataContextEvent);
        }
        checkCrmCycleSync(syncDataContextEvent);
        return syncDataContextEvent.finish("success");
    }

    public void deleteMappingsAndLog(SyncDataContextEvent message) {
        String tenantId = message.getTenantId();
        String userId=message.getSourceContextUserId();
        List<String> syncDataIds = Lists.newArrayList();
        syncDataIds.add(message.getWriteResult().getSyncDataId());
        List<SyncDataData> syncDataDatasResult = syncDataManager.batchGet(tenantId, syncDataIds, true);
        SyncDataData syncDataData = syncDataDatasResult.get(0);
        //正向集成流
        SyncPloyDetailSnapshotData2 snapshotData2 = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncDataData.getSyncPloyDetailSnapshotId()).getData();
        String sourceObjectApiName = message.getSourceData().getApiName();
        String crmObjectApiName=sourceObjectApiName;
        String sourceDataId = message.getSourceData().getId();
        String destDataId = message.getDestDataId();
        StringBuilder logMsg = new StringBuilder();//目前是不用多语的
        logMsg.append(I18NStringEnum.s4001.getI18nValue()).append("[").append(sourceDataId).append("]").append(",").append(I18NStringEnum.s4002.getI18nValue()).append("。");
        logMsg.append(I18NStringEnum.s4003.getI18nValue()).append("[").append(destDataId).append("]").append("(").append(I18NStringEnum.s4004.getI18nValue());
        if(message.getWriteResult()!=null&&!message.getWriteResult().isSuccess()){
            logMsg.append(I18NStringEnum.s4005.getI18nValue()).append(":").append(message.getWriteResult().getErrMsg()).append(")");
        }else{
            logMsg.append(I18NStringEnum.s4006.getI18nValue()).append(")");
        }
        //主对象处理
        if (sourceObjectApiName.equals(snapshotData2.getSourceObjectApiName())) {
            String destObjectApiName = snapshotData2.getDestObjectApiName();
            if(TenantType.ERP.equals(snapshotData2.getSyncPloyDetailData().getSourceTenantType())){
                crmObjectApiName=destObjectApiName;
            }
            int mappingsCount = syncDataMappingManager.deleteMappingsBySourceId(tenantId, sourceDataId, sourceObjectApiName, destObjectApiName);
            //可能有些从对象不会触发删除事件，需要根据主对象删除从对象
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : snapshotData2.getSyncPloyDetailData().getDetailObjectMappings()) {
                int detailMappingsCount = syncDataMappingManager.deleteMappingsByMasterId(tenantId, sourceDataId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName());
            }
            if(!snapshotData2.getSyncPloyDetailData().getDetailObjectMappings().isEmpty()){
                logMsg.append("。").append("(").append(I18NStringEnum.s4007.getI18nValue());
            }
        } else {
            //从对象处理
            String destObjectApiName = null;
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : snapshotData2.getSyncPloyDetailData().getDetailObjectMappings()) {
                //拿到从映射字段集合与目标从对象apiName
                if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                    destObjectApiName = detailObjectMapping.getDestObjectApiName();
                    if(TenantType.ERP.equals(snapshotData2.getSyncPloyDetailData().getSourceTenantType())){
                        crmObjectApiName=destObjectApiName;
                    }
                    break;
                }
            }
            if (StringUtils.isNotBlank(destObjectApiName)) {
                int mappingsCount = syncDataMappingManager.deleteMappingsBySourceId(tenantId, sourceDataId, sourceObjectApiName, destObjectApiName);
            }
        }
        //反向集成流，查集成流（不管是否开启）
        SyncPloyDetailEntity syncPloyDetail = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, snapshotData2.getDestObjectApiName(), snapshotData2.getSourceObjectApiName());
        if (syncPloyDetail != null && StringUtils.isNotBlank(destDataId)) {
            logMsg.append("。").append(I18NStringEnum.s4008.getI18nValue());
            if (sourceObjectApiName.equals(syncPloyDetail.getDestObjectApiName())) {
                //主对象
                String destObjectApiName = snapshotData2.getDestObjectApiName();
                int mappingsCount = syncDataMappingManager.deleteMappingsBySourceId(tenantId, destDataId, syncPloyDetail.getSourceObjectApiName(), syncPloyDetail.getDestObjectApiName());
                //可能有些从对象不会触发删除事件，需要根据主对象删除从对象
                for (com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetail.getDetailObjectMappings()) {
                    int detailMappingsCount = syncDataMappingManager.deleteMappingsByMasterId(tenantId, destDataId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName());
                }
            } else {
                //从对象处理
                com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData.DetailObjectMappingData detailObjMapping = null;
                for (com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetail.getDetailObjectMappings()) {
                    //拿到从映射字段集合与目标从对象apiName
                    if (detailObjectMapping.getDestObjectApiName().equals(sourceObjectApiName)) {
                        detailObjMapping = detailObjectMapping;
                        break;
                    }
                }
                if (detailObjMapping != null) {
                    int mappingsCount = syncDataMappingManager.deleteMappingsBySourceId(tenantId, destDataId, detailObjMapping.getSourceObjectApiName(), detailObjMapping.getDestObjectApiName());
                }

            }
        }
        logMsg.append(")").append("。");
        auditLogService.record(AuditLogI18nConstant.SubModuleEnum.ERPDSS_INTEGRATION_STREAM,
                AuditLogI18nConstant.BizOperationEnum.ERPDSS_DELETE_MAPPING, crmObjectApiName, tenantId, userId, logMsg.toString(), 1);
    }


    /**
     * 检查循环同步
     *
     * @param completeDataWriteMqData
     */
    private void checkCrmCycleSync(SyncDataContextEvent completeDataWriteMqData) {
        try {
            if (!TenantType.ERP.equals(completeDataWriteMqData.getDestTenantType())) {
                //只检查CRM往ERP
                return;
            }
            //参数检查
            if (completeDataWriteMqData.getWriteResult() == null || completeDataWriteMqData.getWriteResult().getSimpleSyncData() == null) {
                return;
            }
            String tenantId = completeDataWriteMqData.getTenantId();
            String ployDetailSnapId = completeDataWriteMqData.getSyncPloyDetailSnapshotId();
            String dataId = completeDataWriteMqData.getWriteResult().getSimpleSyncData().getSourceDataId();
            String objApiName = completeDataWriteMqData.getWriteResult().getSimpleSyncData().getSourceObjectApiName();
            if (StrUtil.hasEmpty(tenantId, ployDetailSnapId, dataId, objApiName)) {
                return;
            }
            crmCycleSyncCheckManager.check(tenantId, ployDetailSnapId, objApiName, dataId);
        } catch (Exception ignored) {
        }
    }

    private SyncDataContextEvent executeCustomFunction(SyncDataContextEvent message) {
        String tenantId = message.getTenantId();
        ExecuteCustomFunctionArg executeCustomFunctionArg = new ExecuteCustomFunctionArg();
        List<String> syncDataIds = Lists.newArrayList();
        try {
            syncDataIds.add(message.getWriteResult().getSyncDataId());
            List<SyncDataData> syncDataDatasResult = syncDataManager.batchGet(tenantId, syncDataIds, true);
            SyncDataData syncDataData = syncDataDatasResult.get(0);
            SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncDataData.getSyncPloyDetailSnapshotId()).getData();
            String functionApiName = syncPloyDetailSnapshotData.getSyncPloyDetailData().getAfterFuncApiName();
            if (StringUtils.isNotBlank(functionApiName)) {
                String tenentId = syncPloyDetailSnapshotData.getSourceTenantId();
                Integer destTenentType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestTenantType();
                executeCustomFunctionArg.setApiName(functionApiName);
                executeCustomFunctionArg.setNameSpace(CustomFunctionConstant.NAME_SPACE);
                executeCustomFunctionArg.setObjectData(syncDataData.getDestData());
                //传从对象数据，新增事件会携带从对象，但是事件为更新时不会携带从对象
                Map<String, ObjectData> detailObject = message.getWriteResult().getDestDetailSyncDataIdAndDestDataMap();
                if (detailObject != null) {
                    Map<String, List<ObjectData>> details = detailObject.values().stream().collect(Collectors.groupingBy(ObjectData::getApiName));
                    Map<String, List<Map<String, Object>>> detailsObjectDataMap = BeanUtil2.deepCopy(details, new TypeToken<Map<String, List<Map<String, Object>>>>() {
                    }.getType());
                    executeCustomFunctionArg.setDetails(detailsObjectDataMap);
                }
                ExecuteCustomFunctionParameterData executeCustomFunctionParameterData = BeanUtil2.deepCopy(syncDataData, ExecuteCustomFunctionParameterData.class);
                //复制不全，余下需set值
                executeCustomFunctionParameterData.setSyncDataId(syncDataData.getId());
                CompleteDataWriteMqData completeDataWriteMqData = BeanUtil2.copy(message, CompleteDataWriteMqData.class);
                completeDataWriteMqData.setWriteResult(BeanUtil2.deepCopy(message.getWriteResult(), CompleteDataWriteMqData.WriteResult.class));
                completeDataWriteMqData.setDetailWriteResults(BeanUtil2.deepCopyList(message.getDetailWriteResults(), CompleteDataWriteMqData.WriteResult.class));
                executeCustomFunctionParameterData.setCompleteDataWriteMqData(completeDataWriteMqData);
                executeCustomFunctionArg.setParameter(executeCustomFunctionParameterData);
                Result2<FunctionServiceExecuteReturnData> result = null;
                result = outerServiceFactory.get(destTenentType).executeCustomFunction(tenentId,
                        message.getDcId(),
                        executeCustomFunctionArg, null, CustomFunctionTypeEnum.AFTER_FUNCTION, message.getSyncPloyDetailSnapshotId(), message.getSourceData().getName());
                //自定义函数执行失败，但是已经同步成功，所以同步数据状态写success
                String msg="";
                if (!result.isSuccess()) {
                    log.warn("after sync execute custom function is fail, syncDataId ={} ,syncPloyDetailSnapshotData = {} ,executeCustomFunctionArg={}", syncDataData.getId(), syncPloyDetailSnapshotData,
                            executeCustomFunctionArg);
                    msg = i18NStringManager.getByEi(I18NStringEnum.s965, tenantId) + result.getErrMsg();
                    TimePointRecorderStatic.setAfterSyncFailedMsg(message.getSyncDataId(), true, msg);
                    message.getWriteResult().setErrMsg(msg);
                    syncDataManager.updateAfterFuncFailed(tenantId, null,PloyDetailNodeEnum.AFTER_FUNCTION,msg,null,msg);
                }else{
                    msg = i18NStringManager.getByEi(I18NStringEnum.s6, tenantId);
                    syncDataManager.updateNodeMsg(tenantId, null,PloyDetailNodeEnum.AFTER_FUNCTION,msg,null);
                }
                try {//上报bizlog
                    SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName(functionApiName).build();
                    if (result.isSuccess()) {
                        dumpLog.setErrType(0);
                    } else {
                        dumpLog.setErrType(2);
                        dumpLog.setSyncErrMsg(msg);
                    }
                    if(SandboxUtil.isNotSandbox(eieaConverter, tenantId)) {
                        BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
                    }
                } catch (Exception e) {
                }
            }
        } catch (Exception e) {
            String msg = e.getMessage();
            syncDataManager.updateAfterFuncFailed(tenantId, null, PloyDetailNodeEnum.AFTER_FUNCTION,msg,null,i18NStringManager.getByEi(I18NStringEnum.s848, tenantId)+":"+msg);
            TimePointRecorderStatic.setAfterSyncFailedMsg(message.getSyncDataId(), true, msg);
            log.warn("after sync execute custom function is fail, executeCustomFunctionArg=" + executeCustomFunctionArg, e);
            message.getWriteResult().setErrMsg(msg);
        }
        return message;
    }
}
