package com.fxiaoke.open.erpsyncdata.writer.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.BatchObjectDataResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsMainNodeProcessor;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ReverseWrite2CrmSourceFieldKey;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 反写目标数据同步结果回crm数据
 */
@Slf4j
@Component
public class NodeReverseWrite2CrmManager extends AbsMainNodeProcessor {
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncDataManager syncDataManager;

    public NodeReverseWrite2CrmManager() {
        super(DataNodeNameEnum.DataReWriteSource);
    }

    @Override
    public boolean needProcess(SyncDataContextEvent ctx) {
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData =
                syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(ctx.getTenantId(), ctx.getSyncPloyDetailSnapshotId()).getData();
        if (syncPloyDetailSnapshotData == null || syncPloyDetailSnapshotData.getSyncPloyDetailData() == null) {
            return false;
        }
        if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType() == TenantType.ERP) {
            return false;
        }
        if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes() != null
                && syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getReverseWriteNode() != null) {//根据是否有节点判断
            //删除，不回写  原逻辑
            return ctx.getDestEventType() == null || ctx.getDestEventType() != EventTypeEnum.DELETE_DIRECT.getType();
        }
        return false;
    }

    @CompareSyncField(syncType = SyncCompareConstant.REVERSE_WRITE)
    public SyncDataContextEvent processMessage(SyncDataContextEvent syncDataContextEvent) {
        log.debug("ReverseWrite2CrmManager processMessage , msg :{}", syncDataContextEvent);
        log.info("ReverseWrite2CrmManager processMessage,msg={}", syncDataContextEvent);
        //message没有copy，不能随意更改，更改需要注意对后续有没有影响
        String tenantId = syncDataContextEvent.getTenantId();
        final String snapshotId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        String dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId, snapshotId);
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData =
                syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, snapshotId).getData();
        //组装更新的数据
        Map<String, List<ObjectData>> objDataMap = getObjectDataMap(tenantId, syncDataContextEvent, syncPloyDetailSnapshotData);
        for (String objApiName : objDataMap.keySet()) {
            HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), -10000);
            List<ObjectData> originDataList = objDataMap.get(objApiName);
            List<com.fxiaoke.crmrestapi.common.data.ObjectData> incrDataList = Lists.newArrayList();
            originDataList.stream().forEach(item -> {
                com.fxiaoke.crmrestapi.common.data.ObjectData objectData = new com.fxiaoke.crmrestapi.common.data.ObjectData();
                objectData.putAll(item);
                incrDataList.add(objectData);
            });
            Long callTime = System.currentTimeMillis();
            Result<BatchObjectDataResult> batchObjectDataResultResult = null;
            String reverseResult = "";
            try {
                batchObjectDataResultResult = objectDataService.batchIncrementUpdate(headerObj, objApiName, incrDataList);
                if (log.isDebugEnabled()) {
                    log.info("ReverseWrite2CrmManager crm batchIncrementUpdate, objApiName:{} incrDataList:{} result:{}", objApiName, JSON.toJSONString(incrDataList, SerializerFeature.WriteMapNullValue), batchObjectDataResultResult);
                }
                if (batchObjectDataResultResult != null && !batchObjectDataResultResult.isSuccess()) {//不成功上报
                    reverseResult=batchObjectDataResultResult.getMessage();
                    syncDataManager.updateReverseWriteFailed(tenantId, objApiName, PloyDetailNodeEnum.REVERSE_WRITE, reverseResult, null,i18NStringManager.getByEi(I18NStringEnum.s1014, tenantId)+":"+reverseResult);
                    TimePointRecorderStatic.setReWriteFailedMsg(syncDataContextEvent.getSyncDataId(), true, reverseResult);
                }else{
                    syncDataManager.updateNodeMsg(tenantId, objApiName, PloyDetailNodeEnum.REVERSE_WRITE, i18NStringManager.getByEi(I18NStringEnum.s6, tenantId), null);
                }
            } catch (Exception e) {//原来没有加catch，现在加上,异常不往外抛了
                reverseResult = e.getMessage();
                syncDataManager.updateReverseWriteFailed(tenantId, objApiName, PloyDetailNodeEnum.REVERSE_WRITE, reverseResult, null,i18NStringManager.getByEi(I18NStringEnum.s1014, tenantId)+":"+reverseResult);
                TimePointRecorderStatic.setReWriteFailedMsg(syncDataContextEvent.getSyncDataId(), true, reverseResult);
                log.warn("ReverseWrite2CrmManager processMessage is fail, e={}", e);
                if (syncDataContextEvent.getWriteResult() != null) {
                    syncDataContextEvent.getWriteResult().setErrMsg(reverseResult);
                }
            } finally {
                final int status = Objects.nonNull(batchObjectDataResultResult) && batchObjectDataResultResult.isSuccess() ? 1 : 2;
                if (batchObjectDataResultResult != null) {
                    reverseResult = batchObjectDataResultResult.isSuccess() ? JSON.toJSONString(batchObjectDataResultResult) : batchObjectDataResultResult.getMessage();
                }
                interfaceMonitorManager.saveErpInterfaceMonitor(tenantId, dcId, objApiName, ErpObjInterfaceUrlEnum.reverseWrite2Crm.name(), JSON.toJSONString(incrDataList), reverseResult, status, callTime, System.currentTimeMillis(), "objectDataService.batchIncrementUpdate", TraceUtil.get(), System.currentTimeMillis() - callTime, null);
            }
        }
        return syncDataContextEvent.nextButNotSetStop2False();
    }

    private Map<String, List<ObjectData>> getObjectDataMap(String tenantId, SyncDataContextEvent message, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        Map<String, List<ObjectData>> result = Maps.newHashMap();
        if (syncPloyDetailSnapshotData == null || syncPloyDetailSnapshotData.getSyncPloyDetailData() == null) {
            log.info("PloyDetail is null message={}", message);
            return result;
        }
        if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes() != null
                && syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getReverseWriteNode() != null) {
            IntegrationStreamNodesData.ReverseWriteNode reverseWriteNode = syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getReverseWriteNode();
            SyncDataContextEvent.WriteResult writeResult = message.getWriteResult();
            if (writeResult != null) {
                ObjectData objectData = getObjectData(tenantId, writeResult, reverseWriteNode.getFieldMappings());
                if (objectData != null) {
                    result.put(objectData.getApiName(), Lists.newArrayList(objectData));
                }
            } else {//从syncData取,没有syncData的也不回写了
                Map<String, SyncDataEntity> syncDataEntityMap = syncDataManager.getAllSyncData(tenantId);
                if(syncDataEntityMap!=null){
                    for (SyncDataEntity syncData : syncDataEntityMap.values()) {
                        if (syncData.getSourceObjectApiName().equals(syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceObjectApiName())) {//主对象
                            ObjectData objectData = getObjectDataBySyncData(tenantId, syncData, reverseWriteNode.getFieldMappings());
                            if (objectData != null) {
                                result.put(objectData.getApiName(), Lists.newArrayList(objectData));
                            }
                            break;
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(reverseWriteNode.getDetailObjectMappings())) {
                Map<String, List<FieldMappingData>> collect = reverseWriteNode.getDetailObjectMappings().stream().collect(Collectors.toMap(DetailObjectMappingsData.DetailObjectMappingData::getDestObjectApiName, DetailObjectMappingsData.DetailObjectMappingData::getFieldMappings));
                if (CollectionUtils.isNotEmpty(message.getDetailWriteResults())) {
                    for (SyncDataContextEvent.WriteResult detailResult : message.getDetailWriteResults()) {
                        String detailObjApiName = detailResult.getSimpleSyncData().getSourceObjectApiName();
                        if (collect.containsKey(detailObjApiName)) {
                            ObjectData detailData = getObjectData(tenantId, detailResult, collect.get(detailObjApiName));
                            if (detailData == null) {
                                continue;
                            }
                            if (result.containsKey(detailData.getApiName())) {
                                result.get(detailData.getApiName()).add(detailData);
                            } else {
                                result.put(detailData.getApiName(), Lists.newArrayList(detailData));
                            }
                        }
                    }
                } else {
                    Map<String, SyncDataEntity> syncDataEntityMap = syncDataManager.getAllSyncData(tenantId);
                    if(syncDataEntityMap!=null){
                        for (SyncDataEntity syncData : syncDataEntityMap.values()) {
                            if (collect.containsKey(syncData.getSourceObjectApiName())) {
                                ObjectData detailData = getObjectDataBySyncData(tenantId, syncData, collect.get(syncData.getSourceObjectApiName()));
                                if (detailData == null) {
                                    continue;
                                }
                                if (result.containsKey(detailData.getApiName())) {
                                    result.get(detailData.getApiName()).add(detailData);
                                } else {
                                    result.put(detailData.getApiName(), Lists.newArrayList(detailData));
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    private ObjectData getObjectData(String tenantId, SyncDataContextEvent.WriteResult writeResult, List<FieldMappingData> fieldMappingDataList) {
        ObjectData objectData = new ObjectData();
        objectData.putTenantId(tenantId);
        objectData.putApiName(writeResult.getSimpleSyncData().getSourceObjectApiName());
        objectData.putId(writeResult.getSimpleSyncData().getSourceDataId());
        for (FieldMappingData fieldMappingData : fieldMappingDataList) {
            if (StringUtils.isBlank(fieldMappingData.getDestApiName())) {
                continue;
            }
            switch (fieldMappingData.getSourceApiName()) {
                case ReverseWrite2CrmSourceFieldKey.SYNC_TIME:
                    objectData.put(fieldMappingData.getDestApiName(), System.currentTimeMillis());
                    break;
                case ReverseWrite2CrmSourceFieldKey.ERROR_CODE:
                    String code = writeResult.isSuccess() ? i18NStringManager.getByEi(I18NStringEnum.s632, tenantId) : i18NStringManager.getByEi(I18NStringEnum.s634, tenantId);
                    objectData.put(fieldMappingData.getDestApiName(), code);
                    break;
                case ReverseWrite2CrmSourceFieldKey.ERROR_MSG:
                    objectData.put(fieldMappingData.getDestApiName(), writeResult.getErrMsg());
                    break;
                case ReverseWrite2CrmSourceFieldKey.DEST_ID:
                    if (writeResult.isSuccess()) {
                        objectData.put(fieldMappingData.getDestApiName(), writeResult.getDestDataId());
                    }
                    break;
                case ReverseWrite2CrmSourceFieldKey.DEST_NAME:
                    if (writeResult.isSuccess() && writeResult.getSimpleSyncData() != null
                            && StringUtils.isNotBlank(writeResult.getSimpleSyncData().getDestDataName())) {
                        objectData.put(fieldMappingData.getDestApiName(), writeResult.getSimpleSyncData().getDestDataName());
                    }
                    break;
                default:
                    if (writeResult.getDestReturnData() != null && writeResult.getDestReturnData().containsKey(fieldMappingData.getSourceApiName())) {
                        objectData.put(fieldMappingData.getDestApiName(), writeResult.getDestReturnData().get(fieldMappingData.getSourceApiName()));
                    }
            }
        }
        // 如果只有 TenantId/ApiName/Id 这三个字段,说明没有数据变更
        if (objectData.keySet().size() <= 3) {
            return null;
        }
        return objectData;
    }

    private ObjectData getObjectDataBySyncData(String tenantId, SyncDataEntity syncData, List<FieldMappingData> fieldMappingDataList) {
        ObjectData objectData = new ObjectData();
        objectData.putTenantId(tenantId);
        objectData.putApiName(syncData.getSourceObjectApiName());
        objectData.putId(syncData.getSourceDataId());
        for (FieldMappingData fieldMappingData : fieldMappingDataList) {
            if (StringUtils.isBlank(fieldMappingData.getDestApiName())) {
                continue;
            }
            switch (fieldMappingData.getSourceApiName()) {
                case ReverseWrite2CrmSourceFieldKey.SYNC_TIME:
                    objectData.put(fieldMappingData.getDestApiName(), System.currentTimeMillis());
                    break;
                case ReverseWrite2CrmSourceFieldKey.ERROR_CODE:
                    String code = i18NStringManager.getByEi(I18NStringEnum.s634, tenantId);
                    objectData.put(fieldMappingData.getDestApiName(), code);
                    break;
                case ReverseWrite2CrmSourceFieldKey.ERROR_MSG:
                    objectData.put(fieldMappingData.getDestApiName(), syncData.getRemark());
                    break;
                case ReverseWrite2CrmSourceFieldKey.DEST_ID:
                    break;
                case ReverseWrite2CrmSourceFieldKey.DEST_NAME:
                    break;
                default:
            }
        }
        // 如果只有 TenantId/ApiName/Id 这三个字段,说明没有数据变更
        if (objectData.keySet().size() <= 3) {
            return null;
        }
        return objectData;
    }

}
