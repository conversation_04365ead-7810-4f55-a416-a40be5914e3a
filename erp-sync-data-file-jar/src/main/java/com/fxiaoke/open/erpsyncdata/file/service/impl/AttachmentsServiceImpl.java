package com.fxiaoke.open.erpsyncdata.file.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3FileManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3DownFileResult;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AttachmentsService;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:36 2023/4/19
 * @Desc:
 */
@Service("attachmentsService")
@Data
@Slf4j
public class AttachmentsServiceImpl implements AttachmentsService {
    @Autowired
    private K3FileManager k3FileManager;
    @Autowired
    private ApiClientHolder holder;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Override
    public Result2<AttachmentsResult> downAndUploadAttachments(AttachmentsArg attachmentsArg) {
        String tenantId = attachmentsArg.getTenantId();
        String dcId = attachmentsArg.getDcId();
        if(StringUtils.isEmpty(dcId)||StringUtils.isEmpty(tenantId)){
            return Result2.newError(ResultCodeEnum.PARAM_ERROR);
        }
        AttachmentsResult attachmentsResult = new AttachmentsResult();
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        K3CloudApiClient k3ApiClient = holder.getK3ApiClient(tenantId,erpConnectInfoEntity.getConnectParams(), dcId);
        if (TenantType.ERP.equals(attachmentsArg.getDestTenantType())) {//crm->erp
            if(CollectionUtils.isEmpty(attachmentsArg.getCrmFileModels())){
                return Result2.newError(ResultCodeEnum.PARAM_ERROR);
            }
            Map<String, Result<Map<String, Object>>> erpResults = Maps.newHashMap();
            for (CrmFileModel crmFileModel : attachmentsArg.getCrmFileModels()) {
                Result<InputStream> inputStreamResult = k3FileManager.crmDownFile(crmFileModel.getPath(), crmFileModel.getExt(), tenantId, crmFileModel.getSize());
                Result<Map<String, Object>> k3FileResult = k3FileManager.k3uploadFile(crmFileModel.getPath(), crmFileModel.getExt(), tenantId, inputStreamResult.getData(), crmFileModel.getFilename(), crmFileModel.getSize(), k3ApiClient);
                erpResults.put(crmFileModel.getPath(), k3FileResult);
            }
            attachmentsResult.setErpResults(erpResults);
        } else {//erp->crm
            if(CollectionUtils.isEmpty(attachmentsArg.getErpFileIds())){
                return Result2.newError(ResultCodeEnum.PARAM_ERROR);
            }
            Map<String, Result<CrmFileModel>> crmResults = Maps.newHashMap();
            for (String sourceValue : attachmentsArg.getErpFileIds()) {
                StringBuilder builder=new StringBuilder();
                Result<StoneFileUploadResponse> stoneFileUploadResponseResult = k3FileManager.k3downAndUploadFile(sourceValue, k3ApiClient,builder);
                if (stoneFileUploadResponseResult.isSuccess()) {
                    StoneFileUploadResponse stoneFileUploadResponse=stoneFileUploadResponseResult.getData();
                            CrmFileModel crmFileModel = CrmFileModel.builder().filename(builder.toString()).ext(stoneFileUploadResponse.getExtensionName()).path(String.format("%s.%s", stoneFileUploadResponse.getPath(), stoneFileUploadResponse.getExtensionName())).createTime(System.currentTimeMillis()).size(stoneFileUploadResponse.getSize()).build();
                    crmResults.put(sourceValue, Result.newSuccess(crmFileModel));
                }else {
                    crmResults.put(sourceValue, Result.newError(com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.CALL_PAAS_FAILED));
                }
            }
            attachmentsResult.setCrmResults(crmResults);
        }
        return Result2.newSuccess(attachmentsResult);
    }
}
