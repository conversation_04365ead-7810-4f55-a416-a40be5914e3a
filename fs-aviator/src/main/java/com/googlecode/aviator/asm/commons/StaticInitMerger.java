/***
 * ASM: a very small and fast Java bytecode manipulation framework Copyright (c) 2000-2011 INRIA,
 * France Telecom All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted
 * provided that the following conditions are met: 1. Redistributions of source code must retain the
 * above copyright notice, this list of conditions and the following disclaimer. 2. Redistributions
 * in binary form must reproduce the above copyright notice, this list of conditions and the
 * following disclaimer in the documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holders nor the names of its contributors may be used to
 * endorse or promote products derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
 * WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package com.googlecode.aviator.asm.commons;

import com.googlecode.aviator.asm.ClassVisitor;
import com.googlecode.aviator.asm.MethodVisitor;
import com.googlecode.aviator.asm.Opcodes;

/**
 * A {@link ClassVisitor} that merges clinit methods into a single one.
 *
 * <AUTHOR> Bruneton
 */
public class StaticInitMerger extends ClassVisitor {

  private String name;

  private MethodVisitor clinit;

  private final String prefix;

  private int counter;

  public StaticInitMerger(final String prefix, final ClassVisitor cv) {
    this(Opcodes.ASM4, prefix, cv);
  }

  protected StaticInitMerger(final int api, final String prefix, final ClassVisitor cv) {
    super(api, cv);
    this.prefix = prefix;
  }

  @Override
  public void visit(final int version, final int access, final String name, final String signature,
      final String superName, final String[] interfaces) {
    cv.visit(version, access, name, signature, superName, interfaces);
    this.name = name;
  }

  @Override
  public MethodVisitor visitMethod(final int access, final String name, final String desc,
      final String signature, final String[] exceptions) {
    MethodVisitor mv;
    if ("<clinit>".equals(name)) {
      int a = Opcodes.ACC_PRIVATE + Opcodes.ACC_STATIC;
      String n = prefix + counter++;
      mv = cv.visitMethod(a, n, desc, signature, exceptions);

      if (clinit == null) {
        clinit = cv.visitMethod(a, name, desc, null, null);
      }
      clinit.visitMethodInsn(Opcodes.INVOKESTATIC, this.name, n, desc);
    } else {
      mv = cv.visitMethod(access, name, desc, signature, exceptions);
    }
    return mv;
  }

  @Override
  public void visitEnd() {
    if (clinit != null) {
      clinit.visitInsn(Opcodes.RETURN);
      clinit.visitMaxs(0, 0);
    }
    cv.visitEnd();
  }
}
