package com.fxiaoke.open.erpsyncdata.apiproxy.model

import cn.hutool.core.date.TimeInterval
import cn.hutool.core.io.unit.DataSizeUtil
import cn.hutool.core.thread.ThreadUtil
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JavaObjectUtils
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil
import spock.lang.Ignore
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/8
 */
@Ignore
class StandardDataTest extends Specification {


    def "testGetObjSize"() {
        def masterDict = [
                "_id"    : "testSize",
                "longStr": "qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq"
        ]
        for (i in 0..<1000) {
            masterDict.put("field$i".toString(), "field$i Value".toString())
        }
        printf("%16s, %16s, %16s, %16s, %16s%n", "num", "size1", "size2", "cost1", "cost2")
        def hold1 = testCalculateObjSize(masterDict, 10)
        def hold2 = testCalculateObjSize(masterDict, 100)
        def hold3 = testCalculateObjSize(masterDict, 1000)
        def hold4 = testCalculateObjSize(masterDict, 5000)
        println(hold4)
        expect:
        true
    }

    private Object testCalculateObjSize(LinkedHashMap<String, String> masterDict, int num) {
        List<StandardData> standardDataList = new ArrayList<>()
        for (i in 0..<num) {
            def data = new StandardData()
            data.setMasterFieldVal(ObjectData.convert(masterDict))
            standardDataList.add(data)
        }
        def ti = new TimeInterval().restart()
        def size1 = JavaObjectUtils.computeObjectSizeInByte(standardDataList)

        def cost1 = ti.intervalRestart()
        //jdk 17不允许使用该方法
//        def size2 = ObjectSizeCalculator.getObjectSize(standardDataList)
        def size2 = RamUsageEstimateUtil.sizeOfObject(standardDataList, 0)
        def cost2 = ti.intervalRestart()

        printf("%16s, %16s, %16s, %16s, %16s%n", num, DataSizeUtil.format(size1), DataSizeUtil.format(size2), cost1, cost2)
        return standardDataList
    }
}
