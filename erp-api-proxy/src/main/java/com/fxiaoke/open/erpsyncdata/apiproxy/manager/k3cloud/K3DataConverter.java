package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3Constant;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ViewExtendVo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.ReadContext;
import com.jayway.jsonpath.spi.cache.Cache;
import com.jayway.jsonpath.spi.cache.CacheProvider;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/10
 */
@Slf4j
@Setter
@Getter
public class K3DataConverter {
    /**
     * 主对象字段列表
     */
    private List<ErpFieldExtendEntity> masterFieldExtends;
    /**
     * id字段扩展
     */
    private ErpFieldExtendEntity idFieldExtend;
    /**
     * 主属性字段扩展
     */
    private ErpFieldExtendEntity numFieldExtend;
    /**
     * key:明细的apiName，value:明细的字段列表
     */
    private Map<String, List<ErpFieldExtendEntity>> detailFieldExtendMap;
    /**
     * 明细对象：id字段扩展
     */
    private Map<String, ErpFieldExtendEntity> detailIdFieldExtend;
    /**
     * key:明细的apiName，value:主对象上明细信息 fieldDefineType=detail
     */
    private Map<String, ErpFieldExtendEntity> masterDetailFieldMap;

    /**
     * K3相关的保存配置项
     */
    private Map<String,Object> saveArgSettings;

    /**
     * 主对象保存时需要保存的复合字段
     * 已经是saveCode
     */
    private SaveCompositeFields saveCompositeFields;

    /**
     * 从对象保存时需要保存的复合字段
     * 已经是saveCode
     */
    private Map<String, SaveCompositeFields> detailSaveCompositeFields;

    private I18NStringManager i18NStringManager;
    private TenantConfigurationManager tenantConfigurationManager;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SaveCompositeFields {
        private List<String> saveCodes;
        private String separator;
    }


    private static final Configuration CONFIGURATION = Configuration.defaultConfiguration()
            .addOptions(Option.SUPPRESS_EXCEPTIONS, Option.DEFAULT_PATH_LEAF_TO_NULL, Option.ALWAYS_RETURN_LIST)
            .jsonProvider(new JacksonJsonProvider());
    private Cache cache = CacheProvider.getCache();

    /**
     * 将平台标准格式数据转换成K3保存参数的数据
     *
     * @param standardData
     * @param saveArg
     */
    public void fillSaveArg(StandardData standardData, SaveArg saveArg, String tenantId) {
        //model结果
        K3Model k3Model = new K3Model();
        List<String> detailIdFields = Lists.newArrayList(saveCompositeFields.getSaveCodes());
        if(numFieldExtend!=null&&numFieldExtend.getQueryCode()!=null){
            detailIdFields.add(numFieldExtend.getQueryCode());//
        }
        masterFieldExtends.removeIf(v -> StringUtils.isBlank(v.getSaveCode()));

        boolean keepNullValue = tenantConfigurationManager.getKeepNullValueConfig(tenantId, standardData.getObjAPIName());
        log.info("K3DataConverter.fillSaveArg,keepNullValue={}",keepNullValue);

        //转换主数据字段
        ObjectData masterObj = standardData.getMasterFieldVal();
        for (ErpFieldExtendEntity erpFieldExtend : masterFieldExtends) {
            String fieldApiName = erpFieldExtend.getFieldApiName();
            if(standardData.getNeedReturnField()!=null&&standardData.getNeedReturnField().containsKey(standardData.getObjAPIName())
                    &&standardData.getNeedReturnField().get(standardData.getObjAPIName()).contains(fieldApiName)){
                detailIdFields.add(erpFieldExtend.getQueryCode());
            }
            if (masterObj.containsKey(fieldApiName)) {
                Object value = masterObj.get(fieldApiName);
                setSaveValue(k3Model, erpFieldExtend.getSaveCode(),erpFieldExtend.getFieldDefineType(), value, keepNullValue);
            }
        }
        //转换从对象数据
        standardData.getDetailFieldVals().forEach((detailApiName, detailObjs) -> {
            List<K3Model> detailModels = new ArrayList<>();
            List<ErpFieldExtendEntity> detailFields = detailFieldExtendMap.get(detailApiName);
            for(ErpFieldExtendEntity detailField : detailFields){
                if(standardData.getNeedReturnField()!=null&&standardData.getNeedReturnField().containsKey(detailApiName)
                        &&standardData.getNeedReturnField().get(detailApiName).contains(detailField.getFieldApiName())){
                    detailIdFields.add(detailField.getQueryCode());
                }
            }
            ErpFieldExtendEntity idFieldExtend = getIdFieldExtend(tenantId,detailFields);
            for (ObjectData detailObj : detailObjs) {
                K3Model detailModel = new K3Model();
                for (ErpFieldExtendEntity detailField : detailFields) {
                    String fieldApiName = detailField.getFieldApiName();
                    if (detailObj.containsKey(fieldApiName)){
                        Object value = detailObj.get(fieldApiName);
                        setSaveValue(detailModel, detailField.getSaveCode(), detailField.getFieldDefineType(), value, keepNullValue);
                    }
                }
                detailModels.add(detailModel);
            }
            ErpFieldExtendEntity masterDetailField = masterDetailFieldMap.get(detailApiName);
            k3Model.put(masterDetailField.getSaveCode(), detailModels);
            detailIdFields.add(masterDetailField.getSaveCode() + "." + idFieldExtend.getSaveCode());
//            从对象复合id
            Optional.ofNullable(MapUtils.emptyIfNull(detailSaveCompositeFields).get(detailApiName))
                    .map(SaveCompositeFields::getSaveCodes)
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(v -> detailIdFields.addAll(v.stream().map(saveCode -> masterDetailField.getSaveCode() + "." + saveCode).collect(Collectors.toList())));
        });

        saveArg.setNeedReturnFields(detailIdFields.stream().distinct().collect(Collectors.toList()));
        //根据配置填充saveArg
        executeConfigFillSaveArg(saveArg);
        saveArg.setModel(k3Model);
        log.info("K3DataConverter.fillSaveArg,saveArg={}",saveArg);
    }

    private void executeConfigFillSaveArg(SaveArg saveArg)  {
            if (ObjectUtils.isNotEmpty(this.getSaveArgSettings())) {
                saveArg.putAll(this.getSaveArgSettings());
//                for (Field field : SaveArg.class.getFields()) {
//                    field.setAccessible(true);
//                     Object value = saveArgSettings.get(field.getName());
//                    if (Objects.nonNull(value)) {
//                        field.set(saveArg, value);
//                    }
//                }
            }
    }

    public static ErpFieldExtendEntity getIdFieldExtend(String tenantId,List<ErpFieldExtendEntity> fieldExtendEntities) {
        Optional<ErpFieldExtendEntity> first = fieldExtendEntities.stream()
                .filter(v -> ErpFieldTypeEnum.id.equals(v.getFieldDefineType())).findFirst();
        if (!first.isPresent()
                || StringUtils.isBlank(first.get().getSaveCode())
                || StringUtils.isBlank(first.get().getViewCode())) {
            throw new ErpSyncDataException(I18NStringEnum.s159,tenantId);
        }
        return first.get();
    }

    /**
     * 转换保存的结果
     *
     * @param saveResult
     * @return
     */
    public Result<ErpIdResult> convertSaveResult(String tenantId,SaveResult saveResult) {
        final SaveResult.Result result = saveResult.getResult();
        if (result == null || result.getResponseStatus() == null) {
            log.warn("save result error,result:{}", saveResult);
            return Result.newError(ResultCodeEnum.RESULT_ERROR, i18NStringManager.getByEi2(I18NStringEnum.s519.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s519.getI18nValue(), i18NStringManager.getByEi(I18NStringEnum.s650,tenantId)),
                    Lists.newArrayList(i18NStringManager.getByEi(I18NStringEnum.s650,tenantId))));
        }
        ResponseStatus responseStatus = result.getResponseStatus();
        if (!responseStatus.getIsSuccess()) {
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, responseStatus.printErrors());
        }
        List<K3Model> needReturnData = result.getNeedReturnData();
        Map<String, List<String>> detailDataIds = new HashMap<>();
        K3Model returnData = needReturnData.get(0);
        String masterId = getMasterDataId(returnData);
        K3Model masterReturnData= new K3Model();
        for(ErpFieldExtendEntity field : masterFieldExtends){//查询编码转换为统一编码
            if(StringUtils.isNotBlank(field.getQueryCode())&&returnData.containsKey(field.getQueryCode())){
                masterReturnData.put(field.getFieldApiName(),returnData.get(field.getQueryCode()));
            }
        }
        // 特殊处理K3C 订单CPQ   CRM不开启，ERP开启CPQ的情况
        if (CollectionUtils.isNotEmpty((List) returnData.get("FSaleOrderEntry")) && ((Map) ((List<K3Model>) returnData.get("FSaleOrderEntry")).get(0)).containsKey("FRowType")) {
            List<K3Model> cacheList = Lists.newArrayList();
            for (K3Model detail : returnData.getDetails("FSaleOrderEntry")) {
                if (!K3Constant.SON.equals(detail.getString("FRowType")) && !"Son".equals(detail.getString("FRowType"))) {
                    K3Model k3Model = detail;
                    cacheList.add(k3Model);
                }
            }
            returnData.put("FSaleOrderEntry", cacheList);
        }
        Map<String, Map<String, Map<String, Object>>> detailReturnData= Maps.newHashMap();
        detailFieldExtendMap.forEach((detailApiName, detailFields) -> {
            ErpFieldExtendEntity masterDetailField = masterDetailFieldMap.get(detailApiName);
            String detailObjStr=masterDetailField.getSaveCode()+"_";
            List<K3Model> details = returnData.getDetails(masterDetailField.getSaveCode());
            if(CollectionUtils.isNotEmpty(details)){
                Map<String, Map<String, Object>> id2Data =Maps.newHashMap();
                for(K3Model detail : details){
                    K3Model detailData= new K3Model();
                    for(ErpFieldExtendEntity detailField : detailFields) {
                        if(StringUtils.isBlank(detailField.getQueryCode())){
                            continue;
                        }
                        if(detailField.getQueryCode().startsWith(detailObjStr)){
                            String newKey=detailField.getQueryCode().replaceFirst(detailObjStr,"");
                            if(detail.containsKey(newKey)){//查询编码转换为统一编码
                                detailData.put(detailField.getFieldApiName(),detail.get(newKey));
                            }
                        }
                        if(detail.containsKey(detailField.getQueryCode())){//查询编码转换为统一编码
                            detailData.put(detailField.getFieldApiName(),detail.get(detailField.getQueryCode()));
                        }
                    }
                    id2Data.put(getSlaveDataId(detailApiName, detail),detailData);
                }
                detailReturnData.put(detailApiName,id2Data) ;
            }
            List<String> detailIdList = details.stream().map(v -> getSlaveDataId(detailApiName, v)).collect(Collectors.toList());
            detailDataIds.put(detailApiName, detailIdList);
        });

        ErpIdResult erpIdResult = new ErpIdResult();
        erpIdResult.setMasterDataName(getMasterDataName(returnData));
        erpIdResult.setMasterDataId(masterId);
        erpIdResult.setDetailDataIds(detailDataIds);
        erpIdResult.setMasterReturnData(masterReturnData);
        erpIdResult.setDetailReturnData(detailReturnData);
        return new Result<>(erpIdResult);
    }

    public String getMasterDataId(K3Model returnData) {
        return getId(saveCompositeFields, returnData);
    }

    public String getMasterDataName(K3Model returnData) {
        if (numFieldExtend == null || StringUtils.isBlank(numFieldExtend.getQueryCode())) {
            return null;
        }
        final String s = returnData.getString(numFieldExtend.getQueryCode());
        return StringUtils.isNotBlank(s) ? s : null;
    }

    public String getSlaveDataId(String detailApiName, K3Model returnData) {
        return getId(detailSaveCompositeFields.get(detailApiName), returnData);
    }

    public String getDataId(SaveResult saveResult,String tenantId) {
        //主对象可能返回id可能返回number
        ErpFieldExtendEntity idFieldExtend = getIdFieldExtend(tenantId,this.masterFieldExtends);
        String dataId;
        if ("id".equalsIgnoreCase(idFieldExtend.getViewCode())) {
            dataId = saveResult.getResult().getId();
        } else {
            dataId = saveResult.getResult().getNumber();
        }
        return dataId;
    }

    private String getId(SaveCompositeFields fields, K3Model returnData) {
        if (Objects.isNull(fields) || CollectionUtils.isEmpty(fields.getSaveCodes())) {
            return null;
        }

        return fields.getSaveCodes().stream()
                .map(code -> {
                    final Object o = returnData.get(code);
                    if (Objects.nonNull(o)) {
                        return o;
                    }
                    // 兼容大小写问题,旧的字段配置有大小写不一致的问题
                    return returnData.entrySet().stream()
                            .filter(entry -> entry.getKey().equalsIgnoreCase(code))
                            .findFirst()
                            .map(Map.Entry::getValue)
                            .orElse(null);
                })
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.joining(fields.getSeparator()));
    }


    /**
     * 转换查看的结果
     * 使用jsonPath读取结果json，转换成平台格式数据
     *
     * @param viewResult
     * @return
     */
    public Result<StandardData> convertViewResult(String tenantId,ErpFieldExtendEntity erpFieldExtendEntity, ViewResult viewResult) {
        if (viewResult == null || viewResult.getResult() == null) {
            log.warn("view result error,result:{}", viewResult);
            return Result.newError(ResultCodeEnum.RESULT_ERROR, i18NStringManager.getByEi2(I18NStringEnum.s519.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s519.getI18nValue(), i18NStringManager.getByEi(I18NStringEnum.s650,tenantId)),
                    Lists.newArrayList(i18NStringManager.getByEi(I18NStringEnum.s650,tenantId))));
        }
        ResponseStatus responseStatus = viewResult.getResult().getResponseStatus();
        if (responseStatus != null) {
            //成功情况下为空
            if (!responseStatus.getIsSuccess()) {
                //接口返回失败
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, responseStatus.printErrors());
            }
        }
        //转换主数据字段
        K3Model masterModel = viewResult.getResult().getResult();
        ReadContext masterDocument = JsonPath.parse(masterModel, CONFIGURATION);
        ObjectData masterFieldVal = new ObjectData();
        String erpId = masterModel.getString("Id");
        masterFieldVal.put("erp_id", erpId);
        masterFieldVal.put("erp_num", erpId);
        if (erpFieldExtendEntity != null && erpFieldExtendEntity.getViewCode() != null
                && masterModel.getString(erpFieldExtendEntity.getViewCode()) != null) {
            masterFieldVal.put("erp_num", masterModel.getString(erpFieldExtendEntity.getViewCode()));
        }
        for (ErpFieldExtendEntity erpFieldExtend : masterFieldExtends) {
            if (erpFieldExtend.getFieldDefineType().equals(ErpFieldTypeEnum.detail)) {
                //明细字段不需转换
                continue;
            }
            String fieldApiName = erpFieldExtend.getFieldApiName();
            if(StringUtils.isBlank(erpFieldExtend.getViewCode())){
                continue;
            }
            JsonPath viewPath = getViewPath(erpFieldExtend.getViewCode(), erpFieldExtend.getViewExtend());
            List<Object> value = masterDocument.read(viewPath);
            if (value.isEmpty()) {
                masterFieldVal.put(fieldApiName, null);//默认为空
                continue;
            }
            masterFieldVal.put(fieldApiName, value.get(0));
        }
        Map<String, List<ObjectData>> detailFieldVals = new HashMap<>(0);
        //转换从对象数据
        detailFieldExtendMap.forEach((detailApiName, detailFieldExtends) -> {
            ErpFieldExtendEntity masterDetailField = masterDetailFieldMap.get(detailApiName);
            List<K3Model> details = masterModel.getDetails(masterDetailField.getViewCode());
            List<ObjectData> detailObjs = new ArrayList<>();
            //两个循环，如有需要，后期考虑优化
            for (K3Model detail : details) {
                ObjectData detailObj = new ObjectData();
                ReadContext detailDocument = JsonPath.parse(detail, CONFIGURATION);
                for (ErpFieldExtendEntity detailFieldExtend : detailFieldExtends) {
                    String fieldApiName = detailFieldExtend.getFieldApiName();
                    if(StringUtils.isBlank(detailFieldExtend.getViewCode())){
                        continue;
                    }
                    JsonPath viewPath = getViewPath(detailFieldExtend.getViewCode(), detailFieldExtend.getViewExtend());
                    List<Object> value = detailDocument.read(viewPath);
                    if (value.isEmpty()) {
                        detailObj.put(fieldApiName, null);//默认为空
                        continue;
                    }
                    Object object=value.get(0);
                    if(ErpFieldTypeEnum.id.equals(detailFieldExtend.getFieldDefineType())){
                        if(object==null||"0".equals(object.toString())){//主键为0的明细不要
                            detailObj=null;
                            break;
                        }
                    }
                    detailObj.put(fieldApiName, object);
                }
                if(detailObj!=null){
                    detailObjs.add(detailObj);
                }
            }
            detailFieldVals.put(detailApiName, detailObjs);
        });
        StandardData standardData = new StandardData();
        standardData.setMasterFieldVal(masterFieldVal);
        standardData.setDetailFieldVals(detailFieldVals);
        return new Result<>(standardData);
    }

    private JsonPath getViewPath(String viewCode, String viewExtendStr) {
        JsonPath jsonPath = cache.get(viewCode);
        if (jsonPath == null) {
            ViewExtendVo viewExtend = getViewExtend(viewCode, viewExtendStr);
            String path = "$." + viewCode;
            //Name,FDataValue需要取中文的
            if (viewExtend.getIsMultiLanguageText()) {
                if (viewCode.contains("MultiLanguageText[0]")) {
                    //从多语言字段中取，换成取中文的
                    path = path.replace("MultiLanguageText[0]", "MultiLanguageText[?(@.LocaleId==2052)]");
                } else {
                    //直接从本字段取，增加取中文条件
                    path += "[?(@.Key==2052)].Value";
                }
            }
            try {
                jsonPath = JsonPath.compile(path);
            } catch (Exception e) {
                log.error("path compile failed,path:{}", path);
                jsonPath = JsonPath.compile("$.ERRORPATH");
            }
            //缓存
            cache.put(viewCode, jsonPath);
        }
        return jsonPath;
    }

    private ViewExtendVo getViewExtend(String viewCode, String viewExtend) {
        if (StringUtils.isBlank(viewExtend)) {
            ViewExtendVo viewExtendVo = new ViewExtendVo();
            //根据viewCode分析，name字段和辅助资料的dataValue默认设置为多语言文本。
            if ("Name".equalsIgnoreCase(viewCode)
                    || StringUtils.endsWithIgnoreCase(viewCode, ".Name")
                    || StringUtils.endsWithIgnoreCase(viewCode, ".FDataValue")
                    || viewCode.contains("MultiLanguageText[0]")) {
                viewExtendVo.setIsMultiLanguageText(true);
            }
            return viewExtendVo;
        }
        return JacksonUtil.fromJson(viewExtend, ViewExtendVo.class);
    }

    public static void setSaveValue(K3Model k3Model, String writeCode, ErpFieldTypeEnum fieldDefineType, Object value, boolean keepNullValue) {
        if (StringUtils.isBlank(writeCode)) {
            return;
        }
        if (value == null) {
            value = getNullValue(fieldDefineType, keepNullValue);
            log.debug("K3DataConverter.setSaveValue,writeCode={},v1={},fieldDefineType={}", writeCode, value, fieldDefineType);
            if (value == null) {
                return;
            }
        }
        Iterable<String> split = Splitter.on(".").split(writeCode);
        Iterator<String> iterator = split.iterator();
        K3Model currentModel = k3Model;
        while (iterator.hasNext()) {
            String code = iterator.next();
            if (iterator.hasNext()) {
                //不是叶子节点
                if (code.endsWith("[0]")) {
                    //单行子单据体的情况
                    code = StringUtils.removeEnd(code, "[0]");
                    currentModel.putIfAbsent(code, Collections.singletonList(new K3Model()));
                    Object l = currentModel.get(code);
                    //这个code应该储存了只有一个K3Model的list
                    if (l instanceof List) {
                        Object o = ((List<?>) l).get(0);
                        if (o instanceof K3Model) {
                            currentModel = (K3Model) o;
                            continue;
                        }
                    }
                } else {
                    currentModel.putIfAbsent(code, new K3Model());
                    Object o = currentModel.get(code);
                    if (o instanceof K3Model) {
                        currentModel = (K3Model) o;
                        continue;
                    }
                }
                log.info("set value error,model:{},writeCode:{},value:{}", JacksonUtil.toJson(k3Model), writeCode, value);
                return;
            } else {
                //叶子节点
                if (code.endsWith("[0]")) {
                    //单值列表
                    code = StringUtils.removeEnd(code, "[0]");
                    if(value==null){
                        currentModel.put(code, Collections.emptyList());
                    }else{
                        currentModel.put(code, Collections.singletonList(value));
                    }

                } else {
                    currentModel.put(code, value);
                }
            }
        }
    }

    private static Object getNullValue(ErpFieldTypeEnum fieldDefineType, boolean keepNullValue) {
        if(fieldDefineType==null){
            return null;
        }
        if(ConfigCenter.K3_NULL_TO_EMPTY_STRING.contains(fieldDefineType.name()) || keepNullValue){
            return "";
        }
        return null;
    }

    public Result<StandardData> convertQueryResult(String tenantId,ErpFieldExtendEntity idFieldExtendEntity, ErpFieldExtendEntity numberFieldExtendEntity, ViewResult viewResult) {
        if (viewResult == null || viewResult.getResult() == null) {
            log.warn("view result error,result:{}", viewResult);
            return Result.newError(ResultCodeEnum.RESULT_ERROR, i18NStringManager.getByEi2(I18NStringEnum.s519.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s519.getI18nValue(), i18NStringManager.getByEi(I18NStringEnum.s650,tenantId)),
                    Lists.newArrayList(i18NStringManager.getByEi(I18NStringEnum.s650,tenantId))));
        }
        ResponseStatus responseStatus = viewResult.getResult().getResponseStatus();
        if (responseStatus != null) {
            //成功情况下为空
            if (!responseStatus.getIsSuccess()) {
                //接口返回失败
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, responseStatus.printErrors());
            }
        }
        //转换主数据字段
        K3Model masterModel = viewResult.getResult().getResult();
        ObjectData masterFieldVal = new ObjectData();
        String erpId = null;
        List<ErpFieldExtendEntity> erpRealIdFieldExtend = masterFieldExtends.stream().filter(v -> v.getViewCode() != null && "Id".equals(v.getViewCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(erpRealIdFieldExtend)) {
            erpId = masterModel.getString(erpRealIdFieldExtend.get(0).getQueryCode());
        }
        if (StringUtils.isBlank(erpId)) {
            erpId = masterModel.getString(idFieldExtendEntity.getQueryCode());
        }
        masterFieldVal.put("erp_id", erpId);
        masterFieldVal.put("erp_num", erpId);
        if (numberFieldExtendEntity != null && numberFieldExtendEntity.getQueryCode() != null
                && masterModel.getString(numberFieldExtendEntity.getQueryCode()) != null) {
            masterFieldVal.put("erp_num", masterModel.getString(numberFieldExtendEntity.getQueryCode()));
        }
        for (ErpFieldExtendEntity erpFieldExtend : masterFieldExtends) {
            if (erpFieldExtend.getFieldDefineType().equals(ErpFieldTypeEnum.detail) || StringUtils.isBlank(erpFieldExtend.getQueryCode())) {
                //明细字段不需转换
                continue;
            }
            String fieldApiName = erpFieldExtend.getFieldApiName();
            masterFieldVal.put(fieldApiName, masterModel.get(erpFieldExtend.getQueryCode()));
        }
        Map<String, List<ObjectData>> detailFieldVals = new HashMap<>(0);
        //转换从对象数据
        detailFieldExtendMap.forEach((detailApiName, detailFieldExtends) -> {
            ErpFieldExtendEntity masterDetailField = masterDetailFieldMap.get(detailApiName);
            List<K3Model> details = masterModel.getDetails(masterDetailField.getViewCode());
            List<ObjectData> detailObjs = new ArrayList<>();
            //两个循环，如有需要，后期考虑优化
            for (K3Model detail : details) {
                ObjectData detailObj = new ObjectData();
                for (ErpFieldExtendEntity detailFieldExtend : detailFieldExtends) {
                    String fieldApiName = detailFieldExtend.getFieldApiName();
                    detailObj.put(fieldApiName, detail.get(detailFieldExtend.getQueryCode()));
                }
                detailObjs.add(detailObj);
            }
            detailFieldVals.put(detailApiName, detailObjs);
        });
        StandardData standardData = new StandardData();
        standardData.setMasterFieldVal(masterFieldVal);
        standardData.setDetailFieldVals(detailFieldVals);
        return new Result<>(standardData);
    }
}
