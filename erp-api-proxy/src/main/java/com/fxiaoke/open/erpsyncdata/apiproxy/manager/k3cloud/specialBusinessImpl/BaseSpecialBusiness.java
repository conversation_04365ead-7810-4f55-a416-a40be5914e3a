package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3SourceBillInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BaseSpecialBusiness implements SpecialBusiness {

    private static final String SALES_ORDER_COM_ID = "SalesOrderComId";
    private static final String VIR_NO = "VirNo";
    private static final String VIR_ID = "VirId";

    private static CommonBusinessManager commonBusinessManager;
    private static ErpObjectFieldDao erpObjectFieldDao;

    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    public  void setErpObjectFieldDao(ErpObjectFieldDao erpObjectFieldDao) {
        BaseSpecialBusiness.erpObjectFieldDao = erpObjectFieldDao;
    }

    @Autowired
    public void setCommonBusinessManager(CommonBusinessManager commonBusinessManager) {
        BaseSpecialBusiness.commonBusinessManager = commonBusinessManager;
    }

    /**
     * 查看erp数据后置动作
     * 一些单据增加虚拟字段
     *
     * @param erpIdArg
     * @param standardData
     * @param erpData
     * @param apiClient
     */
    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        log.debug("trace k3c afterRunView, erpidarg:{}, stddata:{} ", erpIdArg, standardData);
        Map<String, List<K3SourceBillInfo>> k3SourceBillInfoMap = ConfigCenter.K3_SOURCE_BILL_INFO_MAP;
        if (k3SourceBillInfoMap.containsKey(erpIdArg.getObjAPIName())) {
            try {
                boolean needLookupSaleorder = false;
                /**需要检查是否配置了 SalesOrderComId字段的所有erp对象apiname,包括主从*/
                List<String> erpObjAPINameList = Lists.newArrayList();
                erpObjAPINameList.add(erpIdArg.getObjAPIName() + ".BillHead");
                /*** K3C控制不走上查逻辑，通过检查 主对象/从对象 是否配置了 虚拟lookup订单复合字段 SalesOrderComId 来判断。
                 * 对于多账套的情况下，我们简单认为用户要么都需要关联订单，要么都不需要关联订单，因为从这里区分账套很困难，而实际上用户也不会有这种需求。* */
                ErpObjectFieldEntity arg = ErpObjectFieldEntity.builder().tenantId(erpIdArg.getTenantId()).channel(ErpChannelEnum.ERP_K3CLOUD)
                        .fieldApiName("SalesOrderComId").fieldDefineType(ErpFieldTypeEnum.object_reference).build();
                erpObjAPINameList.addAll(standardData.getDetailFieldVals().keySet());

                for (String detailObjAPIName : erpObjAPINameList) {
                    arg.setErpObjectApiName(detailObjAPIName);
                    if (CollectionUtils.isNotEmpty(erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(erpIdArg.getTenantId())).queryList(arg))) {
                        needLookupSaleorder = true;
                        break;
                    }
                }

                if(needLookupSaleorder) {
                    updateSalesOrderIdFromDetailQueryUp(erpIdArg, standardData, apiClient);
                } else {
                    log.info("skip lookup saleorder in k3c afterRunView, ei:{}, erpobj:{}", erpIdArg.getTenantId(), erpIdArg.getObjAPIName());
                }
            } catch(ErpSyncDataException e){
                log.warn("updateSalesOrderIdFrom DetailQueryUp failed,{}",e.getErrMsg());
            } catch (Exception e) {
                log.error("updateSalesOrderIdFrom detail query up exception", e);
            }
        }
    }

    /**
     * 编码换ID 默认FID，FNumber,另外维持一套配置文件,针对特殊字段添加特殊的FID，FNumber字段文件
     *  @param saveArg
     * @param standardData
     * @param apiClient
     * @param saveExtend
     */
    @Override
    public void beforeRunUpdate(SaveArg saveArg, StandardData standardData, K3DataConverter k3DataConverter, K3CloudApiClient apiClient, IdSaveExtend saveExtend) {
        Map<String, String> mappingField = ConfigCenter.APINAME_FID_FNUMBER_MAPPING;
        String apiFieldMapping = mappingField.get(standardData.getObjAPIName());
        List<String> fieldMapping = StringUtils.isEmpty(apiFieldMapping)?null:Splitter.on("|").splitToList(apiFieldMapping);
        String fileId = CollectionUtils.isNotEmpty(fieldMapping) ? fieldMapping.get(0) : "FID";
        String fileNumber = CollectionUtils.isNotEmpty(fieldMapping) ? fieldMapping.get(1) : "FNumber";
        log.info("beforeUpdate field:{},Number:{}",fileId,fileNumber);
        K3Model model = saveArg.getModel();
        //如果主键是Fnumber
        if (fileNumber.equals(k3DataConverter.getIdFieldExtend().getSaveCode())) {
            log.info("save code:{} dataConvert:{}",fileNumber,k3DataConverter);
            //如果主键是Fnumber则处理编码换ID
            Object modelNumber = saveArg.getModel().get(fileNumber);
            //id为空，编码不为空，用编码获取id
            String id = commonBusinessManager.getIdByNumber(standardData.getObjAPIName(), String.valueOf(modelNumber), fileId,
                    fileNumber, apiClient);
            model.put(fileId,id);
        }
    }


    /**
     * 上查关联销售订单
     *
     * @param erpIdArg
     * @param standardData
     * @param apiClient
     */
    protected void updateSalesOrderIdFromDetailQueryUp(ErpIdArg erpIdArg, StandardData standardData,
                                                       K3CloudApiClient apiClient) {
        Map<String, List<K3SourceBillInfo>> k3SourceBillInfoMap = ConfigCenter.K3_SOURCE_BILL_INFO_MAP;
        String objApiName = erpIdArg.getObjAPIName();
        for(K3SourceBillInfo k3SourceBillInfo:k3SourceBillInfoMap.get(objApiName)){
            try {
                String detailEntryApiName = k3SourceBillInfo.getEntryName();
                //需要填充订单的明细数据
                List<ObjectData> detailDatas = standardData.getDetailFieldVals().get(objApiName + "." + detailEntryApiName);
                if(CollectionUtils.isEmpty(detailDatas)){
                    if("SAL_OUTSTOCK".equals(objApiName)){//特殊处理，apiName不一样了
                        detailEntryApiName="SAL_OUTSTOCKENTRY";
                        detailDatas = standardData.getDetailFieldVals().get(objApiName + "." + detailEntryApiName);
                    }else if("SC_RECEIVESETTLEBILL".equals(objApiName)){
                        detailEntryApiName="RECEIVESETTLEBILLENTRY";
                        detailDatas = standardData.getDetailFieldVals().get(objApiName + "." + detailEntryApiName);
                    }
                }
                if (CollectionUtils.isEmpty(detailDatas)) {
                    continue;
                }
                /*
                 * erpIdArg中储存的是单据id，而erpData中billNo大小写不固定，先查询一遍编号
                 * */
                String billNo = commonBusinessManager.getNumberById(objApiName, erpIdArg.getDataId(), k3SourceBillInfo.getFidField(),
                        k3SourceBillInfo.getFbillNoField(), apiClient);
                //上查填充源单编码
                queryUpSrcByDetails(objApiName, apiClient, billNo, detailDatas,k3SourceBillInfo);
                for (ObjectData detailData : detailDatas) {
                    //填充虚拟订单id字段
                    String saleOrderNo = detailData.getString(K3CloudForm.SAL_SaleOrder + VIR_NO);
                    if (StringUtils.isNotBlank(saleOrderNo)) {
                        String saleOrderId = getSalesOrderIdByNumber(apiClient, saleOrderNo);
                        if (StringUtils.isNotEmpty(saleOrderId) && StringUtils.isNotEmpty(saleOrderNo)) {
                            String comOrderId = saleOrderId + "#" + saleOrderNo;
                            standardData.getMasterFieldVal().put(SALES_ORDER_COM_ID, comOrderId);
                            //填充从对象
                            detailData.put(K3CloudForm.SAL_SaleOrder + VIR_ID, saleOrderId);
                            detailData.put(SALES_ORDER_COM_ID, comOrderId);
                        }
                    }
                }
                //填充主对象,如果多个明细，那么取最后一个明细的第一条
                standardData.getMasterFieldVal().put(K3CloudForm.SAL_SaleOrder + VIR_NO, detailDatas.get(0).getString(K3CloudForm.SAL_SaleOrder + VIR_NO));
                standardData.getMasterFieldVal().put(K3CloudForm.SAL_SaleOrder + VIR_ID, detailDatas.get(0).getString(K3CloudForm.SAL_SaleOrder + VIR_ID));
                standardData.getMasterFieldVal().put(SALES_ORDER_COM_ID, detailDatas.get(0).getString(SALES_ORDER_COM_ID));
            } catch (Exception e) {
                log.warn("updateSalesOrderIdFromDetailQueryUp DetailQueryUp failed,{}",e.getMessage());
            }
        }

    }

    private void queryUpSrc(String formId,
                            K3CloudApiClient apiClient,
                            String billNo,
                            ObjectData detailData) {
        if (StringUtils.isBlank(billNo) || StringUtils.isBlank(formId)) {
            //无法上查终止
            return;
        }
        //上查到销售订单即终止
        if (formId.equalsIgnoreCase(K3CloudForm.SAL_SaleOrder)) {
            return;
        }
        if(ConfigCenter.K3_SOURCE_BILL_INFO_MAP.get(formId)==null||ConfigCenter.K3_SOURCE_BILL_INFO_MAP.get(formId).get(0)==null){
            //无配置链路，终止
            return;
        }
        K3SourceBillInfo srcBillInfo = ConfigCenter.K3_SOURCE_BILL_INFO_MAP.get(formId).get(0);
        boolean formCertain = srcBillInfo.getSrcBillFormId() != null;
        //查询源单
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(formId);
        ArrayList<String> fieldKeys;
        if (formCertain) {
            fieldKeys = Lists.newArrayList(srcBillInfo.getFidField(), srcBillInfo.getFbillNoField(),
                    srcBillInfo.getSrcBillNoField());
        } else {
            fieldKeys = Lists.newArrayList(srcBillInfo.getFidField(), srcBillInfo.getFbillNoField(),
                    srcBillInfo.getSrcBillTypeField(), srcBillInfo.getSrcBillNoField());
        }
        queryArg.setFieldKeysByList(fieldKeys);
        queryArg.appendEqualFilter(srcBillInfo.getFbillNoField(), billNo);
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess()) {
            log.error("query src bill failed,arg:{},result:{}", queryArg, result);
            return;
        }
        List<K3Model> srcBills = result.getData();
        if (srcBills.size() < 1) {
            log.info("not found src bill");
            return;
        }
        K3Model srcBill = srcBills.get(0);
        String srcBillType = formCertain ?
                srcBillInfo.getSrcBillFormId() : srcBill.getString(srcBillInfo.getSrcBillTypeField());
        String srcBillNo = srcBill.getString(srcBillInfo.getSrcBillNoField());
        //填充从对象
        detailData.put(srcBillType + VIR_NO, srcBillNo);
        queryUpSrc(srcBillType, apiClient, srcBillNo, detailData);
    }

    private void queryUpSrcByDetails(String formId,
                                     K3CloudApiClient apiClient,
                                     String billNo,
                                     List<ObjectData> detailDatas,
                                     K3SourceBillInfo srcBillInfo) {
        if (srcBillInfo == null) {
            //无配置下推链路，终止
            return;
        }
        boolean formCertain = srcBillInfo.getSrcBillFormId() != null;
        //查询源单
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(formId);
        ArrayList<String> fieldKeys;
        if (formCertain) {
            fieldKeys = Lists.newArrayList(srcBillInfo.getFidField(), srcBillInfo.getFbillNoField(),
                    srcBillInfo.getSrcBillNoField());
        } else {
            fieldKeys = Lists.newArrayList(srcBillInfo.getFidField(), srcBillInfo.getFbillNoField(),
                    srcBillInfo.getSrcBillTypeField(), srcBillInfo.getSrcBillNoField());
        }
        if(StringUtils.isNotBlank(srcBillInfo.getDirectlyRelatedOrderField())){//如果存在直接关联订单字段
            fieldKeys.add(srcBillInfo.getDirectlyRelatedOrderField());
        }
        queryArg.setFieldKeysByList(fieldKeys);
        queryArg.appendEqualFilter(srcBillInfo.getFbillNoField(), billNo);
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        if (!result.isSuccess()) {
            log.error("query src bill failed,arg:{},result:{}", queryArg, result);
            return;
        }
        List<K3Model> srcBills = result.getData();
        if (srcBills.size() != detailDatas.size()) {
            //只支持多个源下推生成的单据并且多个源单据的订单是一样的
            if (detailDatas.size()==1&&"AR_RECEIVEBILL".equals(srcBillInfo.getFormId())){
                ObjectData objectData = detailDatas.get(0);
                String upOrders="";
                ObjectData newObjectData = null;
                for (K3Model srcBill : srcBills) {
                    newObjectData= BeanUtil.deepCopy(objectData,ObjectData.class);
                    String srcBillType = formCertain ?
                      srcBillInfo.getSrcBillFormId() : srcBill.getString(srcBillInfo.getSrcBillTypeField());
                    String srcBillNo = srcBill.getString(srcBillInfo.getSrcBillNoField());
                    //填充从对象
                    objectData.put(srcBillType + VIR_NO, srcBillNo);
                    queryUpSrc(srcBillType, apiClient, srcBillNo, newObjectData);
                    if (StringUtils.isEmpty(upOrders)){
                        upOrders=newObjectData.getString("SAL_SaleOrderVirNo");
                    }else if (!upOrders.equals(newObjectData.getString("SAL_SaleOrderVirNo"))){
                        throw new ErpSyncDataException(I18NStringEnum.s117,
                                apiClient.getTenantId());
                    }
                }
                if (newObjectData!=null){
                  detailDatas.set(0, newObjectData);
                }
            }
            log.warn("src bill size no match detail size.");
            return;
        }
        if(StringUtils.isNotBlank(srcBillInfo.getDirectlyRelatedOrderField())){//如果存在直接关联订单字段
            boolean allDetailDirectlyRelatedOrder=srcBills.stream().allMatch(k3Model -> StringUtils.isNotBlank(k3Model.getString(srcBillInfo.getDirectlyRelatedOrderField())));
            if(allDetailDirectlyRelatedOrder){
                for (int i = 0; i < detailDatas.size(); i++) {
                    ObjectData detailData = detailDatas.get(i);
                    K3Model srcBill = srcBills.get(i);
                    String orderNo=srcBill.getString(srcBillInfo.getDirectlyRelatedOrderField());
                    detailData.put(K3CloudForm.SAL_SaleOrder + VIR_NO,orderNo);
                }
//                return;//----直接返回  有些客户需要查询源单，不仅仅是需要销售订单。原型客户：华大北科
            }
        }
        for (int i = 0; i < detailDatas.size(); i++) {
            ObjectData detailData = detailDatas.get(i);
            K3Model srcBill = srcBills.get(i);
            String srcBillType = formCertain ?
                    srcBillInfo.getSrcBillFormId() : srcBill.getString(srcBillInfo.getSrcBillTypeField());
            String srcBillNo = srcBill.getString(srcBillInfo.getSrcBillNoField());
            //填充从对象
            detailData.put(srcBillType + VIR_NO, srcBillNo);
            queryUpSrc(srcBillType, apiClient, srcBillNo, detailData);
        }
    }


    private String getSalesOrderIdByNumber(K3CloudApiClient apiClient, String saleOrderNo) {
        String saleOrderId = null;
        if (StringUtils.isNotBlank(saleOrderNo)) {
            try {
                saleOrderId = commonBusinessManager.getIdByNumber("SAL_SaleOrder", saleOrderNo, "FID",
                        "FBillNo", apiClient);
            } catch (ErpSyncDataException e) {
                log.warn("found saleOrderId failed,{}", e.getErrMsg());
            }
        }
        return saleOrderId;
    }
}
