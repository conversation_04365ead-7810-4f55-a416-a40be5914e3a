package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connertor.sdk.model.Base;
import com.facishare.open.erp.connertor.sdk.model.RefreshToken;
import com.facishare.open.erp.connertor.service.OauthLoginService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.FacebookDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.LinkedinDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.model.Oauth2ConnectParam;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.lang.reflect.Parameter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/10 17:34:11
 */
@Aspect
@Component
public class CheckProxyTokenExpireAspect {

    @Autowired
    private OauthLoginService oauthLoginService;

    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Before("execution(* com.facishare.open.erp.connertor.service.ConnectorService.*(..)) && args(arg)")
    public void checkToken(JoinPoint joinPoint, Base.ConnectorArg arg) {
        if (notExpire(arg.getExpireTime())) {
            return;
        }

        final MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        final Parameter[] parameters = signature.getMethod().getParameters();
        String channel = null;
        for (int i = 0; i < parameters.length; i++) {
            final PathVariable annotation = parameters[i].getAnnotation(PathVariable.class);
            if (Objects.nonNull(annotation) && Objects.equals(annotation.value(), "channel")) {
                channel = (String) joinPoint.getArgs()[i];
                break;
            }
        }

        refreshToken(channel, arg);
    }

    @Before("execution(* com.facishare.open.erp.connertor.facebook..*(..)) && args(arg)")
    public void checkFbToken(JoinPoint joinPoint, Base.ConnectorArg arg) {
        if (notExpire(arg.getExpireTime())) {
            return;
        }

        refreshToken(FacebookDataManager.Channel, arg);
    }

    @Before("execution(* com.facishare.open.erp.connertor.linkedin..*(..)) && args(arg)")
    public void checkLinkedinToken(JoinPoint joinPoint, Base.ConnectorArg arg) {
        if (notExpire(arg.getExpireTime())) {
            return;
        }

        refreshToken(LinkedinDataManager.Channel, arg);
    }

    private static boolean notExpire(Long expireTime) {
        // 给1分钟的缓冲时间
        return Objects.isNull(expireTime) || expireTime - 60000 > System.currentTimeMillis();
    }

    private void refreshToken(final String channel, final Base.ConnectorArg arg) {
        final RefreshToken.Arg refreshTokenArg = new RefreshToken.Arg();
        refreshTokenArg.setAppId(ConfigCenter.getOauthAppId(channel));
        refreshTokenArg.setAppSecret(ConfigCenter.getOauthAppSecret(channel));
        refreshTokenArg.setConnectParam(arg.getConnectParam());
        refreshTokenArg.setTenantId(arg.getTenantId());

        // 获取connectorInfo
        ErpConnectInfoEntity erpConnectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getConnectId());
        final Oauth2ConnectParam oauth2ConnectParam = erpConnectInfo.getChannel().getConnectParam(erpConnectInfo.getConnectParams());
        if (!notExpire(oauth2ConnectParam.getExpireTime())) {
            arg.setExpireTime(oauth2ConnectParam.getExpireTime());
            arg.setConnectParam(oauth2ConnectParam.getConnectParam());
            return;
        }

        // 防止并发
        final String key = arg.getTenantId() + "refreshToken";
        synchronized (key.intern()) {
            // 二次校验
            erpConnectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getConnectId());
            if (!notExpire(oauth2ConnectParam.getExpireTime())) {
                arg.setExpireTime(oauth2ConnectParam.getExpireTime());
                arg.setConnectParam(oauth2ConnectParam.getConnectParam());
                return;
            }

            final RefreshToken.Result result = oauthLoginService.refreshToken(channel, refreshTokenArg);
            oauth2ConnectParam.setConnectParam(result.getConnectParam());
            oauth2ConnectParam.setExpireTime(result.getExpireTime());

            erpConnectInfo.setConnectParams(JSON.toJSONString(oauth2ConnectParam));
            erpConnectInfo.setUpdateTime(System.currentTimeMillis());
            erpConnectInfoManager.updateById(arg.getTenantId(), erpConnectInfo);

            arg.setExpireTime(result.getExpireTime());
            arg.setConnectParam(result.getConnectParam());
        }
    }
}
