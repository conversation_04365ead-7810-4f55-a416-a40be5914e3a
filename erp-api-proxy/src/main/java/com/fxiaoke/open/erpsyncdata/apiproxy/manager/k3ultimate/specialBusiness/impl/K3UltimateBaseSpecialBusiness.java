package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.K3UltimateSpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 旗舰版特殊逻辑基类，所有的子类必须继承这个类
 *
 * <AUTHOR>
 * @date 2023-09-15
 */
@Component("base")
@Slf4j
public class K3UltimateBaseSpecialBusiness implements K3UltimateSpecialBusiness {
    @Override
    public void afterListErpObjDataByTime(String tenantId,
                                          String dataCenterId,
                                          String objApiName,
                                          TimeFilterArg timeFilterArg,
                                          List<StandardData> standardDataList,
                                          K3UltimateApiService k3UltimateApiService) {

    }

    @Override
    public void afterGetErpObjData(String tenantId,
                                   String dataCenterId,
                                   String objApiName,
                                   ErpIdArg erpIdArg,
                                   StandardData standardData,
                                   K3UltimateApiService k3UltimateApiService) {
        log.info("K3UltimateBaseSpecialBusiness.afterGetErpObjData,tenantId={},dataCenterId={},objApiName={},standardData={},erpIdArg={}",
                tenantId,dataCenterId,objApiName,standardData,erpIdArg);
    }

    @Override
    public void afterReSyncDataById(String tenantId,
                                    String dataCenterId,
                                    String objApiName,
                                    ErpIdArg erpIdArg,
                                    List<StandardData> standardDataList,
                                    K3UltimateApiService k3UltimateApiService) {

    }

    @Override
    public void beforeCreateErpObjData(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       StandardData standardData,
                                       JSONObject requestData,
                                       K3UltimateApiService k3UltimateApiService) {
        log.info("K3UltimateBaseSpecialBusiness.beforeCreateErpObjData,tenantId={},dataCenterId={},objApiName={},standardData={},requestData={}",
                tenantId,dataCenterId,objApiName,standardData,requestData);
    }

    @Override
    public void afterCreateErpObjData(String tenantId,
                                      String dataCenterId,
                                      String objApiName,
                                      StandardData standardData,
                                      JSONObject requestData,
                                      Result<ErpIdResult> result,
                                      K3UltimateApiService k3UltimateApiService) {
        log.info("K3UltimateBaseSpecialBusiness.afterCreateErpObjData,tenantId={},dataCenterId={},objApiName={},standardData={},requestData={},result={}",
                tenantId,dataCenterId,objApiName,standardData,requestData,result);
    }

    @Override
    public void beforeUpdateErpObjData(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       StandardData standardData,
                                       JSONObject requestData,
                                       K3UltimateApiService k3UltimateApiService) {
        log.info("K3UltimateBaseSpecialBusiness.beforeUpdateErpObjData,tenantId={},dataCenterId={},objApiName={},standardData={},requestData={}",
                tenantId,dataCenterId,objApiName,standardData,requestData);
    }

    @Override
    public void afterUpdateErpObjData(String tenantId,
                                      String dataCenterId,
                                      String objApiName,
                                      StandardData standardData,
                                      JSONObject requestData,
                                      Result<ErpIdResult> result,
                                      K3UltimateApiService k3UltimateApiService) {
        log.info("K3UltimateBaseSpecialBusiness.afterUpdateErpObjData,tenantId={},dataCenterId={},objApiName={},standardData={},requestData={},result={}",
                tenantId,dataCenterId,objApiName,standardData,requestData,result);
    }
}
