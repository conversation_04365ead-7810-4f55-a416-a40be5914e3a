package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import org.springframework.stereotype.Component;

@Component
public class MaterialApiTemplate extends K3UltimateBaseApiTemplate {
    @Override
    public String getObjApiName() {
        return K3UltimateObjApiName.bd_material;
    }

    @Override
    public K3UltimateExecuteStepEnum getUpdateExecuteStep() {
        return K3UltimateExecuteStepEnum.UPDATE;
    }

    @Override
    public String getBatchQueryApi() {
        return "/kapi/v2/basedata/bd_material/batchQuery";
    }

    @Override
    public String getBatchAddApi() {
        return "/kapi/v2/basedata/bd_material/batchAdd";
    }

    @Override
    public String getBatchUpdateApi() {
        return "/kapi/v2/basedata/bd_material/batchUpdate";
    }

    @Override
    public String getBatchSubmitApi() {
        return "/kapi/v2/basedata/bd_material/batchSubmit";
    }

    @Override
    public String getBatchUnSubmitApi() {
        return "/kapi/v2/basedata/bd_material/batchUnSubmit";
    }

    @Override
    public String getBatchAuditApi() {
        return "/kapi/v2/basedata/bd_material/batchAudit";
    }

    @Override
    public String getBatchUnAuditApi() {
        return "/kapi/v2/basedata/bd_material/batchUnAudit";
    }

    @Override
    public String getBatchEnableApi() {
        return "/kapi/v2/basedata/bd_material/batchEnable";
    }

    @Override
    public String getBatchDisableApi() {
        return "/kapi/v2/basedata/bd_material/batchDisable";
    }

    @Override
    public String getBatchDeleteApi() {
        return "/kapi/v2/basedata/bd_material/batchDelete";
    }
}
