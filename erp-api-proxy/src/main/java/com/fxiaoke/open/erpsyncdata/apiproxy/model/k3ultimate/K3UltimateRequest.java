package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.CommonIdQueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.K3UltimateQueryBaseArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class K3UltimateRequest<T extends K3UltimateQueryBaseArg> extends K3UltimateBaseRequest implements Serializable {
    private T data;

    public static K3UltimateRequestByQuery<CommonIdQueryArg> buildCommonIdRequest(String tenantId,
                                                                                  String dataCenterId,
                                                                                  String objApiName,
                                                                                  ErpObjInterfaceUrlEnum interfaceUrl,
                                                                                  List<String> idList,
                                                                                  int pageNo,
                                                                                  int pageSize) {

        CommonIdQueryArg queryArg = new CommonIdQueryArg();
        queryArg.setId(idList);
        queryArg.setIds(idList);

        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId, dataCenterId, objApiName,
                interfaceUrl.name());

        K3UltimateRequestByQuery<CommonIdQueryArg> request = new K3UltimateRequestByQuery<>();
        request.setData(queryArg);
        request.setInterfaceMonitorData(interfaceMonitorData);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        return request;
    }

    public static K3UltimateRequestByQuery<K3UltimateQueryBaseArg> buildBaseRequest(String tenantId,
                                                                                    String dataCenterId,
                                                                                    String objApiName,
                                                                                    ErpObjInterfaceUrlEnum interfaceUrl,
                                                                                    K3UltimateQueryBaseArg baseArg,
                                                                                    int pageNo,
                                                                                    int pageSize,
                                                                                    TenantConfigurationManager tenantConfigurationManager) {
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(tenantId, dataCenterId, objApiName,
                interfaceUrl.name());

        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> request = new K3UltimateRequestByQuery<>();
        request.setData(baseArg);
        request.setInterfaceMonitorData(interfaceMonitorData);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        if(tenantConfigurationManager!=null) {
            Boolean serializeNull = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.SERIALIZE_NULL_TENANTS);
            request.setSerializeNull(serializeNull);
        }

        return request;
    }

    public static K3UltimateRequestByQuery<K3UltimateQueryBaseArg> buildBaseRequestByNumber(String tenantId,
                                                                                            String dataCenterId,
                                                                                            String objApiName,
                                                                                            ErpObjInterfaceUrlEnum interfaceUrl,
                                                                                            List<String> numberList,
                                                                                            Map<String, Object> customQueryArg,
                                                                                            int pageNo,
                                                                                            int pageSize,
                                                                                            TenantConfigurationManager tenantConfigurationManager) {
        K3UltimateQueryBaseArg baseArg = new K3UltimateQueryBaseArg();
        baseArg.setNumber(numberList);
        baseArg.setBillno(numberList);
        baseArg.setEnable("1");
        if (customQueryArg != null && !customQueryArg.isEmpty()) {
            baseArg.putAll(customQueryArg);
        }

        return buildBaseRequest(tenantId, dataCenterId, objApiName, interfaceUrl, baseArg, pageNo, pageSize, tenantConfigurationManager);
    }

    public static K3UltimateRequestByQuery<K3UltimateQueryBaseArg> buildBaseRequestById(String tenantId,
                                                                                        String dataCenterId,
                                                                                        String objApiName,
                                                                                        ErpObjInterfaceUrlEnum interfaceUrl,
                                                                                        List<String> idList,
                                                                                        Map<String, Object> customQueryArg,
                                                                                        int pageNo,
                                                                                        int pageSize,
                                                                                        TenantConfigurationManager tenantConfigurationManager) {
        K3UltimateQueryBaseArg baseArg = new K3UltimateQueryBaseArg();
        baseArg.setId(idList);
        baseArg.setEnable("1");
        if (customQueryArg != null && !customQueryArg.isEmpty()) {
            baseArg.putAll(customQueryArg);
        }

        return buildBaseRequest(tenantId, dataCenterId, objApiName, interfaceUrl, baseArg, pageNo, pageSize, tenantConfigurationManager);
    }
}