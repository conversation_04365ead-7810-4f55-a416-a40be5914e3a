package com.fxiaoke.open.erpsyncdata.apiproxy.model.standard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SyncResult extends JSONObject {
    public static final String SUCCESS_CODE = "0";
    private static final String CODE = "code";
    private static final String MESSAGE = "message";
    private static final String DATA = "data";

    public String getCode() {
        return getString(CODE);
    }


    public String getMessage() {
        return getString(MESSAGE);
    }

    public boolean isSuccess() {
        return Objects.equals(getCode(), SyncResult.SUCCESS_CODE);
    }

    public <T> T parseData(TypeReference<T> resType) {
        if (resType.getType() == Void.class) {
            return null;
        }
        Object o = get(DATA);
        if (o instanceof JSON) {
            return JSON.parseObject(((JSON) o).toJSONString(), resType);
        }
        try {
            //尝试强转
            //noinspection unchecked
            return getObject(DATA, resType);
        } catch (Exception ignore) {
        }
        return null;
    }

    public SyncResult(Map<String, Object> map) {
        super(map);
    }

    public static SyncResult parseFromStr(String str) {
        JSONObject jsonObject = JSON.parseObject(str);
        SyncResult syncResult = new SyncResult(jsonObject);
        return syncResult;
    }
}
