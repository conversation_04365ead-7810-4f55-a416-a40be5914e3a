package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.facishare.open.erp.connertor.sdk.model.Base;
import com.facishare.open.erp.connertor.sdk.model.GetById;
import com.facishare.open.erp.connertor.sdk.model.ListByTime;
import com.facishare.open.erp.connertor.sdk.model.dto.ErpObjectData;
import com.facishare.open.erp.connertor.service.ConnectorService;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.ErpInterfaceMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.Oauth2ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/11 10:05:31
 */
public abstract class ErpOverseasProxyDataManager extends BaseErpDataManager {

    @Autowired
    private ConnectorService connectorService;

    @Autowired
    private ErpFieldManager erpFieldManager;

    @Autowired
    private ErpObjManager erpObjManager;

    protected abstract String getChannel();

    @Override
    @ErpInterfaceMonitor(objApiName = "#erpIdArg.objAPIName", type = ErpObjInterfaceUrlEnum.queryMasterById)
    public Result<StandardData> getErpObjData(final ErpIdArg erpIdArg, final ErpConnectInfoEntity connectInfo) {
        final GetById.Arg arg = initConnectorArg(connectInfo, new GetById.Arg());
        arg.setObjAPIName(erpIdArg.getObjAPIName());
        arg.setExtendValue(getIdExtendValue(connectInfo.getTenantId(), connectInfo.getId(), erpIdArg.getObjAPIName()));
        arg.setDataId(erpIdArg.getDataId());

        final ErpObjectData result = connectorService.getById(getChannel(), arg).getErpObjectData();
        StandardData standardData = convert2StandardData(result);
        return Result.newSuccess(standardData);
    }

    private String getIdExtendValue(final String tenantId, final String dcId, final String objAPIName) {
        List<ErpObjectRelationshipEntity> masterRelations = erpObjManager.listMasterRelationsByRealApiName(tenantId, dcId, objAPIName);
        String splitObjApiName = masterRelations.get(0).getErpSplitObjectApiname();
        final ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, splitObjApiName);
        return idField.getFieldExtendValue();
    }

    private static StandardData convert2StandardData(final ErpObjectData result) {
        if (Objects.isNull(result)) {
            return null;
        }

        final ObjectData masterData = ObjectData.convert(result.getMaster());

        final Map<String, List<ObjectData>> detailMap = MapUtils.emptyIfNull(result.getDetails()).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(ObjectData::convert).collect(Collectors.toList())
                ));

        StandardData standardData = new StandardData();
        standardData.setMasterFieldVal(masterData);
        standardData.setDetailFieldVals(detailMap);
        return standardData;
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(final ErpIdArg erpIdArg, final ErpConnectInfoEntity connectInfo) {
        final Result<StandardData> erpObjData = getErpObjData(erpIdArg, connectInfo);
        if (!erpObjData.isSuccess()) {
            return Result.copy(erpObjData);
        }

        final ArrayList<StandardData> result = Lists.newArrayList();
        if (Objects.nonNull(erpObjData.getData())) {
            result.add(erpObjData.getData());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<StandardListData> listErpObjDataByTime(final TimeFilterArg timeFilterArg, final ErpConnectInfoEntity connectInfo) {
        if(timeFilterArg.getOperationType()!=null&& EventTypeEnum.INVALID.getType()==timeFilterArg.getOperationType()){
            return listInvalidErpObjDataByTime(timeFilterArg, connectInfo);
        }else{
           return listUpdateErpObjDataByTime(timeFilterArg, connectInfo);
        }
    }
    @ErpInterfaceMonitor(objApiName = "#timeFilterArg.objAPIName", type = ErpObjInterfaceUrlEnum.queryMasterBatch)
    public Result<StandardListData> listUpdateErpObjDataByTime(final TimeFilterArg timeFilterArg, final ErpConnectInfoEntity connectInfo) {
        return listErpObjDataByTimes(timeFilterArg, connectInfo);
    }

    @ErpInterfaceMonitor(objApiName = "#timeFilterArg.objAPIName", type = ErpObjInterfaceUrlEnum.queryInvalid)
    public Result<StandardListData> listInvalidErpObjDataByTime(final TimeFilterArg timeFilterArg, final ErpConnectInfoEntity connectInfo) {
        return listErpObjDataByTimes(timeFilterArg, connectInfo);
    }

    public Result<StandardListData> listErpObjDataByTimes(final TimeFilterArg timeFilterArg, final ErpConnectInfoEntity connectInfo) {
        // facebook 不支持offset
        if (Objects.nonNull(timeFilterArg.getOffset()) && !Objects.equals(0, timeFilterArg.getOffset())) {
            return Result.newSuccess(new StandardListData());
        }

        // TODO 分页 数据不足,无法测试分页
        final ListByTime.Arg arg = initConnectorArg(connectInfo, new ListByTime.Arg());
        arg.setExtendValue(getIdExtendValue(connectInfo.getTenantId(), connectInfo.getId(), timeFilterArg.getObjAPIName()));
        arg.setStartTime(timeFilterArg.getStartTime());
        arg.setEndTime(timeFilterArg.getEndTime());
        arg.setIncludeDetail(timeFilterArg.isIncludeDetail());
        arg.setOffset(timeFilterArg.getOffset());
        arg.setLimit(timeFilterArg.getLimit());
        arg.setObjAPIName(timeFilterArg.getObjAPIName());
        // 线索暂时没有过滤条件
        // final List<Filter> filterList = ListUtils.emptyIfNull(timeFilterArg.getFilters()).stream()
        //         .map(filterData -> new Filter(filterData.getFieldApiName(), filterData.getOperate(), filterData.getFieldValue()))
        //         .collect(Collectors.toList());
        // arg.setFilters(filterList);

        final ListByTime.Result result = connectorService.listByTime(getChannel(), arg);

        final StandardListData data = new StandardListData();
        data.setTotalNum(result.getTotalNum());
        data.setDataList(ListUtils.emptyIfNull(result.getErpObjectData()).stream().map(ErpOverseasProxyDataManager::convert2StandardData).collect(Collectors.toList()));

        return Result.newSuccess(data);
    }

    @Override
    public Result<ErpIdResult> createErpObjData(final StandardData standardData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<StandardDetailId> createErpObjDetailData(final SyncDataContextEvent doWriteMqData, final StandardDetailData standardDetailData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjData(final StandardInvalidData standardInvalidData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }
    @Override
    public Result<String> deleteErpObjData(final StandardInvalidData standardDeleteData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjDetailData(final StandardInvalidData standardInvalidData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> recoverErpObjData(final StandardRecoverData standardRecoverData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(final StandardData standardData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<StandardDetailId> updateErpObjDetailData(final SyncDataContextEvent doWriteMqData, final StandardDetailData standardDetailData, final ErpConnectInfoEntity connectInfo) {
        return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    public static <T extends Base.ConnectorArg> T initConnectorArg(final ErpConnectInfoEntity connectInfo, T arg) {
        arg.setTenantId(connectInfo.getTenantId());
        arg.setConnectId(connectInfo.getId());
        final Oauth2ConnectParam connectParam = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams());
        arg.setConnectParam(connectParam.getConnectParam());
        arg.setExpireTime(connectParam.getExpireTime());
        return arg;
    }
}
