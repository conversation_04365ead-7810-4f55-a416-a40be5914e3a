package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.CustomFunctionConstantEx;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.factory.SpecialObjHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ObjToMapUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FunctionServiceExecuteReturnData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.PARAM_ERROR;

/**
 * <AUTHOR>
 * @Date: 9:58 2020/12/04
 * @Desc: 特殊数据接口处理
 */
@Slf4j
@Component
public class SpecialWayDataServiceImpl implements SpecialWayDataService{

    @Autowired
    private DBDataManager dbDataManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SpecialObjHandlerFactory specialObjHandlerFactory;
    @Autowired
    private OuterService erpOuterServiceImpl;
    @Autowired
    private ErpTempDataManager erpTempDataManager;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ComplexDataConvertUtils complexDataConvertUtils;

    private static final String CRM_OBJ_API_NAME = "CRM_OBJ_API_NAME";

    /**
     * 根据id获取erp对象数据(dataBase)
     *
     * @param erpIdArg
     * @return
     */
    @Override
    public Result<StandardData> getErpObjDataByIdWithDB(ErpIdArg erpIdArg,String dataCenterId) {
        Result<StandardData> result = erpTempDataManager.getErpObjDataFromMongo(erpIdArg,dataCenterId);

        return result;
    }
    /**
     * 根据id获取erp对象数据(dataBase) 重试专用
     *
     * @param erpIdArg
     * @return
     */
    @Override
    public Result<List<StandardData>> getReSyncObjDataByIdWithDB(ErpIdArg erpIdArg,String dataCenterId) {
        //改为从临时库取，推送数据统一推进临时库
        Result<StandardData> erpObjDataResult = erpTempDataManager.getErpObjDataFromMongo(erpIdArg,dataCenterId);
        if(erpObjDataResult!=null){
            Result<List<StandardData>> dataListResult=Result.newSuccess();
            if(!erpObjDataResult.isSuccess()){
                dataListResult.setErrCode(erpObjDataResult.getErrCode());
                dataListResult.setErrMsg(erpObjDataResult.getErrMsg());
                dataListResult.setData(Lists.newArrayList());
                return dataListResult;
            }else {
                //特殊处理
                List<StandardData> dataList=Lists.newArrayList();
                erpObjDataResult.getData().setSyncLogId(LogIdUtil.get());
                dataList.add(erpObjDataResult.getData());
                List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(erpIdArg.getTenantId(),dataCenterId, erpIdArg.getObjAPIName(),false);
                if(CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)){
                    for(String apiName:destObjApiNameByErpRealApiName){
                        SpecialObjHandler objHandler = specialObjHandlerFactory.getObjHandler(apiName);
                        if(objHandler!=null){
                            objHandler.afterReSyncDataById(erpIdArg,dataList,dataCenterId);
                        }
                    }
                }
                dataListResult.setData(dataList);
                return dataListResult;
            }
        }
        return null;
    }
    /**
     * 根据时间获取erp对象数据(dataBase)
     *
     * @param timeFilterArg
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTimeWithDB(TimeFilterArg timeFilterArg,String dataCenterId) {
        Result<StandardListData> result=dbDataManager.listErpObjDataByTime(timeFilterArg);
        //特殊处理
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(timeFilterArg.getTenantId(),dataCenterId, timeFilterArg.getObjAPIName(),false);
        if(CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)){
            for(String apiName:destObjApiNameByErpRealApiName){
                SpecialObjHandler objHandler = specialObjHandlerFactory.getObjHandler(apiName);
                if(objHandler!=null){
                    objHandler.afterQueryListData(timeFilterArg,result.getData().getDataList(),dataCenterId);
                }
            }
        }
        return result;
    }
    /**
     * 根据id获取erp对象数据(groovy)
     *
     * @param erpIdArg
     * @param erpObjGroovyEntity
     * @return
     */
    @Override
    public Result<StandardData> getErpObjDataByIdWithGroovy(ErpIdArg erpIdArg, ErpObjGroovyEntity erpObjGroovyEntity) {
        Result<String> executeResult = executeCustomFunction(erpIdArg.getTenantId(),
                erpObjGroovyEntity.getDataCenterId(),
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(erpIdArg),
                ErpObjInterfaceUrlEnum.queryMasterById,
                erpIdArg.getSyncPloyDetailSnapshotId(), null, null);
        Result<StandardData> result=Result.newSuccess();
        if(executeResult!=null&&!executeResult.isSuccess()){
            result.setErrCode(executeResult.getErrCode());
            result.setErrMsg(executeResult.getErrMsg());
            return result;
        }

        String jsonStr = executeResult.getData();
        StandardData erpObjDataResult = JSON.parseObject(jsonStr, new TypeReference<StandardData>(){});
        erpObjDataResult.setSyncLogId(LogIdUtil.get());
        result.setData(erpObjDataResult);
        return result;
    }
    /**
     * 根据id获取erp对象数据(groovy)  重试专用
     *
     * @param erpIdArg
     * @param erpObjGroovyEntity
     * @return
     */
    @Override
    public Result<List<StandardData>> getReSyncObjDataByIdWithGroovy(ErpIdArg erpIdArg, ErpObjGroovyEntity erpObjGroovyEntity,String dataCenterId) {
        Result<StandardData> erpObjDataResult = this.getErpObjDataByIdWithGroovy(erpIdArg,erpObjGroovyEntity);
        if(erpObjDataResult!=null){
            Result<List<StandardData>> dataListResult=Result.newSuccess();
            if(!erpObjDataResult.isSuccess()){
                dataListResult.setErrCode(erpObjDataResult.getErrCode());
                dataListResult.setErrMsg(erpObjDataResult.getErrMsg());
                dataListResult.setData(Lists.newArrayList());
                return dataListResult;
            }else {
                //特殊处理
                List<StandardData> dataList=Lists.newArrayList();
                dataList.add(erpObjDataResult.getData());
                List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(erpIdArg.getTenantId(),dataCenterId, erpIdArg.getObjAPIName(),false);
                if(CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)){
                    for(String apiName:destObjApiNameByErpRealApiName){
                        SpecialObjHandler objHandler = specialObjHandlerFactory.getObjHandler(apiName);
                        if(objHandler!=null){
                            objHandler.afterReSyncDataById(erpIdArg,dataList,dataCenterId);
                        }
                    }
                }
                dataListResult.setData(dataList);
                return dataListResult;
            }
        }
        return null;
    }

    /**
     * 根据时间获取erp对象数据(groovy)
     *
     * @param timeFilterArg
     * @param erpObjGroovyEntity
     * @return
     */
    @Override
    public Result<StandardListData> listErpObjDataByTimeWithGroovy(TimeFilterArg timeFilterArg, ErpObjGroovyEntity erpObjGroovyEntity,String dataCenterId) {
        SyncPloyDetailSnapshotEntity snap = syncPloyDetailSnapshotManager.getEntryBySnapshotId(timeFilterArg.getTenantId(),
                timeFilterArg.getSnapshotId());
        String ployDetailId = null;
        if(snap!=null) {
            ployDetailId = snap.getSyncPloyDetailId();
        }
        Result<String> executeResult = executeCustomFunction(erpObjGroovyEntity.getTenantId(),
                dataCenterId,
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(timeFilterArg),
                ErpObjInterfaceUrlEnum.queryMasterBatch, null, null, null);

        Result<StandardListData> result=Result.newSuccess();
        if(executeResult!=null&&!executeResult.isSuccess()){
            result.setErrCode(executeResult.getErrCode());
            result.setErrMsg(executeResult.getErrMsg());
            return result;
        }

        String jsonStr = executeResult.getData();

        //数据长度超过限制，要告警到研发值班。
        if(jsonStr.length() > ConfigCenter.LIST_CONTENT_LENGTH_LIMIT) {
            log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}, function api:{} ",timeFilterArg.getTenantId(), erpObjGroovyEntity.getFuncApiName());
            String msg = i18NStringManager.getByEi2(I18NStringEnum.s646,
                    timeFilterArg.getTenantId(),
                    timeFilterArg.getTenantId(),(ConfigCenter.LIST_CONTENT_LENGTH_LIMIT/(1024*1024))+"",(jsonStr.length()/(1024*1024))+"",erpObjGroovyEntity.getFuncApiName(),timeFilterArg.getObjAPIName());
            SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                    .msg(msg)
                    .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s645,timeFilterArg.getTenantId()))
                    .tenantId(timeFilterArg.getTenantId())
                    .dcId(dataCenterId)
                    .sendSuperAdminIfNoSendTenantAdmin(true)
                    .build();
            if(StringUtils.isNotEmpty(ployDetailId)) {
                arg.setPloyDetailId(ployDetailId);
            }
            notificationService.sendTenantAdminNotice(arg, AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,timeFilterArg.getTenantId()),
                    AlarmType.SYNC_EXCEPTION,
                    AlarmLevel.URGENT);
        }

        StandardListData listErpObjDataResult = JSON.parseObject(jsonStr, new TypeReference<StandardListData>(){});

        if(listErpObjDataResult!=null&& CollectionUtils.isNotEmpty(listErpObjDataResult.getDataList())){
            for(int i=0;i<listErpObjDataResult.getDataList().size();i++){
                if (listErpObjDataResult.getDataList().get(i) == null
                        || listErpObjDataResult.getDataList().get(i).getMasterFieldVal()==null) continue;

                listErpObjDataResult.getDataList().get(i).getMasterFieldVal().putApiName(timeFilterArg.getObjAPIName());
                listErpObjDataResult.getDataList().get(i).getMasterFieldVal().putTenantId(timeFilterArg.getTenantId());
                if(listErpObjDataResult.getDataList().get(i).getDetailFieldVals()==null){
                    listErpObjDataResult.getDataList().get(i).setDetailFieldVals(Maps.newHashMap());
                }
            }
        }
        result.setData(listErpObjDataResult);
        //特殊处理
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(timeFilterArg.getTenantId(),dataCenterId, timeFilterArg.getObjAPIName(),false);
        if(CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)){
            for(String apiName:destObjApiNameByErpRealApiName){
                SpecialObjHandler objHandler = specialObjHandlerFactory.getObjHandler(apiName);
                if(objHandler!=null){
                    objHandler.afterQueryListData(timeFilterArg,result.getData().getDataList(),dataCenterId);
                }
            }
        }
        //支持拼接主键
        //根据dbid+erp_realapiname查询对应的拓展
        complexDataConvertUtils.complexId(listErpObjDataResult,timeFilterArg,dataCenterId);
        return result;
    }

    @Override
    public Result<ErpIdResult> createErpObjDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                          StandardData standardData,
                                                          ErpObjGroovyEntity erpObjGroovyEntity,
                                                          String dataCenterId) {
        //StandardData standardData = FormatConvertUtil.crm2StdErp(doWriteMqData);
        //特殊处理
        String tenantId=doWriteMqData.getDestTenantId();
        SpecialObjHandler objHandler=null;
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(tenantId,dataCenterId, standardData.getObjAPIName(),true);
        if(CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)){
            for(String apiName:destObjApiNameByErpRealApiName){
                objHandler = specialObjHandlerFactory.getObjHandler(apiName);
            }
        }
        if(objHandler!=null){//前动作
            objHandler.beforeCreateErpObjData(tenantId,standardData,dataCenterId);
        }

        Map<String,Object> funcMapArg = ObjToMapUtil.objectToMap(standardData);
        if(StringUtils.isEmpty(doWriteMqData.getSyncPloyDetailSnapshotId())) {
            SyncPloyDetailEntity ployDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getById(tenantId, doWriteMqData.getStreamId());
            if(ployDetailEntity!=null) {
                funcMapArg.put(CRM_OBJ_API_NAME,ployDetailEntity.getSourceObjectApiName());
            }
        }
        Result<String> executeResult = executeCustomFunction(doWriteMqData.getSourceTenantId(),
                dataCenterId,
                erpObjGroovyEntity.getFuncApiName(),
                funcMapArg,
                ErpObjInterfaceUrlEnum.create,
                doWriteMqData.getSyncPloyDetailSnapshotId(),
                doWriteMqData.getMainObjApiName(),
                Opt.ofNullable(doWriteMqData.getSourceData()).map(v -> v.getName()).get());

        Result<ErpIdResult> result=Result.newSuccessByI18N(null,null,null);
        String jsonStr = executeResult.getData();
        if(executeResult!=null&&!executeResult.isSuccess()){
            try{
                if(StringUtils.isNotBlank(jsonStr)){
                    ErpIdResult erpIdResult = JSON.parseObject(jsonStr, new TypeReference<ErpIdResult>(){});
                    result.setData(erpIdResult);
                }
            }catch (Exception e){
                log.warn("parseObject jsonStr:{}", jsonStr);
            }
            result.setErrCode(executeResult.getErrCode());
            result.setErrMsg(executeResult.getErrMsg());
            return result;
        }
        ErpIdResult erpIdResult = JSON.parseObject(jsonStr, new TypeReference<ErpIdResult>(){});
        result.setData(erpIdResult);
        if(objHandler!=null){//后动作
            objHandler.afterCreateErpObjData(tenantId,standardData,result,dataCenterId);
        }
        return result;
    }

    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData
     * @param erpObjGroovyEntity
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                                     StandardDetailData standardDetailData,
                                                                     ErpObjGroovyEntity erpObjGroovyEntity,
                                                                     String dataCenterId) {
        Result<String> executeResult = executeCustomFunction(doWriteMqData.getSourceTenantId(),
                dataCenterId,
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(standardDetailData),
                ErpObjInterfaceUrlEnum.createDetail,
                doWriteMqData.getSyncPloyDetailSnapshotId(), null, null);

        Result<StandardDetailId> result=Result.newSuccess();
        if(executeResult!=null&&!executeResult.isSuccess()){
            result.setErrCode(executeResult.getErrCode());
            result.setErrMsg(executeResult.getErrMsg());
            return result;
        }

        String jsonStr = executeResult.getData();
        StandardDetailId standardDetailId = JSON.parseObject(jsonStr, new TypeReference<StandardDetailId>(){});
        result.setData(standardDetailId);
        return result;
    }

    @Override
    public Result<ErpIdResult> updateErpObjDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                          StandardData standardData,
                                                          ErpObjGroovyEntity erpObjGroovyEntity,
                                                          String dataCenterId) {
        //特殊处理
        String tenantId=doWriteMqData.getDestTenantId();

        SpecialObjHandler objHandler=null;
        List<String> destObjApiNameByErpRealApiName = getCrmObjApiNameByErpRealApiName(tenantId,dataCenterId, standardData.getObjAPIName(),true);
        if(CollectionUtils.isNotEmpty(destObjApiNameByErpRealApiName)){
            for(String apiName:destObjApiNameByErpRealApiName){
                objHandler = specialObjHandlerFactory.getObjHandler(apiName);
            }
        }
        if(objHandler!=null){//前动作
            objHandler.beforeUpdateErpObjData(tenantId,standardData,dataCenterId);
        }

        Map<String,Object> funcMapArg = ObjToMapUtil.objectToMap(standardData);
        if(StringUtils.isEmpty(doWriteMqData.getSyncPloyDetailSnapshotId())) {
            SyncPloyDetailEntity ployDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getById(tenantId, doWriteMqData.getStreamId());
            if(ployDetailEntity!=null) {
                funcMapArg.put(CRM_OBJ_API_NAME,ployDetailEntity.getSourceObjectApiName());
            }
        }
        Result<String> executeResult = executeCustomFunction(doWriteMqData.getSourceTenantId(),
                dataCenterId,
                erpObjGroovyEntity.getFuncApiName(),
                funcMapArg,
                ErpObjInterfaceUrlEnum.update,
                doWriteMqData.getSyncPloyDetailSnapshotId(),
                doWriteMqData.getMainObjApiName(),
                Opt.ofNullable(doWriteMqData.getSourceData()).map(v -> v.getName()).get());

        Result<ErpIdResult> result=Result.newSuccess();
        if(executeResult!=null&&!executeResult.isSuccess()){
            result.setErrCode(executeResult.getErrCode());
            result.setErrMsg(executeResult.getErrMsg());
            return result;
        }

        String jsonStr = executeResult.getData();
        ErpIdResult erpIdResult = JSON.parseObject(jsonStr, new TypeReference<ErpIdResult>(){});
        result.setData(erpIdResult);
        if(objHandler!=null){//后动作
            objHandler.afterUpdateErpObjData(tenantId,standardData,result,dataCenterId);
        }
        return result;
    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param erpObjGroovyEntity
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailDataWithGroovy(SyncDataContextEvent doWriteMqData,
                                                                     StandardDetailData standardDetailData,
                                                                     ErpObjGroovyEntity erpObjGroovyEntity) {
        Result<String> executeResult = executeCustomFunction(doWriteMqData.getSourceTenantId(),
                erpObjGroovyEntity.getDataCenterId(),
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(standardDetailData),
                ErpObjInterfaceUrlEnum.updateDetail,
                doWriteMqData.getSyncPloyDetailSnapshotId(), null, null);

        Result<StandardDetailId> result=Result.newSuccess();
        if(executeResult!=null&&!executeResult.isSuccess()){
            result.setErrCode(executeResult.getErrCode());
            result.setErrMsg(executeResult.getErrMsg());
            return result;
        }

        String jsonStr = executeResult.getData();
        StandardDetailId standardDetailId = JSON.parseObject(jsonStr, new TypeReference<StandardDetailId>(){});
        result.setData(standardDetailId);
        return result;
    }

    /**
     * 作废erp对象明细数据
     */
    @Override
    public Result<String> invalidErpObjDataWithGroovy(StandardInvalidData standardInvalidData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId) {
        Result<String> executeResult = executeCustomFunction(erpObjGroovyEntity.getTenantId(),
                erpObjGroovyEntity.getDataCenterId(),
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(standardInvalidData),
                ErpObjInterfaceUrlEnum.invalid,
                syncPloyDetailSnapshotId,
                null, null);
        return executeResult;
    }
    /**
     * 删除erp对象明细数据
     */
    @Override
    public Result<String> deleteErpObjDataWithGroovy(StandardInvalidData standardInvalidData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId) {
        Result<String> executeResult = executeCustomFunction(erpObjGroovyEntity.getTenantId(),
                erpObjGroovyEntity.getDataCenterId(),
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(standardInvalidData),
                ErpObjInterfaceUrlEnum.invalid,
                syncPloyDetailSnapshotId,
                null, null);
        return executeResult;
    }
    /**
     * 作废erp明细对象明细数据
     */
    @Override
    public Result<String> invalidErpObjDetailDataWithGroovy(StandardInvalidData standardInvalidData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId) {
        Result<String> executeResult = executeCustomFunction(erpObjGroovyEntity.getTenantId(),
                erpObjGroovyEntity.getDataCenterId(),
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(standardInvalidData),
                ErpObjInterfaceUrlEnum.invalidDetail,
                syncPloyDetailSnapshotId, null, null);
        return executeResult;
    }



    @Override
    public Result<String> executeCustomFunction(String tenantId,
                                                String dataCenterId,
                                                String funcApiName,
                                                Map arg,
                                                ErpObjInterfaceUrlEnum interfaceUrl,
                                                final String syncPloyDetailSnapshotId, String objApiName, String objDataName) {
        if(!StringUtils.isNotBlank(funcApiName)){
            return Result.newError(PARAM_ERROR.getErrCode(), I18NStringEnum.s644);
        }

        ExecuteCustomFunctionArg functionArg = new ExecuteCustomFunctionArg();
        functionArg.setApiName(funcApiName);
        functionArg.setBindingObjectAPIName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        functionArg.setNameSpace(CustomFunctionConstantEx.NAME_SPACE);
        functionArg.setObjectData(arg);

        ExecuteCustomFunctionArg.ExecuteCustomFunctionParameterData parameterData = new ExecuteCustomFunctionArg.ExecuteCustomFunctionParameterData();
        if (objApiName != null) {
            parameterData.setSourceObjectApiName(objApiName);
        } else {
            if (StringUtils.isNotEmpty(syncPloyDetailSnapshotId)) {
                SyncPloyDetailSnapshotEntity entryBySnapshotId = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId, syncPloyDetailSnapshotId);
                parameterData.setSourceObjectApiName(entryBySnapshotId.getSourceObjectApiName());
            } else {
                //对于历史数据同步syncPloyDetailSnapshotId=null的场景，从业务方把源对象apiName的值传过来
                if (arg != null) {
                    Object crmObjApiName = arg.get(CRM_OBJ_API_NAME);
                    if (crmObjApiName != null) {
                        parameterData.setSourceObjectApiName(crmObjApiName.toString());
                    }
                }
            }
        }

        functionArg.setParameter(parameterData);


        Result2<FunctionServiceExecuteReturnData> returnDataResult
                = erpOuterServiceImpl.executeCustomFunction(tenantId,dataCenterId,functionArg,interfaceUrl, null, syncPloyDetailSnapshotId, objDataName);

        if(returnDataResult.isSuccess()) {
            return Result.newSuccess(JacksonUtil.toJson(returnDataResult.getData()));
        } else {
            if(returnDataResult.getIntErrCode() == com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum.SOCKET_TIMEOUT.getErrCode()) {
                return Result.newError(ResultCodeEnum.SOCKETTIMEOUT);
            }
            Result<String> result= Result.newError(returnDataResult.getIntErrCode()+"",returnDataResult.getErrMsg());
            if(returnDataResult.getData()!=null){
                result.setData(JacksonUtil.toJson(returnDataResult.getData()));
            }
            return result;
        }
    }

    @Override
    public Result<String> recoverErpObjDataWithGroovy(StandardRecoverData standardRecoverData, ErpObjGroovyEntity erpObjGroovyEntity, final String syncPloyDetailSnapshotId) {
        Result<String> executeResult = executeCustomFunction(erpObjGroovyEntity.getTenantId(),
                erpObjGroovyEntity.getDataCenterId(),
                erpObjGroovyEntity.getFuncApiName(),
                ObjToMapUtil.objectToMap(standardRecoverData),
                ErpObjInterfaceUrlEnum.recover,
                syncPloyDetailSnapshotId, null, null);
        return executeResult;
    }

    /**
     * 通过erp真时apiName获取相关的策略的crm对象apiName
     * @param tenantId
     * @param objApiName
     * @return
     */
    private List<String> getCrmObjApiNameByErpRealApiName(String tenantId,String dataCenterId, String objApiName,Boolean crmIsSource){
        ErpObjectRelationshipEntity erpObjectRelationshipEntity = ErpObjectRelationshipEntity.builder()
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .erpRealObjectApiname(objApiName)
                .build();
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryList(erpObjectRelationshipEntity);
        if(CollectionUtils.isNotEmpty(erpObjectRelationshipEntityList)){
            List<String> splitApiNames = erpObjectRelationshipEntityList.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
            List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByTenantIdAndStatus(tenantId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            if(CollectionUtils.isNotEmpty(syncPloyDetailEntities)){
                if(!crmIsSource){
                    List<String> destApiNames = syncPloyDetailEntities.stream().filter(entity -> splitApiNames.contains(entity.getSourceObjectApiName())).map(SyncPloyDetailEntity::getDestObjectApiName).collect(Collectors.toList());
                    return destApiNames;
                }else{
                    List<String> destApiNames = syncPloyDetailEntities.stream().filter(entity -> splitApiNames.contains(entity.getDestObjectApiName())).map(SyncPloyDetailEntity::getSourceObjectApiName).collect(Collectors.toList());
                    return destApiNames;
                }

            }
        }

        return Lists.newArrayList();
    }
}
