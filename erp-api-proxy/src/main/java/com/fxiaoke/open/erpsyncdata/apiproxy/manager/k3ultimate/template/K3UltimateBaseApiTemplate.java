package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template;

import lombok.Data;
import org.springframework.stereotype.Component;

@Component
@Data
public class K3UltimateBaseApiTemplate extends K3UltimateApiTemplate {
    /**
     * 新增单据执行步骤
     */
    private K3UltimateExecuteStepEnum addExecuteStep = K3UltimateExecuteStepEnum.AUDIT;
    /**
     * 新增单据执行步骤
     */
    private K3UltimateExecuteStepEnum updateExecuteStep = K3UltimateExecuteStepEnum.AUDIT;

    @Override
    public String getObjApiName() {
        return null;
    }
}
