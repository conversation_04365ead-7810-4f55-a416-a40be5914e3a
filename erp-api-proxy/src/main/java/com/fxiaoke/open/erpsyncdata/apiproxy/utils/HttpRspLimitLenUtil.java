package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import cn.hutool.core.lang.Opt;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.Util;
import okio.BufferedSource;
import org.apache.commons.io.input.BoundedInputStream;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.EOFException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;

import static java.nio.charset.StandardCharsets.UTF_8;


@Slf4j
public class HttpRspLimitLenUtil {

    @Data
    @Builder
    public static class ResponseBodyModel{
        //实际读到的内容
        private String body;
        /**
         * 引用header方面后面使用
         */
        private Headers headers;
        //实际长度超过最大系统长度限制，body被截断
        @Builder.Default
        private boolean reachLengthLimit = false;

        @Builder.Default
        private int code = 200;
        public boolean isOk() {
            return code >= 200 && code < 300;
        }
        public String getUnOkMessage() {
            return code + " " + body;
        }

        public static long getSize(ResponseBodyModel responseBodyModel) {
            return Opt.ofNullable(responseBodyModel).map(v -> v.getBody()).map(v -> Long.valueOf(v.length())).orElse(0L);
        }
    }

    /**为什么要自己计算长度？ 因为外部系统五花八门，有些系统返回http-response， 没有content-length或者返回0.
     * */
    public static ResponseBodyModel getLimitLengthRspString(Response response, long rspReadLimitLenByte) {
        ResponseBodyModel.ResponseBodyModelBuilder builder = ResponseBodyModel.builder();
        if (response == null) {
            //response为空应该会直接异常，走不到
            return builder.reachLengthLimit(false).body("").build();
        }
        ResponseBody responseBody = response.body();
        builder.headers(response.headers());
        if(responseBody == null) {
            return builder.reachLengthLimit(false).body("").build();
        }
        ResponseBodyModel model = builder.code(response.code()).build();
        MediaType contentType = responseBody.contentType();
        Charset charset = contentType != null ? contentType.charset(UTF_8) : UTF_8;
        try {
            Charset charset2 = Util.readBomAsCharset(responseBody.source(), charset);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            long contentLen = readHttpContentByPage(bos, responseBody.source(),rspReadLimitLenByte);
            String body = bos.toString(charset2.name());
            model.setBody(body);
            model.setReachLengthLimit(contentLen >= rspReadLimitLenByte);
            return model;
        } catch (Exception e) {
            log.info("body limit 2 string exception", e);
        }
        return null;
    }

    public static long readHttpContentByPage(ByteArrayOutputStream bos, BufferedSource bf, long lengthLimit) {
        int onePageLen = 2048; //每次读2k 字节
        long contentLength = 0;
        byte[] bytes = null;
        boolean isReadLastPage = false;
        int cntRead = 0;
        while (cntRead < 100*1024) { //读200M也作为结束条件。
            cntRead++;
            try {
                bytes = isReadLastPage ? bf.readByteArray(): bf.readByteArray(onePageLen);
                if((null==bytes) || (bytes.length ==0)) { //数据全部读完了。
                    break;
                }
                contentLength+=bytes.length;
                if(bytes.length > 0) {
                    bos.write(bytes);
                }
                if(contentLength >= lengthLimit) {//超过数据最大长度限制,
                    log.warn("read more than {} M data, stop reading more data",lengthLimit/(1024*1024));
                    break;
                }

                if(isReadLastPage) { //已经读完最后一页
                    break;
                }
            }catch (Exception e) {
                if(e instanceof EOFException) { //指定长度如果大于最后剩下的字节数，会抛出异常. 所以要正确独处最后一页，要特殊处理。
                    isReadLastPage = true;
                } else {//其它的异常代码无法自动处理。
                    log.error("readHttpContentByPage get exception, ", e);
                    break;
                }
            }
        };
        return contentLength;
    }
    public static Result<String> convertStreamToString(InputStream is,long limitSize) throws Exception {
        BoundedInputStream boundedInputStream=new BoundedInputStream(is,limitSize);
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        StringBuilder sb = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        if(boundedInputStream.getCount()>=limitSize){//达到最大限制了
            log.warn("convertStreamToString read more than {} M data, stop reading more data",limitSize/(1024*1024));
            return new Result<>(ResultCodeEnum.CONTROL_ERP_PUSH_DATA_LENGTH,limitSize);
        }
        return Result.newSuccess(sb.toString());
    }
}
