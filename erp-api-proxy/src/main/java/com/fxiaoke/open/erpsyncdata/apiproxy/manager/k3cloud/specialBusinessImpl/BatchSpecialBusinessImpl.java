package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/28
 */
@Slf4j
@Component("BD_BatchMainFile")
public class BatchSpecialBusinessImpl extends CommonStockBusinessImpl implements SpecialBusiness {
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 批次也需要
     * @param queryArg
     * @param timeFilterArg
     * @param k3CloudApiClient
     */
    @Override
    public void beforeRunBillQuery(QueryArg queryArg, TimeFilterArg timeFilterArg, K3CloudApiClient k3CloudApiClient) {
        //调用组织机构对象接口
        List<String> orgNos = getSyncStockOrgNos(timeFilterArg.getTenantId(),k3CloudApiClient.getDataCenterId());
        // 设置参数 业务组织和使用组织一般是一致的
        queryArg.appendInFilter("FCreateOrgId.FNumber", orgNos);
        queryArg.setOrderString("FNumber ASC");
    }

    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        String batchNumber = erpData.getString("FNumber");
        if(StringUtils.isBlank(batchNumber)){
            batchNumber = erpData.getString("Number");
        }
        String batchId = erpData.getString("FLOTID");
        if(StringUtils.isBlank(batchId)){
            batchId = erpData.getString("Id");
        }
        String materialNo =erpData.getString("FMaterialID.FNumber");
        if(StringUtils.isBlank(materialNo)){
            materialNo = JsonPath.read(erpData,"$.MaterialID.Number");
        }
        String materialId = erpData.getString("FMaterialID");
        if(StringUtils.isBlank(materialId)){
            materialId = erpData.getString("MaterialID_Id");
        }
        if(StringUtils.isEmpty(materialNo)) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s122.getI18nKey(),
                    erpIdArg.getTenantId(),
                    i18NStringManager.getByEi2(I18NStringEnum.s3703, erpIdArg.getTenantId(),materialId),
                    Lists.newArrayList(materialId)),
                    null,
                    null);
        }
        String bizType = erpData.getString("FBizType");
        if(StringUtils.isBlank(bizType)){
            bizType = JsonPath.read(erpData,"$.BizType");
        }
        Joiner joiner = Joiner.on("/");
        String comNo = joiner.join(materialNo,batchNumber,bizType);
        String comNo2 = joiner.join(comNo,batchId);
        //物料编号+批号，直接把批号替换了
        standardData.getMasterFieldVal().put("name",comNo);
        standardData.getMasterFieldVal().put("FNumber",comNo);
        //批号
        standardData.getMasterFieldVal().put("FNumber2",batchNumber);
        //物料编码、批号、批次id
        standardData.getMasterFieldVal().put("NameId",comNo2);
    }
}
