package com.fxiaoke.open.erpsyncdata.apiproxy.node.context;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class Push2TempCtx extends NodeContext<Push2TempCtx> {
    private String splitObjApiName;
    private String realObjApiName;
    private Integer operationType;
    private boolean directSync = false;
    private List<StandardData> dataList;
    private String locale;


    @Override
    public Integer getSourceTenantType() {
        return TenantType.ERP;
    }
}
