package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.factory.SpecialBusinessFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.NodeDataStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ErpTempDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.ErpTempDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.CheckProductErpDataMonitor;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BsonTimeUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TaskUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CommonConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SearchTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpTempResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 16:19 2021/7/19
 * @Desc:
 */
@Component
@Slf4j
public class ErpTempDataManager {

    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private SpecialBusinessFactory specialBusinessFactory;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private TriggerPollingMongoManager triggerPollingMongoManager;
    @Autowired
    private CheckProductErpDataMonitor checkProductErpDataMonitor;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    public Result<Long> updateStatusByIds(String tenantId, String ployDetailId, List<String> objectIds, Integer syncStatus, String remark) {
        if (syncStatus != null) {
            syncStatus = ErpTempDataStatusEnum.getErpTempDataStatus(syncStatus).getStatus();//指定状态如果不是ErpTempDataStatusEnum中的，统一是其他状态
        }
        return erpTempDataDao.updateSyncStatusByIds(tenantId, ployDetailId, objectIds, syncStatus, remark);
    }

    public Result<Long> updateSendMqSuccessByIds(String tenantId, String ployDetailId, List<String> objectIds) {
        return erpTempDataDao.updateSyncStatusByIds(tenantId, ployDetailId, objectIds, ErpTempDataStatusEnum.STATUS_SEND_MQ_SUC.getStatus(), i18NStringManager.getByEi(I18NStringEnum.s3773, tenantId), Lists.newArrayList("locale"));
    }

    //通过id获取数据
    public Result<StandardData> getErpObjDataFromMongo(ErpIdArg erpIdArg, String dataCenterId) {
        Result<Document> documentResult = getDocument(erpIdArg, dataCenterId);
        if (documentResult.isSuccess() && documentResult.getData() != null
                && StringUtils.isNotBlank(documentResult.getData().getString("data_body"))) {
            StandardData standardData = JSONObject.parseObject(documentResult.getData().getString("data_body"), StandardData.class);
            standardData.getMasterFieldVal().put("mongo_id", documentResult.getData().getObjectId("_id").toString());//把mongo数据库id赋值给主对象mongo_id字段
            Long version = documentResult.getData().getDate("new_last_sync_time") == null ? System.currentTimeMillis() : documentResult.getData().getDate("new_last_sync_time").getTime();
            standardData.setDataVersion(version);
            return Result.newSuccess(standardData);
        }
        return Result.copy(documentResult);
    }

    public Result<Document> getDocument(ErpIdArg erpIdArg, String dataCenterId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(erpIdArg.getTenantId(), dataCenterId);
        ErpChannelEnum channel = connectInfo.getChannel();
        Boolean useNumber = false;
        if (ErpChannelEnum.ERP_K3CLOUD.equals(channel)) {
            K3DataConverter converter = k3DataManager.buildConverter(erpIdArg.getTenantId(), dataCenterId, erpIdArg.getObjAPIName());
            //根据id字段的viewCode判断是否使用id查看
            useNumber = !StringUtils.equalsIgnoreCase(converter.getIdFieldExtend().getViewCode(), "Id");

        }
        return erpTempDataDao.getErpObjDataFromMongo(erpIdArg, dataCenterId, useNumber);
    }
    public Result<Document> getDocumentByNumber(ErpIdArg erpIdArg, String dataCenterId) {
        return erpTempDataDao.getErpObjDataFromMongo(erpIdArg, dataCenterId, true);
    }

    //获取需要轮询的数据
    public Result<ListErpTempResult> listErpObjDataFromMongo(QueryTempTimeFilterArg timeFilterArg, String dataCenterId) {
        List<Document> docs = erpTempDataDao.listErpObjDataFromMongo(timeFilterArg, dataCenterId);
        if (CollectionUtils.isEmpty(docs)) {
            ListErpTempResult retData = new ListErpTempResult();
            List<SyncDataContextEvent> erpObjDataResultList = new ArrayList<>();
            retData.setErpObjDataResultList(erpObjDataResultList);
            return Result.newSuccess(retData);//返回空ListErpObjDataResult
        }

        //有数据时
        boolean complete = docs.size() < timeFilterArg.getLimit();
        List<SyncDataContextEvent> dataList = Lists.newArrayList();
        //最后一条保留一下取数据
        for (Document document : docs) {
            if (ErpTempDataStatusEnum.STATUS_NOT_READY.getStatus().equals(document.getInteger("status"))) {
                //未准备好的数据不返回
                continue;
            }
            StandardData standardData = JSONObject.parseObject(document.getString("data_body"), StandardData.class);
            if (ObjectUtils.isEmpty(standardData)) {
                continue;
            }
            ObjectId id = document.getObjectId("_id");
            standardData.getMasterFieldVal().put("mongo_id", id.toString());//把mongo数据库id赋值给主对象mongo_id字段
            standardData.setSyncLogId(document.getString("sync_log_id"));
            standardData.setDataReceiveType(document.getInteger("data_receive_type"));
            Long version = document.getDate("new_last_sync_time") == null ? System.currentTimeMillis() : document.getDate("new_last_sync_time").getTime();
            standardData.setDataVersion(version);

            //转换格式
            SyncDataContextEvent contextEvent = FormatConvertUtil.stdErp2Crm(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName(), standardData);
            contextEvent.setLocale(document.getString("locale"));
            contextEvent.setPriority(document.getInteger("priority"));
            dataList.add(contextEvent);
        }
        ListErpTempResult tempResult = new ListErpTempResult();
        tempResult.setErpObjDataResultList(dataList);
        //填充最后一条数据信息
        Document lastDoc = docs.get(docs.size() - 1);
        String erpTempId = lastDoc.getObjectId("_id").toString();
        Long lastSyncTime = BsonTimeUtil.bsonTime2Long(lastDoc.get("new_last_sync_time"));
        tempResult.setMaxErpTempId(erpTempId);
        //这个会用来判断是否存在数据
        tempResult.setMaxLastSyncTime(lastSyncTime);
        tempResult.setComplete(complete);
        return Result.newSuccess(tempResult);
    }

    private void saveErpTempDataListNoTrigger(String tenantId, List<ErpTempData> entityList, Boolean updateByNum) {
        if (entityList.isEmpty()){
            return;
        }
        erpTempDataDao.batchUpsertErpTempData(tenantId, entityList, updateByNum);
        // 保存,规定时间内没有erp数据变更就发通知
        checkProductErpDataMonitor.erpDateProduct(tenantId, entityList);
    }

    public ErpTempData buildErpTempData(String tenantId, String dataCenterId, String objApiName, Integer operationType, String dataId, String number,
                                        String dataBody, Integer status, String remark, Long lastSyncTime, Integer dataReceiverType, String locale,Integer allObjDataCount) {
        ErpTempData entity = new ErpTempData();
        entity.setTraceId(TraceContext.get().getTraceId());
        Set<String> taskNum = Sets.newHashSet(CommonConstants.PLOY_AUTO_TASK_KEY);
        if (ObjectUtils.isNotEmpty(TaskUtil.getTaskNumInfo())) {
            taskNum = Sets.newHashSet(TaskUtil.getTaskNumInfo().getTaskNum());
            //设置数据优先级
            entity.setPriority(TaskUtil.getTaskNumInfo().getPriority());
        }
        entity.setTaskNum(taskNum);
        entity.setTenantId(tenantId);
        entity.setDcId(dataCenterId);
        entity.setObjApiName(objApiName);
        //操作类型（新增、更新、作废）
        entity.setOperationType(operationType);
        entity.setDataId(dataId);
        entity.setDataNumber(number);
        entity.setDataBody(dataBody);
        entity.setStatus(status);
        entity.setRemark(remark);
        entity.setLastSyncTime(lastSyncTime);
        entity.setDataReceiveType(dataReceiverType);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(entity.getCreateTime());
        entity.setLocale(locale);
        entity.setAllObjCount(allObjDataCount);
        if (Objects.equals(entity.getDataReceiveType(), DataReceiveTypeEnum.AUTO_POLLING_ERP.getType()) ||
                Objects.equals(entity.getDataReceiveType(), DataReceiveTypeEnum.OUTSIDE_PUSH_DATA.getType())) {
            entity.setLastPollingTime(entity.getCreateTime());
        }
        entity.setSyncLogId(LogIdUtil.get());
        log.debug("buildErpTempData tempdata:{},logId:{}", entity, LogIdUtil.get());
        return entity;
    }

    //保存单条查询的数据入库
    public String singleInsertErpData(ErpIdArg erpIdArg, String dataCenterId, Result<StandardData> result, IdFieldKey idFieldKey) {
        if (idFieldKey == null) {
            return null;
        }
        //入库
        boolean updateByNum = idFieldKey.isUpdateByNum();
        boolean useFakeIdFieldKey = false;
        String k3SpecialNumKey = null;
        SpecialBusiness specialBusiness = null;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(erpIdArg.getTenantId(), dataCenterId);
        if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
            //k3特殊获取数据接口，使用虚拟对象的id字段
            specialBusiness = specialBusinessFactory.getHandlerByName(erpIdArg.getObjAPIName());
            if (specialBusiness.needSpecialGetAndQuery(erpIdArg.getDataId())) {
                switch (erpIdArg.getObjAPIName()) {
                    case K3CloudForm.STK_Inventory:
                        useFakeIdFieldKey = true;
                        k3SpecialNumKey = "stockComId";
                        break;//库存
                    case K3CloudForm.SAL_SC_CUSTMAT:
                        k3SpecialNumKey = "FID";
                        break;//可销控制-客户物料
                }
            }
        }
        //前置判断来源数据类型type
        Integer dataType=ObjectUtils.isNotEmpty(TaskUtil.getTaskNumInfo())?DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType():DataReceiveTypeEnum.AUTO_POLLING_ERP.getType();

        ErpTempData erpTempData;
        if (result != null && result.isSuccess() && result.getData() != null && result.getData().getMasterFieldVal() != null) {
            StandardData data = result.getData();
            String erpId;
            String erpNum;
            if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                erpId = data.getMasterFieldVal().getString("erp_id");
                erpNum = data.getMasterFieldVal().getString("erp_num");
                if (useFakeIdFieldKey) {
                    erpId = data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey());
                    erpNum = erpId;
                }
                if (StringUtils.isBlank(erpNum) && StringUtils.isNotBlank(k3SpecialNumKey)) {
                    erpNum = data.getMasterFieldVal().getString(k3SpecialNumKey);
                }
                if (StringUtils.isBlank(erpId)) {
                    erpId = data.getMasterFieldVal().getString(idFieldKey.getIdFieldKey());
                }
                if (StringUtils.isBlank(erpNum)) {
                    erpNum = data.getMasterFieldVal().getString(idFieldKey.getNumFieldKey());
                }
                if (StringUtils.isBlank(erpNum)) {
                    erpNum = erpId;
                }
                if (K3CloudForm.BD_STOCK.equals(erpIdArg.getObjAPIName()) && specialBusiness != null
                        && specialBusiness.needSpecialGetAndQuery(data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey()))) {//仓库特殊逻辑,包含仓位
                    erpId = data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey());
                    erpNum = data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey());
                }
                log.info("ErpTempDataManager.singleInsertErpData,epr_id={},erp_num={},k3SpecialNumKey={}", erpId, erpNum, k3SpecialNumKey);
            } else {
                erpId = data.getMasterFieldVal().getString(idFieldKey.getIdFieldKey());
                erpNum = data.getMasterFieldVal().getString(idFieldKey.getNumFieldKey());
            }
            //进临时库的时候，组装主对象的复合id。最高优先级，最后处理，因为前面的条件太复杂了，好难写if-else
            if (idFieldKey.getCompositeIdExtend() != null && idFieldKey.getCompositeIdExtend().isComposite()) {
                erpId = idFieldKey.getCompositeIdExtend().composId(data.getMasterFieldVal());
                data.getMasterFieldVal().put(idFieldKey.getFakeIdFieldKey(), erpId);
            }
            if(ObjectUtils.isEmpty(data.getDataReceiveType())){
                //设置数据来源
                data.setDataReceiveType(dataType);
            }
            erpTempData = buildErpTempData(erpIdArg.getTenantId(), dataCenterId, erpIdArg.getObjAPIName(), null, erpId, erpNum, JSONObject.toJSONString(result.getData()),
                    ErpTempDataStatusEnum.STATUS_IS_READY.getStatus(), i18NStringManager.getByEi(I18NStringEnum.s640, erpIdArg.getTenantId()), null, data.getDataReceiveType(), null, result.getData().countAllObjData());
        } else {
            erpTempData = buildErpTempData(erpIdArg.getTenantId(), dataCenterId, erpIdArg.getObjAPIName(), null, erpIdArg.getDataId(), erpIdArg.getDataId(),
                    null, ErpTempDataStatusEnum.STATUS_NOT_READY.getStatus(), result == null ? i18NStringManager.getByEi(I18NStringEnum.s641, erpIdArg.getTenantId()) : result.getErrMsg(), null, dataType, null, 1);
        }
        //单条的，lastSyncTime不更新，不触发轮询临时库
        saveErpTempDataListNoTrigger(erpIdArg.getTenantId(), Lists.newArrayList(erpTempData), updateByNum);
        Document oldDoc;
        if (updateByNum) {
            oldDoc = erpTempDataDao.getErpObjDataByNum(erpTempData.getTenantId(), dataCenterId, erpTempData.getObjApiName(), erpTempData.getDataNumber());
        } else {
            oldDoc = erpTempDataDao.getErpObjDataById(erpTempData.getTenantId(), dataCenterId, erpTempData.getObjApiName(), erpTempData.getDataId());
        }
        if (oldDoc != null) {//返回mongo数据库的id
            return oldDoc.getObjectId("_id").toString();
        }
        return null;
    }

    /**
     * 批量插入或更新临时库数据
     */
    public void batchUpsertErpTempData(String tenantId, String dataCenterId, String realObjApiName, Integer operationType, StandardListData stdListData, @NotNull IdFieldKey idFieldKey, boolean needUpdateLastSyncTime, String locale) {
        if (stdListData == null || CollUtil.isEmpty(stdListData.getDataList())) {
            return;
        }
        boolean updateByNum = idFieldKey.isUpdateByNum();
        //构建临时库数据，全部是ready状态的
        List<ErpTempData> erpTempDataList = buildReadyErpTempDataList(tenantId, dataCenterId, realObjApiName, operationType, stdListData, idFieldKey, needUpdateLastSyncTime,locale);
        //是否有需要同步的数据
        boolean hasNeedSyncData = false;
        //后续有批量查库和upsert操作，分批处理
        List<List<ErpTempData>> split = CollUtil.split(erpTempDataList, 200);
        for (List<ErpTempData> splitErpTempDataList : split) {
            List<ErpTempData> needSyncTempDataList = new ArrayList<>();
            Map<String, Integer> needSyncDataKeyCount = new HashMap<>();
            //校验md5。 原来逐条查询mongo校验MD5改为批量查询。外部系统来的数据，作废不校验md5。 因外部系统不支持传删除类型的数据到集成平台，所以这里只判断作废类型。
            if (operationType == null || (operationType != EventTypeEnum.INVALID.getType())) {
                try {
                    checkMd5(tenantId, dataCenterId, splitErpTempDataList, updateByNum, needSyncTempDataList, needSyncDataKeyCount);
                } catch (Exception e) {
                    log.error("batchUpsertErpTempData compare md5 failed ei={} objapi={}, exception, ", tenantId, realObjApiName, e);
                }
            }
            //是否存在需要同步的数据
            hasNeedSyncData = hasNeedSyncData || !needSyncTempDataList.isEmpty();
            //入库
            saveErpTempDataListNoTrigger(tenantId, splitErpTempDataList, updateByNum);
            //节点监控
            monitorReportManager.sendEnterTempDataMsg(tenantId, dataCenterId, realObjApiName, idFieldKey.getSplitObjApiName(), needSyncTempDataList, System.currentTimeMillis(), 1, "入库", updateByNum);
            //数据流水监控-获取有效数据（仅统计需要同步的数据）
            NodeHelper.batchAddStatusRecord(NodeDataStatus.RECEIVE_VALID, needSyncDataKeyCount);
        }
        //needUpdateLastSyncTime为false的时候不需要触发轮询
        boolean needTriggerPollingTemp = needUpdateLastSyncTime && hasNeedSyncData;
        if (needTriggerPollingTemp) {
            //触发轮询
            Set<String> allSplitObjApiNames = erpObjManager.getRealMainSplitObjApiNamesMap(tenantId, dataCenterId).get(realObjApiName);
            TriggerPollingData triggerPollingData = new TriggerPollingData();
            triggerPollingData.setTenantId(tenantId);
            triggerPollingData.setObjApiName(allSplitObjApiNames);
            triggerPollingData.setTriggerTime(System.currentTimeMillis());
            triggerPollingMongoManager.triggerPolling(triggerPollingData);
        }
    }

    private void checkMd5(String tenantId, String dataCenterId, List<ErpTempData> splitErpTempDataList, boolean updateByNum, List<ErpTempData> needSyncTempDataList, Map<String, Integer> needSyncDataKey) {
        String dataKeyField = updateByNum ? "data_number" : "data_id";
        List<String> dataKeyList = splitErpTempDataList.stream().map(updateByNum ? ErpTempData::getDataNumber : ErpTempData::getDataId).collect(Collectors.toList());
        List<String> md5FieldAPIName = Lists.newArrayList("tenant_id", "dc_id", "obj_api_name", "data_id", "data_number", "data_md5");
        List<Document> documents = erpTempDataDao.batchGetErpField(tenantId, dataCenterId, splitErpTempDataList.get(0).getObjApiName(),
                dataKeyField, dataKeyList, md5FieldAPIName);
        for (ErpTempData data : splitErpTempDataList) {
            String dataKey = updateByNum ? data.getDataNumber() : data.getDataId();
            Optional<Document> first = documents.stream()
                    .filter(m -> m.getString(dataKeyField).equals(dataKey))
                    .findFirst();
            if (first.isPresent()) {
                //临时库中存在旧数据，做Md5的验证
                Document m = first.get();
                if (data.getDataMd5() != null && data.getDataMd5().equals(m.getString("data_md5"))) {
                    //数据md5一致，清空body，不更新body和lastSyncTime
                    data.setLastSyncTime(null);
                    data.setDataBody(null);
                    data.setPriority(null);
                    data.setAllObjCount(null);
                    data.setRemark(i18NStringManager.getByEi(I18NStringEnum.s643, tenantId) + DateTime.of(System.currentTimeMillis()));
                    continue;
                }
            }

            //没有因为md5一致退出时，都会走到这一步
            if (ErpTempDataStatusEnum.STATUS_IS_READY.getStatus().equals(data.getStatus())) {
                //仅处理已准备好的数据,其实上面只会构建已准备好的数据。
                //已准备好的数据，不一定触发轮询，如果needUpdateLastSyncTime为false，是直接同步的，不会触发轮询
                needSyncTempDataList.add(data);
                needSyncDataKey.put(dataKey, data.getAllObjCount());
            }
        }
    }

    /**
     * 构建准备好的数据列表，这构建的数据，都一定是已准备好的
     */
    private @NotNull List<ErpTempData> buildReadyErpTempDataList(String tenantId, String dataCenterId, String realObjApiName, Integer operationType, StandardListData stdListData, @NotNull IdFieldKey idFieldKey, boolean needUpdateLastSyncTime,String locale) {
        boolean useFakeIdFieldKey = false;
        String k3SpecialNumKey = null;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        SpecialBusiness specialBusiness = null;
        if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
            //k3特殊获取数据接口，使用虚拟对象的id字段
            specialBusiness = specialBusinessFactory.getHandlerByName(realObjApiName);
            if (specialBusiness.needSpecialGetAndQuery(null)) {
                switch (realObjApiName) {
                    case K3CloudForm.STK_Inventory:
                        useFakeIdFieldKey = true;
                        k3SpecialNumKey = "stockComId";
                        break;//库存
                    case K3CloudForm.SAL_SC_CUSTMAT:
                        k3SpecialNumKey = "FID";
                        break;//可销控制-客户物料
                }
            }
        }
        //入库
        List<ErpTempData> dataList = Lists.newArrayList();
        for (StandardData data : stdListData.getDataList()) {
            if (data == null || data.getMasterFieldVal() == null) {
                throw new ErpSyncDataException("接口返回格式不正确", I18NStringEnum.s253.getI18nKey(), tenantId);
            }
            ErpTempData erpTempData;
            String erpId;
            String erpNum;
            if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                erpId = data.getMasterFieldVal().getString("erp_id");
                erpNum = data.getMasterFieldVal().getString("erp_num");
                log.debug("ErpTempDataManager.batchUpsertErpTempData,first,epr_id={},erp_num={},k3SpecialNumKey={}", erpId, erpNum, k3SpecialNumKey);
                if (useFakeIdFieldKey) {
                    erpId = data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey());
                    erpNum = erpId;
                }
                if (StringUtils.isBlank(erpNum) && StringUtils.isNotBlank(k3SpecialNumKey)) {
                    erpNum = data.getMasterFieldVal().getString(k3SpecialNumKey);
                    log.info("ErpTempDataManager.batchUpsertErpTempData,k3SpecialNumKey={},erp_num={},", k3SpecialNumKey, erpNum);
                }
                if (StringUtils.isBlank(erpId)) {
                    erpId = data.getMasterFieldVal().getString(idFieldKey.getIdFieldKey());
                }
                if (StringUtils.isBlank(erpNum)) {
                    erpNum = data.getMasterFieldVal().getString(idFieldKey.getNumFieldKey());
                }
                if (StringUtils.isBlank(erpNum)) {
                    erpNum = erpId;
                }
                if (K3CloudForm.BD_STOCK.equals(realObjApiName) && specialBusiness != null
                        && specialBusiness.needSpecialGetAndQuery(data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey()))) {//仓库特殊逻辑,包含仓位
                    erpId = data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey());
                    erpNum = data.getMasterFieldVal().getString(idFieldKey.getFakeIdFieldKey());
                }
            } else {
                erpId = data.getMasterFieldVal().getString(idFieldKey.getIdFieldKey());
                erpNum = data.getMasterFieldVal().getString(idFieldKey.getNumFieldKey());
            }
            //进临时库的时候，组装主对象的复合id。最高优先级，最后处理，因为前面的条件太复杂了，好难写if-else
            if (idFieldKey.getCompositeIdExtend() != null && idFieldKey.getCompositeIdExtend().isComposite()) {
                erpId = idFieldKey.getCompositeIdExtend().composId(data.getMasterFieldVal());
                data.getMasterFieldVal().put(idFieldKey.getFakeIdFieldKey(), erpId);
            }
            log.debug("ErpTempDataManager.batchUpsertErpTempData,end,epr_id={},erp_num={}", erpId, erpNum);
            if (StringUtils.isBlank(erpId) || StringUtils.isBlank(erpNum)) {//获取erpId或者erpNum失败，不入库
                log.warn("batchUpsertErpTempData get id or num failed data={} idFieldKey={}", data, idFieldKey);
            } else {
                if (ObjectUtils.isEmpty(data.getDataReceiveType())) {
                    //设置数据来源类型。
                    if (ObjectUtils.isNotEmpty(TaskUtil.getTaskNumInfo())) {
                        data.setDataReceiveType(DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType());
                    } else {
                        data.setDataReceiveType(DataReceiveTypeEnum.AUTO_POLLING_ERP.getType());
                    }
                }
                Long lastSyncTime = null;
                if (needUpdateLastSyncTime) {
                    lastSyncTime = System.currentTimeMillis();
                }
                erpTempData = buildErpTempData(tenantId, dataCenterId, realObjApiName, operationType, erpId, erpNum, JacksonUtil.toJson(data),
                        ErpTempDataStatusEnum.STATUS_IS_READY.getStatus(), i18NStringManager.getByEi(I18NStringEnum.s642, tenantId), lastSyncTime, data.getDataReceiveType(), locale, data.countAllObjData());
                //MD5校验只取master与detail
                String dataBody = JacksonUtil.toJson(data.getMasterFieldVal()) + JacksonUtil.toJson(data.getDetailFieldVals());
                erpTempData.setDataMd5(ErpTempDataHelper.md5(erpTempData.getDataId(), dataBody));
                dataList.add(erpTempData);
                data.getMasterFieldVal().putIfAbsent("erp_id", erpId);//设置值，后续会用到
            }
        }
        return dataList;
    }

    public Result<List<ErpTempData>> listErpTempData(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                                     Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType, Integer pageNum, Integer pageSize) {
        Result<List<Document>> documentList = erpTempDataDao.listErpObjData(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, searchType, pageNum, pageSize);
        if (!documentList.isSuccess() || CollectionUtils.isEmpty(documentList.getData())) {
            return Result.copy(documentList);
        }
        List<ErpTempData> erpTempDataList = Lists.newArrayList();
        for (Document document : documentList.getData()) {
            ErpTempData erpTempData = ErpTempDataDao.buildErpTempDataFromDoc(document);
            erpTempDataList.add(erpTempData);
        }
        return Result.newSuccess(erpTempDataList);
    }

    public int countErpTempData(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime,
                                Integer sourceDataStatus, Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType,
                                Integer pageNum, Integer pageSize) {
        return (int) erpTempDataDao.countErpObjData(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, searchType, pageNum, pageSize);
    }
    public int countErpTempDataLimit1000(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime,
                                Integer sourceDataStatus, Integer tempDataSyncStatus, String taskNum, String idOrNum, SearchTypeEnum searchType,
                                Integer pageNum, Integer pageSize) {
        return erpTempDataDao.countErpTempDataLimit1000(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime,
                sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum, searchType, pageNum, pageSize);
    }

    public Result<ErpTempData> getErpTempData(String tenantId, String id) {

        Result<Document> document = erpTempDataDao.getErpObjData(tenantId, id);
        if (!document.isSuccess() || document.getData() == null) {
            return Result.copy(document);
        }
        ErpTempData erpTempData = ErpTempDataDao.buildErpTempDataFromDoc(document.getData());
        return Result.newSuccess(erpTempData);
    }

    public Result<Long> removeErpTempDataByMongoId(String tenantId, ObjectId mongoId) {
        return erpTempDataDao.removeErpTempDataByMongoId(tenantId, mongoId);
    }

    public Result<Long> removeErpObjData(String tenantId, String dataCenterId, String erpRealObjectApiName, String ployDetailId, Long startTime, Long endTime, Integer sourceDataStatus,
                                         Integer tempDataSyncStatus, String taskNum, String idOrNum) {
        return erpTempDataDao.removeErpObjData(tenantId, dataCenterId, erpRealObjectApiName, ployDetailId, startTime, endTime, sourceDataStatus, tempDataSyncStatus, taskNum, idOrNum);
    }

    public Result<Long> removeErpObjDataByIdLists(String tenantId, List<ObjectId> idList) {
        return erpTempDataDao.removeErpObjDataByIdLists(tenantId, idList);
    }

    public Long getTenantLastSyncTime(String tenantId) {
        return erpTempDataDao.getTenantLastSyncTime(tenantId);
    }

    public long refreshTimeByIds(String tenantId,String dcId,String realObjApiName,Collection<String> ids){
        UpdateResult updateResult = erpTempDataDao.batchUpdateLastSyncTimeByDataIds(tenantId, dcId, realObjApiName, ids);
        long modifiedCount = updateResult.getModifiedCount();
        return modifiedCount;
    }
}
