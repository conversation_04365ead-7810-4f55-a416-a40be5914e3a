package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3ElementTypeEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ErpObjectDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ErpObjectDescribe.SplitObjDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult.BusinessInfo.EntrysBean;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult.BusinessInfo.NameBean;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult.EntityType;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SelectExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 15:28 2021/10/27
 * @Desc:
 */
@Component
@Slf4j
public class MetaDataInfoManager {
    @Autowired
    private I18NStringManager i18NStringManager;


    private static final String D_NAME = ".Name";
    private static final String D_FNAME = ".FName";
    private static final String D_CODE = ".CODE";
    private static final String D_FCODE = ".FCODE";
    private static final String D_FDATAVALUE = ".FDataValue";
    private static final String D_NUMBER = ".Number";
    private static final String D_FNUMBER = ".FNumber";
    private static final String D_ID = ".Id";
    private static final String _ID = "_Id";
    private static final String D_FID = ".FID";
    private static final String D_FUSERID = ".FUserId";
    private static final String D_BILL_HEAD = ".BillHead";
    private static final String SUFFIX_ID = "(ID)";
    private static Set<String> selectOneErpObj = Sets.newHashSet("QM_SampleScheme", "CN_BANKACNT", "QM_QCScheme", "SAL_ReceiveBillEntry",
            "BD_StockStatus", "PLN_SUPPLYMANAGER", "BD_SETTLETYPE", "BD_Currency", "CMK_LS_Branch", "BOS_ObjectType", "BOS_BillType", "BD_LotCodeRule",
            "BOS_ASSISTANTDATA", "WB_BankDetail", "CMK_HW_MaterialSource", "CMK_HuaWei_SpecialAttribute", "BD_MATERIALCATEGORY", "CMK_HW_SPUINFO",
            "CMK_LS_CashierManage", "PLN_MANUFACTUREPOLICY", "IOS_TransferBizType", "WB_Distrit", "CN_CASHACCOUNT", "BD_UNITGROUP",
            "WB_BankType", "BD_PLANNER", "REQ_WRITEOFFSCHEME", "BD_WAREHOUSEWORKERS", "PLN_PRIORITYCALCULATION", "BD_TimeZones",
            "PLN_PLANAREA", "BD_InvPty", "BOS_FLEXVALUE", "BD_AuxPty_Select", "CMK_VIP_CardLevel", "BD_Inspector", "CRM_SalePhase", "HR_ORG_HRPOST",
            "WB_City", "BD_BUYER", "V_CN_BILLRECEIVABLE", "ASC_ScanPoint", "WB_Province", "IV_GTTAXCODE", "BD_KD100LogisticsCom",
            "CMK_LS_CashierGrp", "CMK_BD_Brand", "CMK_BD_ShoppeInfo", "ENG_PRODUCTMODEL", "BD_RateType", "ENG_Route", "BD_SALCLAUSE",
            "BD_RecCondition", "HR_HM_Card", "BD_BarCodeRule", "ORG_OrganizationsForSelect", "CN_RECPAYPURPOSE", "BD_Expense",
            "CN_INNERACCOUNT", "BD_SAL_DiscountList", "BD_OperatorGroup", "BD_ContactObject", "BD_TaxRate", "Org_AccountSystem", "V_CN_BILLPAYABLE",
            "SEC_DynamicPassword", "CMK_HuaWei_SpecAttrCategory", "BD_ACCTPOLICY", "CMK_GoodsType", "Sal_CustMatMappingView");

    /**
     * 获取K3对象描述
     * 请改动代码后跑一下单元测试
     * 增加了耗时控制
     *
     * @param apiClient
     * @param arg
     * @param ignoreCustomField
     * @return
     */
    public Result<ErpObjectDescribe> getErpObjectDescribe(K3CloudApiClient apiClient,
                                                          QueryBusinessInfoArg arg,
                                                          boolean ignoreCustomField,
                                                          String lang) {
        Result<QueryBusinessInfoResult> queryBusinessInfoResultResult = queryBusinessInfoResult(apiClient, arg);
        if (!queryBusinessInfoResultResult.isSuccess()) {
            return Result.copy(queryBusinessInfoResultResult);
        }
        ErpObjectDescribe erpObjectDescribe = this.analysisK3MetaDataInfo(apiClient, queryBusinessInfoResultResult.getData(), ignoreCustomField,lang);
        if (erpObjectDescribe == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), I18NStringEnum.s44);
        }
        return Result.newSuccess(erpObjectDescribe);
    }

    public Result<QueryBusinessInfoResult> queryBusinessInfoResult(K3CloudApiClient apiClient, QueryBusinessInfoArg arg) {
        return apiClient.queryBusinessInfo(arg);
    }

    public QueryBusinessInfoResult.BusinessInfo queryBusinessInfo(K3CloudApiClient apiClient, QueryBusinessInfoArg arg) {
        Result<QueryBusinessInfoResult> result = queryBusinessInfoResult(apiClient, arg);
        if (result != null && result.getData() != null && result.getData().getResult() != null
                && result.getData().getResult().getNeedReturnData() != null) {
            return result.getData().getResult().getNeedReturnData();
        }
        return null;
    }

    /**
     * 解析K3元数据信息
     * 返回结果无id、channel、tenantid、createTime、updateTime、deleteStatus
     *
     * @return
     */
    public ErpObjectDescribe analysisK3MetaDataInfo(K3CloudApiClient apiClient,
                                                    QueryBusinessInfoResult queryBusinessInfoResult,
                                                    boolean ignoreCustomField,
                                                    String lang) {
        QueryBusinessInfoResult.BusinessInfo businessInfo = queryBusinessInfoResult.getResult().getNeedReturnData();
        if (businessInfo == null) {
            return null;
        }
        MetaDataHelper helper = MetaDataHelper.create(apiClient);
        String realObjApiName = businessInfo.getId();
        String realName = getI18NName(apiClient.getTenantId(),businessInfo.getName(),lang);
        ErpObjectDescribe erpObjectDescribe = new ErpObjectDescribe();
        ErpObjectEntity realObj = new ErpObjectEntity();
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = new LinkedHashMap<>();
        erpObjectDescribe.setRealObj(realObj);
        erpObjectDescribe.setSplitObjDescribes(splitObjDescribes);
        //真实对象信息
        realObj.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        realObj.setErpObjectApiName(realObjApiName);
        realObj.setErpObjectName(realName);
        realObj.setErpObjectExtendValue("DETAIL2DETAIL_SPLIT");
        //拆分对象信息
        List<EntrysBean> entrys = businessInfo.getEntrys();
        //按顺序处理
        entrys.sort(Comparator.comparing(v -> EntityType.valueOf(v.getEntityType())));
        Map<String, EntrysBean> entryMap = entrys.stream().collect(Collectors.toMap(v -> v.getKey(), v -> v));
        if (entrys.isEmpty()) {
            throw new ErpSyncDataException(I18NStringEnum.s161,apiClient.getTenantId());
        }
        for (EntrysBean entry : entrys) {
            SplitObjDescribe splitObjDescribe = new SplitObjDescribe();
            String entryApiName = entry.getEntryName();
            EntityType entityType = EntityType.valueOf(entry.getEntityType());
            String objName = getI18NName(apiClient.getTenantId(),entry.getName(),lang);
            String splitApiName;
            //拆分对象
            ErpObjectEntity splitObj = new ErpObjectEntity();
            //对象关系
            ErpObjectRelationshipEntity relation = new ErpObjectRelationshipEntity();
            splitObjDescribe.setSplitObj(splitObj);
            splitObjDescribe.setObjRelation(relation);
            String mainSplitApiName = realObjApiName + D_BILL_HEAD;
            switch (entityType) {
                case HeadEntity:
                    //主表,apiName加上.BillHead
                    splitApiName = mainSplitApiName;
                    splitObjDescribe.setMain(true);
                    splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
                    splitObj.setErpObjectApiName(mainSplitApiName);
                    splitObj.setErpObjectName(realName);
                    splitObj.setErpObjectExtendValue("");
                    relation.setSplitSeq(1);
                    relation.setErpRealObjectApiname(realObjApiName);
                    relation.setErpSplitObjectApiname(splitApiName);
                    relation.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
                    splitObjDescribes.put(splitApiName, splitObjDescribe);
                    //主对象增加id字段
                    fillIdField(realObjApiName, splitObjDescribe, entry, businessInfo);
                    break;
                case EntryEntity:
                    //多行从表
                    splitObjDescribe.setParentObjApiName(mainSplitApiName);
                    splitApiName = realObjApiName + "." + entryApiName;
                    splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
                    splitObj.setErpObjectApiName(splitApiName);
                    splitObj.setErpObjectName(realName + "-" + objName);
                    splitObj.setErpObjectExtendValue(splitApiName);
                    relation.setSplitSeq(1);
                    relation.setErpRealObjectApiname(realObjApiName);
                    relation.setErpSplitObjectApiname(splitApiName);
                    relation.setSplitType(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT);
                    splitObjDescribes.put(splitApiName, splitObjDescribe);
                    //主对象增加明细字段
                    fillDetailExtend(realObjApiName, mainSplitApiName, splitObjDescribes.get(mainSplitApiName), entry, splitObjDescribes.size(),lang,apiClient.getTenantId());
                    //增加明细id字段和查找关联主对象字段
                    fillDetailIdField(mainSplitApiName, splitApiName, splitObjDescribe, entry,lang,apiClient.getTenantId());
                    break;
                case SubHeadEntity:
                    //表头子结构，单行
                    splitApiName = mainSplitApiName;
                    splitObjDescribe = splitObjDescribes.get(mainSplitApiName);
                    break;
                case SubEntryEntity:
                    //从表子结构
                    splitApiName = realObjApiName + "." + entryApiName;
                    try {
                        EntrysBean parentEntry = entryMap.get(entry.getParentKey());
                        String parentEntryApiName = realObjApiName + "." + parentEntry.getEntryName();
                        splitObjDescribe.setParentObjApiName(parentEntryApiName);
                        SplitObjDescribe parentEntryDesc = splitObjDescribes.get(parentEntryApiName);
                        SplitObjDescribe mainDesc = splitObjDescribes.get(mainSplitApiName);
                        splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
                        splitObj.setErpObjectApiName(splitApiName);
                        splitObj.setErpObjectName(realName + "-" + objName);
                        //记录管理的从表
                        SubDetailExtend subDetailExtend = new SubDetailExtend()
                                .setDetailObjectApiName(parentEntryApiName)
                                .setRealObjectApiName(splitApiName);
                        splitObj.setErpObjectExtendValue(subDetailExtend.toJson());
                        relation.setSplitSeq(1);
                        relation.setErpRealObjectApiname(realObjApiName);
                        relation.setErpSplitObjectApiname(splitApiName);
                        relation.setSplitType(ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL);
                        //主对象增加明细字段
                        fillDetailExtend(realObjApiName, mainSplitApiName, mainDesc, entry, splitObjDescribes.size(),lang,apiClient.getTenantId());
                        //增加表头id,表头编号,明细id，子明细id
                        fillSubDetailIdField(apiClient.getTenantId(),splitApiName, splitObjDescribe, entry, parentEntryDesc, mainDesc,lang);
                        splitObjDescribes.put(splitApiName, splitObjDescribe);
                    } catch (Exception e) {
                        log.info("init subEntry error,{} ", entryApiName, e);
                    }
                    break;
                default:
                    throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s162.getI18nKey(),
                            apiClient.getTenantId(),
                            String.format(I18NStringEnum.s162.getI18nValue(),entityType),
                            Lists.newArrayList(entityType+"")),
                            null,
                            null);
            }
            fillFields(apiClient.getTenantId(), helper, splitApiName, realObjApiName, entry, splitObjDescribe, ignoreCustomField,lang);
        }
        //再加工一些特殊处理
        reProcess(erpObjectDescribe,lang,apiClient.getTenantId());
        return erpObjectDescribe;
    }


    /**
     * 辅助类，储存一些信息减少调用
     */
    private static class MetaDataHelper {
        K3CloudApiClient apiClient;
        Map<String, Result<List<K3Model>>> formOptionCache = new HashMap<>();
        Map<String, QueryBusinessInfoResult.BusinessInfo> businessInfoCache = new HashMap<>();

        private static MetaDataHelper create(K3CloudApiClient apiClient) {
            MetaDataHelper helper = new MetaDataHelper();
            helper.apiClient = apiClient;
            return helper;
        }


        public QueryBusinessInfoResult.BusinessInfo queryBusinessInfo(QueryBusinessInfoArg arg) {
            QueryBusinessInfoResult.BusinessInfo businessInfo = businessInfoCache.computeIfAbsent(arg.getFormId(), v -> {
                Result<QueryBusinessInfoResult> result = apiClient.queryBusinessInfo(arg);
                if (result != null && result.getData() != null && result.getData().getResult() != null
                        && result.getData().getResult().getNeedReturnData() != null) {
                    return result.getData().getResult().getNeedReturnData();
                }
                return null;
            });
            return businessInfo;
        }


        private List<Option> getOptionList(List<String> referenceObjFields, String formId, Map<String, String> filters) {
            List<Option> allOptions = Lists.newArrayList();
            Map<String, String> queryFieldMap = Maps.newHashMap();
            if (referenceObjFields.contains("FName") && referenceObjFields.contains("FNumber")) {
                queryFieldMap.put("label", "FName");
                queryFieldMap.put("value", "FNumber");
            } else if (referenceObjFields.contains("FName") && referenceObjFields.contains("FCODE")) {
                queryFieldMap.put("label", "FName");
                queryFieldMap.put("value", "FCODE");
            } else if (referenceObjFields.contains("FDataValue") && referenceObjFields.contains("FNumber")) {
                queryFieldMap.put("label", "FDataValue");
                queryFieldMap.put("value", "FNumber");
            }

            QueryArg queryArg = new QueryArg();
            queryArg.setFormId(formId);
            queryArg.setFieldKeysByList(Lists.newArrayList(queryFieldMap.values()));
            for (String key : filters.keySet()) {
                queryArg.appendEqualFilter(key, filters.get(key));
            }
            queryArg.setLimit(500);//最多取500个
            String cacheKey = JSON.toJSONString(queryArg);
            Result<List<K3Model>> listResult = formOptionCache.computeIfAbsent(cacheKey, k -> apiClient.queryReturnMap(queryArg));
            Map<Object, Object> optionMap = Maps.newHashMap();
            if (listResult != null && CollectionUtils.isNotEmpty(listResult.getData())) {
                for (K3Model model : listResult.getData()) {
                    Object label = model.get(queryFieldMap.get("label"));
                    Object value = model.get(queryFieldMap.get("value"));
                    if (label != null && value != null && StringUtils.isNotBlank(label.toString()) && StringUtils.isNotBlank(value.toString())) {
                        optionMap.put(value, label);
                    }
                }
            }
            for (Object number : optionMap.keySet()) {
                Option option = new Option();
                option.setLabel(optionMap.get(number));
                option.setValue(number);
                allOptions.add(option);
            }

            return allOptions;
        }

    }

    /**
     * 针对某些对象的特殊处理
     *
     * @param erpObjectDescribe
     */
    private void reProcess(ErpObjectDescribe erpObjectDescribe,String lang, String tenantId) {
        String erpObjectApiName = erpObjectDescribe.getRealObj().getErpObjectApiName();
        switch (erpObjectApiName) {
            case K3CloudForm.SAL_SaleOrder:
                reProcessSaleOrder(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.BD_Customer:
                reProcessCustomer(erpObjectDescribe);
                break;
            case K3CloudForm.BD_MATERIAL:
                reProcessMaterial(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.IV_SALESIC:
                reProcessSalesic(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.IV_SALESOC:
                reProcessSaleSoc(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.STK_Inventory:
                reProcessInventory(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.AR_RECEIVEBILL:
                reProcessReceiveBill(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.AR_REFUNDBILL:
                reProcessRefundBill(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.SAL_OUTSTOCK:
                reProcessOutStock(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.SAL_RETURNSTOCK:
                reProcessReturnStock(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.BD_STOCK:
                reProcessStock(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.BOS_ASSISTANTDATA_DETAIL:
                reProcessAssistantData(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.BATCH_OBJ:
                reProcessBatchObj(erpObjectDescribe);
                break;
            case K3CloudForm.BD_CommonContact:
                reProcessCommonContract(erpObjectDescribe);
                break;
            case K3CloudForm.BD_SerialMainFile:
                reProcessSerialMainFile(erpObjectDescribe,lang,tenantId);
                break;
            case K3CloudForm.SAL_MATERIALGROUP:
                reProcessMaterialGroup(erpObjectDescribe);
                break;
            case K3CloudForm.SAL_SC_CUSTMAT:
                reProcessSalScCustMat(erpObjectDescribe,lang,tenantId);
                break;
            default:
                break;
        }
    }

    private void reProcessSalScCustMat(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        //增加两个虚拟对象
        SplitObjDescribe cust = buildAvailableAccountObj(lang,tenantId);
        SplitObjDescribe productObj = buildAvailableProductObj(lang,tenantId);
        erpObjectDescribe.getSplitObjDescribes().put("SAL_SC_CustMat.AvailableAccountObj",cust);
        erpObjectDescribe.getSplitObjDescribes().put("SAL_SC_CustMat.AvailableProductObj",productObj);
    }

    private SplitObjDescribe buildAvailableAccountObj(String lang, String tenantId) {
        SplitObjDescribe describe = new SplitObjDescribe();
        describe.setMain(false);
        describe.setParentObjApiName("SAL_SC_CustMat.BillHead");
        ErpObjectEntity splitObj = new ErpObjectEntity();
        String custApiName = "SAL_SC_CustMat.AvailableAccountObj";
        splitObj.setErpObjectApiName(custApiName);
        splitObj.setErpObjectName(i18NStringManager.getByEi(I18NStringEnum.s1175,tenantId));
        splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        splitObj.setErpObjectExtendValue(custApiName);
        describe.setSplitObj(splitObj);
        ErpObjectRelationshipEntity relation = new ErpObjectRelationshipEntity();
        relation.setSplitSeq(1);
        relation.setErpRealObjectApiname("SAL_SC_CustMat");
        relation.setErpSplitObjectApiname(custApiName);
        relation.setSplitType(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT);
        describe.setObjRelation(relation);
        addField(custApiName, "DetailId", i18NStringManager.get(I18NStringEnum.s1173,lang,tenantId), ErpFieldTypeEnum.id, null, describe);
        addField(custApiName, "fake_master_detail", i18NStringManager.get(I18NStringEnum.s1174,lang,tenantId), ErpFieldTypeEnum.master_detail, "SAL_SC_CustMat.BillHead", describe);
        addField(custApiName, "AvailableAccountObj", i18NStringManager.get(I18NStringEnum.s1123,lang,tenantId), ErpFieldTypeEnum.object_reference, "BD_Customer.BillHead", describe);
        return describe;
    }


    private SplitObjDescribe buildAvailableProductObj(String lang, String tenantId) {
        SplitObjDescribe describe = new SplitObjDescribe();
        describe.setMain(false);
        describe.setParentObjApiName("SAL_SC_CustMat.BillHead");
        ErpObjectEntity splitObj = new ErpObjectEntity();
        String custApiName = "SAL_SC_CustMat.AvailableProductObj";
        splitObj.setErpObjectApiName(custApiName);
        splitObj.setErpObjectName(i18NStringManager.getByEi(I18NStringEnum.s1176,tenantId));
        splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        splitObj.setErpObjectExtendValue(custApiName);
        describe.setSplitObj(splitObj);
        ErpObjectRelationshipEntity relation = new ErpObjectRelationshipEntity();
        relation.setSplitSeq(1);
        relation.setErpRealObjectApiname("SAL_SC_CustMat");
        relation.setErpSplitObjectApiname(custApiName);
        relation.setSplitType(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT);
        describe.setObjRelation(relation);
        addField(custApiName, "DetailId", i18NStringManager.getByEi(I18NStringEnum.s1173,tenantId), ErpFieldTypeEnum.id, null, describe);
        addField(custApiName, "fake_master_detail", i18NStringManager.getByEi(I18NStringEnum.s1174,tenantId), ErpFieldTypeEnum.master_detail, "SAL_SC_CustMat.BillHead", describe);
        addField(custApiName, "AvailableProductObj", i18NStringManager.getByEi(I18NStringEnum.s1110,tenantId), ErpFieldTypeEnum.object_reference, "BD_MATERIAL.BillHead", describe);
        return describe;
    }

    private static void addField(String objApiName, String apiName, String label, ErpFieldTypeEnum type,String extendValue, SplitObjDescribe desc) {
        ErpObjectFieldEntity field = new ErpObjectFieldEntity();
        field.setErpObjectApiName(objApiName);
        field.setFieldApiName(apiName);
        field.setFieldLabel(label);
        field.setRequired(false);
        field.setFieldDefineType(type);
        field.setFieldExtendValue(extendValue);
        desc.getFields().put(field.getFieldApiName(), field);
    }


    private static void reProcessMaterialGroup(ErpObjectDescribe erpObjectDescribe) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        //FParentId关联自身
        SplitObjDescribe splitObjDescribe = splitObjDescribes.get("SAL_MATERIALGROUP.BillHead");
        ErpObjectFieldEntity fParentId = splitObjDescribe.getFields().get("FParentId");
        fParentId.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        fParentId.setFieldExtendValue("SAL_MATERIALGROUP.BillHead");
    }

    /**
     * 对序列号字段进行特殊处理，增加 虚拟库存字段 和 仓库复合ID（仓库编号||仓位1编号.仓位2编号...) 两个预置字段，提高实施效率
     *
     * @param erpObjectDescribe
     */
    private void reProcessSerialMainFile(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BD_SerialMainFile.BillHead");
        //增加 虚拟库存字段 字段
        ErpObjectFieldEntity virtualStockIdEntity = new ErpObjectFieldEntity();
        virtualStockIdEntity.setErpObjectApiName("BD_SerialMainFile.BillHead");
        virtualStockIdEntity.setFieldApiName("virtual_stock_id");
        virtualStockIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1193,lang,tenantId));
        virtualStockIdEntity.setRequired(false);
        virtualStockIdEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        virtualStockIdEntity.setFieldExtendValue("STK_Inventory.BillHead");
        mainDesc.getFields().put(virtualStockIdEntity.getFieldApiName(), virtualStockIdEntity);

        //增加 仓库复合ID（仓库编号||仓位1编号.仓位2编号...) 字段
        ErpObjectFieldEntity warehouseComIdEntity = new ErpObjectFieldEntity();
        warehouseComIdEntity.setErpObjectApiName("BD_SerialMainFile.BillHead");
        warehouseComIdEntity.setFieldApiName("warehouseComId");
        warehouseComIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1194,lang,tenantId));
        warehouseComIdEntity.setRequired(false);
        warehouseComIdEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        warehouseComIdEntity.setFieldExtendValue("BD_STOCK.BillHead");
        mainDesc.getFields().put(warehouseComIdEntity.getFieldApiName(), warehouseComIdEntity);

        //增加 仓位（ID） 字段
        ErpObjectFieldEntity stockLocIdEntity = new ErpObjectFieldEntity();
        stockLocIdEntity.setErpObjectApiName("BD_SerialMainFile.BillHead");
        stockLocIdEntity.setFieldApiName("FSubHeadEntity.FStockLocId.Id");
        stockLocIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1195,lang,tenantId));
        stockLocIdEntity.setRequired(false);
        stockLocIdEntity.setFieldDefineType(ErpFieldTypeEnum.text);
        mainDesc.getFields().put(stockLocIdEntity.getFieldApiName(), stockLocIdEntity);

        //增加 仓位（ID） 字段扩展字段
        ErpFieldExtendEntity stockLocIdExtendEntity = new ErpFieldExtendEntity();
        stockLocIdExtendEntity.setObjApiName("BD_SerialMainFile.BillHead");
        stockLocIdExtendEntity.setFieldApiName("FSubHeadEntity.FStockLocId.Id");
        stockLocIdExtendEntity.setFieldDefineType(ErpFieldTypeEnum.text);
        stockLocIdExtendEntity.setViewCode("BD_SERIALMASTEROTHER[0].StockLocId.Id");
        stockLocIdExtendEntity.setPriority(65536L);
        mainDesc.getFieldExtends().put(stockLocIdExtendEntity.getFieldApiName(), stockLocIdExtendEntity);
    }

    private void reProcessCommonContract(ErpObjectDescribe erpObjectDescribe) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BD_CommonContact.BillHead");
        mainDesc.getFieldExtends().get("id").setSaveExtend("{\"isAutoSubmitAndAudit\": false,\"useDraft\": false,\"isAutoSubmitWithoutAudit\": false}");
        ErpObjectFieldEntity erpObjectFieldEntity = mainDesc.getFields().get("FCompanyType");
        erpObjectFieldEntity.setFieldDefineType(ErpFieldTypeEnum.select_one);
        erpObjectFieldEntity.setFieldExtendValue("[{\"value\":\"BD_Supplier\",\"label\":\"供应商\"},{\"value\":\"BD_Customer\",\"label\":\"客户\"}]");   // ignoreI18n   元数据
        ErpObjectFieldEntity ssgs = mainDesc.getFields().get("FCompany.FNumber");
        ssgs.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        ssgs.setFieldExtendValue("BD_Customer.BillHead");
    }

    private void reProcessBatchObj(ErpObjectDescribe erpObjectDescribe) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BD_BatchMainFile.BillHead");
        //换FName字段
        ErpObjectFieldEntity nameField = BeanUtil.copy(mainDesc.getFields().get("FName"), ErpObjectFieldEntity.class);
        nameField.setFieldApiName("name");
        mainDesc.getFields().put(nameField.getFieldApiName(), nameField);
        mainDesc.getFields().remove("FName");
        ErpFieldExtendEntity nameFieldExtendEntity = BeanUtil.copy(mainDesc.getFieldExtends().get("FName"), ErpFieldExtendEntity.class);
        nameFieldExtendEntity.setFieldApiName("name");
        mainDesc.getFieldExtends().put(nameFieldExtendEntity.getFieldApiName(), nameFieldExtendEntity);
        mainDesc.getFieldExtends().remove("FName");
    }

    private void reProcessAssistantData(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BOS_ASSISTANTDATA_DETAIL.BillHead");
        //增加虚拟字段
        ErpObjectFieldEntity parentNumberEntity = new ErpObjectFieldEntity();
        parentNumberEntity.setErpObjectApiName("BOS_ASSISTANTDATA_DETAIL.BillHead");
        parentNumberEntity.setFieldApiName("parentNumber");
        parentNumberEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1189,lang,tenantId));
        parentNumberEntity.setRequired(false);
        parentNumberEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        parentNumberEntity.setFieldExtendValue("BOS_ASSISTANTDATA_DETAIL.BillHead");
        mainDesc.getFields().put(parentNumberEntity.getFieldApiName(), parentNumberEntity);
    }

    private void reProcessStock(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BD_STOCK.BillHead");
        //调整原id字段类型
        ErpObjectFieldEntity idField = BeanUtil.copy(mainDesc.getFields().get("id"), ErpObjectFieldEntity.class);
        ErpFieldExtendEntity idFieldExtendEntity = BeanUtil.copy(mainDesc.getFieldExtends().get("id"), ErpFieldExtendEntity.class);
        idField.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1190,lang,tenantId));
        idField.setFieldApiName("Number");
        idFieldExtendEntity.setFieldApiName("Number");
        idFieldExtendEntity.setErpFieldType("e12");
        idFieldExtendEntity.setViewCode("Number");
        idFieldExtendEntity.setSaveCode("FNumber");
        idFieldExtendEntity.setQueryCode("FNumber");
        mainDesc.getFields().put(idField.getFieldApiName(), idField);
        mainDesc.getFieldExtends().put(idFieldExtendEntity.getFieldApiName(), idFieldExtendEntity);
        mainDesc.getFields().get("id").setFieldDefineType(ErpFieldTypeEnum.text);
        mainDesc.getFieldExtends().get("id").setFieldDefineType(ErpFieldTypeEnum.text);
        mainDesc.getFields().remove("FNumber");
        mainDesc.getFieldExtends().remove("FNumber");
        //增加虚拟字段
        ErpObjectFieldEntity parentWarehouseNumberEntity = new ErpObjectFieldEntity();
        parentWarehouseNumberEntity.setErpObjectApiName("BD_STOCK.BillHead");
        parentWarehouseNumberEntity.setFieldApiName("parentWarehouseNumber");
        parentWarehouseNumberEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1191,lang,tenantId));
        parentWarehouseNumberEntity.setRequired(false);
        parentWarehouseNumberEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        parentWarehouseNumberEntity.setFieldExtendValue("BD_STOCK.BillHead");
        mainDesc.getFields().put(parentWarehouseNumberEntity.getFieldApiName(), parentWarehouseNumberEntity);
        //增加虚拟字段
        ErpObjectFieldEntity isLocEntity = new ErpObjectFieldEntity();
        isLocEntity.setErpObjectApiName("BD_STOCK.BillHead");
        isLocEntity.setFieldApiName("isLoc");
        isLocEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1192,lang,tenantId));
        isLocEntity.setRequired(false);
        isLocEntity.setFieldDefineType(ErpFieldTypeEnum.true_or_false);
        isLocEntity.setFieldExtendValue("[{\"label\":\"是\",\"value\":\"true\"},{\"label\":\"否\",\"value\":\"false\"}]");   // ignoreI18n   元数据
        mainDesc.getFields().put(isLocEntity.getFieldApiName(), isLocEntity);

        ErpObjectFieldEntity nameField = BeanUtil.copy(mainDesc.getFields().get("FName"), ErpObjectFieldEntity.class);
        nameField.setFieldApiName("name");
        mainDesc.getFields().put(nameField.getFieldApiName(), nameField);
        mainDesc.getFields().remove("FName");
        ErpFieldExtendEntity nameFieldExtendEntity = BeanUtil.copy(mainDesc.getFieldExtends().get("FName"), ErpFieldExtendEntity.class);
        nameFieldExtendEntity.setFieldApiName("name");
        mainDesc.getFieldExtends().put(nameFieldExtendEntity.getFieldApiName(), nameFieldExtendEntity);
        mainDesc.getFieldExtends().remove("FName");
    }

    private void reProcessCustomer(ErpObjectDescribe erpObjectDescribe) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BD_Customer.BillHead");
        //调整原id字段类型
        ErpObjectFieldEntity idField = BeanUtil.copy(mainDesc.getFields().get("id"), ErpObjectFieldEntity.class);
        ErpFieldExtendEntity idFieldExtendEntity = BeanUtil.copy(mainDesc.getFieldExtends().get("id"), ErpFieldExtendEntity.class);
        idField.setFieldApiName("Number");
        idFieldExtendEntity.setFieldApiName("Number");
        idFieldExtendEntity.setErpFieldType("e12");
        idFieldExtendEntity.setViewCode("Number");
        idFieldExtendEntity.setSaveCode("FNumber");
        idFieldExtendEntity.setQueryCode("FNumber");
        mainDesc.getFields().put(idField.getFieldApiName(), idField);
        mainDesc.getFieldExtends().put(idFieldExtendEntity.getFieldApiName(), idFieldExtendEntity);
        mainDesc.getFields().get("id").setFieldDefineType(ErpFieldTypeEnum.text);
        mainDesc.getFieldExtends().get("id").setFieldDefineType(ErpFieldTypeEnum.text);
    }

    private void reProcessReturnStock(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        addSalesOrderComId(erpObjectDescribe, "SAL_RETURNSTOCK.BillHead",lang,tenantId);
        addSalesOrderComId(erpObjectDescribe, "SAL_RETURNSTOCK.SAL_RETURNSTOCKENTRY",lang,tenantId);
    }

    private void reProcessOutStock(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        addSalesOrderComId(erpObjectDescribe, "SAL_OUTSTOCK.BillHead",lang,tenantId);
        addSalesOrderComId(erpObjectDescribe, "SAL_OUTSTOCK.SAL_OUTSTOCKENTRY",lang,tenantId);
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe detail1 = splitObjDescribes.get("SAL_OUTSTOCK.SAL_OUTSTOCKENTRY");
        detail1.getFields().get("FSOEntryId").setFieldDefineType(ErpFieldTypeEnum.object_reference);
        detail1.getFields().get("FSOEntryId").setFieldExtendValue("SAL_SaleOrder.SaleOrderEntry");
        detail1.getFieldExtends().get("FSOEntryId").setFieldDefineType(ErpFieldTypeEnum.object_reference);
    }

    private void reProcessRefundBill(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        addSalesOrderComId(erpObjectDescribe, "AR_REFUNDBILL.BillHead",lang,tenantId);
        addSalesOrderComId(erpObjectDescribe, "AR_REFUNDBILL.REFUNDBILLENTRY",lang,tenantId);
    }

    private void reProcessReceiveBill(ErpObjectDescribe erpObjectDescribe,String lang, String tenantId) {
        addSalesOrderComId(erpObjectDescribe, "AR_RECEIVEBILL.RECEIVEBILLENTRY",lang,tenantId);
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe objDesc = splitObjDescribes.get("AR_RECEIVEBILL.BillHead");
        //更换查找关联对象
        //往来单位
        objDesc.getFields().get("FCONTACTUNIT.FNumber").setFieldExtendValue("BD_Customer.BillHead");
        //转出往来单位
        objDesc.getFields().get("FOUTCONTACTID.FNumber").setFieldExtendValue("BD_Customer.BillHead");
        //付款单位
        objDesc.getFields().get("FPAYUNIT.FNumber").setFieldExtendValue("BD_Customer.BillHead");
    }

    private void reProcessInventory(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe objDesc = splitObjDescribes.get("STK_Inventory.BillHead");
        //增加仓库复合id字段
        ErpObjectFieldEntity comIdEntity = new ErpObjectFieldEntity();
        comIdEntity.setErpObjectApiName("STK_Inventory.BillHead");
        comIdEntity.setFieldApiName("warehouseComId");
        comIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1194,lang,tenantId));
        comIdEntity.setRequired(false);
        comIdEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        comIdEntity.setFieldExtendValue("BD_STOCK.BillHead");
        objDesc.getFields().put(comIdEntity.getFieldApiName(), comIdEntity);
        //增加库存复合id字段
        ErpObjectFieldEntity stockComIdEntity = new ErpObjectFieldEntity();
        stockComIdEntity.setErpObjectApiName("STK_Inventory.BillHead");
        stockComIdEntity.setFieldApiName("stockComId");
        stockComIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1196,lang,tenantId));
        stockComIdEntity.setRequired(false);
        stockComIdEntity.setFieldDefineType(ErpFieldTypeEnum.id);
        objDesc.getFields().put(stockComIdEntity.getFieldApiName(), stockComIdEntity);
        ErpFieldExtendEntity nExtend = new ErpFieldExtendEntity();
        nExtend.setFieldApiName("stockComId");
        nExtend.setFieldDefineType(ErpFieldTypeEnum.id);
        nExtend.setObjApiName("STK_Inventory");
        nExtend.setViewCode("Id");
        nExtend.setSaveCode("FID");
        nExtend.setQueryCode("FID");
        nExtend.setPriority(0L);
        objDesc.getFieldExtends().put(nExtend.getFieldApiName(), nExtend);
        //调整原id字段类型
        ErpObjectFieldEntity idFieldEntry = objDesc.getFields().get("id");
        idFieldEntry.setFieldDefineType(ErpFieldTypeEnum.text);
        ErpFieldExtendEntity idFieldExtend = objDesc.getFieldExtends().get("id");
        idFieldExtend.setFieldDefineType(ErpFieldTypeEnum.text);
        //增加查找关联批号字段
        ErpObjectFieldEntity flotIdEntity = new ErpObjectFieldEntity();
        flotIdEntity.setErpObjectApiName("STK_Inventory.BillHead");
        flotIdEntity.setFieldApiName("FLot");
        flotIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1197,lang,tenantId));
        flotIdEntity.setRequired(false);
        flotIdEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        flotIdEntity.setFieldExtendValue("BD_BatchMainFile.BillHead");
        objDesc.getFields().put(flotIdEntity.getFieldApiName(), flotIdEntity);
    }

    private void reProcessSaleSoc(ErpObjectDescribe erpObjectDescribe,String lang, String tenantId) {
        addSalesOrderComId(erpObjectDescribe, "IV_SALESOC.BillHead",lang,tenantId);
        addSalesOrderComId(erpObjectDescribe, "IV_SALESOC.SALESICENTRY",lang,tenantId);
    }

    private void reProcessSalesic(ErpObjectDescribe erpObjectDescribe,String lang, String tenantId) {
        addSalesOrderComId(erpObjectDescribe, "IV_SALESIC.BillHead",lang,tenantId);
        addSalesOrderComId(erpObjectDescribe, "IV_SALESIC.SALESICENTRY",lang,tenantId);
    }

    private void addSalesOrderComId(ErpObjectDescribe erpObjectDescribe, String objApiName, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe objDesc = splitObjDescribes.get(objApiName);
        //增加销售订单复合id字段
        ErpObjectFieldEntity comIdEntity = new ErpObjectFieldEntity();
        comIdEntity.setErpObjectApiName(objApiName);
        comIdEntity.setFieldApiName("SalesOrderComId");
        comIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1188,lang,tenantId));
        comIdEntity.setRequired(false);
        comIdEntity.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        comIdEntity.setFieldExtendValue("SAL_SaleOrder.BillHead");
        objDesc.getFields().put(comIdEntity.getFieldApiName(), comIdEntity);
    }


    private void reProcessMaterial(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("BD_MATERIAL.BillHead");
        //增加复合名称字段
        ErpObjectFieldEntity comNameEntity = new ErpObjectFieldEntity();
        comNameEntity.setErpObjectApiName("BD_MATERIAL.BillHead");
        comNameEntity.setFieldApiName("comName");
        comNameEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1187,lang,tenantId));
        comNameEntity.setRequired(false);
        comNameEntity.setFieldDefineType(ErpFieldTypeEnum.text);
        mainDesc.getFields().put(comNameEntity.getFieldApiName(), comNameEntity);
        //是否开启批次序列号管理字段
        ErpObjectFieldEntity hasBatchEntity = new ErpObjectFieldEntity();
        hasBatchEntity.setErpObjectApiName("BD_MATERIAL.BillHead");
        hasBatchEntity.setFieldApiName("VirtualHasBatchAndSerial");
        hasBatchEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1186,lang,tenantId));
        hasBatchEntity.setRequired(false);
        hasBatchEntity.setFieldDefineType(ErpFieldTypeEnum.select_one);
        hasBatchEntity.setFieldExtendValue("[{\"value\":\"1\",\"label\":\"不开启\"},{\"value\":\"2\",\"label\":\"开启批次\"},{\"value\":\"3\",\"label\":\"开启序列号\"}]");   // ignoreI18n   元数据
        mainDesc.getFields().put(hasBatchEntity.getFieldApiName(), hasBatchEntity);
        //增加物料分组编码
        ErpObjectFieldEntity groupNumEntity = new ErpObjectFieldEntity();
        groupNumEntity.setErpObjectApiName("BD_MATERIAL.BillHead");
        groupNumEntity.setFieldApiName("FMaterialGroup.FNumber");
        groupNumEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1185,lang,tenantId));
        groupNumEntity.setRequired(false);
        groupNumEntity.setFieldDefineType(ErpFieldTypeEnum.category);
        mainDesc.getFields().put(groupNumEntity.getFieldApiName(), groupNumEntity);
        ErpFieldExtendEntity nExtend = new ErpFieldExtendEntity();
        nExtend.setFieldApiName("FMaterialGroup.FNumber");
        nExtend.setFieldDefineType(ErpFieldTypeEnum.category);
        nExtend.setObjApiName("BD_MATERIAL");
        nExtend.setViewCode("MaterialGroup.Number");
        nExtend.setSaveCode("FMaterialGroup.FNumber");
        nExtend.setQueryCode("FMaterialGroup.FNumber");
        nExtend.setPriority(0L);
        mainDesc.getFieldExtends().put(nExtend.getFieldApiName(), nExtend);
        //调整原id字段类型
        ErpObjectFieldEntity idField = BeanUtil.copy(mainDesc.getFields().get("id"), ErpObjectFieldEntity.class);
        ErpFieldExtendEntity idFieldExtendEntity = BeanUtil.copy(mainDesc.getFieldExtends().get("id"), ErpFieldExtendEntity.class);
        idField.setFieldApiName("Number");
        idFieldExtendEntity.setFieldApiName("Number");
        idFieldExtendEntity.setViewCode("Number");
        idFieldExtendEntity.setSaveCode("FNumber");
        idFieldExtendEntity.setQueryCode("FNumber");
        mainDesc.getFields().put(idField.getFieldApiName(), idField);
        mainDesc.getFieldExtends().put(idFieldExtendEntity.getFieldApiName(), idFieldExtendEntity);
        mainDesc.getFields().get("id").setFieldDefineType(ErpFieldTypeEnum.text);
        mainDesc.getFieldExtends().get("id").setFieldDefineType(ErpFieldTypeEnum.text);
        //调整name字段
        ErpObjectFieldEntity nameField = mainDesc.getFields().get("FName");
        nameField.setFieldApiName("name");
        ErpFieldExtendEntity nameFieldExtendEntity = mainDesc.getFieldExtends().get("FName");
        nameFieldExtendEntity.setFieldApiName("name");
        mainDesc.getFields().put(nameField.getFieldApiName(), nameField);
        mainDesc.getFieldExtends().put(nameFieldExtendEntity.getFieldApiName(), nameFieldExtendEntity);
        mainDesc.getFields().remove("FName");
        mainDesc.getFieldExtends().remove("FName");
    }

    private void reProcessSaleOrder(ErpObjectDescribe erpObjectDescribe, String lang, String tenantId) {
        LinkedHashMap<String, SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        SplitObjDescribe mainDesc = splitObjDescribes.get("SAL_SaleOrder.BillHead");
        //增加复合id字段
        ErpObjectFieldEntity comIdEntity = new ErpObjectFieldEntity();
        comIdEntity.setErpObjectApiName("SAL_SaleOrder.BillHead");
        comIdEntity.setFieldApiName("ComId");
        comIdEntity.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1184,lang,tenantId));
        comIdEntity.setRequired(false);
        comIdEntity.setFieldDefineType(ErpFieldTypeEnum.id);
        comIdEntity.setFieldExtendValue("{\"composite\":true,\"compositeFields\":[\"id\",\"FBillNo\"],\"viewIdField\":\"id\",\"saveIdField\":\"id\"}");
        mainDesc.getFields().put(comIdEntity.getFieldApiName(), comIdEntity);
        //调整原id字段类型
        mainDesc.getFields().get("id").setFieldDefineType(ErpFieldTypeEnum.text);


        //调整一些字段顺序
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFieldExtends().get("FRowType").setPriority(1L);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFieldExtends().get("FStockOrgId.FNumber").setPriority(2L);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFields().get("FUnitID.FNumber").setRequired(false);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFields().get("FSettleOrgIds.FNumber").setRequired(false);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFields().get("FDeliveryDate").setRequired(false);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFields().get("FReserveType").setRequired(false);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFields().get("FOUTLMTUNIT").setRequired(false);
        splitObjDescribes.get("SAL_SaleOrder.SaleOrderEntry").getFields().get("FRowType").setRequired(false);
    }

    private void fillIdField(String realObjApiName, SplitObjDescribe splitObjDescribe, EntrysBean entry, QueryBusinessInfoResult.BusinessInfo businessInfo) {
        ErpObjectFieldEntity nField = new ErpObjectFieldEntity();
        ErpFieldExtendEntity nExtend = new ErpFieldExtendEntity();
        nField.setErpObjectApiName(realObjApiName + D_BILL_HEAD);
        nField.setFieldApiName("id");
        nField.setFieldLabel("ID");
        nField.setFieldDefineType(ErpFieldTypeEnum.id);
        nExtend.setFieldApiName("id");
        nExtend.setFieldDefineType(ErpFieldTypeEnum.id);
        nExtend.setObjApiName(realObjApiName);
        nExtend.setViewCode("Id");
        nExtend.setSaveCode(entry.getEntryPkFieldName());
        nExtend.setQueryCode(businessInfo.getPkFieldName());
        nExtend.setPriority(0L);
        splitObjDescribe.getFields().put(nField.getFieldApiName(), nField);
        splitObjDescribe.getFieldExtends().put(nExtend.getFieldApiName(), nExtend);
    }

    private void fillDetailIdField(String mainApiName, String splitApiName, SplitObjDescribe splitObjDescribe, EntrysBean entry, String lang, String tenantId) {
        ErpObjectFieldEntity detailIdField = new ErpObjectFieldEntity();
        ErpFieldExtendEntity detailIdExtend = new ErpFieldExtendEntity();
        detailIdField.setErpObjectApiName(splitApiName);
        detailIdField.setFieldApiName("DetailId");
        detailIdField.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1183,lang,tenantId));
        detailIdField.setFieldDefineType(ErpFieldTypeEnum.id);
        detailIdExtend.setFieldApiName("DetailId");
        detailIdExtend.setFieldDefineType(ErpFieldTypeEnum.id);
        detailIdExtend.setObjApiName(splitApiName);
        detailIdExtend.setViewCode("Id");
        detailIdExtend.setSaveCode(entry.getEntryPkFieldName());
        detailIdExtend.setQueryCode(entry.getKey() + "_" + entry.getEntryPkFieldName());
        detailIdExtend.setPriority(0L);
        ErpObjectFieldEntity masterDetailField = new ErpObjectFieldEntity();
        masterDetailField.setErpObjectApiName(splitApiName);
        masterDetailField.setFieldApiName("fake_master_detail");
        masterDetailField.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1174,lang,tenantId));
        masterDetailField.setFieldDefineType(ErpFieldTypeEnum.master_detail);
        masterDetailField.setFieldExtendValue(mainApiName);
        splitObjDescribe.getFields().put(detailIdField.getFieldApiName(), detailIdField);
        splitObjDescribe.getFields().put(masterDetailField.getFieldApiName(), masterDetailField);
        splitObjDescribe.getFieldExtends().put(detailIdExtend.getFieldApiName(), detailIdExtend);
    }


    private void fillSubDetailIdField(String tenantId, String splitApiName, SplitObjDescribe splitObjDescribe, EntrysBean entry,
                                      SplitObjDescribe parentEntryDesc, SplitObjDescribe mainDesc,String lang) {
        //主表主键字段
        fillMainIdField(tenantId, splitApiName, splitObjDescribe, mainDesc,lang);
        //明细主键字段
        fillParentDetailIdField(tenantId, splitApiName, splitObjDescribe, parentEntryDesc,lang);
        //孙表主键
        ErpObjectFieldEntity detailIdField = new ErpObjectFieldEntity();
        ErpFieldExtendEntity detailIdExtend = new ErpFieldExtendEntity();
        detailIdField.setErpObjectApiName(splitApiName);
        detailIdField.setFieldApiName("DetailId");
        detailIdField.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1182,lang,tenantId));
        detailIdField.setFieldDefineType(ErpFieldTypeEnum.id);
        detailIdExtend.setFieldApiName("DetailId");
        detailIdExtend.setFieldDefineType(ErpFieldTypeEnum.id);
        detailIdExtend.setObjApiName(splitApiName);
        detailIdExtend.setViewCode("Id");
        detailIdExtend.setSaveCode(entry.getEntryPkFieldName());
        detailIdExtend.setQueryCode(entry.getKey() + "_" + entry.getEntryPkFieldName());
        detailIdExtend.setPriority(0L);
        splitObjDescribe.getFields().put(detailIdField.getFieldApiName(), detailIdField);
        splitObjDescribe.getFieldExtends().put(detailIdExtend.getFieldApiName(), detailIdExtend);
    }

    private void fillMainIdField(String tenantId, String splitApiName, SplitObjDescribe splitObjDescribe, SplitObjDescribe mainDesc, String lang) {
        String fieldPrefix = "main-";
        ErpObjectFieldEntity mainIdField = mainDesc.getFields().values().stream()
                .filter(v -> v.getFieldDefineType().equals(ErpFieldTypeEnum.id))
                .findFirst().orElseThrow(() -> new ErpSyncDataException(I18NStringEnum.s163,tenantId));
        ErpObjectFieldEntity mainIdField2 = BeanUtil.copy(mainIdField, ErpObjectFieldEntity.class);
        mainIdField2.setErpObjectApiName(splitApiName);
        mainIdField2.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1181,lang,tenantId));
        mainIdField2.setFieldApiName(fieldPrefix + mainIdField.getFieldApiName());
        mainIdField2.setFieldDefineType(ErpFieldTypeEnum.master_detail);
        mainIdField2.setFieldExtendValue(mainDesc.getObjRelation().getErpSplitObjectApiname());
        ErpFieldExtendEntity mainIdFieldExtend = mainDesc.getFieldExtends().get(mainIdField.getFieldApiName());
        ErpFieldExtendEntity mainIdFieldExtend2 = BeanUtil.copy(mainIdFieldExtend, ErpFieldExtendEntity.class);
        mainIdFieldExtend2.setFieldDefineType(ErpFieldTypeEnum.master_detail);
        mainIdFieldExtend2.setObjApiName(splitApiName);
        mainIdFieldExtend2.setFieldApiName(fieldPrefix + mainIdField.getFieldApiName());
        splitObjDescribe.getFields().put(mainIdField2.getFieldApiName(), mainIdField2);
        splitObjDescribe.getFieldExtends().put(mainIdFieldExtend2.getFieldApiName(), mainIdFieldExtend2);
        if (!K3ElementTypeEnum.e12.getName().equals(mainIdFieldExtend.getErpFieldType())) {
            //查找编号字段
            Optional<ErpFieldExtendEntity> mainNoExtOp = mainDesc.getFieldExtends().values().stream()
                    .filter(v -> K3ElementTypeEnum.e12.getName().equals(v.getErpFieldType()))
                    .findFirst();
            if (mainNoExtOp.isPresent()) {
                ErpFieldExtendEntity mainNoFieldExt = BeanUtil.copy(mainNoExtOp.get(), ErpFieldExtendEntity.class);
                mainNoFieldExt.setFieldDefineType(ErpFieldTypeEnum.text);
                mainNoFieldExt.setObjApiName(splitApiName);
                mainNoFieldExt.setFieldApiName(fieldPrefix + mainNoFieldExt.getFieldApiName());
                ErpObjectFieldEntity mainNoField = BeanUtil.copy(mainDesc.getFields().get(mainNoFieldExt.getFieldApiName()), ErpObjectFieldEntity.class);
                mainNoField.setErpObjectApiName(splitApiName);
                mainNoField.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1180,lang,tenantId));
                mainNoField.setFieldDefineType(ErpFieldTypeEnum.text);
                mainNoField.setFieldApiName(fieldPrefix + mainNoField.getFieldApiName());
                splitObjDescribe.getFields().put(mainNoField.getFieldApiName(), mainNoField);
                splitObjDescribe.getFieldExtends().put(mainNoFieldExt.getFieldApiName(), mainNoFieldExt);
            }
        }
    }

    private void fillParentDetailIdField(String tenantId, String splitApiName, SplitObjDescribe splitObjDescribe, SplitObjDescribe mainDesc, String lang) {
        String fieldPrefix = "detail-";
        ErpObjectFieldEntity mainIdField = mainDesc.getFields().values().stream()
                .filter(v -> v.getFieldDefineType().equals(ErpFieldTypeEnum.id))
                .findFirst().orElseThrow(() -> new ErpSyncDataException(I18NStringEnum.s164,tenantId));
        ErpObjectFieldEntity mainIdField2 = BeanUtil.copy(mainIdField, ErpObjectFieldEntity.class);
        mainIdField2.setErpObjectApiName(splitApiName);
        mainIdField2.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1179,lang,tenantId));
        mainIdField2.setFieldApiName(fieldPrefix + mainIdField.getFieldApiName());
        mainIdField2.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        mainIdField2.setFieldExtendValue(mainDesc.getObjRelation().getErpSplitObjectApiname());
        ErpFieldExtendEntity mainIdFieldExtend = mainDesc.getFieldExtends().get(mainIdField.getFieldApiName());
        ErpFieldExtendEntity mainIdFieldExtend2 = BeanUtil.copy(mainIdFieldExtend, ErpFieldExtendEntity.class);
        mainIdFieldExtend2.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        mainIdFieldExtend2.setObjApiName(splitApiName);
        mainIdFieldExtend2.setFieldApiName(fieldPrefix + mainIdField.getFieldApiName());
        splitObjDescribe.getFields().put(mainIdField2.getFieldApiName(), mainIdField2);
        splitObjDescribe.getFieldExtends().put(mainIdFieldExtend2.getFieldApiName(), mainIdFieldExtend2);
    }

    private void fillDetailExtend(String realObjApiName,
                                  String mainSplitApiName,
                                  SplitObjDescribe splitObjDescribe,
                                  EntrysBean entry,
                                  int index,
                                  String lang,
                                  String tenantId) {
        ErpFieldExtendEntity nExtend = new ErpFieldExtendEntity();
        String fieldApiName = realObjApiName + "." + entry.getEntryName();
        nExtend.setFieldApiName(fieldApiName);
        nExtend.setFieldDefineType(ErpFieldTypeEnum.detail);
        nExtend.setObjApiName(realObjApiName);
        nExtend.setViewCode(entry.getEntryName());
        nExtend.setSaveCode(entry.getKey());
        nExtend.setPriority(8000L + index);
        splitObjDescribe.getFieldExtends().put(nExtend.getFieldApiName(), nExtend);
        //字段也加一下，方便删除明细对象的场景
        ErpObjectFieldEntity field = new ErpObjectFieldEntity();
        field.setErpObjectApiName(mainSplitApiName);
        field.setFieldApiName(fieldApiName);
        field.setFieldLabel(i18NStringManager.get(I18NStringEnum.s1177,lang,tenantId) + entry.getEntryName() + i18NStringManager.get(I18NStringEnum.s1178,lang,tenantId));
        field.setFieldDefineType(ErpFieldTypeEnum.detail);
        splitObjDescribe.getFields().put(field.getFieldApiName(), field);
    }

    private void fillFields(String tenantId,
                            MetaDataHelper helper,
                            String splitApiName,
                            String realApiName,
                            EntrysBean entry,
                            SplitObjDescribe splitObjDescribe,
                            boolean ignoreCustomField,
                            String lang) {
        List<EntrysBean.FieldsBean> fields = entry.getFields();
        if (ignoreCustomField) {
            fields.removeIf(v -> v.getKey().startsWith("F_"));
        }
        ErpObjectFieldEntity baseFieldEntity = new ErpObjectFieldEntity();
        baseFieldEntity.setRequired(false);
        ErpFieldExtendEntity baseFieldExtend = new ErpFieldExtendEntity();

        EntityType entityType = EntityType.valueOf(entry.getEntityType());
        baseFieldEntity.setErpObjectApiName(splitApiName);
        if (entityType.equals(EntityType.EntryEntity) || entityType.equals(EntityType.SubEntryEntity)) {
            //明细使用拆分名称
            baseFieldExtend.setObjApiName(splitApiName);
        } else {
            //主表使用真实apiName
            baseFieldExtend.setObjApiName(realApiName);
        }
        LinkedHashMap<String, ErpObjectFieldEntity> fieldMap = new LinkedHashMap<>();
        LinkedHashMap<String, ErpFieldExtendEntity> fieldExtendMap = new LinkedHashMap<>();
        for (int i = 0, fieldsSize = fields.size(); i < fieldsSize; i++) {
            EntrysBean.FieldsBean field = fields.get(i);
            String fieldApiName = field.getKey();
            if (field.getElementType() > 100) {
                log.info("not support types,field:{}", field);
                continue;
            }
            K3ElementTypeEnum eleType = K3ElementTypeEnum.getByType(field.getElementType());
            if (eleType != null) {
                baseFieldExtend.setErpFieldType(eleType.toString());
                if ("FNumber".equals(fieldApiName)) {
                    baseFieldExtend.setErpFieldType(K3ElementTypeEnum.e12.toString());
                }
            } else {
                baseFieldExtend.setErpFieldType(null);
            }
            String name = getI18NName(tenantId, field.getName(),lang);
            //只会关联主表，主表的平台apiName需要增加.BillHead
            String lookUpObjectFormId = field.getLookUpObjectFormId();
            boolean required = field.getMustInput() == 1;
            //查找关联，销售员在里面处理
            if (K3ElementTypeEnum.NUMBER_REFERENCE_TYPES.contains(eleType)) {
                ErpFieldTypeEnum fieldDefineType = ErpFieldTypeEnum.object_reference;
                String loockUpFormApiName = lookUpObjectFormId + D_BILL_HEAD;
                String fieldExtendValue = loockUpFormApiName;
                List<String> referenceObjFields = getBillHeadObjFields(lookUpObjectFormId, helper);//第一个为id字段
                if (K3ElementTypeEnum.e13.equals(eleType) && selectOneErpObj.contains(lookUpObjectFormId)) {//默认为单选，查找单选值
                    Map<String, String> filter = Maps.newHashMap();
                    if (referenceObjFields.contains("FBillFormID")) {
                        filter.put("FBillFormID", realApiName);
                    }
                    fieldDefineType = ErpFieldTypeEnum.select_one;
                    List<Option> options = helper.getOptionList(referenceObjFields, lookUpObjectFormId, filter);
                    fieldExtendValue = GsonUtil.toJson(options);
                }
                //名称字段
                if (referenceObjFields.contains("FName")) {
                    ErpObjectFieldEntity nameField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                    ErpFieldExtendEntity nameExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                    nameField.setFieldApiName(fieldApiName + D_NAME);
                    nameField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1198,lang,tenantId));
                    nameField.setRequired(false);
                    nameField.setFieldDefineType(ErpFieldTypeEnum.text);
                    nameExtend.setFieldApiName(fieldApiName + D_NAME);
                    nameExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                    //没有保存
                    nameExtend.setViewCode(field.getPropertyName() + D_NAME);
                    nameExtend.setQueryCode(fieldApiName + D_FNAME);
                    nameExtend.setPriority(100L + 10 * i);
                    fieldMap.put(nameField.getFieldApiName(), nameField);
                    fieldExtendMap.put(nameExtend.getFieldApiName(), nameExtend);
                }
                //id字段
                if (referenceObjFields.contains("FID")) {
                    //查找关联id
                    ErpObjectFieldEntity idField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                    ErpFieldExtendEntity idExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                    idField.setFieldApiName(fieldApiName + D_ID);
                    idField.setFieldLabel(name + SUFFIX_ID);
                    idField.setFieldDefineType(ErpFieldTypeEnum.object_reference);
                    idField.setRequired(false);
                    idField.setFieldExtendValue(loockUpFormApiName);
                    idExtend.setFieldApiName(fieldApiName + D_ID);
                    idExtend.setFieldDefineType(ErpFieldTypeEnum.object_reference);
                    //没有保存
                    idExtend.setViewCode(field.getPropertyName() + D_ID);
                    idExtend.setQueryCode(fieldApiName);
                    idExtend.setPriority(102L + 10 * i);
                    fieldMap.put(idField.getFieldApiName(), idField);
                    fieldExtendMap.put(idExtend.getFieldApiName(), idExtend);
                } else if (CollectionUtils.isNotEmpty(referenceObjFields)) {
                    //查找关联id
                    String referenceObjIdField = referenceObjFields.get(0);
                    ErpObjectFieldEntity idField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                    ErpFieldExtendEntity idExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                    idField.setFieldApiName(fieldApiName + referenceObjIdField);
                    idField.setFieldLabel(name + SUFFIX_ID);
                    idField.setFieldDefineType(ErpFieldTypeEnum.object_reference);
                    idField.setRequired(false);
                    idField.setFieldExtendValue(loockUpFormApiName);
                    idExtend.setFieldApiName(fieldApiName + referenceObjIdField);
                    idExtend.setFieldDefineType(ErpFieldTypeEnum.object_reference);
                    //没有保存
                    idExtend.setViewCode(field.getPropertyName() + _ID);
                    idExtend.setQueryCode(fieldApiName);
                    idExtend.setPriority(102L + 10 * i);
                    fieldMap.put(idField.getFieldApiName(), idField);
                    fieldExtendMap.put(idExtend.getFieldApiName(), idExtend);
                }
                //编码字段
                if (referenceObjFields.contains("FNumber")) {
                    ErpObjectFieldEntity numberField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                    ErpFieldExtendEntity numberExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                    numberField.setFieldApiName(fieldApiName + D_FNUMBER);
                    numberField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1199,lang,tenantId));
                    numberField.setRequired(required);
                    numberExtend.setFieldApiName(fieldApiName + D_FNUMBER);
                    numberExtend.setViewCode(field.getPropertyName() + D_NUMBER);
                    numberExtend.setSaveCode(field.getKey() + D_FNUMBER);
                    numberExtend.setQueryCode(fieldApiName + D_FNUMBER);
                    numberExtend.setPriority(101L + 10 * i);
                    if (K3ElementTypeEnum.OPERATOR_FORMS.contains(lookUpObjectFormId)) {
                        numberField.setFieldDefineType(ErpFieldTypeEnum.employee);
                        numberExtend.setFieldDefineType(ErpFieldTypeEnum.employee);
                    } else {
                        numberField.setFieldDefineType(fieldDefineType);
                        numberField.setFieldExtendValue(fieldExtendValue);
                        numberExtend.setFieldDefineType(fieldDefineType);
                    }
                    fieldMap.put(numberField.getFieldApiName(), numberField);
                    fieldExtendMap.put(numberExtend.getFieldApiName(), numberExtend);
                } else if (referenceObjFields.contains("FCODE")) {//code字段
                    ErpObjectFieldEntity codeField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                    ErpFieldExtendEntity codeExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                    codeField.setFieldApiName(fieldApiName + D_FCODE);
                    codeField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1199,lang,tenantId));
                    codeField.setRequired(required);
                    codeExtend.setFieldApiName(fieldApiName + D_FCODE);
                    codeExtend.setViewCode(field.getPropertyName() + D_CODE);
                    codeExtend.setSaveCode(field.getKey() + D_FCODE);
                    codeExtend.setQueryCode(fieldApiName + D_FCODE);
                    codeExtend.setPriority(101L + 10 * i);
                    codeField.setFieldDefineType(fieldDefineType);
                    codeField.setFieldExtendValue(fieldExtendValue);
                    codeExtend.setFieldDefineType(fieldDefineType);
                    fieldMap.put(codeField.getFieldApiName(), codeField);
                    fieldExtendMap.put(codeExtend.getFieldApiName(), codeExtend);
                }
                continue;
            }
            //下拉列表
            if (K3ElementTypeEnum.PULL_DOWN_LIST_TYPES.contains(eleType)) {
                //下拉单选字段
                ErpObjectFieldEntity nField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity nExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                nField.setFieldApiName(fieldApiName);
                nField.setFieldLabel(name);
                nField.setRequired(required);
                nField.setFieldDefineType(ErpFieldTypeEnum.select_one);
                //解析选项
                List<SelectExtend> selectExtends = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(field.getExtendValues())) {
                    field.getExtendValues().forEach(v -> selectExtends.add(SelectExtend.of(v.getValue(), v.getCaption())));
                }
                nField.setFieldExtendValue(JacksonUtil.toJson(selectExtends));
                nExtend.setFieldApiName(fieldApiName);
                nExtend.setFieldDefineType(ErpFieldTypeEnum.select_one);
                nExtend.setViewCode(field.getPropertyName());
                nExtend.setSaveCode(field.getKey());
                nExtend.setQueryCode(fieldApiName);
                nExtend.setPriority(100L + 10 * i);
                fieldMap.put(nField.getFieldApiName(), nField);
                fieldExtendMap.put(nExtend.getFieldApiName(), nExtend);
                continue;
            }
            //单位或单据类型
            if (K3ElementTypeEnum.UNIT_TYPES.contains(eleType)) {
                //名称字段
                ErpObjectFieldEntity nameField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity nameExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                nameField.setFieldApiName(fieldApiName + D_NAME);
                nameField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1198,lang,tenantId));
                nameField.setRequired(false);
                nameField.setFieldDefineType(ErpFieldTypeEnum.text);
                nameExtend.setFieldApiName(fieldApiName + D_NAME);
                nameExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                //没有保存
                nameExtend.setViewCode(field.getPropertyName() + D_NAME);
                nameExtend.setQueryCode(fieldApiName + D_FNAME);
                nameExtend.setPriority(100L + 10 * i);
                //编码字段
                ErpObjectFieldEntity numberField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity numberExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                numberField.setFieldApiName(fieldApiName + D_FNUMBER);
                numberField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1199,lang,tenantId));
                numberField.setRequired(required);
                numberField.setFieldDefineType(ErpFieldTypeEnum.select_one);
                //单位的选项后期查找加上
                String fieldExtendValue = "[]";
                List<String> referenceObjFields = getBillHeadObjFields(lookUpObjectFormId, helper);
                if (K3CloudForm.BD_UNIT.equals(lookUpObjectFormId)) {//单位的加上单选
                    List<Option> options = helper.getOptionList(referenceObjFields, lookUpObjectFormId, Maps.newHashMap());
                    fieldExtendValue = GsonUtil.toJson(options);
                } else if (referenceObjFields.contains("FBillFormID")) {//单据类型
                    Map<String, String> filter = Maps.newHashMap();
                    filter.put("FBillFormID", realApiName);
                    List<Option> options = helper.getOptionList(referenceObjFields, lookUpObjectFormId, filter);
                    fieldExtendValue = GsonUtil.toJson(options);
                }
                numberField.setFieldExtendValue(fieldExtendValue);
                numberExtend.setFieldApiName(fieldApiName + D_FNUMBER);
                numberExtend.setFieldDefineType(ErpFieldTypeEnum.select_one);
                numberExtend.setViewCode(field.getPropertyName() + D_NUMBER);
                numberExtend.setSaveCode(field.getKey() + D_FNUMBER);
                numberExtend.setQueryCode(fieldApiName + D_FNUMBER);
                numberExtend.setPriority(101L + 10 * i);

                //id字段
                ErpObjectFieldEntity idField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity idExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                idField.setFieldApiName(fieldApiName + D_ID);
                idField.setFieldLabel(name + SUFFIX_ID);
                idField.setFieldDefineType(ErpFieldTypeEnum.text);
                idField.setRequired(false);
                idExtend.setFieldApiName(fieldApiName + D_ID);
                idExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                //没有保存
                idExtend.setViewCode(field.getPropertyName() + D_ID);
                idExtend.setQueryCode(fieldApiName);
                idExtend.setPriority(102L + 10 * i);
                fieldMap.put(nameField.getFieldApiName(), nameField);
                fieldMap.put(numberField.getFieldApiName(), numberField);
                fieldMap.put(idField.getFieldApiName(), idField);
                fieldExtendMap.put(nameExtend.getFieldApiName(), nameExtend);
                fieldExtendMap.put(numberExtend.getFieldApiName(), numberExtend);
                fieldExtendMap.put(idExtend.getFieldApiName(), idExtend);
                continue;
            }
            //用户
            if (K3ElementTypeEnum.USER_TYPES.contains(eleType)) {
                //用户,查看code增加.Id，保存code增加.FUserId
                //id字段
                ErpObjectFieldEntity idField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity idExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                idField.setFieldApiName(fieldApiName + D_ID);
                idField.setFieldLabel(name + SUFFIX_ID);
                idField.setFieldDefineType(ErpFieldTypeEnum.text);
                idField.setRequired(false);
                idExtend.setFieldApiName(fieldApiName + D_ID);
                idExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                //没有保存
                idExtend.setViewCode(field.getPropertyName() + D_ID);
                idExtend.setSaveCode(field.getKey() + D_FUSERID);
                idExtend.setQueryCode(fieldApiName);
                idExtend.setPriority(102L + 10 * i);
                fieldMap.put(idField.getFieldApiName(), idField);
                fieldExtendMap.put(idExtend.getFieldApiName(), idExtend);
                continue;
            }
            //辅助资料
            if (K3ElementTypeEnum.e30.equals(eleType)) {
                //单选辅助资料 查看code加.FNumber,名称加.FDataValue
                //名称字段
                ErpObjectFieldEntity nameField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity nameExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                nameField.setFieldApiName(fieldApiName + D_FDATAVALUE);
                nameField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1198,lang,tenantId));
                nameField.setRequired(false);
                nameField.setFieldDefineType(ErpFieldTypeEnum.text);
                nameExtend.setFieldApiName(fieldApiName + D_FDATAVALUE);
                nameExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                //没有保存
                nameExtend.setViewCode(field.getPropertyName() + D_FDATAVALUE);
                nameExtend.setQueryCode(fieldApiName + D_FDATAVALUE);
                nameExtend.setPriority(100L + 10 * i);
                //编码字段
                ErpObjectFieldEntity numberField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity numberExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                numberField.setFieldApiName(fieldApiName + D_FNUMBER);
                numberField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1199,lang,tenantId));
                numberField.setRequired(required);
                numberField.setFieldDefineType(ErpFieldTypeEnum.select_one);
                //辅助资料的选项后期查找加上
                String fieldExtendValue = "[]";
//                EntrysBean.FieldsBean找不到关联辅助资料的类别
//                if (StringUtils.isNotBlank(lookUpObjectFormId)) {
//                    Map<String,String>filters=Maps.newHashMap();
//                    List<String> referenceObjFields = getBillHeadObjFields(lookUpObjectFormId, apiClient);
//                    if(referenceObjFields.contains("")){
//                        switch (fieldApiName){
//                            case "":filters.put("","");
//                        }
//                    }
//                    List<Option> optionList = getOptionList(apiClient, referenceObjFields, lookUpObjectFormId,filters);
//                    fieldExtendValue = GsonUtil.toJson(optionList);
//                }
                numberField.setFieldExtendValue(fieldExtendValue);
                numberExtend.setFieldApiName(fieldApiName + D_FNUMBER);
                numberExtend.setFieldDefineType(ErpFieldTypeEnum.select_one);
                numberExtend.setViewCode(field.getPropertyName() + D_FNUMBER);
                numberExtend.setSaveCode(field.getKey() + D_FNUMBER);
                numberExtend.setQueryCode(fieldApiName + D_FNUMBER);
                numberExtend.setPriority(101L + 10 * i);

                //id字段
                ErpObjectFieldEntity idField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity idExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                idField.setFieldApiName(fieldApiName + D_ID);
                idField.setFieldLabel(name + SUFFIX_ID);
                idField.setFieldDefineType(ErpFieldTypeEnum.text);
                idField.setRequired(false);
                idExtend.setFieldApiName(fieldApiName + D_ID);
                idExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                //没有保存
                idExtend.setViewCode(field.getPropertyName() + D_ID);
                idExtend.setQueryCode(fieldApiName);
                idExtend.setPriority(102L + 10 * i);
                fieldMap.put(nameField.getFieldApiName(), nameField);
                fieldMap.put(numberField.getFieldApiName(), numberField);
                fieldMap.put(idField.getFieldApiName(), idField);
                fieldExtendMap.put(nameExtend.getFieldApiName(), nameExtend);
                fieldExtendMap.put(numberExtend.getFieldApiName(), numberExtend);
                fieldExtendMap.put(idExtend.getFieldApiName(), idExtend);
                continue;
            }
            //分组
            if (K3ElementTypeEnum.e33.equals(eleType)) {
                //分组 查看code加.FNumber,名称加.FName
                //名称字段
                ErpObjectFieldEntity nameField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity nameExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                nameField.setFieldApiName(fieldApiName + D_FNAME);
                nameField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1198,lang,tenantId));
                nameField.setRequired(false);
                nameField.setFieldDefineType(ErpFieldTypeEnum.text);
                nameExtend.setFieldApiName(fieldApiName + D_FNAME);
                nameExtend.setFieldDefineType(ErpFieldTypeEnum.text);
                //没有保存
                nameExtend.setViewCode(field.getPropertyName() + D_NAME);
                nameExtend.setQueryCode(fieldApiName + D_FNAME);
                nameExtend.setPriority(100L + 10 * i);
                //编码字段
                ErpObjectFieldEntity numberField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity numberExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                numberField.setFieldApiName(fieldApiName + D_FNUMBER);
                numberField.setFieldLabel(name + i18NStringManager.get(I18NStringEnum.s1199,lang,tenantId));
                numberField.setRequired(required);
                numberField.setFieldDefineType(ErpFieldTypeEnum.select_one);
                String fieldExtendValue = "[]";
                numberField.setFieldExtendValue(fieldExtendValue);
                numberExtend.setFieldApiName(fieldApiName + D_FNUMBER);
                numberExtend.setFieldDefineType(ErpFieldTypeEnum.select_one);
                numberExtend.setViewCode(field.getPropertyName() + D_NUMBER);
                numberExtend.setSaveCode(field.getKey() + D_FNUMBER);
                numberExtend.setQueryCode(fieldApiName + D_FNUMBER);
                numberExtend.setPriority(101L + 10 * i);

                //id字段 查找关联
                ErpObjectFieldEntity idField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity idExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                idField.setFieldApiName(fieldApiName + D_ID);
                idField.setFieldLabel(name + SUFFIX_ID);
                idField.setFieldDefineType(ErpFieldTypeEnum.object_reference);
                idField.setRequired(false);
                idField.setFieldExtendValue(lookUpObjectFormId);
                idExtend.setFieldApiName(fieldApiName + D_ID);
                idExtend.setFieldDefineType(ErpFieldTypeEnum.object_reference);
                //没有保存
                idExtend.setViewCode(field.getPropertyName() + D_ID);
                idExtend.setQueryCode(fieldApiName);
                idExtend.setPriority(102L + 10 * i);
                fieldMap.put(nameField.getFieldApiName(), nameField);
                fieldMap.put(numberField.getFieldApiName(), numberField);
                fieldMap.put(idField.getFieldApiName(), idField);
                fieldExtendMap.put(nameExtend.getFieldApiName(), nameExtend);
                fieldExtendMap.put(numberExtend.getFieldApiName(), numberExtend);
                fieldExtendMap.put(idExtend.getFieldApiName(), idExtend);
                continue;
            }
            //bool值,增加选项
            if (K3ElementTypeEnum.e8.equals(eleType)) {
                //bool值
                ErpObjectFieldEntity nField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
                ErpFieldExtendEntity nExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
                nField.setFieldApiName(fieldApiName);
                nField.setFieldLabel(name);
                nField.setRequired(required);
                nField.setFieldDefineType(ErpFieldTypeEnum.true_or_false);
                nField.setFieldExtendValue("[{\"label\":\"是\",\"value\":\"true\"},{\"label\":\"否\",\"value\":\"false\"}]");   // ignoreI18n   元数据
                nExtend.setFieldApiName(fieldApiName);
                nExtend.setFieldDefineType(ErpFieldTypeEnum.true_or_false);
                nExtend.setViewCode(field.getPropertyName());
                nExtend.setSaveCode(field.getKey());
                nExtend.setQueryCode(fieldApiName);
                nExtend.setPriority(100L + 10 * i);
                fieldMap.put(nField.getFieldApiName(), nField);
                fieldExtendMap.put(nExtend.getFieldApiName(), nExtend);
                continue;
            }
            //其他字段
            ErpObjectFieldEntity nField = BeanUtil.copy(baseFieldEntity, ErpObjectFieldEntity.class);
            ErpFieldExtendEntity nExtend = BeanUtil.copy(baseFieldExtend, ErpFieldExtendEntity.class);
            nField.setFieldApiName(fieldApiName);
            nField.setFieldLabel(name);
            nField.setRequired(required);
            nField.setFieldDefineType(eleType.getErpFieldType());
            nExtend.setFieldApiName(fieldApiName);
            nExtend.setFieldDefineType(eleType.getErpFieldType());
            nExtend.setViewCode(field.getPropertyName());
            nExtend.setSaveCode(field.getKey());
            nExtend.setQueryCode(fieldApiName);
            nExtend.setPriority(100L + 10L * i);
            fieldMap.put(nField.getFieldApiName(), nField);
            fieldExtendMap.put(nExtend.getFieldApiName(), nExtend);
        }
        if (entityType.equals(EntityType.SubHeadEntity)) {
            LinkedHashMap<String, ErpObjectFieldEntity> newFieldMap = new LinkedHashMap<>();
            LinkedHashMap<String, ErpFieldExtendEntity> newFieldExtendMap = new LinkedHashMap<>();
            //单行从表的viewCode和saveCode特殊
            LinkedHashMap<String, ErpFieldExtendEntity> finalFieldExtendMap = fieldExtendMap;
            fieldMap.forEach((k, v) -> {
                String newFieldApiName = entry.getKey() + "." + k;
                v.setFieldApiName(newFieldApiName);
                ErpFieldExtendEntity extend = finalFieldExtendMap.get(k);
                if (extend==null){
                    return;
                }
                extend.setFieldApiName(newFieldApiName);
                if (StringUtils.isNotBlank(extend.getViewCode())) {
                    String newViewCode = entry.getEntryName() + "[0]." + extend.getViewCode();
                    extend.setViewCode(newViewCode);
                }
                if (StringUtils.isNotBlank(extend.getSaveCode())) {
                    String newSaveCode = entry.getKey() + "." + extend.getSaveCode();
                    extend.setSaveCode(newSaveCode);
                }
                newFieldMap.put(newFieldApiName, v);
                newFieldExtendMap.put(newFieldApiName, extend);
            });
            fieldMap = newFieldMap;
            fieldExtendMap = newFieldExtendMap;
        }
        splitObjDescribe.getFieldExtends().putAll(fieldExtendMap);
        splitObjDescribe.getFields().putAll(fieldMap);
    }

    private List<String> getBillHeadObjFields(String lookUpObjectFormId, MetaDataHelper helper) {
        QueryBusinessInfoArg arg = new QueryBusinessInfoArg(lookUpObjectFormId);
        QueryBusinessInfoResult.BusinessInfo businessInfo = helper.queryBusinessInfo(arg);
        List<String> referenceObjFields = Lists.newArrayList();
        if (businessInfo != null && CollectionUtils.isNotEmpty(businessInfo.getEntrys())) {
            EntrysBean billHead = null;
            for (EntrysBean entrysBean : businessInfo.getEntrys()) {
                if ("FBillHead".equals(entrysBean.getKey())) {//主对象
                    billHead = entrysBean;
                    break;
                }
            }
            if (billHead != null && CollectionUtils.isNotEmpty(billHead.getFields())) {
                referenceObjFields.add(billHead.getEntryPkFieldName());//第一个为id字段
                List<String> collect = billHead.getFields().stream().map(EntrysBean.FieldsBean::getKey).collect(Collectors.toList());
                referenceObjFields.addAll(collect);
            }
        }
        return referenceObjFields;
    }

    private static String getI18NName(String tenantId, List<NameBean> nameBeans, String lang) {
        int lcid = 2052;
        if(StringUtils.isNotEmpty(lang)) {
            lcid = I18NStringManager.lang2lcid(lang);
        }
        int lcid2 = lcid;
        Optional<NameBean> any = nameBeans.stream().filter(v -> v.getKey() == lcid2).findAny();
        if (any.isPresent()) {
            return any.get().getValue();
        } else {
            return nameBeans.get(0).getValue();
        }
    }


    @Data
    @EqualsAndHashCode
    public static class Option implements Serializable {
        private Object value;
        private Object label;
    }

}
