package com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation;

import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * 用于监控页面，统计各种接口调用注解
 * @date 2022/12/9 11:53:35
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface InvokeMonitor {
    /**
     * 企业id
     */
    String tenantId();
    /**
     * 连接器id
     */
    String dcId();
    /**
     * erp对象apiName
     */
    String objAPIName();
    /**
     * 调用类型
     */
    InvokeTypeEnum invokeType();
    /**
     * 查询数量
     */
    String count();

    /**
     * 数据内存大小
     */
    String data() default "#result";

    /**
     * erp对象id
     * list 没有 dataId
     */
    String dataId() default "";

    /**
     * 源数据的数据事件类型 1、新增 2、修改 3、作废
     */
    String sourceEventType() default "";

    /**
     * 部分接口没有策略id
     */
    String snapshotId() default "";

    /**
     * 动作
     */
    ActionEnum action();

    /**
     * 符合条件的才保存
     */
    String condition() default "true";

    /**
     * 特征
     */
    InvokeFeature[] invokeFeatures() default {};
}
