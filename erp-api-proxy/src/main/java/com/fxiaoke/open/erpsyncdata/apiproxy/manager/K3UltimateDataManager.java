package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmObjectApiName;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.K3UltimateSpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.factory.K3UltimateSpecialBusinessFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.utils.K3UltimateUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateApiTemplate;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateExecuteStepEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateRequestByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseBySave2;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.CommonIdQueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.K3UltimateQueryBaseArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateConfigModel;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 云星空旗舰版连接器
 *
 * <AUTHOR>
 * @date 2023-09-11
 */

@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.ERP_K3CLOUD_ULTIMATE)
public class K3UltimateDataManager extends BaseErpDataManager {
    @Autowired
    private K3UltimateApiService k3UltimateApiService;
    @Autowired
    private K3UltimateApiTemplateManager k3UltimateApiTemplateManager;
    @Autowired
    private K3UltimateConverter k3UltimateConverter;
    @Autowired
    private K3UltimateSpecialBusinessFactory k3UltimateSpecialBusinessFactory;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private MetadataControllerService metadataControllerService;

    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg,
                                                         ErpConnectInfoEntity connectInfo) {
        String tenantId = timeFilterArg.getTenantId();
        String objApiName = timeFilterArg.getObjAPIName();
        String dataCenterId = connectInfo.getId();

        K3UltimateSpecialBusiness specialBusiness = k3UltimateSpecialBusinessFactory.getHandlerByName(objApiName);

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        if (StringUtils.equalsIgnoreCase(timeFilterArg.getObjAPIName(), K3UltimateObjApiName.bd_customer_contacts)) {
            objApiName = K3UltimateObjApiName.bd_customer;
        }

        Result<K3UltimateResponseByQuery> result = k3UltimateApiService.batchQuery(tenantId,
                dataCenterId,
                objApiName,
                buildPollingQueryArg(timeFilterArg, dataCenterId),
                timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType());
        if (!result.isSuccess()) {
            return Result.copy(result);
        }

        if (StringUtils.equalsIgnoreCase(timeFilterArg.getObjAPIName(), K3UltimateObjApiName.bd_customer_contacts)) {
            objApiName = K3UltimateObjApiName.bd_customer_contacts;
        }

        List<ErpObjectEntity> erpObjectEntityList = erpObjManager.getDetailErpObjEntityList(tenantId, dataCenterId, objApiName);

        StandardListData standardListData = new StandardListData();
        standardListData.setTotalNum(result.getData().getData().getTotalCount());
        for (JSONObject row : result.getData().getData().getRows()) {
            StandardData standardData = getStandardData(row, objApiName, erpObjectEntityList);
            standardListData.getDataList().add(standardData);
        }

        specialBusiness.afterListErpObjDataByTime(tenantId,
                dataCenterId,
                objApiName,
                timeFilterArg,
                standardListData.getDataList(),
                k3UltimateApiService);

        standardListData.setTotalNum(standardListData.getDataList().size());

        return Result.newSuccess(standardListData);
    }

    private StandardData getStandardData(JSONObject erpSrcData, String objApiName, List<ErpObjectEntity> erpObjectEntityList) {
        StandardData standardData = new StandardData();
        standardData.setObjAPIName(objApiName);
        standardData.setMasterFieldVal(ObjectData.convert(erpSrcData));

        if (CollectionUtils.isNotEmpty(erpObjectEntityList)) {
            fillDetailData(standardData, erpSrcData, erpObjectEntityList);
        }

        return standardData;
    }

    private void fillDetailData(StandardData standardData, JSONObject erpSrcData, List<ErpObjectEntity> erpObjectEntityList) {
        for (ErpObjectEntity entity : erpObjectEntityList) {
            if (StringUtils.isEmpty(entity.getErpObjectExtendValue())) continue;
            String detailObjApiName = entity.getErpObjectExtendValue();
            if (!standardData.getDetailFieldVals().containsKey(detailObjApiName)) {
                standardData.getDetailFieldVals().put(detailObjApiName, new ArrayList<>());
            }

            JSONArray detailDataArray = erpSrcData.getJSONArray(detailObjApiName);
            if (detailDataArray == null || detailDataArray.isEmpty()) continue;
            for (int i = 0; i < detailDataArray.size(); i++) {
                ObjectData detailData = ObjectData.convert(detailDataArray.getJSONObject(i));
                detailData.putApiName(detailObjApiName);
                standardData.getDetailFieldVals().get(detailObjApiName).add(detailData);
            }
        }
    }

    private String getFormattedDateTime(Long timestamp) {
        return DateFormatUtils.format(timestamp, "yyyy-MM-dd HH:mm:ss");
    }

    private K3UltimateRequestByQuery<K3UltimateQueryBaseArg> buildPollingQueryArg(TimeFilterArg timeFilterArg, String dataCenterId) {
        String tenantId = timeFilterArg.getTenantId();
        String objApiName = timeFilterArg.getObjAPIName();

        K3UltimateQueryBaseArg queryBaseArg = new K3UltimateQueryBaseArg();
        queryBaseArg.setStart_modifytime(getFormattedDateTime(timeFilterArg.getStartTime()));
        queryBaseArg.setEnd_modifytime(getFormattedDateTime(timeFilterArg.getEndTime()));
        queryBaseArg.setEnable(timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType() ? "0" : "1");

        if (CollectionUtils.isNotEmpty(timeFilterArg.getFilters())) {
            for (List<FilterData> filterDataList : timeFilterArg.getFilters()) {
                for (FilterData filterData : filterDataList) {
                    if (CollectionUtils.isNotEmpty(filterData.getFieldValue())) {
                        queryBaseArg.put(filterData.getFieldApiName(), filterData.getFieldValue().get(0));
                    }
                }
            }
        }
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        if(timeFilterArg.getOperationType()!=null&& EventTypeEnum.INVALID.getType()==timeFilterArg.getOperationType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
        }
        int pageNo = (timeFilterArg.getOffset() / timeFilterArg.getLimit()) + 1 ;
        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> query = K3UltimateRequestByQuery.buildBaseRequest(tenantId,
                dataCenterId,
                objApiName,
                type,
                queryBaseArg,
                pageNo,
                timeFilterArg.getLimit(),
                tenantConfigurationManager);
        return query;
    }

    private K3UltimateRequestByQuery<K3UltimateQueryBaseArg> buildGetByIdQueryArg(String tenantId,
                                                                                  String dataCenterId,
                                                                                  String objApiName,
                                                                                  String dataId) {
        ErpObjectFieldEntity idField = erpFieldManager.findMasterIdField(tenantId, dataCenterId, objApiName);
        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> query = null;
        if (StringUtils.equalsIgnoreCase(idField.getFieldApiName(), "id")) {
            query = K3UltimateRequestByQuery.buildBaseRequestById(tenantId,
                    dataCenterId,
                    objApiName,
                    ErpObjInterfaceUrlEnum.queryMasterById,
                    Lists.newArrayList(dataId),
                    null,
                    1,
                    1,
                    tenantConfigurationManager);
        } else {
            query = K3UltimateRequestByQuery.buildBaseRequestByNumber(tenantId,
                    dataCenterId,
                    objApiName,
                    ErpObjInterfaceUrlEnum.queryMasterById,
                    Lists.newArrayList(dataId),
                    null,
                    1,
                    1,
                    tenantConfigurationManager);
        }

        return query;
    }

    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg,
                                              ErpConnectInfoEntity connectInfo) {
        String tenantId = erpIdArg.getTenantId();
        String objApiName = erpIdArg.getObjAPIName();
        String dataCenterId = connectInfo.getId();

        K3UltimateSpecialBusiness specialBusiness = k3UltimateSpecialBusinessFactory.getHandlerByName(objApiName);

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        String dataId = erpIdArg.getDataId();
        if (StringUtils.equalsIgnoreCase(erpIdArg.getObjAPIName(), K3UltimateObjApiName.bd_customer_contacts)) {
            objApiName = K3UltimateObjApiName.bd_customer;
            ErpObjectFieldEntity contactsIdField = erpFieldManager.findMasterIdField(erpIdArg.getTenantId(),
                    dataCenterId,
                    erpIdArg.getObjAPIName());
            SyncDataMappingsEntity contactsMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId,
                    contactsIdField.getErpObjectApiName(),
                    CrmObjectApiName.CONTACT_API_NAME,
                    erpIdArg.getDataId());
            com.fxiaoke.crmrestapi.common.data.ObjectData objectData = K3UltimateUtils.getCrmObjDetail(tenantId,
                    CrmObjectApiName.CONTACT_API_NAME,
                    contactsMapping.getDestDataId(),
                    metadataControllerService);
            String account_id = objectData.getString("account_id");

            ErpObjectFieldEntity customerIdField = erpFieldManager.findMasterIdField(erpIdArg.getTenantId(),
                    dataCenterId,
                    K3UltimateObjApiName.bd_customer);
            Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = syncDataMappingsManager.getMapping2Way(tenantId,
                    CrmObjectApiName.ACCOUNT_API_NAME,
                    account_id,
                    customerIdField.getErpObjectApiName());
            if(mapping2Way.getLeft()!=null) {
                dataId = mapping2Way.getLeft().getDestDataId();
            } else {
                if(mapping2Way.getRight()!=null) {
                    dataId = mapping2Way.getRight().getSourceDataId();
                }
            }
        }

        Result<K3UltimateResponseByQuery> result = k3UltimateApiService.batchQuery(tenantId,dataCenterId,objApiName,
                buildGetByIdQueryArg(tenantId, dataCenterId, objApiName, dataId), false);
        if (!result.isSuccess()) {
            return Result.copy(result);
        }

        if (result.getData().getData().getTotalCount() == 0) {
            return Result.newError(ResultCodeEnum.UNABLE_TO_FIND_DATA);
        }

        JSONObject masterData = result.getData().getData().getRows().get(0);

        List<ErpObjectEntity> erpObjectEntityList = erpObjManager.getDetailErpObjEntityList(tenantId, dataCenterId, objApiName);
        StandardData standardData = getStandardData(masterData, objApiName, erpObjectEntityList);

        if (StringUtils.equalsIgnoreCase(erpIdArg.getObjAPIName(), K3UltimateObjApiName.bd_customer_contacts)) {
            objApiName = K3UltimateObjApiName.bd_customer_contacts;
        }

        specialBusiness.afterGetErpObjData(tenantId,
                dataCenterId,
                objApiName,
                erpIdArg,
                standardData,
                k3UltimateApiService);

        return Result.newSuccess(standardData);
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg,
                                                           ErpConnectInfoEntity connectInfo) {
        String tenantId = erpIdArg.getTenantId();
        String objApiName = erpIdArg.getObjAPIName();
        String dataCenterId = connectInfo.getId();

        Result<StandardData> erpObjDataResult = this.getErpObjData(erpIdArg, connectInfo);
        if (erpObjDataResult != null) {
            K3UltimateSpecialBusiness specialBusiness = k3UltimateSpecialBusinessFactory.getHandlerByName(objApiName);
            List<StandardData> standardDataList = Lists.newArrayList();
            Result<List<StandardData>> dataListResult = new Result<>();
            if (erpObjDataResult.getData() != null) {
                standardDataList.add(erpObjDataResult.getData());
                specialBusiness.afterReSyncDataById(tenantId,
                        dataCenterId,
                        objApiName,
                        erpIdArg,
                        standardDataList,
                        k3UltimateApiService);
            }
            dataListResult.setErrCode(erpObjDataResult.getErrCode());
            dataListResult.setErrMsg(erpObjDataResult.getErrMsg());
            dataListResult.setData(standardDataList);
            return dataListResult;
        }
        return null;
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData,
                                                ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getObjAPIName();
        String dataCenterId = connectInfo.getId();

//        if(StringUtils.equalsIgnoreCase(objApiName,K3UltimateObjApiName.sm_xssalorder)) {
//            return createXSSaleOrderObjData(standardData, connectInfo);
//        }

        K3UltimateApiTemplate apiTemplate = k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName);

        //联系人特殊逻辑，旗舰版联系人不是独立的对象，所以，联系人的新增等于更新
        if (StringUtils.equalsIgnoreCase(objApiName, K3UltimateObjApiName.bd_customer_contacts)) {
            return updateErpObjData(standardData, connectInfo);
        }

        //创建时移除Id字段
        standardData.removeId();

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        //对明细拼接字段进行转换
        k3UltimateConverter.convertDetailData(standardData);

        //组装ERP请求数据
        Result<JSONObject> requestData = k3UltimateConverter.assemblySaveRequestData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                k3UltimateApiService,
                false);
        if (!requestData.isSuccess()) {
            return Result.copy(requestData);
        }

        K3UltimateSpecialBusiness specialBusiness = k3UltimateSpecialBusinessFactory.getHandlerByName(objApiName);
        specialBusiness.beforeCreateErpObjData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                requestData.getData(),
                k3UltimateApiService);

        Result<K3UltimateResponseBySave2> batchAdd = k3UltimateApiService.batchAdd(tenantId,
                dataCenterId,
                objApiName,
                Lists.newArrayList(requestData.getData()));
        if (!batchAdd.isSuccess()) {
            return Result.copy(batchAdd);
        }

        String masterId = batchAdd.getData().getData().getResult().get(0).getId();

        K3UltimateConfigModel.CreateErpObjConfig createErpObjConfig = tenantConfigurationManager.getK3UltimateCreateErpObjConfig(tenantId,
                dataCenterId,
                objApiName);
        if (createErpObjConfig != null) {
            if (createErpObjConfig.isAutoSubmit()) {
                if (StringUtils.isEmpty(apiTemplate.getBatchSubmitApi())) {
                    return Result.newError(ResultCodeEnum.K3_ULTIMATE_SUBMIT_API_NOT_CONFIG);
                }
                Result<K3UltimateResponseBySave2> batchSubmit = k3UltimateApiService.batchSubmit(tenantId,
                        dataCenterId,
                        objApiName,
                        Lists.newArrayList(masterId));
                if (!batchSubmit.isSuccess()) {
                    if (createErpObjConfig.isDeleteBillWhenSubmitOrAuditFailed()) {
                        if (StringUtils.isEmpty(apiTemplate.getBatchDeleteApi())) {
                            return Result.newError(ResultCodeEnum.K3_ULTIMATE_DELETE_API_NOT_CONFIG);
                        }
                        Result<K3UltimateResponseBySave2> batchDelete = k3UltimateApiService.batchDelete(tenantId,
                                dataCenterId,
                                objApiName,
                                Lists.newArrayList(masterId));
                        if (!batchDelete.isSuccess()) {
                            return Result.newError(batchDelete.getErrCode(), batchDelete.getErrMsg() + "\n提交失败，删除新建的单据失败");
                        }
                    }
                    return Result.copy(batchSubmit);
                }

                if (createErpObjConfig.isAutoAudit()) {
                    if (StringUtils.isEmpty(apiTemplate.getBatchAuditApi())) {
                        return Result.newError(ResultCodeEnum.K3_ULTIMATE_AUDIT_API_NOT_CONFIG);
                    }
                    Result<K3UltimateResponseBySave2> batchAudit = k3UltimateApiService.batchAudit(tenantId,
                            dataCenterId,
                            objApiName,
                            Lists.newArrayList(masterId));
                    if (!batchAudit.isSuccess()) {
                        if (createErpObjConfig.isDeleteBillWhenSubmitOrAuditFailed()) {
                            if (StringUtils.isEmpty(apiTemplate.getBatchUnSubmitApi())) {
                                return Result.newError(ResultCodeEnum.K3_ULTIMATE_UN_SUBMIT_API_NOT_CONFIG);
                            }
                            Result<K3UltimateResponseBySave2> batchUnSubmit = k3UltimateApiService.batchUnSubmit(tenantId,
                                    dataCenterId,
                                    objApiName,
                                    Lists.newArrayList(masterId));
                            if (!batchUnSubmit.isSuccess()) {
                                return Result.newError(batchUnSubmit.getErrCode(), batchUnSubmit.getErrMsg() + "\n审核失败，撤销提交失败");
                            }
                            if (StringUtils.isEmpty(apiTemplate.getBatchDeleteApi())) {
                                return Result.newError(ResultCodeEnum.K3_ULTIMATE_DELETE_API_NOT_CONFIG);
                            }
                            Result<K3UltimateResponseBySave2> batchDelete = k3UltimateApiService.batchDelete(tenantId,
                                    dataCenterId,
                                    objApiName,
                                    Lists.newArrayList(masterId));
                            if (!batchDelete.isSuccess()) {
                                return Result.newError(batchDelete.getErrCode(), batchDelete.getErrMsg() + "\n审核失败，删除新建的单据失败");
                            }
                        }
                        return Result.copy(batchAudit);
                    }
                }
            }
        } else {
            Result<ErpIdResult> result = batchSubmitAndAudit(tenantId, dataCenterId, objApiName, masterId);
            if (!result.isSuccess()) {
                return result;
            }
        }

        ErpIdResult erpIdResult = getErpIdResult(tenantId, dataCenterId, objApiName, batchAdd);
        Result<ErpIdResult> result = Result.newSuccess(erpIdResult);

        specialBusiness.afterCreateErpObjData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                requestData.getData(),
                result,
                k3UltimateApiService);

        return result;
    }

    public Result<ErpIdResult> createXSSaleOrderObjData(StandardData standardData,
                                                        ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getObjAPIName();
        String dataCenterId = connectInfo.getId();
        String orderId = standardData.getMasterFieldVal().getString("order_id");
        String orderNumber = standardData.getMasterFieldVal().getString("order_number");

        K3UltimateApiTemplate apiTemplate = k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName);

        //联系人特殊逻辑，旗舰版联系人不是独立的对象，所以，联系人的新增等于更新
        if (StringUtils.equalsIgnoreCase(objApiName, K3UltimateObjApiName.bd_customer_contacts)) {
            return updateErpObjData(standardData, connectInfo);
        }

        //创建时移除Id字段
        standardData.removeId();

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        //对明细拼接字段进行转换
        k3UltimateConverter.convertDetailData(standardData);

        //组装ERP请求数据
        Result<JSONObject> requestData = k3UltimateConverter.assemblySaveRequestData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                k3UltimateApiService,
                false);
        if (!requestData.isSuccess()) {
            return Result.copy(requestData);
        }

        K3UltimateSpecialBusiness specialBusiness = k3UltimateSpecialBusinessFactory.getHandlerByName(objApiName);
        specialBusiness.beforeCreateErpObjData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                requestData.getData(),
                k3UltimateApiService);

        //订单变更，生成订单变更单
        Result<K3UltimateResponseBySave2> bizChange = k3UltimateApiService.bizChange(tenantId,
                dataCenterId,
                K3UltimateObjApiName.sm_salorder,
                orderId,
                ErpObjInterfaceUrlEnum.createXOrder);
        if (!bizChange.isSuccess()) {
            return Result.copy(bizChange);
        }

        //查询生成的订单变更单
        Result<JSONObject> queryResult = K3UltimateUtils.queryXSOrderDataByOrderBillNo(tenantId,
                dataCenterId,
                objApiName,
                orderNumber,
                k3UltimateApiService);

        String masterId = bizChange.getData().getData().getResult().get(0).getId();

        K3UltimateConfigModel.CreateErpObjConfig createErpObjConfig = tenantConfigurationManager.getK3UltimateCreateErpObjConfig(tenantId,
                dataCenterId,
                objApiName);
        if (createErpObjConfig != null) {
            if (createErpObjConfig.isAutoSubmit()) {
                if (StringUtils.isEmpty(apiTemplate.getBatchSubmitApi())) {
                    return Result.newError(ResultCodeEnum.K3_ULTIMATE_SUBMIT_API_NOT_CONFIG);
                }
                Result<K3UltimateResponseBySave2> batchSubmit = k3UltimateApiService.batchSubmit(tenantId,
                        dataCenterId,
                        objApiName,
                        Lists.newArrayList(masterId));
                if (!batchSubmit.isSuccess()) {
                    if (createErpObjConfig.isDeleteBillWhenSubmitOrAuditFailed()) {
                        if (StringUtils.isEmpty(apiTemplate.getBatchDeleteApi())) {
                            return Result.newError(ResultCodeEnum.K3_ULTIMATE_DELETE_API_NOT_CONFIG);
                        }
                        Result<K3UltimateResponseBySave2> batchDelete = k3UltimateApiService.batchDelete(tenantId,
                                dataCenterId,
                                objApiName,
                                Lists.newArrayList(masterId));
                        if (!batchDelete.isSuccess()) {
                            return Result.newError(batchDelete.getErrCode(), batchDelete.getErrMsg() + "\n提交失败，删除新建的单据失败");
                        }
                    }
                    return Result.copy(batchSubmit);
                }

                if (createErpObjConfig.isAutoAudit()) {
                    if (StringUtils.isEmpty(apiTemplate.getBatchAuditApi())) {
                        return Result.newError(ResultCodeEnum.K3_ULTIMATE_AUDIT_API_NOT_CONFIG);
                    }
                    Result<K3UltimateResponseBySave2> batchAudit = k3UltimateApiService.batchAudit(tenantId,
                            dataCenterId,
                            objApiName,
                            Lists.newArrayList(masterId));
                    if (!batchAudit.isSuccess()) {
                        if (createErpObjConfig.isDeleteBillWhenSubmitOrAuditFailed()) {
                            if (StringUtils.isEmpty(apiTemplate.getBatchUnSubmitApi())) {
                                return Result.newError(ResultCodeEnum.K3_ULTIMATE_UN_SUBMIT_API_NOT_CONFIG);
                            }
                            Result<K3UltimateResponseBySave2> batchUnSubmit = k3UltimateApiService.batchUnSubmit(tenantId,
                                    dataCenterId,
                                    objApiName,
                                    Lists.newArrayList(masterId));
                            if (!batchUnSubmit.isSuccess()) {
                                return Result.newError(batchUnSubmit.getErrCode(), batchUnSubmit.getErrMsg() + "\n审核失败，撤销提交失败");
                            }
                            if (StringUtils.isEmpty(apiTemplate.getBatchDeleteApi())) {
                                return Result.newError(ResultCodeEnum.K3_ULTIMATE_DELETE_API_NOT_CONFIG);
                            }
                            Result<K3UltimateResponseBySave2> batchDelete = k3UltimateApiService.batchDelete(tenantId,
                                    dataCenterId,
                                    objApiName,
                                    Lists.newArrayList(masterId));
                            if (!batchDelete.isSuccess()) {
                                return Result.newError(batchDelete.getErrCode(), batchDelete.getErrMsg() + "\n审核失败，删除新建的单据失败");
                            }
                        }
                        return Result.copy(batchAudit);
                    }
                }
            }
        } else {
            Result<ErpIdResult> result = batchSubmitAndAudit(tenantId, dataCenterId, objApiName, masterId);
            if (!result.isSuccess()) {
                return result;
            }
        }

        ErpIdResult erpIdResult = getErpIdResult(tenantId, dataCenterId, objApiName, bizChange);
        Result<ErpIdResult> result = Result.newSuccess(erpIdResult);

        specialBusiness.afterCreateErpObjData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                requestData.getData(),
                result,
                k3UltimateApiService);

        return result;
    }

    private Result<ErpIdResult> batchSubmitAndAudit(String tenantId,
                                                    String dataCenterId,
                                                    String objApiName,
                                                    String masterId) {
        K3UltimateApiTemplate apiTemplate = k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName);
        if (apiTemplate == null)
            return Result.newSuccess();

        if (StringUtils.isNotEmpty(apiTemplate.getBatchSubmitApi())) {
            Result<K3UltimateResponseBySave2> batchSubmit = k3UltimateApiService.batchSubmit(tenantId,
                    dataCenterId,
                    objApiName,
                    Lists.newArrayList(masterId));
            if (!batchSubmit.isSuccess()) {
                return Result.copy(batchSubmit);
            }
        }

        if (StringUtils.isNotEmpty(apiTemplate.getBatchAuditApi())) {
            Result<K3UltimateResponseBySave2> batchAudit = k3UltimateApiService.batchAudit(tenantId,
                    dataCenterId,
                    objApiName,
                    Lists.newArrayList(masterId));
            if (!batchAudit.isSuccess()) {
                return Result.copy(batchAudit);
            }
        }

        return Result.newSuccess();
    }

    private ErpIdResult getErpIdResult(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       Result<K3UltimateResponseBySave2> batchSaveResult) {
        K3UltimateResponseBySave2.ResultModel resultModel = batchSaveResult.getData().getData().getResult().get(0);
        String masterId = resultModel.getId();
        String masterNumber = resultModel.getNumber();

        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> query = K3UltimateRequestByQuery.buildBaseRequestById(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.queryMasterById,
                Lists.newArrayList(masterId),
                null,
                1,
                500,
                tenantConfigurationManager);

        Result<K3UltimateResponseByQuery> queryResult = k3UltimateApiService.batchQuery(tenantId,dataCenterId,objApiName, query, false);

        ErpIdResult erpIdResult = new ErpIdResult();

        ErpObjectFieldEntity idField = erpFieldManager.findMasterIdField(tenantId, dataCenterId, objApiName);
        if (StringUtils.equalsIgnoreCase(idField.getFieldApiName(), "id")) {
            erpIdResult.setMasterDataId(masterId);
            erpIdResult.setMasterDataName(masterNumber);
        } else {
            erpIdResult.setMasterDataId(masterNumber);
            erpIdResult.setMasterDataName(masterNumber);
        }

        List<ErpObjectEntity> erpObjectEntityList = erpObjManager.getDetailErpObjEntityList(tenantId, dataCenterId, objApiName);

        for (JSONObject masterData : queryResult.getData().getData().getRows()) {
            for (ErpObjectEntity entity : erpObjectEntityList) {
                String detailApiName = entity.getErpObjectExtendValue();
                if (!erpIdResult.getDetailDataIds().containsKey(detailApiName)) {
                    erpIdResult.getDetailDataIds().put(detailApiName, new ArrayList<>());
                }
                JSONArray detailDataArray = masterData.getJSONArray(detailApiName);
                if (detailDataArray == null || detailDataArray.isEmpty()) continue;

                ErpObjectFieldEntity detailIdField = erpFieldManager.findIdField(tenantId, entity.getErpObjectApiName());

                for (int i = 0; i < detailDataArray.size(); i++) {
                    String detailId = detailDataArray.getJSONObject(i).getString(detailIdField.getFieldApiName());
                    erpIdResult.getDetailDataIds().get(detailApiName).add(detailId);
                }
            }
        }

        return erpIdResult;
    }

    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        log.info("K3UltimateDataManager.createErpObjDetailData,doWriteMqData={},standardDetailData={}", doWriteMqData, standardDetailData);
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData,
                                                ErpConnectInfoEntity connectInfo) {
        log.info("K3UltimateDataManager.updateErpObjData,standardData={}", standardData);
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardData.getObjAPIName();
        String dataCenterId = connectInfo.getId();

        K3UltimateBaseApiTemplate apiTemplate = k3UltimateApiTemplateManager.getApiTemplate(tenantId,dataCenterId,objApiName);

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        //对明细拼接字段进行转换
        k3UltimateConverter.convertDetailData(standardData);

        //组装ERP请求数据
        Result<JSONObject> requestData = k3UltimateConverter.assemblySaveRequestData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                k3UltimateApiService,
                true);
        if (!requestData.isSuccess()) {
            return Result.copy(requestData);
        }

        K3UltimateSpecialBusiness specialBusiness = k3UltimateSpecialBusinessFactory.getHandlerByName(objApiName);
        specialBusiness.beforeUpdateErpObjData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                requestData.getData(),
                k3UltimateApiService);
        log.info("K3UltimateDataManager.updateErpObjData,standardData2={}", standardData);

        String masterId = standardData.getMasterFieldVal().getString("id");

        if (StringUtils.equalsIgnoreCase(objApiName, K3UltimateObjApiName.bd_customer_contacts)) {
            masterId = requestData.getData().getString("id");//客户ID
            objApiName = K3UltimateObjApiName.bd_customer;
        }

        if (StringUtils.isNotEmpty(apiTemplate.getBatchUnAuditApi()) && apiTemplate.getUpdateExecuteStep()!= K3UltimateExecuteStepEnum.UPDATE) {
            //先反审核，才能更新单据
            Result<K3UltimateResponseBySave2> batchUnAudit = k3UltimateApiService.batchUnAudit(tenantId,
                    dataCenterId,
                    objApiName,
                    Lists.newArrayList(masterId));
            //603=请先提交审核
            if (!batchUnAudit.isSuccess() && !StringUtils.equalsIgnoreCase(batchUnAudit.getErrCode(), "603")) {
                return Result.copy(batchUnAudit);
            }
        }

        Result<K3UltimateResponseBySave2> batchUpdate = k3UltimateApiService.batchUpdate(tenantId,
                dataCenterId,
                objApiName,
                Lists.newArrayList(requestData.getData()));
        if (!batchUpdate.isSuccess()) {
            return Result.copy(batchUpdate);
        }

        if (apiTemplate.getUpdateExecuteStep()!= K3UltimateExecuteStepEnum.UPDATE) {
            K3UltimateConfigModel.UpdateErpObjConfig updateErpObjConfig = tenantConfigurationManager.getK3UltimateUpdateErpObjConfig(tenantId,
                    dataCenterId,
                    objApiName);
            if (updateErpObjConfig != null) {
                if (updateErpObjConfig.isAutoSubmit()) {
                    if (StringUtils.isEmpty(apiTemplate.getBatchSubmitApi())) {
                        return Result.newError(ResultCodeEnum.K3_ULTIMATE_SUBMIT_API_NOT_CONFIG);
                    }
                    Result<K3UltimateResponseBySave2> batchSubmit = k3UltimateApiService.batchSubmit(tenantId,
                            dataCenterId,
                            objApiName,
                            Lists.newArrayList(masterId));
                    if (!batchSubmit.isSuccess()) {
                        return Result.copy(batchSubmit);
                    }

                    if (updateErpObjConfig.isAutoAudit()) {
                        if (StringUtils.isEmpty(apiTemplate.getBatchAuditApi())) {
                            return Result.newError(ResultCodeEnum.K3_ULTIMATE_AUDIT_API_NOT_CONFIG);
                        }
                        Result<K3UltimateResponseBySave2> batchAudit = k3UltimateApiService.batchAudit(tenantId,
                                dataCenterId,
                                objApiName,
                                Lists.newArrayList(masterId));
                        if (!batchAudit.isSuccess()) {
                            return Result.copy(batchAudit);
                        }
                    }
                }
            } else {
                Result<ErpIdResult> result = batchSubmitAndAudit(tenantId, dataCenterId, objApiName, masterId);
                if (!result.isSuccess()) {
                    return result;
                }
            }
        }

        ErpIdResult erpIdResult = getErpIdResult(tenantId, dataCenterId, objApiName, batchUpdate);
        Result<ErpIdResult> result = Result.newSuccess(erpIdResult);

        specialBusiness.afterUpdateErpObjData(tenantId,
                dataCenterId,
                objApiName,
                standardData,
                requestData.getData(),
                result,
                k3UltimateApiService);

        return result;
    }

    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        log.info("K3UltimateDataManager.updateErpObjDetailData,doWriteMqData={},standardDetailData={}", doWriteMqData, standardDetailData);
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**
     * 作废ERP单据，就是禁用ERP单据
     *
     * @param standardInvalidData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData,
                                            ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardInvalidData.getObjAPIName();
        String dataCenterId = connectInfo.getId();

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        return updateBillStatus(tenantId,
                dataCenterId,
                objApiName,
                standardInvalidData.getMasterFieldVal().getId(),
                false);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData,
                                                  ErpConnectInfoEntity connectInfo) {
        log.info("K3UltimateDataManager.deleteErpObjData,standardInvalidData={}", standardDeleteData);
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData,
                                                  ErpConnectInfoEntity connectInfo) {
        log.info("K3UltimateDataManager.invalidErpObjDetailData,standardInvalidData={}", standardInvalidData);
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**
     * 恢复ERP单据，就是启用ERP单据或反禁用ERP单据
     *
     * @param standardRecoverData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData,
                                            ErpConnectInfoEntity connectInfo) {
        String tenantId = connectInfo.getTenantId();
        String objApiName = standardRecoverData.getObjAPIName();
        String dataCenterId = connectInfo.getId();

        k3UltimateApiService.setErpConnectInfo(connectInfo);

        return updateBillStatus(tenantId,
                dataCenterId,
                objApiName,
                standardRecoverData.getMasterFieldVal().getId(),
                true);
    }

    /**
     * 更新单据的禁用或启用状态
     *
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param dataId
     * @param enable
     * @return
     */
    private Result<String> updateBillStatus(String tenantId,
                                            String dataCenterId,
                                            String objApiName,
                                            String dataId,
                                            boolean enable) {
        CommonIdQueryArg commonIdQueryArg = new CommonIdQueryArg();
        commonIdQueryArg.setId(Lists.newArrayList(dataId));

        Result<K3UltimateResponseBySave2> result = null;
        if (enable) {
            result = k3UltimateApiService.batchEnable(tenantId,
                    dataCenterId,
                    objApiName,
                    Lists.newArrayList(dataId));
        } else {
            result = k3UltimateApiService.batchDisable(tenantId,
                    dataCenterId,
                    objApiName,
                    Lists.newArrayList(dataId));
        }
        if (!result.isSuccess()) {
            return Result.copy(result);
        }

        return Result.newSuccess();
    }
}
