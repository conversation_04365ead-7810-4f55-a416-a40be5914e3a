package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.utils.K3UltimateUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateRequestByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseBySave2;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.K3UltimateQueryBaseArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component(K3UltimateObjApiName.bd_customer)
public class K3UltimateCustomerSpecialBusiness extends K3UltimateBaseSpecialBusiness {
    //客户地址对象编码
    private static final String ENTRY_ADDRESS = "entry_address";

    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Override
    public void afterListErpObjDataByTime(String tenantId,
                                          String dataCenterId,
                                          String objApiName,
                                          TimeFilterArg timeFilterArg,
                                          List<StandardData> standardDataList,
                                          K3UltimateApiService k3UltimateApiService) {
        super.afterListErpObjDataByTime(tenantId, dataCenterId, objApiName, timeFilterArg, standardDataList, k3UltimateApiService);
        if (CollectionUtils.isEmpty(standardDataList)) return;

        ErpObjectEntity detailEntity = erpObjManager.getDetailEntity(tenantId, dataCenterId, objApiName, ENTRY_ADDRESS);
        if (detailEntity == null) return;
        fillCustomerAddressData(tenantId, dataCenterId, objApiName, standardDataList, k3UltimateApiService);
    }

    private Result<K3UltimateResponseByQuery> getCustomerAddressData(String tenantId,
                                                                     String dataCenterId,
                                                                     String customerId,
                                                                     K3UltimateApiService k3UltimateApiService) {
        K3UltimateQueryBaseArg baseArg = new K3UltimateQueryBaseArg();
        baseArg.put("customerid", customerId);
        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> request = K3UltimateRequestByQuery.buildBaseRequest(tenantId,
                dataCenterId,
                K3UltimateObjApiName.bd_address,
                ErpObjInterfaceUrlEnum.queryMasterById,
                baseArg,
                1,
                100,
                tenantConfigurationManager);

        Result<K3UltimateResponseByQuery> batchQuery = k3UltimateApiService.batchQuery(tenantId,
                dataCenterId,
                K3UltimateObjApiName.bd_address,
                request,
                false);
        return batchQuery;
    }

    /**
     * 填充客户地址数据
     *
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param standardDataList
     * @param k3UltimateApiService
     */
    private void fillCustomerAddressData(String tenantId,
                                         String dataCenterId,
                                         String objApiName,
                                         List<StandardData> standardDataList,
                                         K3UltimateApiService k3UltimateApiService) {
        for (StandardData standardData : standardDataList) {
            String customerId = standardData.getMasterFieldVal().getString("id");

            Result<K3UltimateResponseByQuery> batchQuery = getCustomerAddressData(tenantId,
                    dataCenterId,
                    customerId,
                    k3UltimateApiService);
            if (!batchQuery.isSuccess())
                throw new ErpSyncDataException(batchQuery.getErrCode(), batchQuery.getErrMsg(),tenantId);
            if (batchQuery.getData().getData().getTotalCount() == 0) continue;
            List<ObjectData> addressDataList = new ArrayList<>();
            for (JSONObject addressData : batchQuery.getData().getData().getRows()) {
                addressDataList.add(ObjectData.convert(addressData));
            }
            standardData.getDetailFieldVals().put(ENTRY_ADDRESS, addressDataList);
        }
    }

    @Override
    public void afterGetErpObjData(String tenantId,
                                   String dataCenterId,
                                   String objApiName,
                                   ErpIdArg erpIdArg,
                                   StandardData standardData,
                                   K3UltimateApiService k3UltimateApiService) {
        super.afterGetErpObjData(tenantId, dataCenterId, objApiName, erpIdArg, standardData, k3UltimateApiService);
        fillCustomerAddressData(tenantId, dataCenterId, objApiName, Lists.newArrayList(standardData), k3UltimateApiService);
    }

    @Override
    public void afterReSyncDataById(String tenantId,
                                    String dataCenterId,
                                    String objApiName,
                                    ErpIdArg erpIdArg,
                                    List<StandardData> standardDataList,
                                    K3UltimateApiService k3UltimateApiService) {
        super.afterReSyncDataById(tenantId, dataCenterId, objApiName, erpIdArg, standardDataList, k3UltimateApiService);
    }

    @Override
    public void beforeCreateErpObjData(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       StandardData standardData,
                                       JSONObject requestData,
                                       K3UltimateApiService k3UltimateApiService) {
        super.beforeCreateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, k3UltimateApiService);
    }

    @Override
    public void afterCreateErpObjData(String tenantId,
                                      String dataCenterId,
                                      String objApiName,
                                      StandardData standardData,
                                      JSONObject requestData,
                                      Result<ErpIdResult> result,
                                      K3UltimateApiService k3UltimateApiService) {
        super.afterCreateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, result, k3UltimateApiService);
    }

    @Override
    public void beforeUpdateErpObjData(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       StandardData standardData,
                                       JSONObject requestData,
                                       K3UltimateApiService k3UltimateApiService) {
        super.beforeUpdateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, k3UltimateApiService);
    }

    @Override
    public void afterUpdateErpObjData(String tenantId,
                                      String dataCenterId,
                                      String objApiName,
                                      StandardData standardData,
                                      JSONObject requestData,
                                      Result<ErpIdResult> result,
                                      K3UltimateApiService k3UltimateApiService) {
        super.afterUpdateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, result, k3UltimateApiService);
        JSONArray addressDataList = requestData.getJSONArray(ENTRY_ADDRESS);
        if (CollectionUtils.isEmpty(addressDataList)) return;
        String customerId = requestData.getString("id");

        ErpObjectFieldEntity masterIdField = erpFieldManager.findMasterIdField(tenantId, dataCenterId, objApiName);

        List<ErpObjectEntity> erpObjectEntityList = erpObjManager.getDetailErpObjEntityList(tenantId, dataCenterId, objApiName);

        ErpObjectFieldEntity addressObjIdField = null;
        for(ErpObjectEntity entity : erpObjectEntityList) {
            String detailApiName = entity.getErpObjectExtendValue();
            if(StringUtils.equalsIgnoreCase(detailApiName,ENTRY_ADDRESS)) {
                addressObjIdField = erpFieldManager.findIdField(tenantId, entity.getErpObjectApiName());
            }
        }

        Result<K3UltimateResponseByQuery> batchQuery = getCustomerAddressData(tenantId,
                dataCenterId,
                customerId,
                k3UltimateApiService);
        if (!batchQuery.isSuccess())
            throw new ErpSyncDataException(batchQuery.getErrCode(), batchQuery.getErrMsg(),tenantId);

        for (int i = 0; i < addressDataList.size(); i++) {
            JSONObject addressData = addressDataList.getJSONObject(i);
            String number = addressData.getString("number");

            String addressId = null;
            for (JSONObject row : batchQuery.getData().getData().getRows()) {
                if (StringUtils.equalsIgnoreCase(row.getString("number"), number)) {
                    addressId = row.getString("id");
                }
            }

            String customerNumber = addressData.getString("customer_number");
            if(StringUtils.isEmpty(customerNumber)) {
                ErpObjectFieldEntity masterDetailField = erpFieldManager.findMasterDetailField(tenantId,
                        addressObjIdField.getErpObjectApiName(),
                        masterIdField.getErpObjectApiName());
                if(masterDetailField==null) {
                    throw new ErpSyncDataException(I18NStringEnum.s2113,tenantId);
                }
                customerNumber = addressData.getString(masterDetailField.getFieldApiName());
                if(StringUtils.equalsIgnoreCase(masterIdField.getFieldApiName(),"id")) {
                    Result<K3UltimateResponseByQuery> queryById = K3UltimateUtils.queryById(tenantId,
                            dataCenterId,
                            objApiName,
                            customerNumber,
                            null,
                            k3UltimateApiService);
                    if(!queryById.isSuccess()) {
                        throw new ErpSyncDataException(I18NStringEnum.s2114,tenantId);
                    }
                    customerNumber = queryById.getData().getData().getRows().get(0).getString("number");
                }
                addressData.put("customer_number",customerNumber);
            }

            Result<K3UltimateResponseBySave2> batchAddOrUpdate = null;
            if (StringUtils.isEmpty(addressId)) {
                //新增客户地址
                batchAddOrUpdate = k3UltimateApiService.batchAdd(tenantId,
                        dataCenterId,
                        K3UltimateObjApiName.bd_address,
                        Lists.newArrayList(addressData));
            } else {
                //更新客户地址
                addressData.put("id", addressId);
                batchAddOrUpdate = k3UltimateApiService.batchUpdate(tenantId,
                        dataCenterId,
                        K3UltimateObjApiName.bd_address,
                        Lists.newArrayList(addressData));
            }

            if (!batchAddOrUpdate.isSuccess()) {
                throw new ErpSyncDataException(batchAddOrUpdate.getErrCode(), batchAddOrUpdate.getErrMsg(),tenantId);
            }
            if(StringUtils.equalsIgnoreCase(addressObjIdField.getFieldApiName(),"id")) {
                result.getData().getDetailDataIds().get(ENTRY_ADDRESS).add(batchAddOrUpdate.getData().getData().getResult().get(0).getId());
            } else {
                result.getData().getDetailDataIds().get(ENTRY_ADDRESS).add(batchAddOrUpdate.getData().getData().getResult().get(0).getNumber());
            }
        }
    }
}
