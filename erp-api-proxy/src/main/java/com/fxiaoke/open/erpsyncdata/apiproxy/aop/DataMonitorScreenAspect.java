package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.log.dto.DataScreenSyncLogDTO;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AspectSpelUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataMonitorScreenDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.retrofit2.http.Body;
import com.fxiaoke.retrofit2.http.HeaderMap;
import com.fxiaoke.retrofit2.http.Path;
import com.fxiaoke.retrofit2.http.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2023/12/22 10:50
 * 数据监控/大屏 aop
 * @desc
 */
@Aspect
@Component
@Slf4j
public class DataMonitorScreenAspect {

    //crm的调用方式很多，统计一下

    /**
     * 日志上报。
     *
     * @param joinPoint
     */
    @Around(" execution(*  com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler.*(..))" +
            " || execution(*  com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2CrmManager.*(..)) " +
            " || execution(*  com.fxiaoke.open.erpsyncdata.writer.manager.NodeCompleteDataWriteManager.*(..)) " +
            " || execution(*  com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService.*(..)) " +
            " || execution(* com.fxiaoke.crmrestapi.service.ObjectDataService.*(..)) " +
            " || execution(* com.fxiaoke.crmrestapi.service.ObjectDataServiceV3.*(..)) " +
            " || execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService.*(..)) " +
            " || execution(* com.fxiaoke.crmrestapi.service.MetadataActionService.*(..)) " +
            " || execution(* com.fxiaoke.crmrestapi.service.MetadataControllerService.*(..)) " +
            " || execution(* com.fxiaoke.crmrestapi.service.MetadataControllerServiceV3.*(..)) " +
            " || execution(*  com.fxiaoke.open.erpsyncdata.converter.manager.ReSyncDataNodeManager.saveErrorSyncDataByCache(..)) " +
            "|| execution(*  com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager.erpPushDataToDss(..))")
    public Object  around(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object proceed=null;
        try {
             proceed = joinPoint.proceed();
            return proceed;
        } finally {
            // 获取方法的参数名和参数值
            sendBizLog(joinPoint,proceed,start);
        }

    }

    private void sendBizLog(ProceedingJoinPoint joinPoint,Object proceed,long startTime) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        try {

            DataMonitorScreenDTO dataMonitorScreenDTO=new DataMonitorScreenDTO();
            String methodName = methodSignature.getDeclaringType().getName();
            //处理crm restService
            if(methodName.contains("com.fxiaoke.crmrestapi.service.ObjectDataService")||methodName.contains("com.fxiaoke.crmrestapi.service.MetadataActionService")||methodName.contains("com.fxiaoke.crmrestapi.service.MetadataControllerServiceV3")||methodName.contains("com.fxiaoke.crmrestapi.service.ObjectDataServiceV3")){
                //简单判断一下是 读，还是写
                boolean write=isReadMethod(method.getName());
                dataMonitorScreenDTO= dealCrmRequest(joinPoint,method, proceed, startTime,write);
                sendBizLog(dataMonitorScreenDTO,startTime);
            }else {
                //可以找到super的类注解
                DataMonitorScreen dataMonitorScreen =  AnnotationUtils.findAnnotation(method, DataMonitorScreen.class);
                if(ObjectUtils.isNotEmpty(dataMonitorScreen)){
                    //  List<String> paramNameList = Arrays.asList(methodSignature.getParameterNames());
                    //        List<Object> paramList = Arrays.asList(joinPoint.getArgs());
                    // 将方法的参数名和参数值一一对应的放入上下文中
                    //        for (int i = 0; i < paramNameList.size(); i++) {
                    //            ctx.setVariable(paramNameList.get(i), paramList.get(i));
                    //        }
                    StandardEvaluationContext  ctx = AspectSpelUtil.getStandardEvaluationContext(joinPoint,proceed,methodSignature);
                    //注解没有具体的field不能通过常规的getDeclaredFields
                    //method其实就是fieldname
                    Method[] declaredMethods = dataMonitorScreen.annotationType().getDeclaredMethods();

                    for (Method declaredMethod : declaredMethods) {
                        Object fieldValue=null;
                        //拿到注解上的spel。通过context解析数据，

                        String fieldName = declaredMethod.getName(); // 方法名即为字段名
                        fieldValue= declaredMethod.invoke(dataMonitorScreen); // 调用方法获取字段值
                        Field dtoField = DataMonitorScreenDTO.class.getDeclaredField(fieldName);
                        dtoField.setAccessible(true);
                        final String fieldValueString = fieldValue.toString();
                        if(fieldValue instanceof String&&StringUtils.isBlank(fieldValueString)){
                            continue;
                        }

                        if((fieldValue instanceof String&&StringUtils.isNotBlank(fieldValueString))&&(((String) fieldValue).contains("#"))){
                            // 解析SpEL表达式获取结果
                            final Expression expression = AspectSpelUtil.getExpression(fieldValueString);
                            Object description = expression.getValue(ctx,dtoField.getType());
                            dtoField.set(dataMonitorScreenDTO,description);
                        }else {
                            Object convert = Convert.convert(dtoField.getType(), fieldValue);
                            dtoField.set(dataMonitorScreenDTO,convert);
                        }
                    }
                    if(dataMonitorScreenDTO.getSkipSend()!=null&&Boolean.valueOf(dataMonitorScreenDTO.getSkipSend())){
                        //跳过不上报日志。现在用于告警的日志中，不是集成流告警的就忽略
                        return;
                    }
                    //填充threadlocal信息
                    if(ObjectUtils.isNotEmpty(LogIdUtil.getBaseLogNoCreate())){
                        if((String.valueOf(TenantTypeEnum.CRM.getType()).equals(dataMonitorScreenDTO.getSourceSystemType()))){
                            if(StringUtils.isEmpty(dataMonitorScreenDTO.getCrmObjId())){
                                dataMonitorScreenDTO.setCrmObjId(LogIdUtil.getBaseLogNoCreate().getDataId());
                            }
                            if(StringUtils.isEmpty(dataMonitorScreenDTO.getCrmObjApiName())){
                                dataMonitorScreenDTO.setCrmObjApiName(LogIdUtil.getBaseLogNoCreate().getRealObjApiName());
                            }
                        }else{
                            if(StringUtils.isEmpty(dataMonitorScreenDTO.getOutSideObjApiName())){
                                dataMonitorScreenDTO.setOutSideObjApiName(LogIdUtil.getBaseLogNoCreate().getRealObjApiName());
                            }

                        }
                        dataMonitorScreenDTO.setPloyDetailId(LogIdUtil.getBaseLogNoCreate().getStreamId());
                    }
                    sendBizLog(dataMonitorScreenDTO,startTime);
            }
            }
        } catch (Exception e) {
            log.info("data monitor aspect value:{},methodname:{},context:{}",e.getMessage(),method.getName(),proceed);
        }
    }

    private void sendBizLog(DataMonitorScreenDTO dataMonitorScreenDTO,long startTime){
        dataMonitorScreenDTO.setName(IdGenerator.get());
        dataMonitorScreenDTO.setCreateTime(System.currentTimeMillis());
        dataMonitorScreenDTO.setUpdateTime(System.currentTimeMillis());
        dataMonitorScreenDTO.setExecuteTime(System.currentTimeMillis());
        dataMonitorScreenDTO.setExecuteCost(System.currentTimeMillis()-startTime);
        DataScreenSyncLogDTO dataScreenSyncLogDTO= BeanUtil.copy(dataMonitorScreenDTO,DataScreenSyncLogDTO.class);
        // sendlog
        log.debug("inteceptor value :{}", JSONObject.toJSONString(dataScreenSyncLogDTO));
        MonitorBizLogUtil.sendDataScreen(dataScreenSyncLogDTO);
    }
    private boolean isReadMethod(String methodName) {
        String[] readKeywords = {"find", "get", "query","list"};
        for (String keyword : readKeywords) {
            if (methodName.contains(keyword)) {
                return false;
            }
        }
        return true;
    }
    //针对crm的做处理。做不了spel
    private DataMonitorScreenDTO dealCrmRequest(ProceedingJoinPoint joinPoint,Method method,Object proceed,long startTime,boolean write){
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取方法参数的注解信息
        DataMonitorScreenDTO dataScreenSyncLogDTO=new DataMonitorScreenDTO();
        String operationType=write? CommonConstant.WRITE_OPERATE_TYPE:CommonConstant.READ_OPERATE_TYPE;
        dataScreenSyncLogDTO.setOperationType(operationType);
        Annotation[][] parameterAnnotations = signature.getMethod().getParameterAnnotations();
        Object[] args = joinPoint.getArgs();
        // 遍历方法参数的注解信息
        for (int i = 0; i < parameterAnnotations.length; i++) {
            Annotation[] annotations = parameterAnnotations[i];
            Object arg = args[i];
            for (Annotation annotation : annotations) {
                if (annotation instanceof Path) {
                    Path pathAnnotation = (Path) annotation;
                    String key = pathAnnotation.value();
                    if(key.contains("apiName")||(key.contains("describeAPIName"))){
                        String value = (String) arg;
                        dataScreenSyncLogDTO.setCrmObjApiName(value);
                    }
                    //objectId
                    if(key.contains("objectId")){
                        String value = (String) arg;
                        dataScreenSyncLogDTO.setCrmObjId(value);
                    }
                }else if (annotation instanceof HeaderMap) {
                    HeaderMap queryAnnotation = (HeaderMap) annotation;
                    HeaderObj headerObj=(HeaderObj) arg;
                    dataScreenSyncLogDTO.setTenantId(String.valueOf(headerObj.get("X-fs-ei")));
                }else if (annotation instanceof Body) {
                    //转类型
                    if (arg instanceof ActionEditArg) {
                        dataScreenSyncLogDTO.setCrmObjId(((ActionEditArg) arg).getObjectData().getId());
                    } else if (arg instanceof ActionAddArg) {
                        dataScreenSyncLogDTO.setCrmObjId(((ActionAddArg) arg).getObjectData().getId());
                    } else if (arg instanceof ObjectData) {
                        dataScreenSyncLogDTO.setCrmObjId(((ObjectData) arg).getId());
                    } else {
                        // 如果 arg 不是 ActionEditArg、ActionAddArg 或 ObjectData 的实例
                        // 使用默认逻辑或记录日志
                        dataScreenSyncLogDTO.setCrmObjId(LogIdUtil.get());
                    }

                }
            }
        }
        log.info("monitor intercept:{}",LogIdUtil.getBaseLogNoCreate());
        //解析crm返回的结果，
        dataScreenSyncLogDTO.setOperateStatus("1");
        if(proceed instanceof Result){
            Result resultCrm = (Result) proceed;
            dataScreenSyncLogDTO.setOperateStatus(resultCrm.getCode()==0?"1":"2");
        }
        if(ObjectUtils.isNotEmpty(LogIdUtil.getBaseLogNoCreate())){
            dataScreenSyncLogDTO.setDataCenterId(LogIdUtil.getBaseLogNoCreate().getDataCenterId());
            Boolean erp2crm = Optional.ofNullable(LogIdUtil.getBaseLogNoCreate().getErp2crm()).orElse(Boolean.FALSE);
            dataScreenSyncLogDTO.setSourceSystemType(String.valueOf(erp2crm?TenantTypeEnum.ERP.getType():TenantTypeEnum.CRM.getType()));
            dataScreenSyncLogDTO.setPloyDetailId(LogIdUtil.getBaseLogNoCreate().getStreamId());
        }
        return dataScreenSyncLogDTO;
    }


}
