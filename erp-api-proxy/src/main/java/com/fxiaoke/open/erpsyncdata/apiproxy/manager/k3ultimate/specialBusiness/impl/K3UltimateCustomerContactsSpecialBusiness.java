package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.utils.K3UltimateUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component(K3UltimateObjApiName.bd_customer_contacts)
public class K3UltimateCustomerContactsSpecialBusiness extends K3UltimateBaseSpecialBusiness {
    //客户地址对象编码
    public static final String ENTRY_LINKMAN = "entry_linkman";

    @Override
    public void afterListErpObjDataByTime(String tenantId,
                                          String dataCenterId,
                                          String objApiName,
                                          TimeFilterArg timeFilterArg,
                                          List<StandardData> standardDataList,
                                          K3UltimateApiService k3UltimateApiService) {
        super.afterListErpObjDataByTime(tenantId, dataCenterId, objApiName, timeFilterArg, standardDataList, k3UltimateApiService);
        if (CollectionUtils.isEmpty(standardDataList)) return;
        fillContactsData(standardDataList);
    }

    /**
     * 转换并填充联系人数据
     *
     * @param standardDataList
     */
    private void fillContactsData(List<StandardData> standardDataList) {
        List<StandardData> newStandardDataList = new ArrayList<>();
        for (StandardData standardData : standardDataList) {
            JSONArray linkmanDataList = (JSONArray) standardData.getMasterFieldVal().get(ENTRY_LINKMAN);
            if(linkmanDataList==null) continue;
            for (int i = 0; i < linkmanDataList.size(); i++) {
                StandardData newStandardData = new StandardData();
                newStandardData.setObjAPIName(K3UltimateObjApiName.bd_customer_contacts);
                newStandardData.setSyncLogId(standardData.getSyncLogId());
                newStandardData.setDataReceiveType(standardData.getDataReceiveType());
                newStandardData.setDataVersion(standardData.getDataVersion());

                newStandardData.setMasterFieldVal(new ObjectData());
                newStandardData.getMasterFieldVal().put("customer_id", standardData.getMasterFieldVal().getString("id"));
                newStandardData.getMasterFieldVal().put("customer_number", standardData.getMasterFieldVal().getString("number"));
                newStandardData.getMasterFieldVal().put("customer_name", standardData.getMasterFieldVal().getString("name"));

                newStandardData.getMasterFieldVal().putAll(linkmanDataList.getJSONObject(i));
                //用联系人名称虚拟一个联系人编码字段
                String fakeNumber = newStandardData.getMasterFieldVal().getString("contactperson");
                if (StringUtils.isEmpty(fakeNumber)) {
                    String familyName = newStandardData.getMasterFieldVal().getString("familyname");
                    String givenName = newStandardData.getMasterFieldVal().getString("givenname");
                    if (StringUtils.isNotEmpty(familyName) && StringUtils.isNotEmpty(givenName)) {
                        fakeNumber = familyName + givenName;
                    } else {
                        String alias = newStandardData.getMasterFieldVal().getString("alias");
                        if (StringUtils.isNotEmpty(alias)) {
                            fakeNumber = alias;
                        } else {
                            fakeNumber = newStandardData.getMasterFieldVal().getString("id");
                        }
                    }
                }

                newStandardData.getMasterFieldVal().put("number", fakeNumber);

                newStandardDataList.add(newStandardData);
            }
        }
        standardDataList.clear();
        standardDataList.addAll(newStandardDataList);
    }

    @Override
    public void afterGetErpObjData(String tenantId,
                                   String dataCenterId,
                                   String objApiName,
                                   ErpIdArg erpIdArg,
                                   StandardData standardData,
                                   K3UltimateApiService k3UltimateApiService) {
        super.afterGetErpObjData(tenantId, dataCenterId, objApiName, erpIdArg, standardData, k3UltimateApiService);
        List<StandardData> standardDataList = Lists.newArrayList(standardData);
        fillContactsData(standardDataList);
        //覆盖standardData
        standardData.getMasterFieldVal().clear();
        standardData.getDetailFieldVals().clear();
        BeanUtils.copyProperties(standardDataList.get(0),standardData);
    }

    @Override
    public void afterReSyncDataById(String tenantId,
                                    String dataCenterId,
                                    String objApiName,
                                    ErpIdArg erpIdArg,
                                    List<StandardData> standardDataList,
                                    K3UltimateApiService k3UltimateApiService) {
        super.afterReSyncDataById(tenantId, dataCenterId, objApiName, erpIdArg, standardDataList, k3UltimateApiService);
    }

    @Override
    public void beforeCreateErpObjData(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       StandardData standardData,
                                       JSONObject requestData,
                                       K3UltimateApiService k3UltimateApiService) {
        super.beforeCreateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, k3UltimateApiService);
    }

    /**
     * 填充客户联系人数据
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param requestData
     * @param k3UltimateApiService
     * @return
     */
    private JSONObject fillCustomerContactsData(String tenantId,
                                                String dataCenterId,
                                                String objApiName,
                                                JSONObject requestData,
                                                K3UltimateApiService k3UltimateApiService) {
        String customerId = requestData.getString("customer_id");
        String customerNumber = null;
        String customerName = null;

        Result<K3UltimateResponseByQuery> result = null;
        if (StringUtils.isEmpty(customerId)) {
            customerNumber = requestData.getString("customer_number");
            if (StringUtils.isEmpty(customerNumber)) {
                throw new ErpSyncDataException(I18NStringEnum.s2112,tenantId);
            }
            result = K3UltimateUtils.queryByNumber(tenantId,
                    dataCenterId,
                    objApiName,
                    customerNumber,
                    null,
                    k3UltimateApiService);
            if (!result.isSuccess()) {
                throw new ErpSyncDataException(result.getErrCode(), result.getErrMsg(),tenantId);
            }
        } else {
            result = K3UltimateUtils.queryByNumber(tenantId,
                    dataCenterId,
                    objApiName,
                    customerId,
                    null,
                    k3UltimateApiService);
            if (!result.isSuccess()) {
                throw new ErpSyncDataException(result.getErrCode(), result.getErrMsg(),tenantId);
            }
        }
        JSONObject firstCustomerData = result.getData().getData().getRows().get(0);


        customerId = firstCustomerData.getString("id");
        customerNumber = firstCustomerData.getString("number");
        customerName = firstCustomerData.getString("name");
        String createOrgNumber = firstCustomerData.getString("createorg_number");
        String creatorNumber = firstCustomerData.getString("creator_number");
        String modifierNumber = firstCustomerData.getString("modifier_number");

        if(StringUtils.isEmpty(createOrgNumber)) {
            createOrgNumber = requestData.getString("createorg_number");
        }

        if(StringUtils.isEmpty(creatorNumber)) {
            creatorNumber = requestData.getString("creator_number");
        }

        if(StringUtils.isEmpty(modifierNumber)) {
            modifierNumber = requestData.getString("modifier_number");
        }


        JSONObject newRequestData = new JSONObject();
        newRequestData.put("id", customerId);
        newRequestData.put("number", customerNumber);
        newRequestData.put("name", customerName);
        newRequestData.put("createorg_number", createOrgNumber);
        newRequestData.put("creator_number", creatorNumber);
        newRequestData.put("modifier_number", modifierNumber);

        String id = requestData.getString("id");
        if(StringUtils.isEmpty(id)) {
            requestData.put("id", K3UltimateUtils.genId());
        }

        Map<String,Object> linkmanMap = new LinkedHashMap<>();
        linkmanMap.putAll(requestData);
        newRequestData.put(ENTRY_LINKMAN, linkmanMap);

        return newRequestData;
    }

    @Override
    public void afterCreateErpObjData(String tenantId,
                                      String dataCenterId,
                                      String objApiName,
                                      StandardData standardData,
                                      JSONObject requestData,
                                      Result<ErpIdResult> result,
                                      K3UltimateApiService k3UltimateApiService) {
        super.afterCreateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, result, k3UltimateApiService);
    }

    @Override
    public void beforeUpdateErpObjData(String tenantId,
                                       String dataCenterId,
                                       String objApiName,
                                       StandardData standardData,
                                       JSONObject requestData,
                                       K3UltimateApiService k3UltimateApiService) {
        super.beforeUpdateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, k3UltimateApiService);
        JSONObject newRequestData = fillCustomerContactsData(tenantId, dataCenterId, objApiName, requestData, k3UltimateApiService);
        requestData.clear();
        requestData.putAll(newRequestData);
    }

    @Override
    public void afterUpdateErpObjData(String tenantId,
                                      String dataCenterId,
                                      String objApiName,
                                      StandardData standardData,
                                      JSONObject requestData,
                                      Result<ErpIdResult> result,
                                      K3UltimateApiService k3UltimateApiService) {
        super.afterUpdateErpObjData(tenantId, dataCenterId, objApiName, standardData, requestData, result, k3UltimateApiService);
    }
}
