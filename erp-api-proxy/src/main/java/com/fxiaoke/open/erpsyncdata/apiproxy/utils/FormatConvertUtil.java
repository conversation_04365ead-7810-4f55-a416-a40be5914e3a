package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Created by fengyh on 2020/8/14.
 * <p>
 * erp标砖API格式和 dss平台底层格式之间的转换。
 */
public class FormatConvertUtil {

    /**
     * crm同步到ERP时，将dss平台底层所用的crm数据格式，转为erp标砖API标砖格式。
     *
     * @param data
     * @return
     */
    public static StandardData crm2StdErp(SyncDataContextEvent data) {
        StandardData standardData = new StandardData();
        standardData.setNeedReturnField(data.getNeedReturnField());
        standardData.setObjAPIName(data.getDestData().getApiName());
        standardData.setMasterFieldVal(data.getDestData());
        standardData.getMasterFieldVal().put("sync_data_id",data.getSyncDataId());
        Map<String, List<ObjectData>> detailFieldVals = new HashMap<>();
        standardData.setDetailFieldVals(detailFieldVals);
        if (data.getDestDetailSyncDataIdAndDestDataMap() != null) {
            for(String key : data.getDestDetailSyncDataIdAndDestDataMap().keySet()) {
                ObjectData value = data.getDestDetailSyncDataIdAndDestDataMap().get(key);
                value.put("sync_data_id",key);
                String detailApiName = value.getApiName();
                detailFieldVals.putIfAbsent(detailApiName, new ArrayList<>());
                detailFieldVals.get(detailApiName).add(value);
            }
        }
        return standardData;
    }

    /**
     * crm同步到ERP时，将dss平台底层所用的crm数据格式，转为erp标砖API标砖格式。
     *
     * @param data
     * @return
     */
    public static StandardDetailData crm2StdDetailErp(SyncDataContextEvent data) {
        StandardDetailData standardData = new StandardDetailData();
        standardData.setObjAPIName(data.getDestData().getApiName());
        standardData.setMasterDataId(data.getDestDataId());
        LinkedHashMap<String, ObjectData> detailFieldVal = new LinkedHashMap<>();
        standardData.setDetailFieldVal(detailFieldVal);
        //明细数据只会有一条
        String sync_data_id = data.getDestDetailSyncDataIdAndDestDataMap().keySet().iterator().next();
        ObjectData value = data.getDestDetailSyncDataIdAndDestDataMap().get(sync_data_id);
        value.put("sync_data_id",sync_data_id);
        String detailApiName = value.getApiName();
        value.remove("object_describe_api_name");
        detailFieldVal.put(detailApiName, value);
        return standardData;
    }


    /**
     * erp同步到crm时，将erp标砖API标砖格式, 转为dss平台底层所用的crm数据格式。
     * 注意有些预设字段是特殊处理的, 比如erp中的_id字段。
     */
    public static Result<ListErpObjDataResult> listResultStdErp2Crm(String tenantId,String apiName, Result<StandardListData> formatResult) {
        if (!formatResult.isSuccess()) {
            return Result.copy(formatResult);
        }
        StandardListData standardListData = formatResult.getData();
        ListErpObjDataResult retData = new ListErpObjDataResult();
        List<SyncDataContextEvent> erpObjDataResultList = new ArrayList<>();
        retData.setErpObjDataResultList(erpObjDataResultList);
        if (standardListData == null || CollectionUtils.isEmpty(standardListData.getDataList())) {
            return new Result<>(retData);
        }
        retData.setMaxId(standardListData.getMaxId());
        retData.setMaxTime(standardListData.getMaxTime());
        //增加apiName字段
        for (StandardData standardData : standardListData.getDataList()) {
            SyncDataContextEvent erpObjDataResult = stdErp2Crm(tenantId,apiName, standardData);
            erpObjDataResultList.add(erpObjDataResult);
        }
        if(!StringUtils.equalsIgnoreCase(apiName, K3CloudForm.SAL_SC_CUSTMAT)) {
            retData.setComplete(retData.getErpObjDataResultList().size() == 0);
        }
        return new Result<>(retData);
    }

    /**
     * erp同步到crm时，将erp标砖API标砖格式, 转为dss平台底层所用的crm数据格式。
     * 注意有些预设字段是特殊处理的, 比如erp中的_id字段。
     */
    public static Result<SyncDataContextEvent> resultStdErp2Crm(String tenantId,String apiName, Result<StandardData> formatResult) {
        if (!formatResult.isSuccess()) {
            return Result.copy(formatResult);
        }
        return new Result<>(stdErp2Crm(tenantId,apiName, formatResult.getData()));
    }


    /**
     * erp同步到crm时，将erp标砖API标砖格式, 转为dss平台底层所用的crm数据格式。
     * 注意有些预设字段是特殊处理的, 比如erp中的_id字段。
     */
    public static SyncDataContextEvent stdErp2Crm(String tenantId,String apiName, StandardData standardData) {
        if (standardData == null || standardData.getMasterFieldVal() == null || standardData.getDetailFieldVals() == null) {
            throw new ErpSyncDataException(I18NStringEnum.s253,tenantId);
        }
        SyncDataContextEvent erpObjDataResult = new SyncDataContextEvent();
        erpObjDataResult.setSourceData(standardData.getMasterFieldVal());
        //常见情况，erp返回的数据不规范，detail 的key为空，或者detail value有空list.会导致后置的处理有问题。需要前置过滤数据
        erpObjDataResult.setDetailData(filterUnusualData(standardData.getDetailFieldVals()));
        //增加apiName字段
        erpObjDataResult.getSourceData().putApiName(apiName);
        erpObjDataResult.setSyncLogId(standardData.getSyncLogId());
        erpObjDataResult.setDataReceiveType(standardData.getDataReceiveType());
        erpObjDataResult.setDataVersion(standardData.getDataVersion());
        AtomicInteger allObjCount = new AtomicInteger(1);
        erpObjDataResult.getDetailData().forEach((k, v) -> {
            if (v != null) {
                allObjCount.addAndGet(v.size());
                for (ObjectData detailData : v) {
                    detailData.putApiName(k);
                }
            }
        });
        //设置数量
        erpObjDataResult.setAllObjCount(allObjCount.get());
        return erpObjDataResult;
    }

    public static StandardData json2Std(Object obj){
        //防止接口返回的null，修改默认构造的值
        StandardData standardData = BeanUtil.toBean(obj,StandardData.class, CopyOptions.create().ignoreNullValue());
        return standardData;
    }

    public static StandardListData json2StdList(JSONObject jsonObject){
        StandardListData standardListData = new StandardListData();
        standardListData.setTotalNum(jsonObject.getInteger("totalNum"));
        //防止接口返回的null，修改默认构造的值
        JSONArray dataListJson = jsonObject.getJSONArray("dataList");
        if (dataListJson!=null&&!dataListJson.isEmpty()){
            List<StandardData> dataList = dataListJson.stream().map(FormatConvertUtil::json2Std).collect(Collectors.toList());
            standardListData.setDataList(dataList);
        }
        return standardListData;
    }

    /**
     * 过滤错误的数据
     * @param erpDetailData
     * @return
     */
    private static Map<String, List<ObjectData>> filterUnusualData(Map<String, List<ObjectData>> erpDetailData){
        Iterator<Map.Entry<String, List<ObjectData>>> iterator = erpDetailData.entrySet().iterator();
        while (iterator.hasNext()){
            Map.Entry<String, List<ObjectData>> next = iterator.next();
            if(next.getKey()==null||next.getValue()==null){
                iterator.remove();
                continue;
            }
            next.getValue().removeIf(Objects::isNull);
        }
        return erpDetailData;
    }

    public static void main(String[] args) {
        String data="{\n" +
                "    \"objAPIName\": \"ZCUSTOMSet\",\n" +
                "    \"masterFieldVal\": {\n" +
                "        \"Kvgr1\": \"邓大锋\",\n" +
                "        \"Vkbur\": \"1002\",\n" +
                "        \"Kvgr2\": \"缪玲\",\n" +
                "        \"BuSort1\": \"昆山直营\",\n" +
                "        \"Bezei\": \"江苏\",\n" +
                "        \"BuGroup\": \"Z002\",\n" +
                "        \"UndeliTx\": \"江苏苏州\",\n" +
                "        \"TelNumber\": \"1\",\n" +
                "        \"Crdat\": \"0000-00-00\",\n" +
                "        \"Ktext\": \"other\",\n" +
                "        \"Landx\": \"中国\",\n" +
                "        \"Kunnr3\": \"\",\n" +
                "        \"NameCo\": \"1\",\n" +
                "        \"NameOrg1\": \"昆山直营\",\n" +
                "        \"Kunnr2\": \"11\",\n" +
                "        \"Kunnr1\": \"11\",\n" +
                "        \"Partner\": \"110058\",\n" +
                "        \"StrSuppl1\": \"1\",\n" +
                "        \"TelExtens\": \"\",\n" +
                "        \"object_describe_api_name\": \"ZCUSTOMSet\",\n" +
                "        \"tenant_id\": \"735256\"\n" +
                "    },\n" +
                "    \"detailFieldVals\": {\"logKeyValue\":null},\n" +
                "    \"syncLogId\": null,\n" +
                "    \"dataReceiveType\": 1,\n" +
                "    \"dataVersion\": null\n" +
                "}";
        StandardData standardData = JSONObject.parseObject(data,StandardData.class);
        SyncDataContextEvent erpObjDataResult = new SyncDataContextEvent();
        Map<String,List<ObjectData>> valuesMap= Maps.newHashMap();
//        valuesMap.put("detaill", Lists.newArrayList());
//        valuesMap.put("detail", Lists.newArrayList());
//        valuesMap.put(null,null);
//        for (Map.Entry<String, List<ObjectData>> stringListEntry : valuesMap.entrySet()) {
//            valuesMap.remove(stringListEntry.getKey());
//        }
//        standardData.setDetailFieldVals(Maps.newHashMap());
//        standardData.getDetailFieldVals().putAll(valuesMap);
//        for (Map.Entry<String, List<ObjectData>> stringListEntry : standardData.getDetailFieldVals().entrySet()) {
//            standardData.getDetailFieldVals().remove(stringListEntry.getKey());
//        }

        Iterator<Map.Entry<String, List<ObjectData>>> iterator = standardData.getDetailFieldVals().entrySet().iterator();
        while (iterator.hasNext()){
            Map.Entry<String, List<ObjectData>> next = iterator.next();
            if(next.getKey()==null||next.getValue()==null){
                iterator.remove();
                continue;
            }
            next.getValue().removeIf(Objects::isNull);
        }
        erpObjDataResult.setDetailData(standardData.getDetailFieldVals());

        System.out.println(erpObjDataResult);
    }
}
