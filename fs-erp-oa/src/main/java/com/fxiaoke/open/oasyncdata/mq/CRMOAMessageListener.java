package com.fxiaoke.open.oasyncdata.mq;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.fxiaoke.open.oasyncdata.manager.ExternalMsgManager;
import com.fxiaoke.open.oasyncdata.util.MsgUtil;
import org.apache.commons.validator.Msg;
import org.apache.rocketmq.client.consumer.listener.*;
import org.apache.rocketmq.common.message.MessageExt;
import com.fxiaoke.open.oasyncdata.constant.OAMessageTag;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.ExternalToDoManager;
import com.fxiaoke.otherrestapi.metadatastatic.service.IdService;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/11/8 17:03 针对CRM的审批流，代办流同步消息消费
 * @Version 1.0
 */
@Service("crmOAMessageListener")
@Slf4j
public class CRMOAMessageListener implements MessageListenerConcurrently {

    @Autowired
    private ExternalToDoManager externalToDoManager;
    @Autowired
    private ExternalMsgManager externalMsgManager;
    @Autowired
    private IdGenerator idGenerator;


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : list) {
            try {
                TraceUtil.initTrace("fs-erp-oa-"+msg.getMsgId());
                    String receiveTags = msg.getTags();
                    switch (receiveTags) {
                        case OAMessageTag.CREATE_TO_DO_TAG:
                            CreateTodoArg createTodoArg = JSONObject.parseObject(msg.getBody(), CreateTodoArg.class);
                            externalToDoManager.createTodo(createTodoArg,false,null);
                            break;
                        case OAMessageTag.DEAL_TO_DO_TAG:
                            DealTodoArg dealTodoArg
                                    = JSONObject.parseObject(msg.getBody(), DealTodoArg.class);
                            externalToDoManager.dealTodo(dealTodoArg,false,null);
                            break;
                        case OAMessageTag.DELETE_TO_DO:
                            DeleteTodoArg deleteTodoArg
                                    = JSONObject.parseObject(msg.getBody(), DeleteTodoArg.class);
                            externalToDoManager.deleteTodo(deleteTodoArg,false,null);
                            break;
                        case OAMessageTag.TEXT_MSG_TAG:
                            SendTextMessageArg sendTextMessageArg=JSONObject.parseObject(msg.getBody(), SendTextMessageArg.class);
                            externalMsgManager.sendTextMessage(sendTextMessageArg,null);
                            break;
                        case OAMessageTag.CARD_MSG_TAG:
                            SendTextCardMessageArg sendTextCardMessageArg=JSONObject.parseObject(msg.getBody(), SendTextCardMessageArg.class);
                            externalMsgManager.sendTextCardMessage(sendTextCardMessageArg,null);
                            break;
                        default:
                            break;

                    }
            } catch (Exception e) {
                log.error("QiXinMsgListener consume  failed.", e);
                TraceUtil.removeTrace();
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            TraceUtil.removeTrace();
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
