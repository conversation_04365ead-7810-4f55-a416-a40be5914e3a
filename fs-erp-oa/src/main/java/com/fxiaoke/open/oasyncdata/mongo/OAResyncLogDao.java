package com.fxiaoke.open.oasyncdata.mongo;


import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.ErpSyncMonitorLog;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.config.ConfigConst;
import com.fxiaoke.open.oasyncdata.config.ProcessInfoOA;
import com.fxiaoke.open.oasyncdata.constant.OASyncLogEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OAResyncLogDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.model.QueryMessageRetryArg;
import com.fxiaoke.open.oasyncdata.model.QueryMessageRetryIdArg;
import com.fxiaoke.open.oasyncdata.model.QueryOASyncLogArg;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/23
 * oa重试
 * @Desc:
 */
@Slf4j
@Component
public class OAResyncLogDao {
    @Autowired
    private OASyncLogMongoStore mongoStore;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASettingsDao oaSettingsDao;
    @Autowired
    private RedisDataSource redisDataSource;
    private static final String status = "status";
    private static final String tenant_id = "tenantId";
    private static final String business_type = "businessType";
    private static final String event_type = "eventType";
    private static final String receiver_id = "receiverId";
    private static final String nextRetryTime = "nextRetryTime";
    private static final String data_center_id = "dataCenterId";
    private static final String data_id = "dataId";
    private static final String data_mapping_id = "dataMappingId";




    public void batchInsert(String tenantId, List<OAResyncLogDoc> syncLogs) {
        UpdateOneModel<OAResyncLogDoc> updateOneModel;
        List<WriteModel<OAResyncLogDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (OAResyncLogDoc syncLog : syncLogs) {
            updateOneModel = new UpdateOneModel<>(OAMappingDataHelper.updateResyncMessage(syncLog), OAMappingDataHelper.upsertMessageResync(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<OAResyncLogDoc> resyncCollection = mongoStore.getResyncCollection(tenantId);
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = resyncCollection.bulkWrite(requests, bulkOption);
            //自增字段
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }



    public List<OAResyncLogDoc> pageByFilters(QueryMessageRetryArg arg) {
        List<OAResyncLogDoc> result = new ArrayList<>();
        MongoCollection<OAResyncLogDoc> collection = mongoStore.getResyncCollection(arg.getTenantId());
        Bson filter = buildFilter(arg);
        int offset=(arg.getPage()-1)*arg.getPageSize();
        collection.find(filter)
                .sort(Sorts.descending("nextRetryTime"))
                .skip(offset)
                .limit(arg.getPageSize())
                .into(result);
        return result;
    }

    public List<OAResyncLogDoc> getDataByDataId(OAResyncLogDoc oaResyncLogDoc) {
        List<OAResyncLogDoc> result = new ArrayList<>();
        MongoCollection<OAResyncLogDoc> collection = mongoStore.getResyncCollection(oaResyncLogDoc.getTenantId());
        QueryMessageRetryIdArg queryMessageRetryArg=BeanUtil.copy(oaResyncLogDoc,QueryMessageRetryIdArg.class);
        Bson filter = buildFilterByRetryDataId(queryMessageRetryArg);
        List<OAResyncLogDoc> into = collection.find(filter).into(result);


        return into;
    }

    private Bson buildFilter(QueryMessageRetryArg arg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(tenant_id, arg.getTenantId()));
        if (arg.getDataCenterId() != null ) {
            filters.add(Filters.eq(data_center_id, arg.getDataCenterId()));
        }

        if (arg.getEventType() != null ) {
            filters.add(Filters.in(event_type, arg.getEventType()));
        }
        if (arg.getBusinessTypes() != null ) {
            filters.add(Filters.eq(business_type, arg.getBusinessTypes()));
        }
        if (arg.getStatus() != null ) {
            filters.add(Filters.in(status, arg.getStatus()));
        }
        if (arg.getEventTime() != null ) {
            filters.add(Filters.lte(nextRetryTime, arg.getEventTime()));
        }
        Bson filter = Filters.and(filters);
        return filter;
    }

    public void deleteByDataId(QueryMessageRetryIdArg queryMessageRetryIdArg){
        Bson bson = buildFilterByRetryDataId(queryMessageRetryIdArg);
        mongoStore.getResyncCollection(queryMessageRetryIdArg.getTenantId()).deleteOne(bson);
    }

    public void deleteByDataIds(String tenantId,List<ObjectId> objectIds){
        Bson bson=Filters.and(Filters.in("_id", objectIds));
       mongoStore.getResyncCollection(tenantId).deleteMany(bson);
    }

    private Bson buildFilterByRetryDataId(QueryMessageRetryIdArg arg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(tenant_id, arg.getTenantId()));
        if (arg.getDataCenterId() != null ) {
            filters.add(Filters.eq(data_center_id, arg.getDataCenterId()));
        }

        if (arg.getEventType() != null ) {
            filters.add(Filters.in(event_type, arg.getEventType()));
        }
        if (arg.getDataId() != null ) {
            filters.add(Filters.eq(data_id, arg.getDataId()));
        }
        if (arg.getReceiverId() != null ) {
            filters.add(Filters.in(receiver_id, arg.getReceiverId()));
        }

        Bson filter = Filters.and(filters);
        return filter;
    }


}
