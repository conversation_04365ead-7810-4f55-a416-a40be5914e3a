package com.fxiaoke.open.oasyncdata.controller.oa;


import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.ConfigOARouteManager;
import com.fxiaoke.open.oasyncdata.manager.ExternalToDoManager;
import com.fxiaoke.open.oasyncdata.manager.SyncLogManager;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OASyncLogService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 同步日志接口
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA维护资料相关接口")
@RestController("oaSyncLogController")
@RequestMapping("cep/oa/syncLog")
@Slf4j
public class OASyncLogController extends BaseController {
    @Autowired
    private OASyncLogService oaSyncLogService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ConfigOARouteManager configOARouteManager;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;
    @Autowired
    private ExternalToDoManager externalToDoManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static ExecutorService executorService = new ThreadPoolExecutor(10, 10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    @ApiOperation(value = "获取OA某条同步历史日志")
    @RequestMapping(value = "/getOASyncLogInfo", method = RequestMethod.POST)
    public Result<List<OASyncLogVO>> getOASyncLogInfo(@RequestBody QueryOASyncLogArg queryOASyncLogArg) {
        String tenantId = getLoginUserTenantId();
        queryOASyncLogArg.setTenantId(tenantId);
        //日志数据太多，不做刷库操作。针对需要的企业的才进行设置dataCenterId。日志过期后再放开
        if(ConfigCenter.FILTER_LOG_TENANT_ID.contains(tenantId)){
            queryOASyncLogArg.setDataCenterId(getDcId());
        }
        return oaSyncLogService.queryOALog(queryOASyncLogArg);
    }

    @ApiOperation(value = "获取OA同步历史日志")
    @RequestMapping(value = "/getOASyncLogInfoGroup", method = RequestMethod.POST)
    public Result<OASyncLogResultArg> getOASyncLogInfoGroup(@RequestBody QueryOASyncLogArg queryOASyncLogArg,
                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        queryOASyncLogArg.setTenantId(tenantId);
        //日志数据太多，不做刷库操作。针对需要的企业的才进行设置dataCenterId。日志过期后再放开
        if(ConfigCenter.FILTER_LOG_TENANT_ID.contains(tenantId)){
            queryOASyncLogArg.setDataCenterId(getDcId());
        }
        return oaSyncLogService.queryOALogGroup(queryOASyncLogArg,lang);
    }

    @ApiOperation(value = "重试日志的接口")
    @RequestMapping(value = "/reSyncData", method = RequestMethod.POST)
    public DeferredResult<Result<String>> reSyncData(@RequestBody Map<String,String> arg,
                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        DeferredResult<Result<String>> deferredResult = new DeferredResult<>(1000L * 5, Result.newError(ResultCodeEnum.ASYNC_RETRY.getErrMsg()));
        log.info("reSyncData data:{}", JSONObject.toJSONString(arg));
        Result<String> logId = oaSyncLogService.reSyncData(arg.get("logId"),dataCenterId, tenantId,lang);
        logId.setErrMsg(i18NStringManager.get2(logId.getI18nKey(),lang,tenantId,logId.getErrMsg(),logId.getI18nExtra()));
        deferredResult.setResult(logId);
        return deferredResult;
    }

    @ApiOperation(value = "重试日志的接口")
    @RequestMapping(value = "/reSyncListData", method = RequestMethod.POST)
    public DeferredResult<Result<String>> reSyncListData(@RequestBody QueryListOASyncLogArg  logArg,
                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        DeferredResult<Result<String>> deferredResult = new DeferredResult<>(1000L * 5, Result.newError(ResultCodeEnum.ASYNC_RETRY.getErrMsg()));
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        String preTranceContextId= TraceUtil.get();
        if(CollectionUtils.isNotEmpty(logArg.getSyncLogIds())){
            log.info("reSyncData data:{}", JSONObject.toJSONString(logArg));
            //限制100重试一次
            executorService.submit(()->{
                TraceUtil.initTrace(preTranceContextId);
                reSyncLogData(logArg.getSyncLogIds(),tenantId,dataCenterId,deferredResult,lang);
            });

        }else{
            Result<List<OASyncLogVO>> listResult = oaSyncLogService.queryOALog(logArg);
            if(CollectionUtils.isNotEmpty(listResult.getData())){
                List<String> collect = listResult.getData().stream().map(OASyncLogVO::getId).collect(Collectors.toList());
                executorService.submit(()->{
                    TraceUtil.initTrace(preTranceContextId);
                    reSyncLogData(collect,tenantId,dataCenterId,deferredResult,lang);
                });

            }
        }
        return deferredResult;
    }

    private Result<String> reSyncLogData(List<String> collect,String tenantId,String dataCenterId,DeferredResult<Result<String>> resultDeferredResult, String lang) {
        //限制100重试一次
        List<List<String>> partition = Lists.partition(collect, 100);
        Result<String> objectResult = Result.newSuccess();
        for (List<String> subLogIds : partition) {
            for (String subLogId : subLogIds) {
                Result<String> syncResult = oaSyncLogService.reSyncData(subLogId, dataCenterId,tenantId,lang);
                if (!syncResult.isSuccess()) {
                    objectResult=syncResult;
                }
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if(StringUtils.isNotEmpty(objectResult.getI18nKey())) {
                objectResult.setErrMsg(i18NStringManager.get2(objectResult.getI18nKey(),lang,tenantId,objectResult.getErrMsg(),objectResult.getI18nExtra()));
                log.info("OASyncLogController.reSyncLogData,objectResult={}",JSONObject.toJSONString(objectResult));
            }
            resultDeferredResult.setResult(objectResult);

        }
        return Result.newSuccess();
    }


    @ApiOperation(value = "修复数据")
    @RequestMapping(value = "/fixData", method = RequestMethod.POST)
    public Result<String> fixData(@RequestBody FixLogArg arg) {
        String tenantId = getLoginUserTenantId();
        return oaSyncLogService.fixData(arg.getTenantId(), arg.getStartTime(),arg.getEndTime());
    }

//    @ApiOperation(value = "传输数据")
//    @RequestMapping(value = "/transferData", method = RequestMethod.POST)
//    public Result<String> transferData(@RequestBody FixLogArg arg) {
//        String tenantId = getLoginUserTenantId();
//
//        List<String> list = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listTenantId();
//        for (String enterValue : list) {
//            log.info("enter value"+enterValue);
//            new Thread(() ->{Result<String> stringResult = oaSyncLogService.transferData(enterValue, arg.getStartTime(), arg.getEndTime());}).start();
//
//
//        }
//        return Result.newSuccess();
//    }

    @ApiOperation(value = "新增或者修改企业路由")
    @RequestMapping(value = "/addOrUpdateTenantRoute", method = RequestMethod.POST)
    public Result<Boolean> addOrUpdateTenantRoute(@RequestBody AddRouteArg addRouteArg) {
        if (StringUtils.isBlank(addRouteArg.getTenantId())&&StringUtils.isBlank(addRouteArg.getResourceId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        return Result.newSuccess(configOARouteManager.configRoute(addRouteArg.getTenantId(),addRouteArg.getResourceId()));
    }

    @ApiOperation(value = "插入pg数据")
    @RequestMapping(value = "/insertRoute", method = RequestMethod.POST)
    public Result<Boolean> insertUpdateData(@RequestBody AddRouteArg addRouteArg) {
        if (StringUtils.isBlank(addRouteArg.getTenantId())&&StringUtils.isBlank(addRouteArg.getResourceId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        configOARouteManager.initRoute(addRouteArg.getTenantId(),addRouteArg.getResourceId());
        return Result.newSuccess();
    }


}
