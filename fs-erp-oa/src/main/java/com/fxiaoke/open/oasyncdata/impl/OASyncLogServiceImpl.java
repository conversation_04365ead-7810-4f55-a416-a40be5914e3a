package com.fxiaoke.open.oasyncdata.impl;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.constant.*;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OAResyncLogDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.manager.ExternalMsgManager;
import com.fxiaoke.open.oasyncdata.manager.ExternalToDoManager;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.mongo.OAResyncLogDao;
import com.fxiaoke.open.oasyncdata.mongo.OASettingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogSnapshotDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncLogDao;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.SyncLogManager;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OASyncLogService;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.redisson.executor.CronExpression;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collector;

/**
 * <AUTHOR>
 * @Date: 2021/3/19
 * @Desc:OA日志
 */
@Service("oaSyncLogService")
@Slf4j
@Data
public class OASyncLogServiceImpl implements OASyncLogService {
    @Autowired
    private OASyncLogDao oaSyncLogDao;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASyncLogSnapshotDao oaSyncLogSnapshotDao;

    @Autowired
    private SyncLogManager syncLogManager;

    @Autowired
    private OASyncApiDao oaSyncApiDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;
    @Autowired
    private OASettingsDao oaSettingsDao;
    @Autowired
    private OAResyncLogDao oaResyncLogDao;
    @Autowired
    private ExternalToDoManager externalToDoManager;
    @Autowired
    private ExternalMsgManager externalMsgManager;

    private static ExecutorService executorService;


    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("oaSyncLogService-%d").build();
        executorService = new ThreadPoolExecutor(10, 30, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), workerFactory);
    }

    @Override
    public Result<List<OASyncLogVO>> queryOALog(QueryOASyncLogArg queryOASyncLogArg) {
        log.info("queryOALog = {},tenantId={}", queryOASyncLogArg);

        List<OASyncLogSnapshotDoc> oaSyncLogSnapshotDocs = oaSyncLogSnapshotDao.pageByFilters(queryOASyncLogArg);
        List<OASyncLogVO> oaSyncLogVOList = Lists.newArrayList();
        for (OASyncLogSnapshotDoc oaSyncLogEntity : oaSyncLogSnapshotDocs) {
            OASyncLogVO oaSyncLogVO = new OASyncLogVO();
            BeanUtils.copyProperties(oaSyncLogEntity, oaSyncLogVO);
            oaSyncLogVO.setCreateTime(oaSyncLogEntity.getCreateTime().getTime());
            oaSyncLogVO.setUpdateTime(oaSyncLogEntity.getUpdateTime().getTime());
            oaSyncLogVO.setId(oaSyncLogEntity.getId().toString());
            oaSyncLogVOList.add(oaSyncLogVO);
        }
        return Result.newSuccess(oaSyncLogVOList);
    }

    @Override
    public Result<OASyncLogResultArg> queryOALogGroup(QueryOASyncLogArg queryOASyncLogArg, String lang) {
        log.info("queryOALog = {},tenantId={}", queryOASyncLogArg);
        int total = (int) oaSyncLogMappingsDao.countMappings(queryOASyncLogArg);
        List<OASyncLogMappingDoc> oaSyncLogMappingDocs = oaSyncLogMappingsDao.pageByFilters(queryOASyncLogArg);
        OASyncLogResultArg oaSyncLogResultArg = new OASyncLogResultArg();
        List<OASyncLogVO> oaSyncLogVOList = Lists.newArrayList();
        for (OASyncLogMappingDoc oaSyncLogEntity : oaSyncLogMappingDocs) {
            OASyncLogVO oaSyncLogVO = new OASyncLogVO();
            BeanUtils.copyProperties(oaSyncLogEntity, oaSyncLogVO);
            oaSyncLogVO.setCreateTime(oaSyncLogEntity.getCreateTime().getTime());
            oaSyncLogVO.setUpdateTime(oaSyncLogEntity.getUpdateTime().getTime());
            oaSyncLogVO.setBusinessType(i18NStringManager, lang, queryOASyncLogArg.getTenantId());
            oaSyncLogVO.setId(oaSyncLogEntity.getId().toString());
            oaSyncLogVOList.add(oaSyncLogVO);
        }
        oaSyncLogResultArg.setTotal(total);
        oaSyncLogResultArg.setDataList(oaSyncLogVOList);
        oaSyncLogResultArg.setPage(queryOASyncLogArg.getPage());
        oaSyncLogResultArg.setPageSize(queryOASyncLogArg.getPageSize());
        return Result.newSuccess(oaSyncLogResultArg);
    }

    @Override
    public Result<String> reSyncData(String logId, String dataCenterId, String tenantId, String lang) {
        log.info("crmApprovalMessageListener consume msgId:{},afterTraceId:{}", TraceUtil.get());
        return syncLogManager.reSyncData(tenantId, dataCenterId, logId, lang);

    }

    @Override
    public Result<String> reSyncDataList(List<String> logId, String dataCenterId, String tenantId, String lang) {

        log.info("crmApprovalMessageListener consume msgId:{},afterTraceId:{}", TraceUtil.get());
        for (String itemLogId : logId) {
            syncLogManager.reSyncData(tenantId, dataCenterId, itemLogId, lang);
        }
        return new Result(ResultCodeEnum.SUCCESS.getErrCode(), ResultCodeEnum.ASYNC_RETRY.getErrMsg(), null);
    }


    @Override
    public Result<String> reSyncAllFailData(String tenantId) {
        //判断是不是指定时间
        String cronExpress = "0 0 3 * * ? *";
        // 查询指定的企业的cron表达式
        QueryOASettingArg queryOASettingArg = new QueryOASettingArg();
        queryOASettingArg.setTenantId(tenantId);
        queryOASettingArg.setType(OATenantEnum.OA_RETRY_CRON_EXPRESSION.name());
        OASettingDoc configByType = oaSettingsDao.getConfigByType(tenantId, queryOASettingArg);
        if (ObjectUtils.isNotEmpty(configByType)) {
            cronExpress = configByType.getConfiguration();
        }

        if (isTimeToTrigger(cronExpress, 5)) {
            log.info("execute cronExpression tenantId:{} cronExpress:{}", tenantId, cronExpress);
            List<OAConnectInfoEntity> byTenantId = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
            for (OAConnectInfoEntity oaConnectInfoEntity : byTenantId) {
                String dataCenterId = oaConnectInfoEntity.getId();
                List<OASyncApiEntity> oaSyncApiEntityList = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAUsedSyncApiList(tenantId, dataCenterId);
                Long time = System.currentTimeMillis() - ConfigCenter.OA_RE_TIME;
                for (OASyncApiEntity oaSyncApiEntity : oaSyncApiEntityList) {
                    QueryOASyncLogArg queryOASyncLogArg = new QueryOASyncLogArg();
                    boolean continueQuery = true;
                    int page = 1;
                    while (continueQuery) {
                        queryOASyncLogArg.setTenantId(tenantId);
                        queryOASyncLogArg.setDataCenterId(dataCenterId);
                        queryOASyncLogArg.setEventType(oaSyncApiEntity.getEventType());
                        queryOASyncLogArg.setObjApiName(oaSyncApiEntity.getObjApiName());
                        //queryOASyncLogArg.setStatus(OASyncLogEnum.SUCCESS.getSyncType());
                        queryOASyncLogArg.setPage(page);
                        List<OASyncLogMappingDoc> oaSyncLogMappingDocs = oaSyncLogMappingsDao.queryFailData(queryOASyncLogArg, new Date(time));
                        for (OASyncLogMappingDoc oaSyncLogMappingDoc : oaSyncLogMappingDocs) {
                            Result<String> result = null;
                            try {
                                if (ConfigCenter.OA_ONLY_RETRY_DEAL_FAILED_EI_LIST.contains(tenantId)) {
                                    //只重试处理（更新）失败的待办
                                    if (StringUtils.equalsIgnoreCase(oaSyncLogMappingDoc.getEventType(), OAEventEnum.DEAL.getEventStatus())
                                            && StringUtils.equalsIgnoreCase(oaSyncLogMappingDoc.getStatus(), OASyncLogEnum.FAIL.getSyncType())) {
                                        log.info("OASyncLogServiceImpl.reSyncAllFailData,retry deal failed,oaSyncLogMappingDoc={}", oaSyncLogMappingDoc);
                                        result = syncLogManager.reSyncData(tenantId, dataCenterId, String.valueOf(oaSyncLogMappingDoc.getId()), null);
                                        log.info("OASyncLogServiceImpl.reSyncAllFailData,retry deal failed,result={}", result);
                                    }
                                } else {
                                    //重试新增失败，处理失败的待办
                                    result = syncLogManager.reSyncData(tenantId, dataCenterId, String.valueOf(oaSyncLogMappingDoc.getId()), null);
                                }
                            } catch (Exception e) {
                                log.warn("exception reSyncAllFailData fail, id = {},result={}", oaSyncLogMappingDoc, e.getMessage());
                                continue;
                            }
                            if (result == null) continue;

                            if (!result.isSuccess()) {
                                log.warn("reSyncAllFailData fail, id = {},result={}", oaSyncLogMappingDoc, result);
                            }
                        }
                        if (queryOASyncLogArg.getPageSize() > oaSyncLogMappingDocs.size()) {
                            continueQuery = false;
                        } else {
                            page++;
                            queryOASyncLogArg.setPage(page);
                        }
                        log.info("reSyncAllFailData tenantid:{}.page:{},eventType:{},size:{}", tenantId, page, oaSyncApiEntity.getEventType(), oaSyncLogMappingDocs.size());
                    }

                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> resyncSettingCondition(String tenantId) {
        log.info("setting condition:{}",tenantId);
        //查询企业配置项,避免之前开启，后续关闭后，数据还在重试
        List<OAConnectInfoEntity> byTenantId = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
        for (OAConnectInfoEntity oaConnectInfoEntity : byTenantId) {
            QueryOASettingArg queryOASettingArg = new QueryOASettingArg();
            queryOASettingArg.setType(tenantId);
            queryOASettingArg.setCurrentDcId(oaConnectInfoEntity.getId());
            queryOASettingArg.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
            OASettingDoc configByType = oaSettingsDao.getConfigByType(tenantId, queryOASettingArg);
            if (ObjectUtils.isEmpty(configByType)) {
                return Result.newSuccess();
            }
            OAMessageResyncRule oaMessageResyncRule = JSONObject.parseObject(configByType.getConfiguration(), OAMessageResyncRule.class);
            if (Boolean.FALSE.equals(oaMessageResyncRule.getStatus())) {
                return Result.newSuccess();
            }
            List<ObjectId> needRemoveDataIds=Lists.newArrayList();
            //提醒，待办
            for (String businessType : oaMessageResyncRule.getBusinessTypes()) {

                Collections.sort(oaMessageResyncRule.getResyncEventTypes());
                //事件类型
                for (String resyncEventType : oaMessageResyncRule.getResyncEventTypes()) {

                    extractedTodo(tenantId, oaMessageResyncRule, needRemoveDataIds,businessType,resyncEventType,oaConnectInfoEntity.getId());
                    if(CollectionUtils.isNotEmpty(needRemoveDataIds)){
                        oaResyncLogDao.deleteByDataIds(tenantId,needRemoveDataIds);
                    }
                }
            }
        }
        return null;
    }


    private void extractedTodo(String tenantId, OAMessageResyncRule oaMessageResyncRule, List<ObjectId> needRemoveDataIds,String businessType,String eventType,String dataCenterId) {
        QueryMessageRetryArg queryMessageRetryArg = new QueryMessageRetryArg();
        queryMessageRetryArg.setBusinessTypes(businessType);
        queryMessageRetryArg.setEventType(eventType);
        queryMessageRetryArg.setDataCenterId(dataCenterId);
        queryMessageRetryArg.setEventTime(System.currentTimeMillis());
        int page = 1;

        boolean continueQuery = true;
        while (continueQuery) {
            queryMessageRetryArg.setTenantId(tenantId);
            queryMessageRetryArg.setPage(page);
            int limitTries= oaMessageResyncRule.getResyncCounts();
            List<OAResyncLogDoc> oaResyncLogDocs = oaResyncLogDao.pageByFilters(queryMessageRetryArg);
            if (CollectionUtils.isEmpty(oaResyncLogDocs)) {
                continueQuery=false;
                break;
            }
            for (OAResyncLogDoc oaResyncLogDoc : oaResyncLogDocs) {
                String dataJson = oaResyncLogDoc.getDataJson();
                if(StringUtils.isEmpty(dataJson)||ObjectUtils.isEmpty(oaResyncLogDoc.getDataMappingId())){
                    needRemoveDataIds.add(oaResyncLogDoc.getId());
                    continue;
                }
                syncLogManager.reSyncData(tenantId,dataCenterId,oaResyncLogDoc.getDataMappingId().toString(),null);
//                switch (oaResyncLogDoc.getMessageType()){
//                    case OAMessageTag.CREATE_TO_DO_TAG:
//                        CreateTodoArg createTodoArg=JSONObject.parseObject(dataJson,new TypeReference<CreateTodoArg>(){});
//                        externalToDoManager.createTodo(createTodoArg,true,oaResyncLogDoc.getDataCenterId());
//                        break;
//                    case OAMessageTag.DEAL_TO_DO_TAG:
//                        DealTodoArg dealTodoArg=JSONObject.parseObject(dataJson,new TypeReference<DealTodoArg>(){});
//                        externalToDoManager.dealTodo(dealTodoArg,true,oaResyncLogDoc.getDataCenterId());
//                        break;
//                    case OAMessageTag.DELETE_TO_DO:
//                        DeleteTodoArg deleteTodoArg=JSONObject.parseObject(dataJson,new TypeReference<DeleteTodoArg>(){});
//                        externalToDoManager.deleteTodo(deleteTodoArg,true,oaResyncLogDoc.getDataCenterId());
//                        break;
//                    case OAMessageTag.TEXT_MSG_TAG:
//                        SendTextMessageArg sendTextMessageArg=JSONObject.parseObject(dataJson, SendTextMessageArg.class);
//                        externalMsgManager.sendTextMessage(sendTextMessageArg,oaResyncLogDoc.getDataCenterId());
//                        break;
//                    case OAMessageTag.CARD_MSG_TAG:
//                        SendTextCardMessageArg sendTextCardMessageArg=JSONObject.parseObject(dataJson, SendTextCardMessageArg.class);
//                        externalMsgManager.sendTextCardMessage(sendTextCardMessageArg,oaResyncLogDoc.getDataCenterId());
//                        break;
//                    default:
//                        break;
//                }
                if(oaResyncLogDoc.getRetryCount()+1>=limitTries){
                    needRemoveDataIds.add(oaResyncLogDoc.getId());
                }
            }
            page+=1;
        }
    }

    /**
     * rangeMinute 设置的范围
     *
     * @param expression
     * @param rangeMinute
     * @return
     */
    private boolean isTimeToTrigger(String expression, Integer rangeMinute) {
        try {
            CronExpression cron = new CronExpression(expression);
            Calendar calendar = Calendar.getInstance();
            // 将分钟和秒设置为 0 calendar.getTime().getTime()
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0); // 如果需要将毫秒也设置为 0.避免任务延迟的导致不触发
            Date startRange = new Date(calendar.getTime().getTime() - rangeMinute * 60000);

            Date endRange = new Date(calendar.getTime().getTime() + rangeMinute * 60000);

            while (startRange.before(endRange)) {
                if (cron.isSatisfiedBy(startRange)) {
                    return true;
                }
                startRange = new Date(startRange.getTime() + 60000); // 每次增加1分钟
            }
        } catch (Exception e) {
            log.info("xlljob express error message:{}", e.getMessage());
        }
        return false;
    }

    @Override
    public Result<String> fixData(String tenantId, Long startTime, Long endTime) {
        return Result.newSuccess();
    }

    @Override
    public Result<String> transferData(String tenantId, Long startTime, Long endTime) {

//        boolean hasContinue = true;
//        int limit = 100;
//        int offset = 0;
//        while (hasContinue) {
//            List<oaResyncLogDoc> oaSyncLogEntities = oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).
//                    queryOALogHistory(tenantId, startTime, endTime, limit, offset);
//            if (CollectionUtils.isEmpty(oaSyncLogEntities)) {
//                hasContinue = false;
//            }
//            for (oaResyncLogDoc oaResyncLogDoc : oaSyncLogEntities) {
//                Date createDate = new Date(oaResyncLogDoc.getCreateTime());
//                Date updateDate = new Date(oaResyncLogDoc.getUpdateTime());
//                OASyncLogSnapshotDoc oaSyncLogSnapshotDoc = BeanUtil.copy(oaResyncLogDoc, OASyncLogSnapshotDoc.class);
//                oaSyncLogSnapshotDoc.setCreateTime(createDate);
//                oaSyncLogSnapshotDoc.setUpdateTime(updateDate);
//                ObjectId preObjectMappingId = ObjectId.get();
//                oaSyncLogSnapshotDoc.setId(preObjectMappingId);
//                oaSyncLogSnapshotDao.saveLog(tenantId, oaSyncLogSnapshotDoc, preObjectMappingId, false);
//                OASyncLogMappingDoc oaSyncLogMappingDoc = BeanUtil.copy(oaResyncLogDoc, OASyncLogMappingDoc.class);
//                oaSyncLogMappingDoc.setCreateTime(createDate);
//                oaSyncLogMappingDoc.setUpdateTime(updateDate);
//                oaSyncLogMappingDoc.setLastSyncLogId(preObjectMappingId);
//                oaSyncLogMappingsDao.batchInsert(tenantId, Lists.newArrayList(oaSyncLogMappingDoc));
//            }
//            offset += 100;
//            try {
//                Thread.sleep(500);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        }
        return Result.newSuccess();
    }

}
