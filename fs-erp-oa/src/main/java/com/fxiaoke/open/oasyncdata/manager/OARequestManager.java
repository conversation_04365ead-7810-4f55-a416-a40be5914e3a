package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.SyncRuleArg;
import com.fxiaoke.open.oasyncdata.constant.OAAPLTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.OARequestModel;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.util.FunctionUtils;
import com.fxiaoke.open.oasyncdata.util.HeaderScriptUtil;
import com.fxiaoke.open.oasyncdata.util.XmlUtil;
import com.github.trace.TraceContext;
import com.google.gson.Gson;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.xml.rpc.ParameterMode;
import javax.xml.rpc.encoding.XMLType;
import java.util.*;

/**
 * 处理发送OA审批流数据
 *
 * <AUTHOR>
 * @date 2021/1/13
 */
@Component("oaRequestManager")
@Slf4j
public class OARequestManager {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Autowired
    private FunctionUtils customFuncManager;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private I18NStringManager i18NStringManager;

    // 产品化：调用参数
    public Result<String> callWebService(String endpoint, String methodName, Object params, List<String> parametersNameList) {
        String result = null;
        try {
            Service service = new Service();
            Call call = (Call) service.createCall();
            call.setTargetEndpointAddress(new java.net.URL(endpoint));
            for (String parametersName : parametersNameList) {
                call.addParameter(parametersName, XMLType.XSD_STRING, ParameterMode.IN);//参数名
            }
            call.setOperationName(methodName);        // 调用的方法名
            call.setReturnType(XMLType.XSD_STRING);    // 返回值类型：String
            String json = new Gson().toJson(params);
            result = (String) call.invoke(new Object[]{json});// 远程调用
            log.info("result = {}", result);
        } catch (Throwable e) {
            log.error("Call OA webservice fail", e);
            return Result.newError(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage());
        }
        return Result.newSuccess(result);
    }

    public Result<String> callRestfulService(OAConnectParam connectParam,
                                             String url,
                                             Object params,
                                             String tenantId,
                                             String requestType,
                                             OASyncLogSnapshotDoc oaSyncLogSnapshotDoc) {
        Map<String,Object> dataMap =new HashedMap();
        dataMap.put("requestData",params);
        dataMap.put("url",url);
        dataMap.put("header",connectParam.getHeader());
        Map<String, String> headerMap= Collections.emptyMap();

        if (StringUtils.isNotEmpty(connectParam.getAplApiName(OAAPLTypeEnum.URLSCRIPT))){
            //执行自定义函数
            Result<Map<String, Object>> mapResult =
                    customFuncManager.executeFunction(tenantId, connectParam.getAplApiName(OAAPLTypeEnum.URLSCRIPT), dataMap, null);
            if (mapResult.isSuccess()&&mapResult.getData()!=null&&mapResult.getData().get("url")!=null){
                url=mapResult.getData().get("url").toString();
            }else{
                String message=mapResult.getErrMsg()==null?i18NStringManager.getByEi(I18NStringEnum.s1259,tenantId):mapResult.getErrMsg();
                return Result.newError(message);
            }
        }else {
            url = HeaderScriptUtil.getUrl(connectParam.getUrlScript(), url,tenantId);
        }

        if (StringUtils.isNotEmpty(connectParam.getAplApiName(OAAPLTypeEnum.HEADER))){
            //执行自定义函数

            Result<Map<String, Object>> mapResult =
                    customFuncManager.executeFunction(tenantId, connectParam.getAplApiName(OAAPLTypeEnum.HEADER), dataMap, null);
            if (mapResult.isSuccess()&&mapResult.getData()!=null){
                Map<String, Object> data = mapResult.getData();
                headerMap=new HashedMap();
                for (Map.Entry<String, Object> entry : data.entrySet()) {
                    headerMap.put(entry.getKey(),String.valueOf(entry.getValue()));
                }
            }else{
                String message=mapResult.getErrMsg()==null?i18NStringManager.getByEi(I18NStringEnum.s1260,tenantId):mapResult.getErrMsg();
                return Result.newError(message);
            }
        }else {
            headerMap = HeaderScriptUtil.getHeaderMap(connectParam.getHeaderScript(), url,tenantId);
        }

        if (ObjectUtils.isEmpty(headerMap)||headerMap.isEmpty()) {
            headerMap = connectParam.getHeader();
        }

        String responseResult = null;
        dataMap.put("requestData",params);
        dataMap.put("url",url);
        dataMap.put("header",headerMap);
        String oaRequestAplApiName = connectParam.getAplApiName(OAAPLTypeEnum.OA_REQUEST);
        if(StringUtils.isNotEmpty(oaRequestAplApiName)){
            //执行统一函数，返回url,header,requestBody
            Result<Map<String, Object>> mapResult =
                    customFuncManager.executeFunction(tenantId, oaRequestAplApiName, dataMap, null);
            if(mapResult.isSuccess()&&mapResult.getData()!=null){
                Object result = mapResult.getData().get("result");
                if (result != null) {
                    responseResult = result.toString();
                }
                String finalUrl = url;
                url= Optional.ofNullable(mapResult.getData().get("url")).map(Objects::toString).orElseGet(() -> finalUrl);
                headerMap= Optional.ofNullable(mapResult.getData().get("header")).filter(o -> o instanceof Map).map(o -> (Map<String, String>) o).
                        orElseGet(() -> connectParam.getHeader());
                Object finalParams = params;
                params=Optional.ofNullable(mapResult.getData().get("requestData")).orElseGet(() -> finalParams);
            }
        }

        String response = responseResult;
        try {
            log.info("post request {}:{}", tenantId, params);
            if (oaSyncLogSnapshotDoc != null) {
                if(StringUtils.isNotEmpty(responseResult)) {
                    oaSyncLogSnapshotDoc.setUrl(null);
                    oaSyncLogSnapshotDoc.setAplApiName(oaRequestAplApiName);
                } else {
                    oaSyncLogSnapshotDoc.setUrl(url);
                    oaSyncLogSnapshotDoc.setAplApiName(null);
                }

                oaSyncLogSnapshotDoc.setMethod(requestType);
                oaSyncLogSnapshotDoc.setHeader(JSONObject.toJSONString(headerMap));
                oaSyncLogSnapshotDoc.setBody(params == null ? "" : params.toString());
            }
            if(StringUtils.isEmpty(responseResult)) {
                response = this.requestData(url, params, headerMap,requestType);
            }
            if (oaSyncLogSnapshotDoc != null) {
                oaSyncLogSnapshotDoc.setResponse(response);
            }
            log.info("post response {}:{}", tenantId, response);
            // webservice解析返回结果，暂时只能解析xml包含json的格式
            if (response.startsWith("<")) {
                Map<String, String> map = XmlUtil.xmlToMap(response);
                String responseCache = response;
                if(ObjectUtils.isNotEmpty(connectParam.getResultFormat().getXmlJsonField())){
                    response = map.get(connectParam.getResultFormat().getXmlJsonField());
                }else {
                    //有些返回的xml是类型这样的格式，out的数据不是json.
                    // <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    //    <soap:Body>
                    //        <ns1:sendTodoResponse xmlns:ns1="http://webservice.notify.sys.kmss.landray.com/">
                    //            <return>
                    //                <returnState>2</returnState>
                    //            </return>
                    //        </ns1:sendTodoResponse>
                    //    </soap:Body>
                    //</soap:Envelope>
                    response=JSONObject.toJSONString(map);
                }

                if (StringUtils.isEmpty(response)) {
                    log.warn("post failed,url:{},param:{},header:{},response:{}", url, params, headerMap, response);
                    return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, responseCache);
                }
            }
            JSONObject resultObj = JSONObject.parseObject(response);
            OAConnectParam.ResultFormat resultFormat = connectParam.getResultFormat();
            String resultCode = resultObj.getString(resultFormat.getCodeName());
            String resultMsg = resultObj.getString(resultFormat.getMsgName());
            //有些客户需要jsonPath路径解析的。
            if (resultFormat.getCodeName().startsWith("$")) {
                resultCode = JsonPath.read(response, resultFormat.getCodeName()).toString();
            }
            if (resultFormat.getSuccessCode().equals(resultCode)) {
                //调用接口成功
                return Result.newSuccess(response);
            } else {
                //请求失败
                if(ObjectUtils.isEmpty(resultMsg)){
                    resultMsg=response+ TraceContext.get().getReason();
                }
                log.warn("post failed,url:{},param:{},header:{},response:{}", url, params, headerMap, response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
            }
        } catch (Exception e) {
            log.error("Call OA restful service fail:{},response:{}", e,e.getMessage());
            if(oaSyncLogSnapshotDoc!=null) {
                oaSyncLogSnapshotDoc.setResponse(e.getMessage()+TraceContext.get().getReason());
            }
            return Result.newError(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage());
        }
    }

    public String requestData(String url,Object params, Map<String, String> headerMap,String requestMode){
        String httpResponse="";
        switch (requestMode){
                case "POST":
                    httpResponse = proxyHttpClient.postUrl(url, params, headerMap);
                    break;
                case "GET":
                    httpResponse=proxyHttpClient.getUrl(url,headerMap);
                    break;
                case "DELETE":
                    httpResponse=proxyHttpClient.deleteUrlByJson(url,params,headerMap);
                    break;
                case "PUT":
                    httpResponse=proxyHttpClient.putUrlByJson(url,params,headerMap);
                    break;
                default:
                    break;

            }
            return httpResponse;
    }


    public Result<String> callCustomFuncAndRestfulService(String tenantId,
                                                          OAConnectParam oaConnectParam,
                                                          OARequestModel oaRequestModel,
                                                          String requestUrl,
                                                          String requestModel,
                                                          String dataCenterId,
                                                          OASyncLogSnapshotDoc oaSyncLogSnapshotDoc) {
        //用户没绑定直接返回错误
        Result<String> result = Result.newError(ResultCodeEnum.OA_USER_NOT_BIND);
        Result<String> requestDataResult = null;
        String todoAplApiName = oaConnectParam.getAplApiName(OAAPLTypeEnum.TODO);
        if (StringUtils.isNotEmpty(oaRequestModel.getOaReceiverId())) {
            if(StringUtils.isNotEmpty(todoAplApiName)) {
                requestDataResult = customFuncManager.getRequestData(tenantId,
                        todoAplApiName,
                        oaRequestModel.getRequestJson(),
                        oaSyncLogSnapshotDoc);
                if(requestDataResult.isSuccess()) {
                    oaRequestModel.setRequestJson(requestDataResult.getData());
                }
            }
            if(requestDataResult!=null && requestDataResult.isSuccess()==false) {
                result = requestDataResult;
            } else {
                //最后都会调用这个方法请求oa接口
                result = callRestfulService(oaConnectParam, requestUrl, oaRequestModel.getRequestJson(),
                        tenantId,requestModel, oaSyncLogSnapshotDoc);
            }
        }else {
            Result<Object> genericInfo = oaSettingService.getGenericInfo(tenantId, OATenantEnum.OA_RECORD_NOT_BIND_ACCOUNT, dataCenterId);
            if(ObjectUtils.isNotEmpty(genericInfo.getData())){
                SyncRuleArg syncRuleArg=JSONObject.parseObject(JSONObject.toJSONString(genericInfo.getData()),SyncRuleArg.class);
                if(!syncRuleArg.getNeedSync()){
                    result = Result.newError(ResultCodeEnum.OA_NOT_SYNC);
                }

            }
        }
        log.info("OARequestManager.callCustomFuncAndRestfulService,result={}",result);
        return result;
    }
}
