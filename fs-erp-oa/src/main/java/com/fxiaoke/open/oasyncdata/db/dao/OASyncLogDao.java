package com.fxiaoke.open.oasyncdata.db.dao;


import com.fxiaoke.open.oasyncdata.db.entity.OASyncLogEntity;
import com.fxiaoke.open.oasyncdata.model.OaSyncLogDaoArg;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/9
 * @Desc:
 */
@Repository
@Deprecated
public interface OASyncLogDao extends ErpBaseDao<OASyncLogEntity>, ITenant<OASyncLogDao> {


    List<OASyncLogEntity> queryOALog(@Param("tenantId") String tenantId, @Param("objApiName") String objApiName, @Param("id") String id,
                                     @Param("status") String status, @Param("dataId") String dataId, @Param("eventType") String eventType, @Param(
            "receiverId") String receiverId,@Param("businessId") String businessId);

    List<OASyncLogEntity> queryOALogGroup(@Param("oaSyncLogDaoArg") OaSyncLogDaoArg oaSyncLogDaoArg, @Param("limit") Integer pageSize, @Param("offset") int offset);

    int countOALogGroup(@Param("oaSyncLogDaoArg") OaSyncLogDaoArg oaSyncLogDaoArg);

    List<String> queryByTypeApi(@Param("tenantId") String tenantId, @Param("eventType") String eventType, @Param("objApiName") String objApiName,
                                @Param("time") Long time);

    int getLogExceprionNum(@Param("tenantId") String tenantId, @Param("dataId") String dataId, @Param("eventType") String eventType);

    /**
     * 查询指定企业，指定时间的同步
     */
    List<OASyncLogEntity> queryOALogHistory(@Param("tenantId") String tenantId,
                                     @Param("startTime") Long startTime, @Param("endTime") Long endTime ,@Param("limit") Integer limit,
                                            @Param("offset") Integer offset);
    Integer updateSyncLogEntity(@Param ("syncLogEntity") OASyncLogEntity syncLogEntity,@Param("id") String id);
}