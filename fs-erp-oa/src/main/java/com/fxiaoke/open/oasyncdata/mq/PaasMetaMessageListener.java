package com.fxiaoke.open.oasyncdata.mq;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.AutoBindArg;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.OAFlowManager;
import com.fxiaoke.open.oasyncdata.model.OAObjectDataMqData;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;


/**
 * <AUTHOR>
 * 监听员工的事件
 */
@Service("paasMetaMessageListener")
@Slf4j
public class PaasMetaMessageListener implements MessageListenerConcurrently {

    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private I18NStringManager i18NStringManager;

    private String OA_USER_LOCK="OA_USER_LOCK_%s";

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        /**
         * {"body":[{"eventId":"6466db399638b421343dd34c","beforeTriggerData":
         * {"last_modified_time":*************,"last_followed_time":*************},
         * "context":{"appId":"CRM","upstreamOwnerId":null,"outTenantId":null,"tenantId":"78437","outUserId":null,"userId":null},
         * "batch":true,"entityId":"AccountObj","triggerType":"u","objectId":"5d0b35b68f37110001a53a2d",
         * "afterTriggerData":{"last_modified_time":*************,"last_followed_time":*************}}],
         * "dataSource":null,"entityId":null,"name":"object_data","op":"u","tenantId":"78437"}
         */
        for (MessageExt msg : msgs) {
            try {
                TraceUtil.initTrace("fs-erp-oa-"+msg.getMsgId());
                OAObjectDataMqData oaObjectDataMqData = JSONObject.parseObject(msg.getBody(), OAObjectDataMqData.class);
                for (OAObjectDataMqData.EventObject eventObject : oaObjectDataMqData.getBody()) {
                    String objectApiName = eventObject.getEntityId();
                    if ("PersonnelObj".equals(objectApiName)) {
                        log.info("paas meta counstme msg :{}",JSONObject.toJSONString(oaObjectDataMqData));
                        String tenantId = eventObject.getContext().getTenantId();
                        //判断是否为空
                        List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
                        if(CollectionUtils.isNotEmpty(oaConnectInfoEntities)){
                            for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {
                                String dataCenterId=oaConnectInfoEntity.getId();
                                //设置了自动绑定规则的，才会更新账户映射
                                Result<Object> genericInfo = oaSettingService.getGenericInfo(tenantId, OATenantEnum.OA_AUTO_BIND_FIELD,dataCenterId);
                                if (ObjectUtils.isNotEmpty(genericInfo.getData())) {
                                    String objectDataId = eventObject.getObjectId();
                                    HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
                                    GetByIdArg getByIdArg = new GetByIdArg();
                                    getByIdArg.setDescribeApiName(objectApiName);
                                    getByIdArg.setDataId(objectDataId);
                                    getByIdArg.setIncludeInvalid(true);
                                    com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> getObjectData
                                            = objectDataServiceV3.getById(headerObj, getByIdArg);
                                    if (getObjectData.isSuccess() && ObjectUtils.isNotEmpty(getObjectData.getData())) {
                                        AutoBindArg autoBindArg = JSONObject.parseObject(JSONObject.toJSONString(genericInfo.getData()), AutoBindArg.class);
                                        ObjectData objectData = getObjectData.getData().getObjectData();
                                        Object oaUsrId = objectData.get(autoBindArg.getOaUserIdField());
                                        Object oaUserName = objectData.get(autoBindArg.getOaUserNameFiled());
                                        if (ObjectUtils.isNotEmpty(oaUserName) && ObjectUtils.isNotEmpty(oaUsrId)) {
                                            //两个字段都不为空，才设置映射。
                                            //会有并发的冲突
                                            String fxUser = objectData.get("user_id").toString();
                                            String fxUserName = objectData.get("name").toString();
                                            RLock oaLock = redissonClient.getLock(String.format(OA_USER_LOCK, fxUser));
                                            boolean lockValue = oaLock.tryLock(10, TimeUnit.SECONDS);
                                            if(lockValue){
                                                try {
                                                    ErpFieldDataMappingEntity erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).
                                                            findOneData(tenantId, ErpFieldTypeEnum.employee_oa, fxUser, null);
                                                    if (ObjectUtils.isNotEmpty(erpFieldDataMappingEntities)) {
                                                        erpFieldDataMappingEntities.setErpDataId(oaUsrId.toString());
                                                        erpFieldDataMappingEntities.setDataCenterId(dataCenterId);
                                                        erpFieldDataMappingEntities.setErpDataName(oaUserName.toString());
                                                        erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(erpFieldDataMappingEntities);
                                                    } else {
                                                        erpFieldDataMappingEntities = new ErpFieldDataMappingEntity();
                                                        erpFieldDataMappingEntities.setErpDataName(oaUserName.toString());
                                                        erpFieldDataMappingEntities.setErpDataId(oaUsrId.toString());
                                                        erpFieldDataMappingEntities.setChannel(ErpChannelEnum.OA);
                                                        erpFieldDataMappingEntities.setDataType(ErpFieldTypeEnum.employee_oa);
                                                        erpFieldDataMappingEntities.setFsDataId(fxUser);
                                                        erpFieldDataMappingEntities.setFsDataName(fxUserName);
                                                        erpFieldDataMappingEntities.setDataCenterId(dataCenterId);
                                                        erpFieldDataMappingEntities.setTenantId(tenantId);
                                                        erpFieldDataMappingEntities.setCreateTime(System.currentTimeMillis());
                                                        erpFieldDataMappingEntities.setUpdateTime(System.currentTimeMillis());
                                                        erpFieldDataMappingEntities.setId(idGenerator.get());
                                                        erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).upsertEntity(erpFieldDataMappingEntities);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }finally {
                                                    oaLock.unlock();
                                                }
                                            }

                                        }
                                    }
                                }
                            }

                        }

                    }
                }
            } catch (Exception e) {
                log.error("crmApprovalMessageListener consume  failed. e:{}，msgId:{}", e,msg.getMsgId());
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;

            }
            TraceUtil.removeTrace();
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


}
