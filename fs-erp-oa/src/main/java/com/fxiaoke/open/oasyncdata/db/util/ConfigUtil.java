package com.fxiaoke.open.oasyncdata.db.util;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/20
 */
@Slf4j
public class ConfigUtil {

    public static Integer parseInt(String config, Integer defaultValue) {
        try {
            int tenantNum = Integer.parseInt(config);
            return tenantNum;
        } catch (NumberFormatException e) {
            log.error("config util parse error", e);
        }
        return defaultValue;
    }
}
