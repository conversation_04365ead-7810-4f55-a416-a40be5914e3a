package com.fxiaoke.open.oasyncdata.mongo;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.converter.EIEAConverter;
import com.facishare.qixin.api.constant.AuthSourceType;
import com.facishare.qixin.api.model.EnterpriseEnv;
import com.facishare.qixin.api.model.message.content.AdvanceText;
import com.facishare.qixin.api.model.message.content.TextInfo;
import com.facishare.qixin.api.model.open.arg.OpenSendMessageBatchAsyncArg;
import com.facishare.qixin.api.model.session.InternationalInfo;
import com.facishare.qixin.api.open.OpenMessageBatchService;
import com.facishare.userlogin.api.service.UserCenterService;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.ErpSyncMonitorLog;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.config.ConfigConst;
import com.fxiaoke.open.oasyncdata.config.ProcessInfoOA;
import com.fxiaoke.open.oasyncdata.constant.CrmTypeMessageEnum;
import com.fxiaoke.open.oasyncdata.constant.OASyncLogEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OAResyncLogDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.manager.OAExcelFileManager;
import com.fxiaoke.open.oasyncdata.manager.UserCenterManager;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.util.DelayQueueUtil;
import com.fxiaoke.open.oasyncdata.util.OAExcelUtils;
import com.fxiaoke.open.oasyncdata.util.TextInfoUtil;
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg;
import com.fxiaoke.paasauthrestapi.common.data.HeaderObj;
import com.fxiaoke.paasauthrestapi.common.result.Result;
import com.fxiaoke.paasauthrestapi.result.RoleUserResult;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/23
 * @Desc:
 */
@Slf4j
@Component
public class OASyncLogSnapshotDao {
    @Autowired
    private OASyncLogMongoStore mongoStore;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASettingsDao oaSettingsDao;
    @Autowired
    private OAResyncLogDao oaResyncLogDao;
    @Autowired
    private RedisDataSource redisDataSource;
    private static final String data_id = "dataId";
    private static final String data_name = "dataName";
    private static final String object_name = "objectName";
    private static final String status = "status";
    private static final String message = "message";
    private static final String object_api_name = "objApiName";
    private static final String event_type = "eventType";
    private static final String title = "title";
    private static final String receiver_id = "receiverId";
    private static final String create_time = "createTime";
    private static final String update_time = "updateTime";
    private static final String last_sync_log_id = "lastSyncLogId";
    private static final String data_json = "dataJson";
    private static final String data_center_id = "dataCenterId";
    private static final String url = "url";
    private static final String aplApiName = "aplApiName";
    private static final String method = "method";
    private static final String header = "header";
    private static final String body = "body";
    private static final String response = "response";
    private DelayQueueUtil<OASyncLogSnapshotDoc> delayQueueUtil;
    private String MESSAGE_NOTIFY_KEY="%s_%s";
    @Autowired
    private PaasAuthService paasAuthService;
    @Autowired
    private OpenMessageBatchService openMessageBatchService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OAExcelFileManager oaExcelFileManager;
    @Autowired
    private UserCenterManager userCenterManager;
    @PostConstruct
    public void afterPropertiesSet() {
        delayQueueUtil = new DelayQueueUtil<>((key, list) -> {
            //数据结构由一个tenant_ploy的企业集成集合与以企业集成集合的value做为key,记录一段时间内同步过的数据id+score(time)
            sendSyncMessageToUser(list, key);
        });
        delayQueueUtil.setBatchProcessTimeLimitInSecond(5*60);//delay 5分钟
    }
    public void saveLog(String tenantId, OASyncLogSnapshotDoc syncLog, ObjectId mappingLogId, boolean autoRetry) {
        mappingLogId = ObjectUtils.isEmpty(mappingLogId) ? ObjectId.get() : mappingLogId;
        MongoCollection<OASyncLogSnapshotDoc> collection = mongoStore.getOASyncLogSnapshotCollection(tenantId);
        collection.insertOne(syncLog);
        OASyncLogMappingDoc oaSyncLogMappingDocs = BeanUtil.copy(syncLog, OASyncLogMappingDoc.class);
        oaSyncLogMappingDocs.setId(mappingLogId);
        oaSyncLogMappingDocs.setLastSyncLogId(syncLog.getId());
        oaSyncLogMappingsDao.batchInsert(tenantId, Lists.newArrayList(oaSyncLogMappingDocs));
        // 失败的http调用，这里上报。配置的企业
        buildbizLog(tenantId, syncLog);
        boolean configStatus=false;
        try {
            if((OASyncLogEnum.FAIL.getSyncType().equals(syncLog.getStatus()) || OASyncLogEnum.EXCEPTION.getSyncType().equals(syncLog.getStatus()))){
                QueryOASettingArg queryOASettingArg = new QueryOASettingArg();
                queryOASettingArg.setCurrentDcId(syncLog.getDataCenterId());
                queryOASettingArg.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                OASettingDoc configByType = oaSettingsDao.getConfigByType(tenantId, queryOASettingArg);
                if(ObjectUtils.isNotEmpty(configByType)){
                    OAMessageResyncRule oaMessageResyncRule = JSONObject.parseObject(configByType.getConfiguration(), OAMessageResyncRule.class);
                    configStatus=oaMessageResyncRule.getStatus().equals(Boolean.TRUE);
                    //根据配置，插入重试
                    //判断是不是需要存储重试
                    if (configStatus && oaMessageResyncRule.getMessageStatus().contains(syncLog.getStatus()) && oaMessageResyncRule.getBusinessTypes().contains(syncLog.getBusinessType())) {

                        OAResyncLogDoc oaResyncLogDoc = BeanUtil.copy(syncLog, OAResyncLogDoc.class);
                        oaResyncLogDoc.setDataMappingId(mappingLogId);
                        oaResyncLogDoc.setId(new ObjectId());
                        //计算下一次需要重试的时间
                        long executeTime=System.currentTimeMillis()+ oaMessageResyncRule.getIntervalMinutes()*60*1000;
                        oaResyncLogDoc.setNextRetryTime(executeTime);
                        //mongo实现自增count
                        List<OAResyncLogDoc> dataByDataId = oaResyncLogDao.getDataByDataId(oaResyncLogDoc);
                        if(CollectionUtils.isNotEmpty(dataByDataId)){
                            oaResyncLogDoc.setRetryCount(dataByDataId.get(0).getRetryCount()+1);
                        }
                        oaResyncLogDao.batchInsert(tenantId, Lists.newArrayList(oaResyncLogDoc));
                        if(CollectionUtils.isNotEmpty(dataByDataId)){
                            //判断data的重试次数需要通知
                            Integer retryCount = oaResyncLogDoc.getRetryCount();
                            if(retryCount>=oaMessageResyncRule.getResyncCounts()){
                                //聚合消息
                                String builderKey=String.format(MESSAGE_NOTIFY_KEY,tenantId,syncLog.getDataCenterId());
                                delayQueueUtil.produceDataBatch(builderKey, Lists.newArrayList(syncLog));
                            }
                        }
                    }

                }


            }
        } catch (Exception e) {
            log.error("save data logerror message:{}",e.getMessage());
        }
        if(OASyncLogEnum.SUCCESS.getSyncType().equals(syncLog.getStatus())&&configStatus&&autoRetry){
            //成功的数据，需要去除重试的数据
            QueryMessageRetryIdArg queryMessageRetryIdArg=new QueryMessageRetryIdArg();
            queryMessageRetryIdArg.setDataId(syncLog.getDataId());
            queryMessageRetryIdArg.setEventType(syncLog.getEventType());
            queryMessageRetryIdArg.setTenantId(syncLog.getTenantId());
            queryMessageRetryIdArg.setReceiverId(syncLog.getReceiverId());
            queryMessageRetryIdArg.setDataCenterId(syncLog.getDataCenterId());
            oaResyncLogDao.deleteByDataId(queryMessageRetryIdArg);
        }

    }

    public void sendSyncMessageToUser(List<OASyncLogSnapshotDoc> oaSyncLogSnapshotDocs,String dataKey){
        if(CollectionUtils.isNotEmpty(oaSyncLogSnapshotDocs)){
            String tenantId=oaSyncLogSnapshotDocs.get(0).getTenantId();
            Integer ei = Integer.valueOf(tenantId);
            String dataCenterId = oaSyncLogSnapshotDocs.get(0).getDataCenterId();
            HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(ei),-1000);
            String enterpriseAccount = eieaConverter.enterpriseIdToAccount(ei);
            Result<RoleUserResult> managerUsers =
                    paasAuthService.roleUser(headerObj, new RoleUserArg("CRM", ei, -10000, ConfigConst.CRM_USER_MANAGER));
            OpenSendMessageBatchAsyncArg asyncArg = new OpenSendMessageBatchAsyncArg();
            asyncArg.setPostId( UUID.randomUUID().toString());//postId
            Set<String> users = managerUsers.getResult().getUsers();
            List<Integer> userLists=Lists.newArrayList();
            users.stream().mapToInt(Integer::parseInt).forEach(userLists::add);
            asyncArg.setToUserList(userLists);//接收人员
            asyncArg.setMessageType("AT");//消息类型
            String content=convertContent(tenantId,dataCenterId,oaSyncLogSnapshotDocs);
            asyncArg.setMessageContent(content);
            asyncArg.setInnerApp(false);
            asyncArg.setSource(AuthSourceType.system);
            asyncArg.setEnv(EnterpriseEnv.INNER);//内部企业
            asyncArg.setEnterpriseAccount(enterpriseAccount);//企业EA
            String appId = ConfigCenter.ERP_SYNC_DATA_APP_ID;
            asyncArg.setAppId(appId);//服务号ID
            asyncArg.setContentInfo(new InternationalInfo());//内容国际化信息
            asyncArg.setSummaryInfo(new InternationalInfo());//推送描述国际化信息
            boolean result = openMessageBatchService.sendMessageBatchAsyncV2(asyncArg);
            log.info("send MSG arg:{}:{}",JSONObject.toJSONString(asyncArg),result);
        }


    }

    private String convertContent(String tenantId,String dataCenterId,List<OASyncLogSnapshotDoc> oaSyncLogSnapshotDocs){
        List<TextInfo> textInfos=Lists.newArrayList();

        StringBuilder content=new StringBuilder().append(i18NStringManager.getByEi(I18NStringEnum.s5007,tenantId)).append("\n");
        content.append(i18NStringManager.getByEi(I18NStringEnum.s5008,tenantId)).append("\n");
        com.fxiaoke.open.oasyncdata.result.base.Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId, dataCenterId);
        String dataCenterName=oaConnectInfo.getData().getConnectParams().getConnectOaName();
        content.append(dataCenterName).append("\n");
        Map<String, List<OASyncLogSnapshotDoc>> collect = oaSyncLogSnapshotDocs.stream().collect(Collectors.groupingBy(OASyncLogSnapshotDoc::getBusinessType));
        List<OASyncLogSnapshotDoc> toDologs = collect.get(CrmTypeMessageEnum.CRM_TODO_TYPE.getType());
        List<OASyncLogSnapshotDoc> notifyLogs = collect.get(CrmTypeMessageEnum.CRM_NOTIFY.getType());

        Integer todoErrorCount=CollectionUtils.isNotEmpty(toDologs)?toDologs.size():0;
        Integer notifyErrorCount=CollectionUtils.isNotEmpty(notifyLogs)?notifyLogs.size():0;
        String toDOMessage=String.format(i18NStringManager.getByEi(I18NStringEnum.s5009,tenantId),todoErrorCount,notifyErrorCount);
        content.append(toDOMessage).append("\n");
        TextInfo titleInfo= TextInfoUtil.colorText();
        textInfos.add(titleInfo);
        titleInfo.setContent(content.toString());
        String tnFilePath = getSyncDataStatusExcelTnFilePath(tenantId, oaSyncLogSnapshotDocs);
        if (StringUtils.isNotEmpty(tnFilePath)) {
            String previewUrl = String.format(userCenterManager.getTnViewUrlFormat(tenantId), tnFilePath, "xlsx");
            String downloadUrl = String.format(userCenterManager.getDownloadFilePath(tenantId), tnFilePath, "oaMessage" + DateUtil.date().toString("yyyyMMdd") + ".xlsx");
            TextInfo text1 = TextInfoUtil.text(I18NStringEnum.s5010.getText() + "  ");
            TextInfo text2 = TextInfoUtil.url(I18NStringEnum.kPreviewOnline.getText(), previewUrl,false);
            TextInfo text3 = TextInfoUtil.text("  ",false);
            TextInfo text4 = TextInfoUtil.url(I18NStringEnum.kDownLoadFile.getText(), downloadUrl,false);
            textInfos.add(text1);
            textInfos.add(text2);
            textInfos.add(text3);
            textInfos.add(text4);
        }
        AdvanceText advanceText = new AdvanceText();
        advanceText.setTextInfoList(textInfos);
        String messageContent = JSON.toJSONString(advanceText, SerializerFeature.DisableCircularReferenceDetect);
        return messageContent;
    }

    private String getSyncDataStatusExcelTnFilePath(String tenantId, List<OASyncLogSnapshotDoc> syncDataStatusMessages){
        List<OAMessageNotifyExcelVo> syncDataNotifyExcelVos = BeanUtil.copyList(syncDataStatusMessages, OAMessageNotifyExcelVo.class);
        OAExcelFileResult.Arg<OAMessageNotifyExcelVo> syncDataNotifyExcelVoArg = new OAExcelFileResult.Arg<>();
        syncDataNotifyExcelVoArg.setTenantId(tenantId);
        syncDataNotifyExcelVoArg.setFileName(i18NStringManager.getByEi2(I18NStringEnum.s5010.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s5010.getI18nValue(), tenantId, LocalDateTime.now().toString()),
                Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
        syncDataNotifyExcelVoArg.setDataList(syncDataNotifyExcelVos);
        syncDataNotifyExcelVoArg.setSheetNames(Lists.newArrayList(i18NStringManager.getByEi(I18NStringEnum.s625,tenantId)));
        com.fxiaoke.open.oasyncdata.result.base.Result<OAExcelFileResult.Result> resultResult = oaExcelFileManager.buildExcelFileResult(i18NStringManager, null, tenantId, syncDataNotifyExcelVoArg);

        log.info("get Excel urlResult:{},{}",resultResult.getErrMsg(),resultResult.getData());
        if(resultResult.isSuccess()){
            return resultResult.getData().getTnFilePath();
        }
        return null;
    }




    public void buildbizLog(String tenantId,OASyncLogSnapshotDoc oaSyncLogSnapshotDoc){
        try {
            QueryOASettingArg queryOASettingArg = new QueryOASettingArg();
            queryOASettingArg.setTenantId(ConfigConst.GLOBAL_TENANT_ID);
            queryOASettingArg.setType(OATenantEnum.OA_SUPPORT_ERROR_LOG.name());
            OASettingDoc oaSettingDoc = getConfigurationType(tenantId, queryOASettingArg);
            if (ObjectUtils.isNotEmpty(oaSettingDoc)) {
                String configuration = oaSettingDoc.getConfiguration();
                Set<String> tenantIds = ImmutableSet.copyOf(Splitter.on(";").split(configuration));
                //配置的企业不上报，避免有测试企业在做干扰测试
                if (!tenantIds.contains(tenantId) && (OASyncLogEnum.FAIL.getSyncType().equals(oaSyncLogSnapshotDoc.getStatus()) || OASyncLogEnum.EXCEPTION.getSyncType().equals(oaSyncLogSnapshotDoc.getStatus()))) {
                    ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder erpSyncMonitorLogDTOBuilder = ErpSyncMonitorLogDTO.builder()
                            .appName(ProcessInfoOA.appName)
                            .serverIp(ProcessInfoOA.serverIp)
                            .profile(ProcessInfoOA.profile)
                            .traceId(TraceUtil.get())
                            .module(OATenantEnum.OA_SUPPORT_ERROR_LOG.name())
                            .eventId(oaSyncLogSnapshotDoc.getDataId())
                            .action(OATenantEnum.OA_SUPPORT_ERROR_LOG.name())
                            .tenantId(tenantId)
                            .status("fail");

                    BizLogClient.send("biz-log-erpsyncmonitor", Pojo2Protobuf.toMessage(erpSyncMonitorLogDTOBuilder, ErpSyncMonitorLog.class).toByteArray());
                    log.info("send biz log,{}", erpSyncMonitorLogDTOBuilder);
                }
            }
        } catch (Exception e) {
            log.warn("send biz log error:{}", e.getMessage());
        }

    }

    public OASettingDoc getConfigurationType(String tenantId,QueryOASettingArg queryOASettingArg){
        OASettingDoc configByType = oaSettingsDao.getConfigByType(tenantId, queryOASettingArg);
        return configByType;
    }


    public List<OASyncLogSnapshotDoc> pageByFilters(QueryOASyncLogArg arg) {
        List<OASyncLogSnapshotDoc> result = new ArrayList<>();
        MongoCollection<OASyncLogSnapshotDoc> collection = mongoStore.getOASyncLogSnapshotCollection(arg.getTenantId());
        Bson filter = buildFilter(arg);
        int offset=(arg.getPage()-1)*arg.getPageSize();
        collection.find(filter)
                .sort(Sorts.descending("updateTime"))
                .skip(offset)
                .limit(arg.getPageSize())
                .into(result);
        return result;
    }

    public OASyncLogSnapshotDoc getById(String tenantId, String objectId){
        Bson queryFilter=Filters.and(Filters.eq("tenantId", tenantId),Filters.eq("_id",new ObjectId(objectId)));
        List<OASyncLogSnapshotDoc> result = new ArrayList<>();
        mongoStore.getOASyncLogSnapshotCollection(tenantId).find(queryFilter).limit(1).into(result);
        if(CollectionUtils.isNotEmpty(result)){
            return result.get(0);
        }
        return null;
    }

    private Bson buildFilter(QueryOASyncLogArg arg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq("tenantId", arg.getTenantId()));
        if (arg.getDataCenterId() != null ) {
            filters.add(Filters.eq(data_center_id, arg.getDataCenterId()));
        }
        if (arg.getDataId() != null ) {
            filters.add(Filters.eq(data_id, arg.getDataId()));
        }
        if (arg.getDataName() != null ) {
            filters.add(Filters.eq(data_name, arg.getDataName()));
        }
        if (arg.getReceiverId() != null ) {
            filters.add(Filters.eq(receiver_id, arg.getReceiverId()));
        }
        if (arg.getEventType() != null ) {
            filters.add(Filters.eq(event_type, arg.getEventType()));
        }
        if (arg.getObjApiName() != null ) {
            filters.add(Filters.eq(object_api_name, arg.getObjApiName()));
        }
        if (arg.getStatus() != null ) {
            filters.add(Filters.eq(status, arg.getStatus()));
        }

        Bson filter = Filters.and(filters);
        return filter;
    }

    public long deleteByObjectId(String tenantId, ObjectId objectId){

        Bson deleteBson=Filters.and(Filters.eq("tenantId", tenantId),Filters.eq("_id", objectId));
        DeleteResult deleteResult = mongoStore.getOASyncLogSnapshotCollection(tenantId).deleteMany(deleteBson);
        return deleteResult.getDeletedCount();
    }

    public long updateByObjectId(String tenantId, ObjectId objectId,OASyncLogSnapshotDoc oaSyncLogSnapshotDoc){

        Bson queryFilter=Filters.and(Filters.eq("tenantId", tenantId),Filters.eq("_id", objectId));

        UpdateResult updateResult = mongoStore.getOASyncLogSnapshotCollection(tenantId).updateMany(queryFilter, buildUpdate(oaSyncLogSnapshotDoc));
        return updateResult.getModifiedCount();
    }

    private Bson buildUpdate(OASyncLogSnapshotDoc oaSyncLogSnapshotDoc){
        List<Bson> updates = new ArrayList<>();
        if(oaSyncLogSnapshotDoc.getDataJson()!=null){
            updates.add(set(data_json,oaSyncLogSnapshotDoc.getDataJson()));
        }
        if(oaSyncLogSnapshotDoc.getStatus()!=null){
            updates.add(set(status,oaSyncLogSnapshotDoc.getStatus()));
        }
        if(oaSyncLogSnapshotDoc.getMessage()!=null){
            updates.add(set(message,oaSyncLogSnapshotDoc.getMessage()));
        }
        if(StringUtils.isNotEmpty(oaSyncLogSnapshotDoc.getUrl())) {
            updates.add(set(url,oaSyncLogSnapshotDoc.getUrl()));
        }
        if(StringUtils.isNotEmpty(oaSyncLogSnapshotDoc.getAplApiName())) {
            updates.add(set(aplApiName,oaSyncLogSnapshotDoc.getAplApiName()));
        }
        if(StringUtils.isNotEmpty(oaSyncLogSnapshotDoc.getMethod())) {
            updates.add(set(method,oaSyncLogSnapshotDoc.getMethod()));
        }
        if(StringUtils.isNotEmpty(oaSyncLogSnapshotDoc.getHeader())) {
            updates.add(set(header,oaSyncLogSnapshotDoc.getHeader()));
        }
        if(StringUtils.isNotEmpty(oaSyncLogSnapshotDoc.getBody())) {
            updates.add(set(body,oaSyncLogSnapshotDoc.getBody()));
        }
        if(StringUtils.isNotEmpty(oaSyncLogSnapshotDoc.getResponse())) {
            updates.add(set(response,oaSyncLogSnapshotDoc.getResponse()));
        }
        return Updates.combine(updates);
    }


}
