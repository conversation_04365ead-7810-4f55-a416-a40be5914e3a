<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>账号未绑定错误页面</title>
  <style>
    html,body {
      position: relative;
      margin: 0;
      height: 100%;
      font-family: Arial, sans-serif;
      overflow: hidden;
    }
    .error-container {
      position: absolute;
      left: 50%;
      top: 10%;
      transform: translateX(-50%);
    }
    .error-container img {
      width: 25em;
      height: auto;
      padding-bottom: 1em;
    }
    .error-message {
      font-size: 1em;
      padding: 0 20px;
      box-sizing: border-box;
    }
    .error-message {
      font-size: 1em;
      padding: 0 20px;
      box-sizing: border-box;
    }
    .error-message span {
      display: flex;
      flex-direction: column;
    }
  </style>
</head>
<body>
<div class="error-container">
  <img src="https://a9.fspage.com/FSR/uipaas/dd-error.svg" alt="错误图片" class="error-image">
  <p class="error-message">抱歉，您的账号登录有异常，请根据错误信息进行排查</p>
  <%
    String errorMessage = request.getParameter("error");
    if (errorMessage != null && !errorMessage.isEmpty()) {
  %>
  <p class="error-message"><strong>错误信息：</strong><%= errorMessage %></p>
  <%
    }
    String traceId = request.getParameter("traceId");
    if (traceId != null && !traceId.isEmpty()) {
  %>
  <p class="error-message"><strong>traceId：</strong><%= traceId %></p>
  <%
    }
  %>
</div>
</body>
</html>
