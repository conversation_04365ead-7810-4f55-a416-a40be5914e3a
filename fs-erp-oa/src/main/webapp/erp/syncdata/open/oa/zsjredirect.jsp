<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>招商局</title>
</head>
<style>
  html,
  body,
  .zsy_app {
    margin: 0;
    padding: 0;
    height: 100%;
    font-size: 14px;
  }
  .zsy_app .zsy_app__content {
    height: 100%;
    background-color: #fff;
    background-repeat: no-repeat;
  }
  .zsy_app__error {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    max-width: 700px;
    min-height: 300px;
    max-height: 500px;
    box-shadow: 0 0 6px #ccc;
    padding: 4rem;
    box-sizing: border-box;
    background: #fff;
    overflow-y: auto;
  }
  .error_msg {
    display: block;
    font-size: 1rem;
    line-height: 1.5;
    margin-top: 1rem;
    color: #545861;
    text-align: center;
  }
  .error_msg__detail {
    display: block;
    color: #c1c5ce;
    text-align: center;
    margin-top: 1.5rem;
  }

  /* PC样式 */
  @media (min-width: 768px) {
    .zsy_app__content {
      background-size: cover;
    }
    .zsy_app__error {
      min-width: 700px;
    }
  }

  /* 移动设备样式 */
  @media (max-width: 768px) {
    .zsy_app__content {
      background-size: 350% 100%;
    }
    .zsy_app__error {
      min-width: 375px;
      top: 35%;
      width: 100%;
      box-shadow: none;
      background: transparent;
    }
  }
</style>

<body>
<div class="zsy_app">
  <div class="zsy_app__content">
    <div class="zsy_app__error">
      <img src="./images/error.svg" alt="error" />
    </div>
  </div>
</div>
</body>

<script type="text/javascript">
  window.onload = () => {
    // 页面加载完执行的逻辑
    const paramsMap = parseSearchParams();
    const errorWrapNode = document.querySelector(".zsy_app__error");
    const imgNode = document.querySelector(".zsy_app__content");

    if (paramsMap.imgUrl) {
      imgNode.style.backgroundImage = "url(" + decodeURIComponent(paramsMap.imgUrl) + ")";
    }

    if (paramsMap.error) {
      const errorNode =
              '<span class="error_msg">' + decodeURIComponent(paramsMap.error) + "</span>";
      errorWrapNode.innerHTML += errorNode;
    }

    if (paramsMap.traceId) {
      const traceIdNode =
              '<span class="error_msg__detail">' +
              decodeURIComponent(paramsMap.traceId) +
              "</span>";
      errorWrapNode.innerHTML += traceIdNode;
    }
  };

  const parseSearchParams = function () {
    const paramsMap = {};
    const searchParams = (window.location.search || "")
            .substring(1)
            .split("&");

    searchParams.forEach((item) => {
      if (item) {
        const splitArr = item.split("=");
        paramsMap[splitArr[0]] = splitArr[1];
      }
    });

    return paramsMap;
  };
</script>
</html>
