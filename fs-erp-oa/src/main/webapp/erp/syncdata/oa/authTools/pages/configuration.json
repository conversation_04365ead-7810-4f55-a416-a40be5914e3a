{"type": "page", "title": "OA配置页面信息", "body": [{"type": "tpl", "tpl": "OA配置页面信息", "inline": false, "id": "u:c1690a98c250"}, {"type": "crud", "syncLocation": false, "api": {"method": "POST", "url": "./getConfigurationList"}, "filter": {"title": "筛选", "body": [{"size": "sm", "label": "tenantId", "type": "input-text", "name": "tenantId", "clearable": true, "clearValueOnEmpty": true}, {"label": "类型", "type": "select", "name": "type", "searchable": true, "creatable": true, "clearable": true, "clearValueOnEmpty": true, "size": "lg", "overlay": {"width": "150%", "align": "left"}, "source": "get:./getTypeList"}]}, "columns": [{"name": "id", "label": "ID", "type": "text", "id": "u:791844e8e089"}, {"type": "text", "name": "tenantId", "label": "企业账号id", "id": "u:4e2013a6577d"}, {"type": "text", "name": "enterpriseName", "label": "企业名称", "id": "u:4e2013a6577dd"}, {"type": "text", "label": "配置类型", "name": "type", "source": "get:./getTypeList", "id": "u:eb883f95e46f"}, {"type": "text", "name": "configuration", "label": "配置信息", "id": "u:4e2013a6577d"}, {"name": "dataCenterId", "label": "数据中心id", "type": "text", "id": "u:0389da5acc1a"}, {"type": "operation", "label": "操作", "buttons": [{"label": "编辑", "type": "button", "actionType": "dialog", "level": "link", "editorSetting": {"behavior": "update"}, "dialog": {"title": "编辑", "body": {"type": "form", "api": "post:./editConfiguration", "body": [{"name": "id", "label": "ID", "type": "input-text", "readOnly": false}, {"name": "tenantId", "label": "企业id", "type": "input-text"}, {"name": "dataCenterId", "label": "数据中心id", "type": "input-text"}, {"label": "类型", "type": "select", "name": "type", "searchable": true, "creatable": true, "clearable": true, "size": "lg", "overlay": {"width": "150%", "align": "left"}, "source": "get:./getTypeList"}, {"name": "configuration", "label": "配置信息", "type": "input-text"}]}}, "id": "u:b78ac64d9a25"}, {"type": "button", "label": "删除", "actionType": "ajax", "level": "link", "className": "text-danger", "confirmText": "确定要删除？", "api": {"method": "post", "url": "./deleteConfiguration?id=${id}", "data": {"id": "${id}", "tenantId": "${tenantId}", "dataCenterId": "${dataCenterId}", "type": "${type}"}}, "editorSetting": {"behavior": "delete"}, "id": "u:a8c336182a62"}], "id": "u:256286a9901f"}], "bulkActions": [], "itemActions": [], "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "editorSetting": {"behavior": "create"}, "dialog": {"title": "新增", "body": {"type": "form", "api": {"method": "post", "url": "./addConfiguration"}, "body": [{"name": "tenantId", "label": "企业id", "type": "input-text"}, {"id": "dcIdSelect", "name": "dataCenterId", "label": "dataCenterId", "type": "input-text", "labelField": "connectOaName", "valueField": "id", "source": "get:./getDataCenterIds?tenantId=${tenantId}", "searchable": true}, {"label": "类型", "type": "select", "name": "type", "searchable": true, "creatable": true, "clearable": true, "clearValueOnEmpty": true, "size": "lg", "overlay": {"width": "150%", "align": "left"}, "source": "get:./getTypeList"}, {"type": "input-text", "name": "configuration", "label": "配置信息"}]}}, "id": "u:123fe760e424"}, "bulkActions"], "id": "u:2cbf0e972d29"}], "id": "u:c246714f204d"}