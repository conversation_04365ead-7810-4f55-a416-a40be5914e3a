package oa;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.MetadataControllerServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.*;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OAResyncLogDoc;
import com.fxiaoke.open.oasyncdata.mongo.OAResyncLogDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncLogDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.manager.*;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.saml.IDPCredentials;
import com.fxiaoke.open.oasyncdata.saml.OpenSamlImplementation;
import com.fxiaoke.open.oasyncdata.saml.SamlKeyStoreProvider;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.service.OAObjectFieldService;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OASyncApiService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jasig.cas.client.util.XmlUtils;
import org.junit.Test;
import org.opensaml.saml.saml2.core.AuthnRequest;
import org.opensaml.saml.saml2.core.impl.LogoutRequestImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/25
 */
@Slf4j
public class OAmanagerTest extends BaseTest {
    //    @Autowired
//    private OASamlAuthController oaSamlAuthController;
    @Autowired
    private OpenSamlImplementation openSamlImplementation;
    @Autowired
    private OAObjectFieldService oaObjectFieldService;
    @Autowired
    private OASyncApiService oaSyncApiService;
    @Autowired
    private ExternalToDoManager externalToDoManager;


    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private OASyncLogDao oaSyncLogDao;

    @Autowired
    private OASyncApiDao oaSyncApiDao;


    @Autowired
    private UserManager userManager;

    @Autowired
    private ApprovalTaskManager approvalTaskManager;

    @Autowired
    private OACommonFieldManager oACommonFieldManager;

    @Autowired
    private OARequestManager oaRequestManager;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OAFlowManager oaFlowManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataControllerServiceV3 metadataControllerServiceV3;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ExternalMsgManager externalMsgManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OAResyncLogDao oaResyncLogDao;

//    @Test
//    public void testHasSyncPloyDetailSnapshot() throws MarshallingException, SecurityException, SignatureException {
//        oaSamlAuthController.generateIDPMetadataXML();
//    }

    @Test
    public void testCredential() {
        String key = "-----BEGIN RSA PRIVATE KEY-----\n" +
                "Proc-Type: 4,ENCRYPTED\n" +
                "DEK-Info: AES-128-CBC,73F05BF6638EE6B41915CCE3E4DB48B8\n" +
                "\n" +
                "NLeHDmIEf4bNCPkCeRT7kcC/y9BTrKi6lV0ZrI4G1M8dQLD4ObsM9kJSCgQy0G1F\n" +
                "Fw9xLA6mBObnkd4FFI37pi/OELotAH/ECojk0Z2+FxS7oxGUnq+3dZMnCXnDZBZm\n" +
                "7oaNEdAtCRujcBK5ND5eDr6ImNodY6VOED65v1cf3el0Lnk5zXCKzCFfLgntoq0O\n" +
                "VyqloWsAaQZ9pOId8ngSdlQkqHOINcZxzjGP9IPCWvho1xW3afNxkSKB3KKyDRhe\n" +
                "ZQas8YB/1mE2r99YnFJc9K/dfI5sBCp3p5+t3K0N0QAJx34BtjyZOq1gRFJ7z/bd\n" +
                "CS/irrx8lgiBaTeHoifSXtmK/57F8WnzMMHyISQzvTY8AX66Q7A5c7vy1dAWwnMQ\n" +
                "Cr4pourHw/dYGXMTnRis/GRVHX9KQg98N9W3TPV4wFtZPPGms1ZUlDXp9/N04WP9\n" +
                "b2q6uGi7Q2u9xMp7T1OugziuUtQmslw45DQRkh3tqGeT0dVha/DAjsImKXr/rVWq\n" +
                "ci7saErc1pbLpA0itjJY4cS2LvUWEZPzByaWGEYxwZMVGJ4AYu8is8lWRxQI9L8H\n" +
                "dC6yAvJjbp1+C/vLewP7eU2CofypLlfKduec0GhemLpN5ipEUkwVqHKcD4nhZ7e8\n" +
                "1Z4CQh62uLS7ntPv6jpvJ8zvXrCOoAfnE8FpZje/sdStCkEX9yDm+QwayPE8USai\n" +
                "MXKMKtUkuJhRKA4OucvHIjh3MXfS3ybxcqSJFG86ETOS7XqwHIscKe8XqyYtEApA\n" +
                "r6uZyPrP9YaNlEU37kc7pP+J15WkJ8zudrTX1he7NpRihg9ZYOtPVD9m4Avlg6ex\n" +
                "VSxq9ahEDvjBy7iS9uS8SUggVm9ZJ+d+Q34/pE35ZQ8U88a6st5KHKVGxnilELeD\n" +
                "eWg76oQA66S/MQmCH7MlIDNST8saLlyCR7wVRFL+5hzMo6sbPJWiqqM9WyLvrTXD\n" +
                "L0qPhQvJq2+FBUsjjfPNxglXS1hBzZqOztJe4asSRR9LhVOiwRMwOWzaU5sfg81w\n" +
                "J8OrIMZfHY/leKGjrfBXfM+3jvVaGQGzwDGkI5QliZrRrXzjht0tJ6AuZ40DW15F\n" +
                "cEnXQPasB5hEt5lTevHomTaN2yapvL8WlFm3MD653fMr4uNwVMtRF+XpXDROqQW9\n" +
                "AQBkjJdq6HDVfgeYK0f/sZFp7wjHHlHTaSkIK+fn3Glk2Vj5mqCPj49r2kYqVeVV\n" +
                "qSRtU/z718VoF0krnBF7FBTVeApV5TJs+Ol0WSWUEr8cOTYzOl7CDEOBA4R8D3lp\n" +
                "aGKfT9YIg/okFUOZ2aheL5ZvAkOk9esnJrGqydHe31EpS1h/gDUuziBD1TVPj6kf\n" +
                "JiQf0rQU5SpU/RFhSyl1FyUhAuJBJ0nniUmHvDg4F2zexY/1wQItSTFN/4oe8daA\n" +
                "RU+z2iHa0HLZozKMg38Jwcb/EfJHfgc1ikyzNGWJGYWGmv7CvWo41A47mpEnGxKf\n" +
                "aREQECRDeua25vh9Br4D5LV6/fZFvvdn4l8ogGohk9Gqv8QH0M8Tu0y3Uxat3Tk9\n" +
                "vlKQen2eF4AS6+PxNi5X6eorko3UIIObGNHNEi6AYmUDblGvhmD9IfJ1hCgQ5eqR\n" +
                "-----END RSA PRIVATE KEY-----\n";
        String credential = "-----BEGIN CERTIFICATE-----\n" +
                "MIID2zCCAsOgAwIBAgIUOVDrjfV9mXHna0pWR00rrB48rc0wDQYJKoZIhvcNAQEL\n" +
                "BQAwfTELMAkGA1UEBhMCQ04xCzAJBgNVBAgMAkJKMQswCQYDVQQHDAJCSjEQMA4G\n" +
                "A1UECgwHZnhpYW9rZTEQMA4GA1UECwwHZnhpYW9rZTEQMA4GA1UEAwwHZnhpYW9r\n" +
                "ZTEeMBwGCSqGSIb3DQEJARYPdXNzQGZ4aWFva2UuY29tMB4XDTIwMDcwNDA0NTAw\n" +
                "OVoXDTIwMDgwMzA0NTAwOVowfTELMAkGA1UEBhMCQ04xCzAJBgNVBAgMAkJKMQsw\n" +
                "CQYDVQQHDAJCSjEQMA4GA1UECgwHZnhpYW9rZTEQMA4GA1UECwwHZnhpYW9rZTEQ\n" +
                "MA4GA1UEAwwHZnhpYW9rZTEeMBwGCSqGSIb3DQEJARYPdXNzQGZ4aWFva2UuY29t\n" +
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArhdRu9pOvKmiN+nycY6Y\n" +
                "ic/SHdDIEQINEiwkInKj0r3i5U1WP/tP/ulahRubwTwG4hNbqsv7QXvGJpiuE11Z\n" +
                "GGIdNn8obHb9lGf0uydlioDSxpCPFCc4AyAd9L1PQYwbQljJO/Ee1iSKAARA5YET\n" +
                "1HjrhV75TvMnKta0jaXsuItCPm+Nacf19xMwlxggxawcJikdVkyr3yG4SgSXHgkb\n" +
                "U2tlGnluWhCPxtH9ZBp8IzdfOSjqPSCcXPL0qgiDPGmJ71Ul10tbbXGHbkZCOaAp\n" +
                "/tFCo/BUoHaLRG6LZofLYUqDmAIqe7xE2mWWq3HOna5dsmrHszf9ju2IpkUnZN9d\n" +
                "kwIDAQABo1MwUTAdBgNVHQ4EFgQUJE3GH3qnyKuZh3jSj2HtjK9ua6QwHwYDVR0j\n" +
                "BBgwFoAUJE3GH3qnyKuZh3jSj2HtjK9ua6QwDwYDVR0TAQH/BAUwAwEB/zANBgkq\n" +
                "hkiG9w0BAQsFAAOCAQEAndHbMj7gHKMNJa/fTF17oomWjpcTR4ZaDZQuzAkoJP5E\n" +
                "2Z0OIVE7gaSgXimBbtoicTiXBp1IabZQSQcIdjQTaFC8nzdZaDTsp5HTYYOvyojs\n" +
                "XiL0h1HTl4I48ETxWsnZypLRuwdyDeMEb+6ndGtG7DZ4ts84YHqjLIh5yJrMGb0+\n" +
                "2U8/CVKCNW+sMrT5vasdDSX2HqcKV+ac00awKLtkfnMjOs64Xg6gNsoGrEzZWUXg\n" +
                "voTwcQ25Q2njRTt7zB0t37a4UX1+TOlhmykmKhTZqPsy+eKV11LWqh0F26SeCVOu\n" +
                "pzctiTX8oL7xk5QVBTQ5/qKr1whwye7dyhU4CN/n+g==\n" +
                "-----END CERTIFICATE-----\n";
        SamlKeyStoreProvider.getCredential(key, credential, "fxiaoke_uss");
    }

    @Test
    public void idpTest() {
        IDPCredentials.getCredential("fxiaoke");
    }

    @Test
    public void testElement() {
        String caseResult = "<cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>\n" +
                "\t<cas:authenticationSuccess>\n" +
                "\t\t<cas:user>zhouzhikai</cas:user>\n" +
                "\t\t<cas:attributes>\n" +
                "\t\t\t<cas:tgt>\n" +
                "\t\t\t\tTGT-1145-X3y7ZKjuAHxcIbpP1IpNWDsn-mdlrezTj7vhJTPJsp53chhHjHF85q4jO2fvjnbZb8Arg-sso-6c99d7445b-mjt4qobjectId=61b0123fce70620006adba86\n" +
                "\t\t\t</cas:tgt>\n" +
                "\t\t\t<cas:GH>zhouzhikai</cas:GH>\n" +
                "\t\t\t<cas:isFromNewLogin>false</cas:isFromNewLogin>\n" +
                "\t\t\t<cas:authenticationDate>2021-12-10T17:39:50.331</cas:authenticationDate>\n" +
                "\t\t\t<cas:successfulAuthenticationHandlers>MultiIdPasswordAuthenticationHandler\n" +
                "\t\t\t</cas:successfulAuthenticationHandlers>\n" +
                "\t\t\t<cas:RJXM>周智凯</cas:RJXM>\n" +
                "\t\t\t<cas:samlAuthenticationStatementAuthMethod>urn:oasis:names:tc:SAML:1.0:am:password\n" +
                "\t\t\t</cas:samlAuthenticationStatementAuthMethod>\n" +
                "\t\t\t<cas:credentialType>UsernamePasswordCredential</cas:credentialType>\n" +
                "\t\t\t<cas:RJEMAIL><EMAIL></cas:RJEMAIL>\n" +
                "\t\t\t<cas:completedLoginMethod>UsernamePassword</cas:completedLoginMethod>\n" +
                "\t\t\t<cas:authenticationMethod>MultiIdPasswordAuthenticationHandler</cas:authenticationMethod>\n" +
                "\t\t\t<cas:TEL>***********</cas:TEL>\n" +
                "\t\t\t<cas:longTermAuthenticationRequestTokenUsed>false</cas:longTermAuthenticationRequestTokenUsed>\n" +
                "\t\t\t<cas:RJGH>R16582</cas:RJGH>\n" +
                "\t\t\t<cas:primaryKey>GH</cas:primaryKey>\n" +
                "\t\t</cas:attributes>\n" +
                "\t</cas:authenticationSuccess>\n" +
                "</cas:serviceResponse>";
        String user = XmlUtils.getTextForElement(caseResult, "user");
        String userAccount = XmlUtils.getTextForElement(caseResult, "GH");
        String userName = XmlUtils.getTextForElement(caseResult, "RJXM");
        String email = XmlUtils.getTextForElement(caseResult, "RJEMAIL");
        String tel = XmlUtils.getTextForElement(caseResult, "TEL");
        String employeeNumber = XmlUtils.getTextForElement(caseResult, "RJGH");
        System.out.println(user);
        ;
    }

    @Test
    public void testData() {
        String data = "/erp/syncdata/open/oa/saml/sso";

        boolean result = data.startsWith("/erp/syncdata/noAuth")
                || data.startsWith("/cep")
                || data.startsWith("/swagger")
                || data.startsWith("/inner")
                || data.startsWith("/erp/syncdata/open/oa")
                || data.startsWith("/erp/syncdata/open/saml")
                || data.startsWith("/erp/syncdata/out");

        String login = "<samlp:AuthnRequest xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\" xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:assertion\" ID=\"ONELOGIN_2d4e3465-5a34-4c72-a58b-7710eecd899a\" Version=\"2.0\" IssueInstant=\"2021-12-14T09:31:33Z\" Destination=\"https://www.ceshi112.com/erp/syncdata/open/saml/sso\" ProtocolBinding=\"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST\" AssertionConsumerServiceURL=\"https://erpdss.my.ceshi112.com/saml2/sp/sso/login\"><saml:Issuer>spn:434e38be64b7f51a674bce6dcad2422e</saml:Issuer><samlp:NameIDPolicy Format=\"urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified\" AllowCreate=\"true\" /></samlp:AuthnRequest>";
        AuthnRequest authnRequest = (AuthnRequest) openSamlImplementation.transferXML2SAMLObject(login);

        String loginOut = "<samlp:LogoutRequest xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\" xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:assertion\" ID=\"ONELOGIN_3c27e9d2-b431-44ff-bddf-e642af96b418\" Version=\"2.0\" IssueInstant=\"2021-12-14T09:33:10Z\" Destination=\"https://www.ceshi112.com/erp/syncdata/open/saml/logout\" ><saml:Issuer>spn:434e38be64b7f51a674bce6dcad2422e</saml:Issuer><saml:NameID Format=\"urn:oasis:names:tc:SAML:2.0:nameid-format:entity\">https://www.ceshi112.com/erp/syncdata/open/saml/metadata</saml:NameID></samlp:LogoutRequest>";
        LogoutRequestImpl loginOutRes = (LogoutRequestImpl) openSamlImplementation.transferXML2SAMLObject(loginOut);
        System.out.println("success");
    }

    @Test
    public void updateOA() {
        List<String> allTenantIds = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
        List<String> tenantIds = allTenantIds.stream().distinct().collect(Collectors.toList());//去重
        List<String> NOT_RE_SYNC_TENANT = ConfigCenter.NOT_RE_SYNC_TENANT;
        tenantIds.removeAll(NOT_RE_SYNC_TENANT);


        List<OASyncApiVO> oaSyncApiVOS = Lists.newArrayList();
        OASyncApiVO oaSyncApiVO = new OASyncApiVO();
        oaSyncApiVO.setObjApiName("BpmTask");
        oaSyncApiVO.setTenantId("82379");
        oaSyncApiVO.setDescription("流程");
        oaSyncApiVO.setEventType("3");
        oaSyncApiVO.setStatus("2");
        oaSyncApiVOS.add(oaSyncApiVO);

        OASyncApiVO oaSyncApiVO1 = new OASyncApiVO();
        BeanUtils.copyProperties(oaSyncApiVO, oaSyncApiVO1);
        oaSyncApiVO1.setEventType("2");
        oaSyncApiVO1.setObjApiName("BpmTask");

        OASyncApiVO oaSyncApiVO2 = new OASyncApiVO();
        BeanUtils.copyProperties(oaSyncApiVO, oaSyncApiVO1);
        oaSyncApiVO2.setEventType("2");
        oaSyncApiVO2.setStatus("3");
        oaSyncApiVO2.setObjApiName("BpmTask");
        oaSyncApiVO2.setId("677278967910498304");

        oaSyncApiVOS.add(oaSyncApiVO);
        oaSyncApiVOS.add(oaSyncApiVO1);
        oaSyncApiVOS.add(oaSyncApiVO2);



        oaSyncApiService.updateOASyncApi(oaSyncApiVOS, "82379",null);
    }

    @Test
    public void testUpdateStatus() {

        String msg="[\n" +
                "    {\n" +
                "        \"eventType\": \"1\",\n" +
                "        \"status\": \"1\",\n" +
                "        \"url\": \"https://www.ceshi112.com/erp/syncdata/open/oa/hello\",\n" +
                "        \"requestMode\": \"POST\",\n" +
                "        \"tenantId\": \"84801\",\n" +
                "        \"description\": \"\",\n" +
                "        \"createTime\": 1684832086447,\n" +
                "        \"updateTime\": 1684908918221,\n" +
                "        \"id\": \"888392800357974016\",\n" +
                "        \"objApiName\": \"ApprovalTaskObj\",\n" +
                "        \"dataTemplate\": \"{\\n    \\\"action\\\": \\\"3\\\",\\n    \\\"kingdeeTodoDTO\\\": {\\n        \\\"jobNos\\\":\\\"#F001\\\",\\n        \\\"sourceId\\\": \\\"#F012\\\"\\n    },\\n    \\\"notifyTodoDTO\\\": {\\n        \\\"modelName\\\": \\\"请审批【#F054】提交的流程：【#F056】\\\",\\n        \\\"optType\\\": \\\"1\\\",\\n        \\\"taskId\\\": \\\"#F012\\\"\\n    },\\n    \\\"sysCode\\\": \\\"S0447\\\"\\n}\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventType\": \"2\",\n" +
                "        \"status\": \"1\",\n" +
                "        \"url\": \"https://www.ceshi112.com/erp/syncdata/open/oa/hello\",\n" +
                "        \"requestMode\": \"POST\",\n" +
                "        \"tenantId\": \"84801\",\n" +
                "        \"description\": \"\",\n" +
                "        \"createTime\": 1684151168509,\n" +
                "        \"updateTime\": 1684908918241,\n" +
                "        \"id\": \"886108018919997440\",\n" +
                "        \"objApiName\": \"ApprovalTaskObj\",\n" +
                "        \"dataTemplate\": \"{\\n name: '111',\\n code: 1\\n}\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventType\": \"3\",\n" +
                "        \"status\": \"1\",\n" +
                "        \"url\": \"https://www.ceshi112.com/erp/syncdata/open/oa/hello\",\n" +
                "        \"requestMode\": \"DELETE\",\n" +
                "        \"tenantId\": \"84801\",\n" +
                "        \"description\": \"\",\n" +
                "        \"createTime\": 1684833144831,\n" +
                "        \"updateTime\": 1684908918252,\n" +
                "        \"id\": \"888396351725502464\",\n" +
                "        \"objApiName\": \"BpmTask\",\n" +
                "        \"dataTemplate\": \"{\\n    \\\"action\\\": \\\"3\\\",\\n    \\\"kingdeeTodoDTO\\\": {\\n        \\\"jobNos\\\":\\\"#F001\\\",\\n        \\\"sourceId\\\": \\\"#F012\\\"\\n    },\\n    \\\"notifyTodoDTO\\\": {\\n        \\\"modelName\\\": \\\"请审批【#F054】提交的流程：【#F056】\\\",\\n        \\\"optType\\\": \\\"1\\\",\\n        \\\"taskId\\\": \\\"#F012\\\"\\n    },\\n    \\\"sysCode\\\": \\\"S0447\\\"\\n}\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"eventType\": \"3\",\n" +
                "        \"status\": \"1\",\n" +
                "        \"url\": \"https://www.ceshi112.com/erp/syncdata/open/oa/hello\",\n" +
                "        \"requestMode\": \"DELETE\",\n" +
                "        \"tenantId\": \"84801\",\n" +
                "        \"description\": \"\",\n" +
                "        \"createTime\": 1684833144831,\n" +
                "        \"updateTime\": 1684908918252,\n" +
                "        \"id\": \"886106317106315264\",\n" +
                "        \"objApiName\": \"ApprovalTaskObj\",\n" +
                "        \"dataTemplate\": \"{\\n name: '111',\\n code: 1\\n}\"\n" +
                "    }\n" +
                "]";
        List<OASyncApiVO> oaSyncApiVOS= JSONArray.parseArray(msg,OASyncApiVO.class);

        oaSyncApiService.updateOASyncApi(oaSyncApiVOS, "82379",null);

        OASyncApiSettingVo oaSyncApiSettingVo = new OASyncApiSettingVo();
        oaSyncApiSettingVo.setStatus("1");
        oaSyncApiSettingVo.setEventType("1");
        oaSyncApiSettingVo.setTenantId("82379");
        oaSyncApiService.updateOaApiStatus(oaSyncApiSettingVo, "82379",null);
    }

    @Test
    public void testoa() {
            String data="{\"ea\":\"88521\",\"ei\":88521,\"extraDataMap\":{\"objectApiName\":\"LeadsObj\",\"objectId\":\"64fade67cac9ec0001e41a6b\"},\"generateUrlType\":1,\"groupKeys\":[],\"messageContent\":\"管理员 给您分配了销售线索：待办验证080201\",\"receiverChannelData\":\"{\\\"appId\\\":\\\"crmNotify\\\"}\",\"receiverChannelType\":1,\"receiverIds\":[1002],\"senderId\":0,\"title\":\"分配销售线索\",\"url\":\"todo?apiname=LeadsObj&id=64fade67cac9ec0001e41a6b&ea=88521&ea=88521\"}";

            String filedJson="{\"type\":\"card\",\"content\":\"管理员 给您分配了销售线索：待办验证080201\",\"TENANTID\":\"88521\",\"prototype\":\"#H010\",\"appId\":\"{\"appId\":\"crmNotify\"}\"}";
        filedJson=filedJson.replaceAll("#H010",data);
        filedJson=filedJson.replace("#H010",data);

            //        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult1 = approvalTaskManager.queryBizDataByType("88521",
//                "64e85c4910623d0001c3a91c401", "LeadsObj","64e85c4910623d0001c3a91c",ObjectApiEnum.TO_DISTRIBUTE_LEADSOBJ.getBizType(), EventTypeEnum.UPDATE.getType());
//
//        log.info("describeResult");


//        OAConnectInfoEntity oaConnectInfoEntity2 = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81961")).getByTenantId("81961");
//
//        log.info("oaConnectInfoEntity");
        //String data ="{\"ea\":\"88521\",\"ei\":88521,\"senderId\":1000,\"receiverIds\":[1000],\"sourceId\":\"64edb0558e6c060001993ca5401\",\"bizType\":\"401\",\"url\":\"todo?apiname\\u003dLeadsObj\\u0026id\\u003d64edb0558e6c060001993ca5\\u0026ea\\u003d88521\",\"title\":\"待分配的销售线索\",\"content\":\"待办验证080210008\",\"form\":[{\"key\":\"姓名\",\"value\":\"待办验证080210008\"},{\"key\":\"公司名称\",\"value\":\"待办验证080206\"},{\"key\":\"手机\",\"value\":\"--\"},{\"key\":\"线索阶段\",\"value\":\"潜在线索(Lead)\"},{\"key\":\"负责人\",\"value\":\"--\"}],\"generateUrlType\":1,\"extraDataMap\":{\"objectApiName\":\"LeadsObj\",\"objectId\":\"64edb0558e6c060001993ca5\"},\"groupKeys\":[]}";
       // String data ="{\"ea\":\"83384\",\"ei\":83384,\"senderId\":-10000,\"receiverIds\":[1041],\"sourceId\":\"64edbc38c6b151236b6b7e8a\",\"bizType\":\"457WaitApproval\",\"url\":\"bpm?workflowInstanceId\\u003d64edbc38c6b151236b6b7e89\\u0026ea\\u003d83384\",\"content\":\"工单待办类型(2023-08-29 17:36)\",\"form\":[{\"key\":\"流程主题\",\"value\":\"工单待办类型(2023-08-29 17:36)\"},{\"key\":\"任务名称\",\"value\":\"待审批工单\"},{\"key\":\"任务开始时间\",\"value\":\"2023-08-29 17:36\"},{\"key\":\"允许停留时长\",\"value\":\"0.00小时\"},{\"key\":\"工单编码\",\"value\":\"WO-20230829-0029\"}],\"generateUrlType\":2,\"extraDataMap\":{\"activityId\":\"1663903099365\",\"objectApiName\":\"CasesObj\",\"activityInstanceId\":\"2\",\"workflowInstanceId\":\"64edbc38c6b151236b6b7e89\",\"taskId\":\"64edbc38c6b151236b6b7e8a\",\"objectId\":\"64edbc373d6abc0001e9a123\"},\"groupKeys\":[\"webhookMessageDistribute\",\"dingCloudGroup\"]}";
//        String data ="{\"ea\":\"89029_sandbox\",\"ei\":89029,\"senderId\":1000,\"receiverIds\":[1000],\"sourceId\":\"64f86fd670ba610001a6a5ed402\",\"bizType\":\"402\",\"url\":\"todo?apiname\\u003dLeadsObj\\u0026id\\u003d64f86fd670ba610001a6a5ed\\u0026ea\\u003d89029_sandbox\",\"title\":\"待跟进的销售线索\",\"content\":\"线索池消息推送07\",\"form\":[{\"key\":\"姓名\",\"value\":\"线索池消息推送07\"},{\"key\":\"公司\",\"value\":\"线索池消息推送07\"},{\"key\":\"手机\",\"value\":\"--\"},{\"key\":\"线索阶段\",\"value\":\"潜在线索(Lead)\"},{\"key\":\"负责人\",\"value\":\"管理员\"}],\"generateUrlType\":1,\"extraDataMap\":{\"objectApiName\":\"LeadsObj\",\"objectId\":\"64f86fd670ba610001a6a5ed\"},\"groupKeys\":[]}";    CreateTodoArg createTodoArg = JSONObject.parseObject(data, CreateTodoArg.class);
//        externalToDoManager.createTodo(createTodoArg);


//
//        String createTodoArgJson = new Gson().toJson(createTodoArg);
//        log.info("ExternalToDoManager createTodo json:{}", createTodoArgJson);
//        String tenantId = String.valueOf(createTodoArg.getEi());
//        CreateTodoResult createTodoResult = new CreateTodoResult();
//        createTodoResult.setCode(200);
//        ObjectApiEnum objectApiEnum = ObjectApiEnum.getObjApiEnumByBizType(createTodoArg.getBizType());
//
//        // 插入同步中日志
//        String logId = idGenerator.get();
//        ObjectApiEnum objApiEnumByBizType = ObjectApiEnum.getObjApiEnumByBizType(createTodoArg.getBizType());
//        //查询detail
//        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = approvalTaskManager.queryBizDataByType(tenantId, createTodoArg.getSourceId(), objApiEnumByBizType.getObjApiName(),createTodoArg.getExtraDataMap().get("objectId"),objApiEnumByBizType.getBizType(),EventTypeEnum.ADD.getType());
//        ObjectData objectData = describeResult.getData().getData();
//        //组装title,不同业务类型标识的title字段不一致
//        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
//        OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId, OAEventEnum.CREATE.getEventStatus(),
//                objectApiEnum.getObjApiName());
//        Map<String, String> title = oACommonFieldManager.builderTitle(objectData, oaSyncApiEntity,"");
//        OASyncLogEntity oaSyncLogEntity =
//                OASyncLogEntity.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).status(OASyncLogEnum.SYNC.getSyncType()).id(logId).
//                        updateTime(System.currentTimeMillis()).createTime(System.currentTimeMillis()).objApiName(objectApiEnum.getObjApiName()).
//                        eventType(OAEventEnum.CREATE.getEventStatus()).message("同步中").title(title.get("name")).dataJson(createTodoArgJson).build();
//
//
//        oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaSyncLogEntity);
//        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
//
//        // json字段转换
//        // 另外manager,避免影响
////        List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeOAField(tenantId, oaSyncApiEntity, describeResult.getData(), null);
//        List<OARequestModel> oaRequestModelList = oaFieldManager.exchangeOAField(tenantId, oaSyncApiEntity, Lists.newArrayList(describeResult.getData().getData()), null);
//        for (OARequestModel oaRequestModel : oaRequestModelList) {
//            // 替换布局字段
//            oACommonFieldManager.exchangeLayoutField(createTodoArg, oaRequestModel);
//            // 用户没绑定直接返回错误
//            Result<String> result = Result.newError(ResultCodeEnum.OA_USER_NOT_BIND);
//            if (StringUtils.isNotEmpty(oaRequestModel.getOaReceiverId())) {
//                result = oaRequestManager.callRestfulService(oaConnectParam, oaSyncApiEntity.getUrl(),
//                        oaRequestModel.getRequestJson(), tenantId);
//
//                log.info("result");
//            }
//
//            if (!result.isSuccess()) {
//                log.warn("createTodo error,data={},result={}", new Gson().toJson(createTodoArg), result);
//                OASyncLogEntity oaSyncLogEntity1 =
//                        OASyncLogEntity.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).status(OASyncLogEnum.FAIL.getSyncType()).id(idGenerator.get()).
//                                updateTime(System.currentTimeMillis()).createTime(System.currentTimeMillis()).objApiName(objectApiEnum.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
//                                eventType(OAEventEnum.CREATE.getEventStatus()).title(title.get("name")).message(result.getErrMsg()).dataJson(oaRequestModel.getRequestJson()).build();
//                oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaSyncLogEntity1);
//            } else {
//                OASyncLogEntity oaSyncLogEntity2 =
//                        OASyncLogEntity.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).status(OASyncLogEnum.SUCCESS.getSyncType()).id(idGenerator.get()).
//                                updateTime(System.currentTimeMillis()).createTime(System.currentTimeMillis()).objApiName(objectApiEnum.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
//                                eventType(OAEventEnum.CREATE.getEventStatus()).title(title.get("name")).message(result.getErrMsg()).dataJson(oaRequestModel.getRequestJson()).build();
//                oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaSyncLogEntity2);
//            }
//            // 上传神策
//            BuriedSitesStatisticsUtil.uploadBuriedStitesLog(tenantId, objectApiEnum.getObjApiName());
//        }
//        oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(logId);
    }


    @Test
    public void testDeal() {

        List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.listInfo("88521");

        List<OAConnectParam> oaConnectParams =oaConnectInfoEntities.stream().map(u -> JSONObject.parseObject(u.toString(), OAConnectParam.class)).collect(Collectors.toList());

        String deal = "{\"ea\":\"88521\",\"ei\":88521,\"receiverIds\":[1000],\"sourceId\":\"65d317a9c26b8d323e19a182\",\"bizType\":\"452\",\"handleUserIds\":[1000],\"generateUrlType\":0,\"extraDataMap\":{},\"groupKeys\":[]}";
        String dealTodoArgJson = new Gson().toJson(deal);
//        DealTodoArg dealTodoArg = JSONObject.parseObject(deal, DealTodoArg.class);
//        log.info(" ExternalToDoManager dealTodo json:{}", dealTodoArgJson);
//        externalToDoManager.dealTodo(dealTodoArg);
        CreateTodoArg createTodoArg=JSONObject.parseObject(deal,CreateTodoArg.class);
        externalToDoManager.createTodo(createTodoArg,false,null);
//        DealTodoResult dealTodoResult = new DealTodoResult();
//        dealTodoResult.setCode(200);
//        String tenantId = String.valueOf(dealTodoArg.getEi());
//        ObjectApiEnum objectApiEnum = ObjectApiEnum.getObjApiEnumByBizType(dealTodoArg.getBizType());
//
//        // 插入同步中日志
//        String logId = idGenerator.get();
//
//        ObjectApiEnum objApiEnumByBizType = ObjectApiEnum.getObjApiEnumByBizType(dealTodoArg.getBizType());
//        //查询detailscribeResult = approvalTaskManager.queryBizDataByType(tenantId, dealTodoArg.getSourceId(), objApiEnumByBizType.getObjApiName(),dealTodoArg.getExtraDataMap().get(""),objApiEnumByBizType.getBizType(),EventTypeEnum.UPDATE.getType());
////        ObjectData objectData = describeResult.getData().getData();
//        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> de
//
//        String title = objectData.get("object_api_name__r") + "-" + objectData.get("object_data_id__r");
//        OASyncLogEntity oaSyncLogEntity =
//                OASyncLogEntity.builder().tenantId(tenantId).dataId(dealTodoArg.getSourceId()).status(OASyncLogEnum.SYNC.getSyncType()).id(logId).
//                        updateTime(System.currentTimeMillis()).createTime(System.currentTimeMillis()).objApiName(objectApiEnum.getObjApiName()).
//                        eventType(OAEventEnum.DEAL.getEventStatus()).message("同步中").dataJson(dealTodoArgJson).title(title).build();
//        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
//        OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId, OAEventEnum.DEAL.getEventStatus(),
//                objectApiEnum.getObjApiName());
//
//        oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaSyncLogEntity);
//        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
//
//
//        // json字段转换
//        List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeOAField(tenantId, oaSyncApiEntity, describeResult.getData(),
//                dealTodoArg.getOperators());
//        for (OARequestModel oaRequestModel : oaRequestModelList) {
//            // 用户没绑定直接返回错误
//            Result<String> result = Result.newError(ResultCodeEnum.OA_USER_NOT_BIND);
//            if (StringUtils.isNotEmpty(oaRequestModel.getOaReceiverId())) {
//                result = oaRequestManager.callRestfulService(oaConnectParam, oaSyncApiEntity.getUrl(),
//                        oaRequestModel.getRequestJson(), tenantId);
//            }
//        }

    }


    @Test
    public void testDelete() {
        String deleteJson = "{\"ea\":\"89029_sandbox\",\"ei\":89029,\"sourceId\":\"6597a9e2f6e92d00019f9a80401\",\"bizType\":\"401\",\"onlyDeleteEmployee\":true,\"deleteEmployeeIds\":[1000],\"generateUrlType\":0,\"extraDataMap\":{},\"groupKeys\":[]}";
        DeleteTodoArg deleteTodoArg = JSONObject.parseObject(deleteJson, DeleteTodoArg.class);
        DeleteTodoResult deleteTodoResult1 = externalToDoManager.deleteTodo(deleteTodoArg,false,null);


//        DeleteTodoResult deleteTodoResult = new DeleteTodoResult();
//        deleteTodoResult.setCode(200);
//        String tenantId = String.valueOf(deleteTodoArg.getEi());
//        ObjectApiEnum objectApiEnum = ObjectApiEnum.getObjApiEnumByBizType(deleteTodoArg.getBizType());
//
//        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
//        OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId, OAEventEnum.DELETE.getEventStatus(),
//                objectApiEnum.getObjApiName());
//
//        // 插入同步中日志
//        String logId = idGenerator.get();
//        OASyncLogEntity oaSyncLogEntity =
//                OASyncLogEntity.builder().tenantId(tenantId).dataId(deleteTodoArg.getSourceId()).status(OASyncLogEnum.SYNC.getSyncType()).id(logId).
//                        updateTime(System.currentTimeMillis()).createTime(System.currentTimeMillis()).objApiName(objectApiEnum.getObjApiName()).
//                        eventType(OAEventEnum.DELETE.getEventStatus()).message("同步中").dataJson(deleteJson).build();
//        oaSyncLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaSyncLogEntity);
//        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
//
//        // json字段转换
//        List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeDeleteOAField(tenantId, oaSyncApiEntity, null, deleteTodoArg);
//        for (OARequestModel oaRequestModel : oaRequestModelList) {
//            // 用户没绑定直接返回错误
//            Result<String> result = Result.newError(ResultCodeEnum.OA_USER_NOT_BIND);
//            if (StringUtils.isNotEmpty(oaRequestModel.getOaReceiverId())) {
//                result = oaRequestManager.callRestfulService(oaConnectParam, oaSyncApiEntity.getUrl(),
//                        oaRequestModel.getRequestJson(), tenantId);
//            }



    }


    @Test
    public void testOa() throws UnsupportedEncodingException {
        List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId("88521").listInfo("88521");
        List<OAConnectParam> oaConnectParams =oaConnectInfoEntities.stream().map(u -> JSONObject.parseObject(u.getConnectParams().toString(), OAConnectParam.class)).collect(Collectors.toList());


    }


    @Test
    public void testCrmFlow() {

        for (int i = 0; i < 10; i++) {
            OAFlowMqConfigEntity oaFlowMqConfigEntity = new OAFlowMqConfigEntity();
            oaFlowMqConfigEntity.setEventType("instance_complete");
            oaFlowMqConfigEntity.setTenantId("8196100");

            oaFlowMqConfigEntity.setObjApiName(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getObjApiName());
            List<OAFlowMqConfigEntity> oaFlowMqConfigEntities = oaFlowManager.queryList("8196100", oaFlowMqConfigEntity);
            oaFlowMqConfigEntity.setTenantId("819610");
            oaFlowManager.queryList("819610", oaFlowMqConfigEntity);
            oaFlowManager.queryList("819610", oaFlowMqConfigEntity);
        }

    }

    @Test
    public void testCrmData() throws UnsupportedEncodingException {

        Integer tenantId = Integer.valueOf("84801");
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add("64489af8787f0b000177dc19");
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setObjectDataId("64489af8787f0b000177dc19");
        controllerDetailArg.setObjectDescribeApiName("AccountObj");
        controllerDetailArg.setIsFromRecycleBin(Boolean.TRUE);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> accountObj1 = objectDescribeService.getDescribe(headerObj, "AccountObj");
        Result<ObjectData> accountObj = getAllStatusData("AccountObj", "84801", "64489af8787f0b000177dc19");
        accountObj1.getData().setData(accountObj.getData());
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> detail = metadataControllerService.detail(headerObj, "AccountObj", controllerDetailArg);


        log.info("accountObj");

    }

    public Result<ObjectData> getAllStatusData(String objectApiName, String tenantId, String id) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.setSearchSource("db");
        searchQueryInfo.addFilter("_id", Collections.singletonList(id), FilterOperatorEnum.EQ);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2", "-1", "0", "1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> resultResult = objectDataServiceV3.findOne(headerObj, findV3Arg);
        ObjectData objectData = new ObjectData();
        try {
            ObjectDataGetByIdV3Result data = resultResult.getData();
            if (data == null || data.getObjectData() == null) {
                log.warn("not found data,obj:{},id:{}", objectApiName, id);
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), "未查询到数据");
            }
            objectData.putAll(data.getObjectData());
        } catch (CrmBusinessException e) {
            log.warn("getObjectData CrmBusinessException = " + e.toString());
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), e.toString());
        }
        return Result.newSuccess(objectData);
    }

    @Test
    public void testExternalMsg() {

         String data = "{\n" +
                "    \"ea\":\"88521\",\n" +
                "    \"ei\":88521,\n" +
                "    \"extraDataMap\":{\n" +
                "        \"objectApiName\":\"LeadsObj\",\n" +
                "        \"objectId\":\"64fade67cac9ec0001e41a6b\"\n" +
                "    },\n" +
                "    \"generateUrlType\":1,\n" +
                "    \"groupKeys\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"messageContent\":\"管理员 给您分配了销售线索：待办验证080201\",\n" +
                "    \"receiverChannelData\":\"{\\\"appId\\\":\\\"crmNotify\\\"}\",\n" +
                "    \"receiverChannelType\":1,\n" +
                "    \"receiverIds\":[\n" +
                "        1002\n" +
                "    ],\n" +
                "    \"senderId\":0,\n" +
                "    \"title\":\"分配销售线索\",\n" +
                "    \"url\":\"todo?apiname=LeadsObj&id=64fade67cac9ec0001e41a6b&ea=88521&ea=88521\"\n" +
                "}";
        SendTextCardMessageArg sendTextMessageArg = JSONObject.parseObject(data, SendTextCardMessageArg.class);
        Result<String> stringResult = externalMsgManager.sendTextCardMessage(sendTextMessageArg,null);

    }

    @Test
    public void testDatainsert(){
        OAResyncLogDoc oaResyncLogDoc=new OAResyncLogDoc();
        oaResyncLogDoc.setTenantId("XXXX");
        oaResyncLogDoc.setBusinessDataId("xxxxx");
        oaResyncLogDoc.setDataCenterId("xxxxx");
        oaResyncLogDoc.setBusinessDataId("xxxxx");
//        oaResyncLogDoc.setRetryCount(0);
        oaResyncLogDoc.setNextRetryTime(System.currentTimeMillis());
        oaResyncLogDao.batchInsert("88521x",Lists.newArrayList(oaResyncLogDoc));
    }
    @Test
    public void testdb(){
        List<String> allTenantIds = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
        List<String> tenantIds = allTenantIds.stream().distinct().collect(Collectors.toList());//去重
        for (String tenantIdSetting : tenantIds) {
            Result<List<OAConnectInfoVO>> listResult = oaConnParamService.listInfoByTenantId(tenantIdSetting);
            if(ConfigCenter.NOT_RE_SYNC_TENANT.contains(tenantIdSetting)){
                if(CollectionUtils.isNotEmpty(listResult.getData())){
                    for (OAConnectInfoVO datum : listResult.getData()) {
                        String dataCenterId=datum.getId();
                        OASettingVO oaSettingVO=new OASettingVO();
                        oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                        oaSettingVO.setTenantId(tenantIdSetting);
                        oaSettingVO.setDataCenterId(dataCenterId);
                        OAMessageResyncRule oaMessageResyncRule=new OAMessageResyncRule();
                        oaMessageResyncRule.setStatus(Boolean.FALSE);
                        oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
                        oaSettingService.upsertSettingInfo(tenantIdSetting, oaSettingVO,dataCenterId);
                    }
                }
                continue;
            }
            if(ConfigCenter.OA_ONLY_RETRY_DEAL_FAILED_EI_LIST.contains(tenantIdSetting)){
                if(CollectionUtils.isNotEmpty(listResult.getData())){
                    for (OAConnectInfoVO dataItem : listResult.getData()) {
                        System.out.println("OAmanagerTest.testdb:{}"+dataItem.getId());
                        String dataCenterId=dataItem.getId();
                        OASettingVO oaSettingVO=new OASettingVO();
                        oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                        oaSettingVO.setTenantId(tenantIdSetting);
                        oaSettingVO.setDataCenterId(dataCenterId);
                        OAMessageResyncRule oaMessageResyncRule=new OAMessageResyncRule();
                        oaMessageResyncRule.setStatus(Boolean.TRUE);
                        oaMessageResyncRule.setMessageStatus(Lists.newArrayList("0","2"));
                        oaMessageResyncRule.setResyncCounts(2);
                        oaMessageResyncRule.setIntervalMinutes(10);
                        oaMessageResyncRule.setBusinessTypes(Lists.newArrayList("crmToDo"));
                        oaMessageResyncRule.setResyncEventTypes(Lists.newArrayList("2","3"));
                        oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
                        oaSettingService.upsertSettingInfo(tenantIdSetting, oaSettingVO,dataCenterId);
                    }
                }
                continue;
            }
            if(CollectionUtils.isNotEmpty(listResult.getData())){
                for (OAConnectInfoVO datum : listResult.getData()) {
                    String dataCenterId=datum.getId();
                    OASettingVO oaSettingVO=new OASettingVO();
                    oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
                    oaSettingVO.setTenantId(tenantIdSetting);
                    oaSettingVO.setDataCenterId(dataCenterId);
                    OAMessageResyncRule oaMessageResyncRule=new OAMessageResyncRule();
                    oaMessageResyncRule.setStatus(Boolean.TRUE);
                    oaMessageResyncRule.setResyncCounts(2);
                    oaMessageResyncRule.setMessageStatus(Lists.newArrayList("0","2"));
                    oaMessageResyncRule.setIntervalMinutes(10);
                    oaMessageResyncRule.setBusinessTypes(Lists.newArrayList("crmToDo"));
                    oaMessageResyncRule.setResyncEventTypes(Lists.newArrayList("1","2","3"));
                    oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
                    oaSettingService.upsertSettingInfo(tenantIdSetting, oaSettingVO,dataCenterId);
                }
            }

        }
    }


}
