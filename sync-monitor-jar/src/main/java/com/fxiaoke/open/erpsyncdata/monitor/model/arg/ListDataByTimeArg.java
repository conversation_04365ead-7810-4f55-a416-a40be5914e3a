package com.fxiaoke.open.erpsyncdata.monitor.model.arg;

import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
@Data
public class ListDataByTimeArg {
    private String tenantId;
    private String objApiName;
    /**
     * 用于获取数据时过滤
     */
    private String filterString;
    /**
     * 数据起始时间
     */
    private Long beginTime;
    /**
     * 数据结束时间
     */
    private Long endTime;
    /**
     * 数据起始ID，查询结果不包含
     */
    private String beginId;
    private Integer limit = 1000;
}
