package com.fxiaoke.open.erpsyncdata.monitor.task;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataFailedDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncDataFailedEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 同步数据失败定时批量写任务
 * <AUTHOR>
 * @date 2023.07.31
 */
@Component
@Slf4j
public class SyncDataFailedTask {
    @Autowired
    private SyncDataFailedDao syncDataFailedDao;

    public List<SyncDataFailedEntity> syncDataFailedList = new CopyOnWriteArrayList<>();

    private Timer timer = new Timer();
    private TimerTask timerTask = new TimerTask() {
        @Override
        public void run() {
            if(CollectionUtils.isEmpty(syncDataFailedList)) return;
            syncDataFailedDao.insertMany(syncDataFailedList);
            syncDataFailedList.clear();
            log.info("sync data failed task,insertMany success");
        }
    };

    @PostConstruct
    public void start() {
        log.info("sync data failed task start");
        timer.schedule(timerTask,5 * 1000,10 * 1000);
    }

    public synchronized boolean exist(String tenantId, String dcId, String ployDetailId, String sourceDataId) {
        Iterator<SyncDataFailedEntity> iterator = syncDataFailedList.iterator();
        while (iterator.hasNext()) {
            try {
                SyncDataFailedEntity entity = iterator.next();
                if(StringUtils.equalsIgnoreCase(entity.getTenantId(),tenantId)
                        && StringUtils.equalsIgnoreCase(entity.getDataCenterId(),dcId)
                        && StringUtils.equalsIgnoreCase(entity.getPloyDetailId(),ployDetailId)
                        && StringUtils.equalsIgnoreCase(entity.getSourceDataId(),sourceDataId)) {
                    return true;
                }
            } catch (Exception e) {
                log.info("SyncDataFailedTask.exist,exception={}",e.getMessage());
            }
        }
        return false;
    }

    public void insert(String tenantId, String dcId, String ployDetailId, String sourceDataId) {
        SyncDataFailedEntity entity = new SyncDataFailedEntity();
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dcId);
        entity.setPloyDetailId(ployDetailId);
        entity.setSourceDataId(sourceDataId);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        syncDataFailedList.add(entity);
    }
}
