package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DelayDataNodeMsgDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DataNodeMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.github.phantomthief.pool.KeyAffinityExecutor;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 17:44 2023/1/12
 * @Desc: 数据节点mq处理
 */
@Service
@Slf4j
public class DataNodeMsgProcessor extends AbstractMonitorMqOrderlyProcessor<DataNodeMsg> {
    @Autowired
    private DataNodeMsgDao dataNodeMsgDao;
    @Autowired
    private DelayDataNodeMsgDao delayDataNodeMsgDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private ErpObjManager erpObjManager;

    private static KeyAffinityExecutor keyAffinityExecutor = null;
    private Integer queueBufferSize = 20000;
    private Integer parallelism = 20;
    private Integer waitTimeout = 60;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;


    public DataNodeMsgProcessor() {
        super(MonitorType.DATA_NODE_MSG, new TypeReference<DataNodeMsg>() {
        });
    }

    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
        ConfigFactory.getConfig("erp-sync-data-all", "MonitorDataNode", iConfig -> {
            parallelism = iConfig.getInt("parallelism", 20);
            queueBufferSize = iConfig.getInt("queueBufferSize", 20000);
            waitTimeout = iConfig.getInt("waitTimeout", 60);
        });
        keyAffinityExecutor = KeyAffinityExecutor
                .newSerializingExecutor(parallelism, queueBufferSize, "DataNodeMsg-processor-%d");
    }


    @Override
    void process(DataNodeMsg dataNodeMsg) {
    }

    @Override
    void batchProcess(List<DataNodeMsg> dataNodeMsgList) {
        try {
            List<ListenableFuture> list = Lists.newArrayList();
            for (DataNodeMsg dataNodeMsg : dataNodeMsgList) {
                ListenableFuture<List<DataNodeMsgDoc>> submit = keyAffinityExecutor.submit(dataNodeMsg.getTenantId() + dataNodeMsg.getDataId(), () -> processDataNodeMsg(dataNodeMsg));
                list.add(submit);
            }
            //获取处理结果-节点信息
            List<DataNodeMsgDoc> dataNodeMsgDocs = Lists.newArrayList();
            for (ListenableFuture<List<DataNodeMsgDoc>> future : list) {
                try {
                    List<DataNodeMsgDoc> docs = future.get(waitTimeout, TimeUnit.SECONDS);//最多等待60s
                    if (CollectionUtils.isNotEmpty(docs)) {
                        dataNodeMsgDocs.addAll(docs);
                    }
                } catch (Exception e) {
                    log.info("batchProcess Exception e={}", e);
                }
            }
            //批量处理节点信息
            if (CollectionUtils.isNotEmpty(dataNodeMsgDocs)) {
                Map<String, List<DataNodeMsgDoc>> collect = dataNodeMsgDocs.stream().collect(Collectors.groupingBy(DataNodeMsgDoc::getTenantId));
                for (List<DataNodeMsgDoc> tenantMsgList : collect.values()) {
                    batchProcessDataNodeMsg(tenantMsgList);
                }
            }
        } catch (Exception e) {
            log.info("keyAffinityExecutor.submit failed e={}", e);
        }
    }

    private List<DataNodeMsgDoc> processDataNodeMsg(DataNodeMsg dataNodeMsg) {
        try {
            if (dataNodeMsg == null || dataNodeMsg.getTenantId() == null || dataNodeMsg.getNodeType() == null || dataNodeMsg.getNodeName() == null
                    || dataNodeMsg.getObjApiName() == null || dataNodeMsg.getDataId() == null) {
                log.info("dataNodeMsg params is null,dataNodeMsg={}", dataNodeMsg);
                return null;
            }
            if (monitorReportManager.notNeedProcessNodesMsg(dataNodeMsg.getTenantId())) {
                return null;
            }
            log.info("processDataNodeMsg process dataNodeMsg={}", dataNodeMsg);
            switch (dataNodeMsg.getNodeType()) {
                case start:
                    return handleStartDataNode(dataNodeMsg);
                case process:
                    return handleProcessDataNode(dataNodeMsg);
                case end:
                    return handleEndDataNode(dataNodeMsg);
                case operate:
                    return handleOperateDataNode(dataNodeMsg);
                default:
                    handleDefaultDataNodeType(dataNodeMsg);
            }
        } catch (Exception e) {
            log.warn("process DataNodeMsg Exception={}", e);
        }
        return null;
    }

    private List<DataNodeMsgDoc> handleOperateDataNode(DataNodeMsg dataNodeMsg) {
        if (StringUtils.isNotBlank(dataNodeMsg.getMsgDetail())) {
            String tenantId = dataNodeMsg.getTenantId();
            String objApiName = dataNodeMsg.getObjApiName();//虚拟主对象apiName
            DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, objApiName, dataNodeMsg.getDataId(), dataNodeMsg.getVersion(),
                    dataNodeMsg.getStreamId(), dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(), dataNodeMsg.getRemark(),
                    dataNodeMsg.getTraceId(), dataNodeMsg.getMsgDetail());
            return Lists.newArrayList(dataNodeMsgDoc);
        }
        return Lists.newArrayList();
    }

    private void handleDefaultDataNodeType(DataNodeMsg dataNodeMsg) {
        log.warn("this dataNodeTypeEnum do not have processing logic. dataNodeMsg={}", dataNodeMsg);
    }

    private List<DataNodeMsgDoc> buildDataNodeMsgDoc(DataNodeMsg dataNodeMsg) {
        String tenantId = dataNodeMsg.getTenantId();
        String objApiName = dataNodeMsg.getObjApiName();//虚拟主对象apiName
        DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, objApiName, dataNodeMsg.getDataId(), dataNodeMsg.getVersion(),
                dataNodeMsg.getStreamId(), dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(), dataNodeMsg.getRemark(),
                dataNodeMsg.getTraceId(), null);
        return Lists.newArrayList(dataNodeMsgDoc);
    }


    private void batchProcessDataNodeMsg(List<DataNodeMsgDoc> dataNodeMsgDocs) {
        if (CollectionUtils.isEmpty(dataNodeMsgDocs)) {
            return;
        }
        List<DataNodeMsgDoc> endNodeList = dataNodeMsgDocs.stream().filter(msg -> DataNodeTypeEnum.end.name().equals(msg.getNodeTypes().get(0))).collect(Collectors.toList());
        List<DataNodeMsgDoc> operateNodeList = dataNodeMsgDocs.stream().filter(msg -> DataNodeTypeEnum.operate.name().equals(msg.getNodeTypes().get(0))).collect(Collectors.toList());
        dataNodeMsgDocs = dataNodeMsgDocs.stream().filter(msg -> !DataNodeTypeEnum.operate.name().equals(msg.getNodeTypes().get(0))).collect(Collectors.toList());
        batchUpsertDataNodeMsg(dataNodeMsgDocs);
        //操作信息
        batchProcessOperateNode(operateNodeList);
        if (CollectionUtils.isEmpty(endNodeList)) {
            return;
        }

        List<String> noVersionUniqueKeyList = Lists.newArrayList();
        for (DataNodeMsgDoc dataNodeMsgDoc : endNodeList) {
            StringBuffer uniqueKey = new StringBuffer();
            uniqueKey.append(dataNodeMsgDoc.getTenantId()).append("_").append(dataNodeMsgDoc.getObjApiName())
                    .append("_").append(dataNodeMsgDoc.getDataId()).append("_").append(dataNodeMsgDoc.getStreamId());
            noVersionUniqueKeyList.add(uniqueKey.toString());
            List<DataNodeMsgDoc> nodeMsgList = dataNodeMsgDao.getByUniqueKey(dataNodeMsgDoc.getTenantId(), Lists.newArrayList(dataNodeMsgDoc.getUniqueKey()));
            if (CollectionUtils.isNotEmpty(nodeMsgList)) {
                if(nodeMsgList.get(0).getNodeNames().contains(DataNodeNameEnum.ConsumeCrmMq.name())||nodeMsgList.get(0).getNodeNames().contains(DataNodeNameEnum.EnterTempData.name())){//erp数据，包含开始节点才上报，并且删除，否则等待超时任务处理（入临时库节点有可能延迟）
                    monitorReportManager.sendBizLog(nodeMsgList.get(0).getTenantId(), nodeMsgList.get(0).getObjApiName(), nodeMsgList.get(0).getDataId(),
                            nodeMsgList.get(0).getVersion(), nodeMsgList.get(0).getStreamId(), true, nodeMsgList.get(0));
                    dataNodeMsgDao.deleteLteVersionNodesMsg(nodeMsgList.get(0).getTenantId(), nodeMsgList.get(0).getObjApiName(), nodeMsgList.get(0).getDataId(),
                            nodeMsgList.get(0).getStreamId(), nodeMsgList.get(0).getVersion());//不管其他，有结束节点，删除小于这个版本的数据节点信息
                }
            }
        }
        //删除延迟数据
        delayDataNodeMsgDao.deleteByNoVersionUniqueKey(noVersionUniqueKeyList);
    }

    private void batchProcessOperateNode(List<DataNodeMsgDoc> operateNodeList) {
        if (CollectionUtils.isEmpty(operateNodeList)) {
            return;
        }
        for (DataNodeMsgDoc nodeMsgDoc : operateNodeList) {
            String tenantId = nodeMsgDoc.getTenantId();
            String objApiName = nodeMsgDoc.getObjApiName();
            String dataId = nodeMsgDoc.getDataId();
            String streamId = nodeMsgDoc.getStreamId();
            Map<String, Object> operateMsg = JacksonUtil.fromJson(nodeMsgDoc.getOperateMsg(), Map.class);
            for (String key : operateMsg.keySet()) {
                if ("deleteInVersionNodesMsg".equals(key) && operateMsg.get(key) != null) {
                    List<Long> versionList = (List<Long>) operateMsg.get(key);
                    dataNodeMsgDao.deleteInVersionNodesMsg(tenantId, objApiName, dataId, streamId, versionList);
                } else if ("deleteLtVersionEnterTempDataNodesMsg".equals(key) && operateMsg.get(key) != null) {
                    Long version = Long.valueOf(operateMsg.get(key).toString());
                    dataNodeMsgDao.deleteLtVersionEnterTempDataNodesMsg(tenantId, objApiName, dataId, streamId, version);
                }
            }

        }
    }

    private void batchUpsertDataNodeMsg(List<DataNodeMsgDoc> dataNodeMsgDocs) {//不允许同一个uniqueKey在同一批upsert,必须按顺序处理
        if (CollectionUtils.isEmpty(dataNodeMsgDocs)) {
            return;
        }
        List<String> uniqueKeys = Lists.newArrayList();
        List<DataNodeMsgDoc> upsertList = Lists.newArrayList();
        List<DataNodeMsgDoc> nextUpsertList = Lists.newArrayList();
        for (DataNodeMsgDoc dataNodeMsgDoc : dataNodeMsgDocs) {
            if (uniqueKeys.contains(dataNodeMsgDoc.getUniqueKey())) {
                nextUpsertList.add(dataNodeMsgDoc);
            } else {
                upsertList.add(dataNodeMsgDoc);
                uniqueKeys.add(dataNodeMsgDoc.getUniqueKey());
            }
        }
        if (CollectionUtils.isNotEmpty(upsertList)) {
            if (upsertList.size() > 100) {
                List<List<DataNodeMsgDoc>> partition = Lists.partition(upsertList, 100);//每批最大为100
                for (List<DataNodeMsgDoc> list : partition) {
                    dataNodeMsgDao.batchUpsertDataNodeMsgDoc(list);
                }
            } else {
                dataNodeMsgDao.batchUpsertDataNodeMsgDoc(upsertList);
            }
        }
        if (CollectionUtils.isNotEmpty(nextUpsertList)) {
            batchUpsertDataNodeMsg(nextUpsertList);
        }
    }


    private List<DataNodeMsgDoc> handleEndDataNode(DataNodeMsg dataNodeMsg) {
        if (dataNodeMsg.getVersion() == null) {
            return Lists.newArrayList();
        }
        if (DataNodeNameEnum.ObjOverDetailLimit.equals(dataNodeMsg.getNodeName())) {
            return handleObjOverDetailLimitNode(dataNodeMsg);
        }
        //结束节点必须包含ei+objApiName+dataId+version+streamId
        return this.buildDataNodeMsgDoc(dataNodeMsg);
    }

    private List<DataNodeMsgDoc> handleObjOverDetailLimitNode(DataNodeMsg dataNodeMsg) {
        String tenantId = dataNodeMsg.getTenantId();
        List<SyncPloyDetailSnapshotEntity> snapshots = this.getSyncPloyDetailSnapshot(tenantId, Lists.newArrayList(dataNodeMsg.getObjApiName()), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (CollectionUtils.isNotEmpty(snapshots)) {
            List<DataNodeMsgDoc> dataNodeMsgDocs = Lists.newArrayList();
            for (SyncPloyDetailSnapshotEntity entity : snapshots) {
                String sourceObjectApiName = entity.getSourceObjectApiName();
                String dataId = dataNodeMsg.getDataId();
                DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, sourceObjectApiName, dataId, dataNodeMsg.getVersion(), entity.getSyncPloyDetailId(),
                        dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(), dataNodeMsg.getRemark(), dataNodeMsg.getTraceId(), null);
                dataNodeMsgDocs.add(dataNodeMsgDoc);
            }
            return dataNodeMsgDocs;
        }
        return Lists.newArrayList();
    }

    private List<DataNodeMsgDoc> handleProcessDataNode(DataNodeMsg dataNodeMsg) {
        if (!DataNodeNameEnum.OutTempData.equals(dataNodeMsg.getNodeName())
                && dataNodeMsg.getVersion() == null) {
            return Lists.newArrayList();
        }
        switch (dataNodeMsg.getNodeName()) {
            //出临时库节点，erp->crm
            case OutTempData:
                return handleOutTempDataNode(dataNodeMsg);
            case DataTriggerProcess:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataBeforeFunc:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataFieldMapping:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case QueryCrmObject2DestNode:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataDuringFunc:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataWriteDest:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataReWriteSource:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataIdMapping:
                return buildDataNodeMsgDoc(dataNodeMsg);
            case DataAfterFunc:
                return buildDataNodeMsgDoc(dataNodeMsg);
            default:
                handleDefaultDataNodeName(dataNodeMsg);
        }
        return Lists.newArrayList();
    }

    private List<DataNodeMsgDoc> handleOutTempDataNode(DataNodeMsg dataNodeMsg) {
        String tenantId = dataNodeMsg.getTenantId();
        String objApiName = dataNodeMsg.getObjApiName();//虚拟对象apiName
        //只查询主数据
        List<SyncPloyDetailSnapshotEntity> snapshots = getSyncPloyDetailSnapshot(tenantId, Lists.newArrayList(objApiName), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (CollectionUtils.isNotEmpty(snapshots)) {
            List<DataNodeMsgDoc> dataNodeMsgDocs = Lists.newArrayList();
            Document doc = JacksonUtil.fromJson(dataNodeMsg.getMsgDetail(), Document.class);
            Long version;
            if (doc.getLong("new_last_sync_time") != null) {
                version = doc.getLong("new_last_sync_time");
            } else {
                version = doc.getLong("last_sync_time");
            }
            for (SyncPloyDetailSnapshotEntity entity : snapshots) {
                String sourceObjectApiName = entity.getSourceObjectApiName();
                DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, sourceObjectApiName, dataNodeMsg.getDataId(), version,
                        entity.getSyncPloyDetailId(), dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(),
                        dataNodeMsg.getRemark(), dataNodeMsg.getTraceId(), null);
                dataNodeMsgDocs.add(dataNodeMsgDoc);
                //把低版本的入库日志删除
                if (tenantId != null && sourceObjectApiName != null && dataNodeMsg.getDataId() != null && version != null) {
                    Map<String, Object> detail = Maps.newHashMap();
                    detail.put("deleteLtVersionEnterTempDataNodesMsg", version);
                    DataNodeMsgDoc deleteLtVersion = DataNodeMsgDoc.create(tenantId, sourceObjectApiName, dataNodeMsg.getDataId(), version,
                            entity.getSyncPloyDetailId(), DataNodeTypeEnum.operate, DataNodeNameEnum.EnterTempData, dataNodeMsg.getNodeTime(),
                            dataNodeMsg.getRemark(), dataNodeMsg.getTraceId(), JacksonUtil.toJson(detail));
                    dataNodeMsgDocs.add(deleteLtVersion);
                    //dataNodeMsgDao.deleteLtVersionEnterTempDataNodesMsg(tenantId, objApiName, dataNodeMsg.getDataId(), entity.getSyncPloyDetailId(), version);
                }
            }
            return dataNodeMsgDocs;
        }
        return Lists.newArrayList();
    }


    private List<DataNodeMsgDoc> handleStartDataNode(DataNodeMsg dataNodeMsg) {
        switch (dataNodeMsg.getNodeName()) {
            //进临时库节点,erp->crm
            case EnterTempData:
                return handleEnterTempDataNode(dataNodeMsg);
            //消费paasMq,crm->erp
            case ConsumeCrmMq:
                return handleConsumeCrmMqNode(dataNodeMsg);
            default:
                handleDefaultDataNodeName(dataNodeMsg);
        }
        return Lists.newArrayList();
    }

    private void handleDefaultDataNodeName(DataNodeMsg dataNodeMsg) {
        log.warn("this dataNodeNameEnum do not have processing logic. dataNodeMsg={}", dataNodeMsg);
    }

    private List<DataNodeMsgDoc> handleConsumeCrmMqNode(DataNodeMsg dataNodeMsg) {
        String tenantId = dataNodeMsg.getTenantId();
        String objApiName = dataNodeMsg.getObjApiName();//对象apiName
        //目前只处理主对象，从对象的直接丢掉了
        List<SyncPloyDetailSnapshotEntity> snapshots = getSyncPloyDetailSnapshot(tenantId, Lists.newArrayList(objApiName), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (CollectionUtils.isNotEmpty(snapshots)) {
            //再次确认是不是主对象，因为存在crm从对象对接到erp主对象的情况
            String sourceMasterObjectApiName = getCrmMasterObjectApiName(tenantId, objApiName);
            if (StringUtils.isBlank(sourceMasterObjectApiName)) {//为空说明是主对象
                List<DataNodeMsgDoc> dataNodeMsgDocs = Lists.newArrayList();
                for (SyncPloyDetailSnapshotEntity entity : snapshots) {
                    String sourceObjectApiName = entity.getSourceObjectApiName();
                    DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, sourceObjectApiName, dataNodeMsg.getDataId(), dataNodeMsg.getVersion(),
                            entity.getSyncPloyDetailId(), dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(),
                            dataNodeMsg.getRemark(), dataNodeMsg.getTraceId(), null);
                    dataNodeMsgDocs.add(dataNodeMsgDoc);
                }
                return dataNodeMsgDocs;
            }
        }
        return Lists.newArrayList();
    }

    private String getCrmMasterObjectApiName(String tenantId, String objApiName) {
        Result2<String> crmMasterObjectApiName = syncPloyDetailSnapshotManager.getCrmMasterObjectApiName(tenantId, objApiName);
        if (crmMasterObjectApiName.isSuccess()){
            return crmMasterObjectApiName.getData();
        }
        return null;
    }

    public List<SyncPloyDetailSnapshotEntity> getSyncPloyDetailSnapshot(String tenantId, List<String> objApiNames, Integer status) {
        List<SyncPloyDetailSnapshotEntity> resultList=Lists.newArrayList();
        List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotManager.getSyncPloyDetailSnapshotEntities(tenantId, objApiNames, status);
        if (CollectionUtils.isNotEmpty(list)) {
            for(SyncPloyDetailSnapshotEntity entity:list){
                if (System.currentTimeMillis()-entity.getCreateTime()>1000*31L) {//暂时忽略31s内的数据上报
                    resultList.add(entity);
                }
            }
        }
        return resultList;
    }

    private List<DataNodeMsgDoc> handleEnterTempDataNode(DataNodeMsg dataNodeMsg) {
        String tenantId = dataNodeMsg.getTenantId();
        String dcId = dataNodeMsg.getDataCenterId();
        String objApiName = dataNodeMsg.getObjApiName();//真实主对象apiName
        //中间对象apiName
        String splitObjApiName = dataNodeMsg.getSplitObjApiName();
        if (StrUtil.isBlank(splitObjApiName)){
            //旧逻辑，先保持，全网后可移除
            return oldHandleEnterTemp(dataNodeMsg, tenantId, dcId, objApiName);
        }
        //新逻辑,
        List<String> streamIds = syncPloyDetailManager.listEnableStreamIdsBySrcObj(tenantId, dcId, splitObjApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        for (String streamId : streamIds) {
            List<DataNodeMsgDoc> dataNodeMsgDocs = Lists.newArrayList();
            DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, splitObjApiName, dataNodeMsg.getDataId(), dataNodeMsg.getVersion(), streamId,
                    dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(), dataNodeMsg.getRemark(), dataNodeMsg.getTraceId(), null);
            dataNodeMsgDocs.add(dataNodeMsgDoc);
            return dataNodeMsgDocs;
        }
        return new ArrayList<>();
    }

    private @NotNull List<DataNodeMsgDoc> oldHandleEnterTemp(DataNodeMsg dataNodeMsg, String tenantId, String dcId, String objApiName) {
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjManager.listMasterRelationsByRealApiName(tenantId, dcId, objApiName);
        if (CollectionUtils.isNotEmpty(erpObjectRelationshipEntities)) {
            List<String> erpSplitObjApiNames = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
            List<SyncPloyDetailSnapshotEntity> snapshots = this.getSyncPloyDetailSnapshot(tenantId, erpSplitObjApiNames, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            if (CollectionUtils.isNotEmpty(snapshots)) {
                List<DataNodeMsgDoc> dataNodeMsgDocs = Lists.newArrayList();
                Document doc = JacksonUtil.fromJson(dataNodeMsg.getMsgDetail(), Document.class);
                if (!"SAL_SaleOrder".equals(objApiName)&&erpSplitObjApiNames.size() > 1) {
                    snapshots=removeSnapShots(tenantId, dataNodeMsg,doc,snapshots);//拆分对象如果主键字段不一样，取的值不一样，会导致误报
                }

                Long version;
                if (doc.getLong("new_last_sync_time") != null) {
                    version = doc.getLong("new_last_sync_time");
                } else {
                    version = doc.getLong("last_sync_time");
                }
                if (version == null) {
                    return dataNodeMsgDocs;
                }
                for (SyncPloyDetailSnapshotEntity entity : snapshots) {
                    String sourceObjectApiName = entity.getSourceObjectApiName();
                    String dataId = dataNodeMsg.getDataId();
                    if ("SAL_SaleOrder".equals(objApiName)) {
                        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, sourceObjectApiName);
                        if (idField != null) {
                            CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
                            if (compositeIdExtend != null && compositeIdExtend.isComposite()) {//写死了
                                //旧的先临时兼容下
                                Object id = doc.get("data_id");
                                Object number = doc.get("data_number");
                                if (Objects.equals(id, number)) {
                                    //一样的其实是因为number为空格
                                    dataId = id + "# ";
                                } else {
                                    dataId = id + "#" + number;
                                }
                            }
                        }

                    }
                    DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create(tenantId, sourceObjectApiName, dataId, version, entity.getSyncPloyDetailId(),
                            dataNodeMsg.getNodeType(), dataNodeMsg.getNodeName(), dataNodeMsg.getNodeTime(), dataNodeMsg.getRemark(), dataNodeMsg.getTraceId(), null);
                    dataNodeMsgDocs.add(dataNodeMsgDoc);
                }
                return dataNodeMsgDocs;
            }
        }
        return Lists.newArrayList();
    }

    private List<SyncPloyDetailSnapshotEntity> removeSnapShots(String tenantId, DataNodeMsg dataNodeMsg, Document doc, List<SyncPloyDetailSnapshotEntity> snapshots) {
        String dataId = dataNodeMsg.getDataId();
        ObjectId mongoId = getDocObjectId(doc);
        Result<Document> document = erpTempDataDao.getErpObjData(tenantId, mongoId.toString());
        if (!document.isSuccess() || document.getData() == null) {
            return snapshots;
        }
        Map<String, Object> standardData = JSONObject.parseObject(document.getData().getString("data_body"), Map.class);
        if (standardData != null && standardData.get("masterFieldVal") != null) {
            Map<String, Object> masterFieldVal = (Map<String, Object>) standardData.get("masterFieldVal");
            List<SyncPloyDetailSnapshotEntity> result = Lists.newArrayList();
            for (SyncPloyDetailSnapshotEntity entity : snapshots) {
                ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, entity.getSourceObjectApiName());
                String objBodyDataId = masterFieldVal.get(idField.getFieldApiName()).toString();
                if (objBodyDataId.equals(dataId)) {
                    result.add(entity);
                }
            }
            return result;
        }

        return snapshots;
    }

    public static ObjectId getDocObjectId(Document doc) {
        Object id = doc.get("_id");
        if(id==null){
            return null;
        }
        if (id instanceof ObjectId) {
            return (ObjectId) id;
        } else if (id instanceof String) {
            return new ObjectId(((String) id));
        }else{
            Map<String, Object> idMap = (Map)id;
            int timestamp = (int) idMap.get("timestamp");
            int counter = (int) idMap.get("counter");
            short processIdentifier = Short.parseShort(String.valueOf(idMap.get("processIdentifier")));
            int machineIdentifier = (int) idMap.get("machineIdentifier");
            return new ObjectId(timestamp, machineIdentifier, processIdentifier, counter);
        }
    }

}
