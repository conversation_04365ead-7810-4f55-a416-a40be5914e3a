package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.RejectPolicy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.trace.TraceContext;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 入队后，单条或处理
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@Slf4j
public abstract class AbstractMonitorMqProcessor<T> implements InitializingBean, DisposableBean {
    private ThreadPoolExecutor executor;
    /**
     * 队列不直接使用线程池队列是为了实现灵活的入队拒绝逻辑，
     * 以及针对后续可能的队列扩展。
     */
    private LinkedBlockingQueue<MessageExt> queue;
    @Getter
    private final MonitorType monitorType;

    @Setter(AccessLevel.PROTECTED)
    private Integer queueSize = 100000;

    @Setter(AccessLevel.PROTECTED)
    private Integer threadSize = 3;

    private final Type type;

    public AbstractMonitorMqProcessor(final MonitorType monitorType) {
        this.monitorType = monitorType;
        type = ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    /**
     * @param monitorType
     * @param type        当重写{@link #parseObject}时可以传null
     */
    protected AbstractMonitorMqProcessor(MonitorType monitorType, TypeReference<T> type) {
        this.monitorType = monitorType;
        this.type = type.getType();
    }

    @Override
    public void afterPropertiesSet() {
        ConfigFactory.getConfig("erp-sync-data-monitor", monitorType.name(), config -> {
            loadConfig(config);
        });
    }

    /**
     * 反序列化消息，出队后反序列化。
     *
     * @param message
     * @return
     */
    protected T parseObject(Message message) {
        T obj = JSON.parseObject(message.getBody(), type);
        return obj;
    }

    /**
     * 批量处理消息
     */
    abstract void process(T obj);

    /**
     * 不做特殊逻辑，一定快速返回
     *
     * @param messages
     * @return
     */
    public void processMsgs(List<MessageExt> messages) {
        try {
            for (int i = 0; i < messages.size(); i++) {
                MessageExt msg = messages.get(i);
                boolean offer = queue.offer(msg);
                if (!offer){
                    //后面一批都直接丢弃
                    log.info("offer failed,type:{},msgs{}", monitorType, messages.subList(i,messages.size()));
                    break;
                }
            }
            executor.execute(() -> {
                //当线程池还有线程时起新线程消费队列数据
                while (true) {
                    Message msg = queue.poll();
                    if (msg == null) {
                        return;
                    }
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                    try {
                        T msgObj = parseObject(msg);
                        process(msgObj);
                    } finally {
                        TraceContext.remove();
                    }
                }
            });
        } catch (Exception e) {
            //出错时直接跳过msg
            log.error("offer error,", e);
        }
    }


    /**
     * queue重置会可能丢弃一些数据
     */
    protected void loadConfig(IConfig config) {
        this.threadSize = config.getInt("threadSize", threadSize);
        Integer oldQueueSize = queueSize;
        //修改队列长度将会丢掉旧队列里面的所有消息，重置队列。
        this.queueSize = config.getInt("queueSize", queueSize);
        if (queue == null || !oldQueueSize.equals(queueSize)) {
            queue = new LinkedBlockingQueue<>(queueSize);
        }
        if (executor == null) {
            executor = ExecutorBuilder.create()
                    .setCorePoolSize(threadSize)
                    .setMaxPoolSize(threadSize)
                    .setWorkQueue(new SynchronousQueue<>())
                    .setKeepAliveTime(1, TimeUnit.MINUTES)
                    .setAllowCoreThreadTimeOut(true)
                    .setHandler(RejectPolicy.DISCARD.getValue())
                    .setThreadFactory(new NamedThreadFactory("Monitor-%s"))
                    .build();
        } else {
            if (executor.getPoolSize() != threadSize) {
                executor.setCorePoolSize(threadSize);
                executor.setMaximumPoolSize(threadSize);
            }
        }
    }

    @Override
    public void destroy() {
        executor.shutdown();
    }
}
