package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.monitor.manager.AlertAndBreakManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/4
 */
@Service
public class FailedSyncDataProcessor extends AbstractMonitorMqProcessor<Pair<String,SyncDataEntity>> {
    @Autowired
    private AlertAndBreakManager alertAndBreakManager;

    public FailedSyncDataProcessor() {
        super(MonitorType.FAILED_SYNC_DATA, new TypeReference<Pair<String,SyncDataEntity>>() {
        });
    }
    @Override
    void process(Pair<String, SyncDataEntity> pair) {
        alertAndBreakManager.incrFailedSyncDataNum(pair.getKey(), pair.getValue());
    }
}
