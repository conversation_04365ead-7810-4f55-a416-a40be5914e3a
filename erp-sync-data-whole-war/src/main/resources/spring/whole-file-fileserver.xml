<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
  <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
    <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
  </bean>
</beans>