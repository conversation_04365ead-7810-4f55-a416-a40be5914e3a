{"type": "page", "title": "PG管理", "body": [{"type": "tabs", "tabs": [{"title": "索引信息", "body": [{"type": "collapse", "key": "1", "active": true, "header": "筛选条件", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:2d6609d18d84", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "input-text", "label": "表名或表名前缀", "name": "tableNamePrefix", "id": "u:ce3608a27b4a", "value": "sync_data_mappings", "required": true, "validateOnChange": true}, {"type": "input-text", "label": "tenantId", "name": "tenantId", "id": "u:5fedced7eb8a", "size": "sm", "description": ""}, {"type": "textarea", "label": "存在索引", "name": "indexDef", "id": "u:c47fb5606b51", "minRows": 2, "maxRows": 20, "description": "", "hidden": false, "size": "full", "labelRemark": {"icon": "fa fa-question-circle", "trigger": ["click"], "className": "Remark--warning", "placement": "top", "content": "使用右匹配查询输入索引创建语句右边部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false;", "title": "说明", "rootClose": true}, "placeholder": "筛选存在某索引的表"}, {"type": "textarea", "label": "缺少索引", "name": "missIndexDef", "id": "u:084131a53df8", "minRows": 2, "maxRows": 20, "description": "", "hidden": false, "size": "full", "labelRemark": {"icon": "fa fa-question-circle", "trigger": ["click"], "className": "Remark--warning", "placement": "top", "content": "使用右匹配查询输入索引创建语句右边部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false;", "title": "说明", "rootClose": true}, "placeholder": "筛选缺少某索引的表"}], "mode": "horizontal", "id": "u:66d234542aa6", "reload": "", "canAccessSuperData": false, "target": "pgIndexCrud", "debug": false, "submitText": "获取索引列表", "persistData": true, "wrapWithPanel": true}], "id": "u:58d192519c68", "hidden": false, "collapsable": true}, {"type": "crud", "syncLocation": true, "api": {"method": "get", "url": "../pg/listIndexInfos", "messages": {}, "requestAdaptor": "", "adaptor": "", "replaceData": false, "data": {"&": "$$", "dbName": "${dbName}"}, "dataType": "json"}, "columns": [{"label": "dbN<PERSON>", "type": "text", "name": "dbN<PERSON>", "id": "u:cae909b89050"}, {"label": "tenantId", "type": "text", "name": "tenantId", "id": "u:d86786dbc099"}, {"label": "tableName", "type": "text", "name": "tableName", "id": "u:95d59547a3d1"}, {"label": "indexName", "type": "text", "name": "indexName", "id": "u:784ae5627ce3"}, {"label": "indexDef", "type": "text", "name": "indexDef", "id": "u:19bc3e06c7af"}, {"label": "indexNamePattern", "type": "text", "name": "indexNamePattern", "id": "u:8be7942378e6", "placeholder": "-", "sortable": false}, {"label": "existConnectInfo", "type": "text", "name": "existConnectInfo", "id": "u:ccc53e9e4e9f", "placeholder": "-", "sortable": false}, {"type": "operation", "label": "操作", "id": "u:a6050922be41", "buttons": [{"type": "button", "label": "删除索引", "onEvent": {"click": {"actions": [{"outputVar": "responseResult", "actionType": "ajax", "args": {"options": {}, "api": {"url": "../pg/dropIndexByName", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"&": "$$"}}}}, {"componentId": "u:41ac64fe02ab", "actionType": "reload", "args": {"resetPage": true}, "dataMergeMode": "merge"}]}}, "id": "u:4c41e1ea2ca0", "level": "danger", "hiddenOn": "${!indexName}", "confirmText": "确认删除？"}]}], "bulkActions": [], "itemActions": [], "filterSettingSource": ["dbN<PERSON>", "tenantId", "tableName", "indexName", "indexDef", "indexNamePattern", "existConnectInfo"], "features": [], "id": "u:41ac64fe02ab", "messages": {}, "headerToolbar": [{"type": "bulk-actions"}], "filter": null, "perPageAvailable": [10, 50, 100], "initFetch": false, "initFetchOn": "dbName!=0", "autoFillHeight": false, "name": "pgIndexCrud", "perPage": 20, "footerToolbar": [{"type": "statistics"}, {"type": "pagination"}, {"type": "switch-per-page", "tpl": "内容", "wrapperComponent": "", "id": "u:6571f85d9f1c"}]}], "id": "u:4974310aad30"}, {"title": "索引操作", "body": [{"type": "collapse-group", "activeKey": ["1"], "body": [{"type": "collapse", "key": "1", "active": true, "header": "创建索引", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:4c0e8d587b25", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "input-text", "label": "表名前缀", "name": "tableNamePrefix", "id": "u:31a0b7f70cc5", "value": "sync_data_mappings", "required": true, "validateOnChange": true, "readOnly": true, "disabled": true}, {"type": "input-text", "label": "索引名称", "name": "indexNamePattern", "id": "u:171b8db1e7d9", "description": "必须包含{tenantId},执行时会替换为真实tenantId\n。删除索引不使用，"}, {"type": "textarea", "label": "限定企业，逗号分隔", "name": "tenantIdStr", "id": "u:2d1d59b87ae9", "minRows": 3, "maxRows": 20}, {"type": "textarea", "label": "索引语句", "name": "indexDef", "id": "u:247b358d250e", "minRows": 3, "maxRows": 20, "value": "", "description": "索引表名后的部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false"}, {"type": "checkbox", "name": "uniqueIndex", "label": "是否唯一索引(默认否)"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:752320a29765", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "mode": "horizontal", "id": "u:604d20b197ce", "reload": "", "canAccessSuperData": false, "target": "", "debug": false, "submitText": "创建索引", "persistData": true, "wrapWithPanel": true, "api": {"url": "../pg/createIndexes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}}], "id": "u:c9d892e071ce", "hidden": false, "collapsable": true, "name": "text"}], "id": "u:a92537a35f3d"}, {"type": "collapse-group", "activeKey": ["1"], "body": [{"type": "collapse", "key": "1", "active": true, "header": "删除索引", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:9507b2a6caad", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "input-text", "label": "表名前缀", "name": "tableNamePrefix", "id": "u:9f22fb43e8d0", "value": "sync_data_mappings", "required": true, "validateOnChange": true, "readOnly": true, "disabled": true}, {"type": "textarea", "label": "限定企业，逗号分隔", "name": "tenantIdStr", "id": "u:0fb0faa3f99c", "minRows": 3, "maxRows": 20}, {"type": "textarea", "label": "索引语句", "name": "indexDef", "id": "u:fbc60bb1fe73", "minRows": 3, "maxRows": 20, "value": "", "description": "索引表名后的部分，如：(dest_data_id, dest_object_api_name, source_object_api_name, tenant_id)\n WHERE is_deleted = false"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:46895f6d5a2e", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "mode": "horizontal", "id": "u:25fe01dced46", "reload": "", "canAccessSuperData": false, "target": "", "debug": false, "submitText": "删除索引", "persistData": true, "wrapWithPanel": true, "api": {"url": "../pg/dropIndexes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}}], "id": "u:97a59cdb0b5a", "hidden": false, "collapsable": true, "name": "text"}], "id": "u:3d7e054d62af"}, {"type": "collapse-group", "activeKey": ["1"], "body": [{"type": "collapse", "key": "1", "active": true, "header": "比较代码创建缺少索引", "body": [{"type": "form", "title": "", "body": [{"label": "dbN<PERSON>", "type": "radios", "name": "dbN<PERSON>", "id": "u:4c07b25e8d58", "source": "${dbs}", "selectFirst": true, "autoFill": {}, "onEvent": {"change": {"weight": 0, "actions": []}}}, {"type": "textarea", "label": "限定企业，逗号分隔", "name": "tenantIdStr", "id": "u:2d17ae9d59b8", "minRows": 3, "maxRows": 20}, {"type": "checkbox", "name": "deleteIndex", "label": "是否删除多余索引(默认否)"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:75229765320a", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "mode": "horizontal", "id": "u:604197ced20b", "reload": "", "canAccessSuperData": false, "target": "", "debug": false, "submitText": "比较代码创建缺少索引", "persistData": true, "wrapWithPanel": true, "api": {"url": "../pg/compareIndexes", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}}], "id": "u:c9ced892e071", "hidden": false, "collapsable": true, "name": "text"}], "id": "u:a935f3d2537a"}], "id": "u:e8df2268cbad"}, {"title": "迁移数据", "body": [{"type": "form", "title": "表单", "body": [{"type": "input-text", "label": "迁移企业，分号隔开", "name": "sourceTenantId", "id": "u:5a60c97f8819"}, {"type": "input-text", "label": "指向目标数据库", "name": "destDataBaseTenantId", "id": "u:5a60c97f9918"}, {"type": "textarea", "label": "结果", "name": "result", "id": "u:46895f6d5bbe", "minRows": 3, "maxRows": 20, "static": false, "hidden": false, "readOnly": true, "disabled": true}], "api": {"url": "../pg/brushTableData", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "responseData": {"result": "$$"}}, "id": "u:ef7db9efbaac", "submitText": "执行"}], "id": "u:7446761cdee8"}]}], "id": "u:18fe38ed7064", "pullRefresh": {"disabled": true}, "regions": ["body", "toolbar", "header"], "initApi": {"url": "../pg/preInitData", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "dataType": "json"}, "style": {"boxShadow": " 0px 0px 0px 0px transparent"}, "data": {}, "asideResizor": false, "name": "pgIndexManager"}