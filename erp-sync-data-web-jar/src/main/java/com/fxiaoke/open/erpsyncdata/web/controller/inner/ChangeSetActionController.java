package com.fxiaoke.open.erpsyncdata.web.controller.inner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.admin.model.DisablePloySnapshotArg;
import com.fxiaoke.open.erpsyncdata.admin.model.InitConnectInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.common.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MigrateTableManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/28
 */
@Slf4j
@Api(tags = "连接信息内部调用接口")
@RestController
@RequestMapping("inner/erp/syncdata")
public class ChangeSetActionController extends AsyncSupportController {
    @Autowired
    private MigrateTableManager migrateTableManager;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "初始化连接器")
    @RequestMapping(value = "/connectinfo/init", method = RequestMethod.POST)
    public Result<Void> init(@RequestBody InitConnectInfoArg arg) {
        log.info("inner init tenantTable begin,arg:{}", arg);
        if (arg.getTenantIds() == null) {
            return Result.newSuccess();
        }
        for (String tenantId : arg.getTenantIds()) {
            try {
                Result<ErpConnectInfoEntity> initCrmDc = connectInfoService.getOrCreateCrmDc(tenantId,null);
                if (!initCrmDc.isSuccess()) {
                    return Result.copy(initCrmDc);
                }
                String s = migrateTableManager.initTenantTable(tenantId);
                log.info("inner init tenantTable success,tenantId:{},result:{}", tenantId, s);
            } catch (Exception e) {
                log.info("inner init tenantTable error,tenantId:{}", tenantId, e);
            }
        }
        return Result.newSuccess();
    }

    @ApiOperation(value = "停用集成流快照")
    @RequestMapping(value = "/ploysnapshot/disable", method = RequestMethod.POST)
    public Result<Void> disablePloySnapshot(@RequestBody DisablePloySnapshotArg arg) {
        log.info("disable Ploy Snapshot,arg:{}", arg);
        if (arg.getTenantId() == null) {
            return Result.newSuccess();
        }

        adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                .updateStatusByPloyDetailId(arg.getPloyId(), SyncPloyDetailStatusEnum.DISABLE.getStatus());

        return Result.newSuccess();
    }

    @ApiOperation(value = "重新启用集成流")
    @RequestMapping(value = "/ploy/restart", method = RequestMethod.POST)
    public Result<Void> restartPloy(@RequestBody DisablePloySnapshotArg arg,
                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        log.info("restart Ploy Snapshot,arg:{}", arg);
        if (StringUtils.isBlank(arg.getTenantId()) || StringUtils.isBlank(arg.getPloyId())) {
            return Result.newSuccess();
        }

//        停用旧快照
        adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                .updateStatusByPloyDetailId(arg.getPloyId(), SyncPloyDetailStatusEnum.DISABLE.getStatus());

//        旧的更改集没有status参数，不启用集成流
        if (!Objects.equals(arg.getStatus(), 1)) {
            return Result.newSuccess();
        }

//        启用
        final SyncPloyDetailResult ployDetailResult = adminSyncPloyDetailService.getById(arg.getTenantId(), arg.getPloyId(),lang).getData();
        if (ployDetailResult == null) {
            return Result.newSuccess();
        }
        final String erpDcId = Objects.equals(ployDetailResult.getSourceTenantType(), TenantType.ERP) ?
                ployDetailResult.getSourceDataCenterId() : ployDetailResult.getDestDataCenterId();

//        耗时过长,使用异步处理,防止启用时间过长导致超时导致更改集失败
        asyncExecute(() -> adminSyncPloyDetailService.checkAndUpdatePloyStatus(arg.getTenantId(), -10000, erpDcId, arg.getPloyId(), SyncPloyDetailStatusEnum.ENABLE.getStatus(), true,lang));

        return Result.newSuccess();
    }


    /**
     * 内部调用推送的接口
     */
    /**
     * <p>第三方主动推送数据到平台。数据格式有第三方设置。给到平台，通过groovy脚本解析</p>
     *
     * @param request
     * @param headers

     * <AUTHOR>
     * @return
     * @throws
     */
    @ControllerMethodProxy()
    @RequestMapping(value = {"/objdata/push"}, method = RequestMethod.POST)
    public Result<Object> erpPushDataToDss(HttpServletRequest request, @RequestHeader HttpHeaders headers,@RequestHeader(value = "tenantId", required = false) @ControllerTenantIDArg String tenantId) throws UnsupportedEncodingException {

        return doPush(request, headers,false);
    }
    @ControllerMethodProxy()
    @RequestMapping(value = {"/objdata/asyncpush"}, method = RequestMethod.POST)
    public Result<Object> erpAsyncPushDataToDss(HttpServletRequest request, @RequestHeader HttpHeaders headers , @RequestHeader(value = "tenantId", required = false) @ControllerTenantIDArg String tenantId) throws UnsupportedEncodingException {

        return doPush(request, headers,true);
    }

    private Result<Object> doPush(HttpServletRequest request, HttpHeaders headers,Boolean isAsyncPush) throws UnsupportedEncodingException {
        Result result = new Result();
        String tenantId =  headers.getFirst("tenantId") ;
        TraceUtil.initTraceWithFormat(tenantId);
        String data;
        try {
            Result<String> dataResult = HttpRspLimitLenUtil.convertStreamToString(request.getInputStream(),ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
            if(!dataResult.isSuccess()){
                return Result.copy(dataResult);
            }
            data=dataResult.getData();
        } catch (Exception e) {
            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            result.setErrMsg(e.getMessage());
            return result;
        }
        JSONObject jsonObject = JSON.parseObject(data);
        String dataCenterId=jsonObject.getString("dataCenterId") ;
        String objectApiName=jsonObject.getString("objectApiName") ;
        if(StringUtils.isEmpty(objectApiName)){
            objectApiName=jsonObject.getString("objAPIName");
        }
        String operationType=jsonObject.getString("operationType") ;
        String id = jsonObject.getString("id");
        String contentLength=headers.getFirst("content-length");
        String destObjectApiName=jsonObject.getString("destObjectApiName") ;
        boolean directSync=false;
        if(jsonObject.containsKey("directSync")){
            directSync=jsonObject.getBoolean("directSync");
        }
        if(isAsyncPush){//如果是异步接口，就算传了这些参数也没有用
            destObjectApiName=null;
            directSync=false;
        }
        //检查请求体大小
        if(StringUtils.isNotEmpty(contentLength)){
            if(Long.valueOf(contentLength)> ConfigCenter.CONTENT_LENGTH_LIMIT){
                String errMsg=  String.format(ResultCodeEnum.CONTROL_ERP_PUSH_DATA_LENGTH.getErrMsg(),ConfigCenter.CONTENT_LENGTH_LIMIT);
                return Result.newError(ResultCodeEnum.CONTROL_ERP_PUSH_DATA_LENGTH.getErrCode(), errMsg);
            }
        }
        // header包含idEncode且为1时，对header的id进行URL解码
        String idEncode = headers.getFirst("idEncode");
        if(StringUtils.isNotBlank(idEncode) && CommonConstant.ONE.equals(idEncode) && StringUtils.isNotBlank(id)){
            id = URLDecoder.decode(id,"UTF-8");
        }

        if(StringUtils.isBlank(dataCenterId)){
            List<ErpConnectInfoEntity> entities= erpConnectInfoManager.listByTenantId(tenantId);
            dataCenterId=entities.get(0).getId();//默认第一个数据中心
        }


        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        if (connectInfoEntity==null){
            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            result.setErrMsg(i18NStringManager.getByEi2(I18NStringEnum.s1290.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s1290.getI18nValue(), dataCenterId),
                    Lists.newArrayList(dataCenterId)));
            return result;
        }
/**
 * 暂时不能通过ConnectParams里面的pushDataApiNames判断是否启用了推送，因为同时启用推送和轮询，pushDataApiNames不存在推送对象apiName
 */
//        if (StringUtils.isEmpty(connectInfoEntity.getConnectParams())||!connectInfoEntity.getConnectParams().contains(objectApiName)){
//            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
//            result.setErrMsg(String.format("当前账套%s对象未启用推送功能：%s",dataCenterId,objectApiName));
//            return result;
//        }

        //根据这个代码推测，objectApiName和erpVisualApiName应该是不能为空的。应该吧
        String erpVisualApiName = erpObjDataPushManager.validateErpObjApiName(tenantId, objectApiName,dataCenterId);
        if (erpVisualApiName == null) {
            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            result.setErrMsg(i18NStringManager.getByEi2(I18NStringEnum.s1163.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s1163.getI18nValue(), dataCenterId,objectApiName),
                    Lists.newArrayList(dataCenterId,objectApiName)));
            return result;
        }
        String logId = syncLogManager.initLogId(tenantId, objectApiName);
        TraceUtil.initTrace(logId);
        final String locale = Objects.nonNull(request.getHeader("Accept-Language")) ? request.getLocale().toLanguageTag() : null;
        Result<Object> objectResult = erpObjDataPushManager.erpPushDataToDss(tenantId, objectApiName, erpVisualApiName, operationType, data, GsonUtil.toJson(headers), id, dataCenterId, directSync,destObjectApiName,null, locale);
        TraceUtil.removeTrace();
        LogIdUtil.clear();
        return objectResult;
    }
}
