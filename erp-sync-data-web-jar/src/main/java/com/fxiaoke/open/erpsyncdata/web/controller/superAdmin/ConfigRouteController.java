package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminBrushDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigRouteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 14:14 2022/4/28
 * @Desc:
 */
@Slf4j
@Api(tags = "企业连接信息设置相关接口")
@RestController("ConfigRouteController")
@RequestMapping("erp/syncdata/superadmin/configRoute")
public class ConfigRouteController extends BaseController {
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private SuperAdminBrushDataService superAdminBrushDataService;

    @ApiOperation(value = "新增或者修改企业路由")
    @RequestMapping(value = "/addOrUpdateTenantRoute", method = RequestMethod.GET)
    public Result<Boolean> addOrUpdateTenantRoute(@RequestParam String tenantId, @RequestParam String resourceId) {
        if (StringUtils.isBlank(tenantId) && StringUtils.isBlank(resourceId)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        return Result.newSuccess(configRouteManager.configRoute(tenantId, resourceId));
    }

    @ApiOperation(value = "批量新增或者修改企业路由")
    @RequestMapping(value = "/batchAddOrUpdateTenantRoute", method = RequestMethod.POST)
    public Result<Boolean> batchAddOrUpdateTenantRoute(@RequestParam Boolean allTenant, @RequestParam String ignoreTenantIds, @RequestParam String tenantIds, @RequestParam String resourceId) {
        Set<String> tenantIdList = Sets.newHashSet();
        if (allTenant != null && allTenant) {
            Set<String> ignoreTenantIdList = Sets.newHashSet();
            if (StringUtils.isNotBlank(ignoreTenantIds)) {
                ignoreTenantIdList = ImmutableSet.copyOf(Splitter.on(",").split(ignoreTenantIds));
            }
            List<String> allTenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
            allTenantIds.removeAll(ignoreTenantIdList);
            tenantIdList.addAll(allTenantIds);
        } else {
            if (StringUtils.isBlank(tenantIds) && StringUtils.isBlank(resourceId)) {
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
            }
            tenantIdList = ImmutableSet.copyOf(Splitter.on(",").split(tenantIds));
        }
        log.info("batchAddOrUpdateTenantRoute tenantIdList={} resourceId={}", tenantIdList, resourceId);
        List<String> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tenantIdList)) {
            for (String ei : tenantIdList) {
                Boolean success = configRouteManager.configRoute(ei, resourceId);
                list.add(ei + "->" + success);
            }
        }
        log.info("batchAddOrUpdateTenantRoute result={} ", list);
        return Result.newSuccess();
    }

    @ApiOperation(value = "初始化路由配置")
    @RequestMapping(value = "/initRoute", method = RequestMethod.GET)
    public Result<Integer> initRoute(@RequestParam String resourceId) {
        List<String> allTenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
        Map<String, List<String>> configMap = Maps.newHashMap();
        configMap.put(resourceId, allTenantIds);
        ErpTenantConfigurationEntity config = new ErpTenantConfigurationEntity();
        config.setChannel(ErpChannelEnum.ALL.name());
        config.setDataCenterId("0");
        config.setTenantId("all");
        config.setType(TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name());
        config.setId(idGenerator.get());
        config.setConfiguration(JSONObject.toJSONString(configMap));
        config.setCreateTime(System.currentTimeMillis());
        config.setUpdateTime(System.currentTimeMillis());
        int insert = tenantConfigurationManager.insert(config.getTenantId(), config);
        return Result.newSuccess(insert);
    }

    @ApiOperation(value = "刷sync_data,sync_data_mappings,erp_processed_data数据")
    @RequestMapping(value = "/brushTableData", method = RequestMethod.POST)
    public Result<String> brushTableData(@RequestBody List<String> sourceTenantId, @RequestHeader(value = "destDataBaseTenantId") String destDataBaseTenantId) {//destTenantId是能指向目标数据库的虚拟企业id
        return superAdminBrushDataService.brushTableData(sourceTenantId, destDataBaseTenantId);
    }

    @ApiOperation(value = "刷企业对象分发时间配置")
    @RequestMapping(value = "/brushDispatcherTime", method = RequestMethod.POST)
    public Result<String> brushDispatcherTime(@RequestBody List<String> sourceTenantIds, @RequestHeader(value = "time") Integer time, @RequestHeader(value = "cancel") Boolean cancel) {
        return superAdminBrushDataService.brushDispatcherTime(sourceTenantIds, time, cancel);
    }

    @ApiOperation(value = "获取企业路由信息")
    @RequestMapping(value = "/getTenantIdRoute", method = RequestMethod.GET)
    public Result<Map<String, Set<String>>> getTenantIdRoute(@RequestParam Boolean onlySandBox,@RequestParam String destRoute) {
        return superAdminBrushDataService.getTenantIdRoute(onlySandBox,destRoute);
    }
}
