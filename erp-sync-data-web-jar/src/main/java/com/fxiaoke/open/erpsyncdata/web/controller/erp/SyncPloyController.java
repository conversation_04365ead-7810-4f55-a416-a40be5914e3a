package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListDetailObjectArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListMasterObjectArg;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncObjectResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.MigratePloyService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Api(tags = "同步策略相关接口")
@RestController("erpSyncPloyController")
@RequestMapping("cep/syncPloy")
public class SyncPloyController extends BaseController {

    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private MigratePloyService migratePloyService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;

    @ReloadableProperty("dest.object.black.list")
    private String destObjectBlackListJson;

    @ApiOperation(value = "获取主对象列表")
    @RequestMapping(value = "/listMasterSyncObjects", method = RequestMethod.POST)
    public Result<List<SyncObjectResult>> listMasterSyncObjects(@RequestBody ListMasterObjectArg arg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        arg.getTenantIds().add(getLoginUserTenantId());
        return adminSyncPloyDetailService.listMasterSyncObjects(arg,lang);
    }
    @ApiOperation(value = "获取企业所有crm对象列表")
    @RequestMapping(value = "/listTenantAllCrmObjects", method = RequestMethod.POST)
    public Result<List<SyncObjectResult>> listTenantAllCrmObjects() {
        return adminSyncPloyDetailService.listTenantAllCrmObjects(getLoginUserTenantId());
    }

    @ApiOperation(value = "获取从对象列表")
    @RequestMapping(value = "/listDetailSyncObjects", method = RequestMethod.POST)
    public Result<Set<SyncObjectResult>> listDetailSyncObjects(@RequestBody ListDetailObjectArg arg) {
        arg.getTenantIds().add(getLoginUserTenantId());
        List<ObjectDescribe> allObjectDescribeAndFields = crmRemoteManager.listAllObjectAndFieldsByTenant(arg.getTenantIds().get(0));
        Set<SyncObjectResult> allSyncObjectResult = this.listDetailSyncObjectsByMasterObject(allObjectDescribeAndFields, arg.getMasterObjectApiName());
        for (int i = 1; i < arg.getTenantIds().size(); i++) {
            List<ObjectDescribe> objectDescribeAndFields = crmRemoteManager.listAllObjectAndFieldsByTenant(arg.getTenantIds().get(i));
            Set<SyncObjectResult> syncObjectResult = this.listDetailSyncObjectsByMasterObject(objectDescribeAndFields, arg.getMasterObjectApiName());
            allSyncObjectResult.retainAll(syncObjectResult);
        }
        return Result.newSuccess(allSyncObjectResult);
    }

    private Set<SyncObjectResult> listDetailSyncObjectsByMasterObject(List<ObjectDescribe> allObjectDescribeAndFields, String masterObjectApiName){
        Set<SyncObjectResult> results = new HashSet<>();
        for (ObjectDescribe objectDescribe : allObjectDescribeAndFields) {
            if (!objectDescribe.isActive() || objectDescribe.isDelete()) {
                continue;
            }
            boolean isDetailObjectByMasterApiName = false;
            for (FieldDescribe fieldDescribe : objectDescribe.getFields().values()) {
                if ("master_detail".equals(fieldDescribe.getType()) && fieldDescribe.getTargetApiName().equals(masterObjectApiName)) {
                    isDetailObjectByMasterApiName = true;
                }
            }
            if (isDetailObjectByMasterApiName) {
                SyncObjectResult syncObjectResult = new SyncObjectResult();
                syncObjectResult.setApiName(objectDescribe.getApiName());
                syncObjectResult.setName(objectDescribe.getDisplayName());
                results.add(syncObjectResult);
            }
        }
        return results;
    }

}
