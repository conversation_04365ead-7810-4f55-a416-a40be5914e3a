package com.fxiaoke.open.erpsyncdata.web.controller.setUp;


import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetInterfaceInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetInterfaceLogIdBySyncDataIdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncDataIdArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.model.InterfaceDebug;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.admin.service.InterfaceDebugService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpObjInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceSimpleMsgResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjNameDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

/**
 * erp接口管理
 *
 * <AUTHOR>
 * @Date: 20:27 2021/8/10
 * @Desc:
 */
@Slf4j
@Api(tags = "erp接口监控相关接口")
@RestController("erpInterfaceMonitorController")
@RequestMapping("cep/setUp/erpInterfaceMonitor")
public class ErpInterfaceMonitorController extends AsyncSupportController {
    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private InterfaceDebugService interfaceDebugService;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 获取指定接口调用记录列表
     * @param arg
     * @return
     */
    @ApiOperation(value = "获取指定接口调用记录（接口详情）")
    @RequestMapping(value = "/queryObjInterfaceList", method = RequestMethod.POST)
    public Result<QueryResult<List<ErpInterfaceMonitorResult>>> queryObjInterfaceList(@RequestBody QueryErpObjInterfaceMonitorArg arg,
                                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        return erpInterfaceMonitorService.queryObjInterfaceList(tenantId, dcId, userId, arg,lang);
    }

    /**
     * 获取对象所有类型接口列表（接口列表）
     * @param arg
     * @return
     */
    @ApiOperation(value = "获取对象所有类型接口列表（接口列表）")
    @RequestMapping(value = "/queryAllObjInterfaceList", method = RequestMethod.POST)
    public Result<List<ErpInterfaceSimpleMsgResult>> queryAllObjInterfaceList(@RequestBody QueryErpInterfaceMonitorArg arg,
                                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        return erpInterfaceMonitorService.queryAllObjInterfaceList(tenantId, dcId, userId, arg,lang);
    }

    /**
     * 获取接口单次调用详情
     * @param idArg
     * @return
     */
    @ApiOperation(value = "获取接口单次调用详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    public Result<ErpInterfaceMonitorResult> getObjInterfaceMonitor(@RequestBody BaseArg idArg,
                                                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpInterfaceMonitorService.getObjInterfaceMonitor(tenantId, userId, idArg,lang);
    }

    /**
     * 获取所有ERP对象
     * @return
     */
    @ApiOperation(value = "获取所有erp真实对象")
    @RequestMapping(value = "/queryAllErpRealObj", method = RequestMethod.POST)
    public Result<List<ErpObjNameDescResult>> queryAllErpRealObj(@RequestBody CepArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        return erpInterfaceMonitorService.queryAllErpRealObj(tenantId, dcId, userId);
    }

    /**
     * 下载 接口监控详情
     * @param idArg
     * @return
     */
    @ApiOperation(value = "下载")
    @RequestMapping(value = "/download", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> downloadObjInterfaceMonitor(@RequestBody BaseArg idArg,
                                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String ea = getEa();
        Integer userId = getLoginUserId();
        Result<ErpInterfaceMonitorResult> erpInterfaceMonitorResultResult = erpInterfaceMonitorService.getObjInterfaceMonitor(tenantId, userId, idArg,lang);
        if (erpInterfaceMonitorResultResult == null || erpInterfaceMonitorResultResult.getData() == null
                || StringUtils.isBlank(erpInterfaceMonitorResultResult.getData().getResult())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, JSONObject.toJSONString(erpInterfaceMonitorResultResult.getData()).getBytes(),lang);
        BuildExcelFile.Result result = new BuildExcelFile.Result();
        String fileName = StringUtils.appendIfMissing(idArg.getId(), ".txt");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }

    /**
     * 根据同步记录id获取接口调用记录Id
     *
     * @param arg
     * @return 接口调用记录
     */
    @ApiOperation(value = "根据同步记录id获取接口调用记录Id",httpMethod = "POST")
    @RequestMapping(value = "/getIdBySyncDataId", method = RequestMethod.POST)
    public Result<String> getIdBySyncDataId(@ApiParam(name = "同步记录Id") @RequestBody SyncDataIdArg arg) {   // ignoreI18n  注解
        String tenantId = getLoginUserTenantId();
        GetInterfaceLogIdBySyncDataIdArg build = GetInterfaceLogIdBySyncDataIdArg.builder().tenantId(tenantId).syncDataId(arg.getSyncDataId()).build();
        build.setStartLogTime(System.currentTimeMillis() - 60 * 60 * 1000);//默认查询间隔前后1小时
        build.setEndLogTime(System.currentTimeMillis() + 60 * 60 * 1000);
        return interfaceDebugService.getInterfaceLogIdBySyncDataId(build);
    }

    /**
     * 获取接口信息
     *
     * @param arg
     * @return
     */
    @ApiOperation(value = "获取接口信息",httpMethod = "POST")
    @RequestMapping(value = "/getInterfaceInfo", method = RequestMethod.POST)
    public Result<InterfaceDebug.InfoData> getInterfaceInfo(@RequestBody GetInterfaceInfoArg arg,
                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        InterfaceDebug.GetInfoArg arg2 = InterfaceDebug.GetInfoArg.builder()
                .tenantId(tenantId)
                .dcId(dcId)
                .objApiName(arg.getObjApiName())
                .type(arg.getType()).build();
        return interfaceDebugService.getInterfaceInfo(arg2,lang);
    }

    @ApiOperation(value = "810获取接口信息",httpMethod = "POST")
    @RequestMapping(value = "/getInterfaceInfoV2", method = RequestMethod.POST)
    public Result<InterfaceDebug.InfoData> getInterfaceInfoV2(@RequestBody GetInterfaceInfoArg arg,
                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        InterfaceDebug.GetInfoArg arg2 = InterfaceDebug.GetInfoArg.builder()
                .tenantId(tenantId)
                .dcId(dcId)
                .objApiName(arg.getObjApiName())
                .type(arg.getType()).build();
        return interfaceDebugService.getInterfaceInfoV2(arg2,lang);
    }

    /**
     * 调试获取单条数据接口
     *
     * @param arg
     * @return
     */
    @ApiOperation(value = "调试获取单条数据接口")
    @RequestMapping(value = "/debugGetData", method = RequestMethod.POST)
    public DeferredResult<Result<ErpInterfaceMonitorResult>> debugGetData(@RequestBody InterfaceDebug.GetDataArg arg,
                                                                          @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        arg.setTenantId(tenantId);
        arg.setDcId(dcId);
        Result<ErpInterfaceMonitorResult> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = i18NStringManager.get(I18NStringEnum.s104,lang,tenantId);
        return asyncExecuteByCustomerConsumer(() ->interfaceDebugService.debugGetData(arg,lang),10,true,actionName,generateConsumer(actionName,true,lang),timeoutResult,lang);

    }

    /**
     * 调试获取单条数据接口
     *
     * @param arg
     * @return
     */
    @ApiOperation(value = "810调试获取单条数据接口")
    @RequestMapping(value = "/debugGetDataV2", method = RequestMethod.POST)
    public DeferredResult<Result<ErpInterfaceMonitorResult>> debugGetDataV2(@RequestBody InterfaceDebug.GetDataArg arg,
                                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        arg.setTenantId(tenantId);
        arg.setDcId(dcId);
        Result<ErpInterfaceMonitorResult> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = i18NStringManager.get(I18NStringEnum.s105,lang,tenantId);
        return asyncExecuteByCustomerConsumer(() ->interfaceDebugService.debugGetDataV2(arg,lang),10,true,actionName,generateConsumer(actionName,true,lang),timeoutResult,lang);
    }

    /**
     * 调试获取列表数据接口
     *
     * @param arg
     * @return
     */
    @ApiOperation(value = "调试获取列表数据接口")
    @RequestMapping(value = "/debugListData", method = RequestMethod.POST)
    public DeferredResult<Result<ErpInterfaceMonitorResult>> debugListData(@RequestBody InterfaceDebug.ListDataArg arg,
                                                                           @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        arg.setTenantId(tenantId);
        arg.setDcId(dcId);
        Result<ErpInterfaceMonitorResult> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = i18NStringManager.get(I18NStringEnum.s106,lang,tenantId);
        return asyncExecuteByCustomerConsumer(() ->interfaceDebugService.debugListData(arg,lang),10,true,actionName,generateConsumer(actionName,true,lang),timeoutResult,lang);
    }

    /**
     * 调试获取列表数据接口
     * 暂时只有两个场景 :
     * 1.API配置中设置筛选条件(filters!=null)
     * 2.接口调试(固定为读更新接口)
     *
     * @param arg
     * @return
     */
    @ApiOperation(value = "调试获取列表数据接口V2")
    @RequestMapping(value = "/debugListDataV2", method = RequestMethod.POST)
    public DeferredResult<Result<ErpInterfaceMonitorResult>> debugListDataV2(@RequestBody InterfaceDebug.ListDataArg arg,
                                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        arg.setTenantId(tenantId);
        arg.setDcId(dcId);
        Result<ErpInterfaceMonitorResult> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = i18NStringManager.get(I18NStringEnum.s107,lang,tenantId);
        return asyncExecuteByCustomerConsumer(() ->interfaceDebugService.debugListDataV2(arg,lang),10,true,actionName,generateConsumer(actionName,true,lang),timeoutResult,lang);

    }
}
