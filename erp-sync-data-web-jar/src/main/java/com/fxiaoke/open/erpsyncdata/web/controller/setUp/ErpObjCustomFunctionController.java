package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjCustomFunctionService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjCustomFunctionResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionResult;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 19:44 2020/8/17
 * @Desc:
 */
@Slf4j
@Api(tags = "erp对象自定义函数相关接口")
@RestController("setUpErpObjCustomFunctionController")
@RequestMapping("cep/setUp/erpObjCustomFunction")
@ManagedTenantIntercept
public class ErpObjCustomFunctionController extends BaseController {
    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;
    @Autowired
    private CustomFunctionService customFunctionService;


    @ApiOperation(value = "获取erp对象自定义函数")
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @Deprecated
    public Result<List<ErpObjCustomFunctionResult>> query() {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return erpObjCustomFunctionService.query(tenantId,dataCenterId,userId);
    }

    @ApiOperation(value = "更新或者新增erp对象自定义函数")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result<String> update(@RequestBody ErpObjCustomFunctionResult erpObjCustomFunctionResult,
                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        erpObjCustomFunctionResult.setDataCenterId(dataCenterId);
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), userId);
        FunctionServiceFindArg findArg = new FunctionServiceFindArg();
        findArg.setApiName(erpObjCustomFunctionResult.funcApiName);
        findArg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        Result2<FunctionResult<FunctionServiceFindResult>> functionResult = customFunctionService.find(headerObj,findArg);
        if(functionResult==null || functionResult.getData()==null || functionResult.getData().getResult()==null || !functionResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.CUSTOM_FUNC_APINAME_TYPE_NOT_EXIST);
        }

        return erpObjCustomFunctionService.update(tenantId,userId, erpObjCustomFunctionResult,lang);
    }

    @ApiOperation(value = "删除自定义函数")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Result<String> delete(@RequestBody BaseArg deleteArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        return erpObjCustomFunctionService.delete(tenantId, userId, dcId, deleteArg);
    }
//    @ApiOperation(value = "执行erp对象自定义函数")
//    @RequestMapping(value = "/execute", method = RequestMethod.POST)
//    public Result<String> execute(@RequestBody ErpObjCustomFunctionResult erpObjCustomFunctionResult) {
//        String tenantId = getLoginUserTenantId();
//        Integer userId = getLoginUserId();
//        return erpObjCustomFunctionService.execute(tenantId,userId,erpObjCustomFunctionResult);
//    }

}
