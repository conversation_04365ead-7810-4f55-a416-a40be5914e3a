package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.model.ErpAnalysisField;
import com.fxiaoke.open.erpsyncdata.admin.model.InitK3Obj;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpObjectFieldExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ExportObjectFieldData;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportObjectFieldData;
import com.fxiaoke.open.erpsyncdata.admin.remote.StoneFileManager;
import com.fxiaoke.open.erpsyncdata.admin.result.DeleteErpObjFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjPresetService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ObjectDescArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:27 2020/8/18
 * @Desc:
 */
@Slf4j
@Api(tags = "erp对象字段设置相关接口")
@RestController("setUpErpObjectFieldsController")
@RequestMapping("cep/setUp/erpObjectFields")
@ManagedTenantIntercept
public class ErpObjectFieldsController extends AsyncSupportController {
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpObjPresetService erpObjPresetService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private StoneFileManager stoneFileManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpConnectInfoManager connectorInfoManager;
    @Autowired
    private DBFileManager dbFileManager;
    @Autowired
    private UserCenterService userCenterService;

    @ApiOperation(value = "获取erp对象字段信息")
    @RequestMapping(value = "/pageErpObjectFieldsByObjApiName", method = RequestMethod.POST)
    @Deprecated
    public Result<QueryResult<List<ErpObjectFieldResult>>> queryErpObjectFields(@RequestBody QueryErpObjectFieldsArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        if (Strings.isNullOrEmpty(arg.getErpObjectApiName())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String dataCenterId = getDcId();
        return erpObjectFieldsService.pageErpObjectFieldsByObjApiName(tenantId, dataCenterId, userId, arg);
    }

    //    @ApiOperation(value = "更新或者新增erp对象字段信息")
//    @RequestMapping(value = "/updateErpObjectFields", method = RequestMethod.POST)
//    public Result<List<ErpObjectFieldResult>> updateErpObjectFields(@RequestBody ErpObjectFieldResult erpObjectFieldResult) {
//        Integer tenantId = getIntLoginUserTenantId();
//        Integer userId = getLoginUserId();
//        return erpObjectFieldsService.updateErpObjectFields(tenantId, userId, erpObjectFieldResult);
//    }
    @ApiOperation(value = "通过真实对象获取对象字段详细信息")
    @RequestMapping(value = "/queryErpObjectFieldsByActualObj", method = RequestMethod.POST)
    public Result<ErpObjectRelationshipResult> queryErpObjectFieldsByActualObj(@RequestBody ErpObjectDescResult arg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId(tenantId, userId, arg, dataCenterId, lang);
    }

    @ApiOperation(value = "删除erp对象字段信息，这个接口需要下线")
    @RequestMapping(value = "/deleteErpObjectFields", method = RequestMethod.POST)
    public Result<String> deleteErpObjectFields(@RequestBody DeleteErpObjectFieldsArg deleteArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpObjectFieldsService.deleteErpObjectFields(tenantId, userId, deleteArg);
    }

    @ApiOperation(value = "删除erp对象字段信息")
    @RequestMapping(value = "/batchDeleteErpObjectFields", method = RequestMethod.POST)
    public Result<List<DeleteErpObjFieldResult>> batchDeleteErpObjectFields(@RequestBody DeleteErpObjectFieldsArg deleteArg,
                                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpObjectFieldsService.batchDeleteErpObjectFields(tenantId, userId, deleteArg.getIdList(), lang);
    }

    @ApiOperation(value = "更新或者新增字段信息")
    @RequestMapping(value = "/updateErpObjectFields", method = RequestMethod.POST)
    public Result<ErpObjectFieldResult> updateErpObjectFields(@RequestBody UpsertErpObjectFieldArg arg,
                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return erpObjectFieldsService.updateErpObjectFields(tenantId, dataCenterId, userId, arg, lang);
    }

    @ApiOperation(value = "按字段顺序批量更新ERP对象字段信息，仅用于编辑对象字段页面，保存ERP对象排序后的字段信息")
    @RequestMapping(value = "/updateErpObjectFieldsInOrder", method = RequestMethod.POST)
    public Result<Void> updateErpObjectFieldsInOrder(@RequestBody ErpObjectRelationshipResult erpObjectRelationshipResult) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        if (userId == null || StringUtils.isEmpty(dataCenterId)) return Result.newError(ResultCodeEnum.PARAM_ERROR);
        return erpObjectFieldsService.updateErpObjectFieldsInOrder(tenantId, dataCenterId, erpObjectRelationshipResult);
    }

    @ApiOperation(value = "校验对象字段信息")
    @RequestMapping(value = "/checkErpObjectFields", method = RequestMethod.POST)
    public Result<String> checkErpObjectFields(@RequestBody ErpObjectDescResult arg,
                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return erpObjectFieldsService.checkErpObjectFields(tenantId, userId, arg, dataCenterId, lang);
    }

    @ApiOperation(value = "获取ERP字段选项")
    @RequestMapping(value = "/queryErpFieldItem", method = RequestMethod.POST)
    public Result<List<ErpFieldItemResult>> queryErpFieldItem(@RequestBody ErpFieldItemArg arg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return erpObjectFieldsService.queryErpFieldItem(tenantId, dataCenterId, arg);
    }

    @ApiOperation(value = "获取K3c字段元数据信息")
    @RequestMapping(value = "/queryK3cloudErpField", method = RequestMethod.POST)
    @Deprecated
    public Result<ErpObjectFieldResult> queryK3cloudErpField(@RequestBody QueryK3cloudErpFieldArg arg,
                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return erpObjectFieldsService.queryK3cloudErpField(tenantId, dataCenterId, arg.getErpObjectApiName(), arg.getErpObjectFieldName(), lang);
    }


    /**
     * 当resultCode为's306240238',说明解析进行中，前端需要等待
     */
    @ApiOperation(value = "解析K3C字段信息,指定对象")
    @RequestMapping(value = "/analyzeK3ObjFieldByObj", method = RequestMethod.POST)
    public DeferredResult<Result<ErpAnalysisField>> asyncAnalyzeK3ObjFieldByObj(@RequestBody InitK3Obj.AnalysisFieldArg arg,
                                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        String actionName = i18NStringManager.get(I18NStringEnum.s2020, lang, tenantId);
        DeferredResult<Result<ErpAnalysisField>> resultDeferredResult = asyncExecuteWithTimeOutResult(actionName,
                (deferredResult) -> {
                    Result<ErpAnalysisField> analyzeK3ObjField = erpObjPresetService.analyzeK3ObjField(tenantId, dataCenterId, arg, lang);
                    analyzeK3ObjField.setI18nKey(null);
                    analyzeK3ObjField.setI18nExtra(null);
                    deferredResult.setResult(analyzeK3ObjField);
                },
                10, Result.newError(ResultCodeEnum.ANALYZING_K3_OBJ)
        );
        return resultDeferredResult;
    }


    /**
     * 当resultCode为's306240238',说明解析进行中，前端需要等待
     */
    @ApiOperation(value = "解析K3C字段信息,指定对象 字段")
    @RequestMapping(value = "/analyzeK3ObjFieldByField", method = RequestMethod.POST)
    public DeferredResult<Result<ErpObjectFieldResult>> asyncAnalyzeK3ObjFieldByField(@RequestBody InitK3Obj.AnalysisFieldArg arg,
                                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        String actionName = i18NStringManager.get(I18NStringEnum.s2021, lang, tenantId);
        DeferredResult<Result<ErpObjectFieldResult>> resultDeferredResult = asyncExecuteWithTimeOutResult(actionName,
                (deferredResult) -> {
                    Result<ErpAnalysisField> erpAnalysisFieldResult = erpObjPresetService.analyzeK3ObjField(tenantId, dataCenterId, arg, lang);
                    if (!erpAnalysisFieldResult.isSuccess()) {
                        deferredResult.setResult(Result.clearI18NKey(Result.copy(erpAnalysisFieldResult)));
                    }
                    if (erpAnalysisFieldResult.getData().getErpObjectFields().isEmpty()) {
                        //未查到
                        deferredResult.setResult(Result.clearI18NKey(Result.newSuccess()));
                    }
                    deferredResult.setResult(Result.clearI18NKey(Result.newSuccess(erpAnalysisFieldResult.getData().getErpObjectFields().get(0))));
                },
                10, Result.newError(ResultCodeEnum.ANALYZING_K3_OBJ)
        );
        return resultDeferredResult;
    }


    /**
     * 当resultCode为's306243405',说明解析进行中，前端需要等待
     */
    @ApiOperation(value = "解析字段信息，指定对象，可指定字段")
    @RequestMapping(value = "/parseObjField", method = RequestMethod.POST)
    public DeferredResult<Result<ErpAnalysisField>> parseObjField(@RequestBody ObjectDescArg.ParseObjFieldBySplit arg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        String lang = getLang();
        String actionName = i18NStringManager.get(I18NStringEnum.s3303, lang, tenantId);
        DeferredResult<Result<ErpAnalysisField>> resultDeferredResult = asyncExecuteWithTimeOutResult(actionName,
                (deferredResult) -> deferredResult.setResult(erpObjPresetService.parseObjectField(tenantId, dataCenterId, arg)),
                10, Result.newError(ResultCodeEnum.PARSING_OBJ_FIELD)
        );
        return resultDeferredResult;
    }

    @ApiOperation(value = "导入对象字段数据")
    @RequestMapping(value = "/importObjectFieldData", method = RequestMethod.POST)
    public DeferredResult<Result<ImportExcelFile.FieldImportResult>> importObjectFieldData(@RequestBody ImportObjectFieldData.Arg arg) {
        String tenantId = getLoginUserTenantId();
        final String lang = getLang();

        return asyncExecute(() -> {
            ImportExcelFile.FieldImportArg importArg = new ImportExcelFile.FieldImportArg();
            importArg.setTenantId(tenantId);
            importArg.setDataCenterId(getDcId());
            importArg.setUserId(getLoginUserId());
            importArg.setErpRealObjectApiName(arg.getErpRealObjectApiName());
            importArg.setExcelType(arg.getExcelType());
            try (InputStream inputStream = stoneFileManager.downloadByPath(tenantId, arg.getNpath(), "xlsx")) {
                importArg.setFileStream(inputStream);
                return erpObjectFieldsService.batchImportErpObjectFields(importArg, lang);
            } catch (Exception e) {
                log.error("ExcelFileController.importObjectFieldData,exception={}", e.getMessage());
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR, e.getMessage());
            }
        }, 10, false, i18NStringManager.get(I18NStringEnum.s3622, lang, tenantId), Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT), lang);
    }

    @ApiOperation(value = "导出对象字段数据")
    @RequestMapping(value = "/exportObjectFieldData", method = RequestMethod.POST)
    public Result<ExportObjectFieldData.Result> exportObjectFieldData(@RequestBody ExportObjectFieldData.Arg arg) {
        String tenantId = getLoginUserTenantId();
        final String lang = getLang();
        final String dcId = getDcId();
        final String objectApiName = arg.getErpObjectApiName();

        final List<ErpObjectFieldExcelVo> vos = getErpObjectFieldExcelVos(tenantId, dcId, objectApiName);

        final ExcelSheetArg excelSheetArg = new ExcelSheetArg();
        excelSheetArg.setSheetName(objectApiName);
        excelSheetArg.setDataList(vos);
        excelSheetArg.setClazz(ErpObjectFieldExcelVo.class);

        // 文件名称
        final String now = DateTimeFormatter.ofPattern("yyyyMMdd_HHmm").format(LocalDateTime.now());
        final String fileName = objectApiName + "_" + now;

        // 生成文件
        final Result<String> uploadExcel = dbFileManager.writeAndUploadExcel(tenantId, fileName, Lists.newArrayList(excelSheetArg), lang);
        if (!uploadExcel.isSuccess()) {
            return Result.copy(uploadExcel);
        }
        final String npath = uploadExcel.getData();

        // 返回文件路径
        final ExportObjectFieldData.Result result = new ExportObjectFieldData.Result();
        String url = String.format(userCenterService.getDownloadFilePath(tenantId), npath, fileName + ".xlsx");
        result.setDownloadUrl(url);

        return Result.newSuccess(result);
    }

    @NotNull
    private List<ErpObjectFieldExcelVo> getErpObjectFieldExcelVos(String tenantId, String dcId, String objectApiName) {
        final ErpConnectInfoEntity connectInfo = connectorInfoManager.getByIdAndTenantId(tenantId, dcId);
        boolean hasExtends = Objects.equals(connectInfo.getChannel(), ErpChannelEnum.ERP_K3CLOUD);
        final List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjManager.listRelationsByRealApiName(tenantId, dcId, objectApiName);
        return erpObjectRelationshipEntities.stream()
                .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                .flatMap(apiName -> {
                    final List<ErpObjectFieldEntity> erpObjectFieldEntities = erpFieldManager.queryAllField(tenantId, dcId, apiName);
                    if (!hasExtends) {
                        return erpObjectFieldEntities.stream().map(entity -> Pair.of(entity, (ErpFieldExtendEntity) null));
                    }

                    final Map<String, ErpFieldExtendEntity> extendEntityMap = erpFieldManager.queryFieldMap(tenantId, dcId, apiName);
                    return erpObjectFieldEntities.stream().map(entity -> Pair.of(entity, extendEntityMap.get(entity.getFieldApiName())));
                }).map(pair -> {
                    final ErpObjectFieldEntity objectField = pair.getKey();
                    final ErpFieldExtendEntity fieldExtend = pair.getValue();
                    final ErpObjectFieldExcelVo erpObjectFieldExcelVo = new ErpObjectFieldExcelVo();
                    erpObjectFieldExcelVo.setErpObjectApiName(objectField.getErpObjectApiName());
                    erpObjectFieldExcelVo.setFieldLabel(objectField.getFieldLabel());
                    erpObjectFieldExcelVo.setFieldApiName(objectField.getFieldApiName());
                    erpObjectFieldExcelVo.setFieldDefineType(objectField.getFieldDefineType().name());
                    erpObjectFieldExcelVo.setFieldExtendValue(objectField.getFieldExtendValue());
                    if (Objects.nonNull(fieldExtend)) {
                        erpObjectFieldExcelVo.setViewCode(fieldExtend.getViewCode());
                        erpObjectFieldExcelVo.setSaveCode(fieldExtend.getSaveCode());
                        erpObjectFieldExcelVo.setQueryCode(fieldExtend.getQueryCode());
                        erpObjectFieldExcelVo.setRequired(objectField.getRequired());
                    }
                    return erpObjectFieldExcelVo;
                }).collect(Collectors.toList());
    }
}
