package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CategoryFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.service.SpecialSyncService;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ProductCategorySyncResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 2021/1/13
 * @Desc: 产品分类同步控制器
 */
@Slf4j
@Api(tags = "产品分类同步控制器")
@RestController()
@RequestMapping("cep/setUp/productCategory")
public class ProductCategorySyncController extends BaseController {
    @Autowired
    private SpecialSyncService specialSyncService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    @Qualifier("redissonClient")
    private RedissonClient redissonClient;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "从k3c获取物料分组数据并自动同步到crm产品分类")
    @RequestMapping(value = "/autoSync", method = RequestMethod.POST)
    public Result<String> autoSync(@RequestBody CepArg arg,
                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Integer loginUserId = getLoginUserId();
        String redisRock = CommonConstants.SYNC_CATE_LOCK + tenantId + dcId;
        RLock lock = redissonClient.getLock(redisRock);
        try {
            if (lock.tryLock(5L, 3600L, TimeUnit.SECONDS)) {
                ParallelUtils.createBackgroundTask().submit(() -> {
                    try {
                        Result<List<CategoryFieldDataMappingExcelVo>> listResult = specialSyncService.syncKcMaterialGroup2Cate(tenantId, loginUserId, dcId,lang);
                        redisDataSource.get(this.getClass().getSimpleName()).del(redisRock);
                        sendResult(tenantId, loginUserId, listResult,lang);
                    } finally {
                        lock.unlock();
                    }
                }).run();
                return Result.newSuccessByI18N(i18NStringManager.get(I18NStringEnum.s328, lang, tenantId), I18NStringEnum.s328.getI18nKey(),null);
            } else {
                return Result.newSuccessByI18N(i18NStringManager.get(I18NStringEnum.s329, lang, tenantId), I18NStringEnum.s329.getI18nKey(),null);
            }
        } catch (InterruptedException e) {
            log.error("lock InterruptedException", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    @ApiOperation(value = "查询同步日志")
    @RequestMapping(value = "/querySyncLog", method = RequestMethod.POST)
    public Result<ProductCategorySyncResult> querySyncLog(@RequestBody CepArg arg) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Integer loginUserId = getLoginUserId();
        return specialSyncService.getSyncKcMaterialGroup2CateLog(tenantId, loginUserId, dcId);
    }


    private void sendResult(String tenantId, Integer userId, BaseResult result,String lang) {
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(getDcId());
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(result.getErrMsg());
        sendTextNoticeArg.setMsgTitle(i18NStringManager.get2(I18NStringEnum.s330.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s330.getI18nValue(), LocalDateTime.now()),
                Lists.newArrayList(LocalDateTime.now().toString())));
        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,lang,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }
}
