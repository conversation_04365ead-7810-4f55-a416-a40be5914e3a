package com.fxiaoke.open.erpsyncdata.web.controller.noAuth;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.U8DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8.U8DataManagerAbstracta;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 集成平台获取连接参数
 * Created by fengyh on 2020/9/23.
 */

@RestController("erpConnParamController")
@RequestMapping("erp/syncdata/noAuth/connParam")
@Slf4j
public class ErpConnParamController extends BaseController {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private U8DataManager u8DataManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @ApiOperation(value = "获取u8的链接参数")
    @RequestMapping(value = "/getU8ConnParam", method = RequestMethod.GET)
    public Result<String> getU8ConnParam(@RequestParam("ei") String ei,
                                         @RequestParam(value = "ds_sequence",required = false) Integer dsSequence) {

        U8DataManagerAbstracta u8DataManagerAbstracta = u8DataManager.getDefaultBean();
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoManager.listByTenantId(ei);
        erpConnectInfoEntities.removeIf(v->!v.getChannel().equals(ErpChannelEnum.ERP_U8));
        U8ConnectParam u8ConnectParam = null;
        for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
            u8ConnectParam = GsonUtil.fromJson(erpConnectInfoEntity.getConnectParams(), U8ConnectParam.class);
            if (dsSequence==null||u8ConnectParam.getDs_sequence().equals(dsSequence)){
                break;
            }
        }
        if (u8ConnectParam==null){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), I18NStringEnum.s94);
        }
        try {
            String cookedParam = u8DataManagerAbstracta.getRequestParams(u8ConnectParam);
            log.info("trace getU8ConnParam for ei:{}, get result:{}", ei, cookedParam);
            return Result.newSuccess(cookedParam);
        } catch (Exception e) {
            log.error("getRequestParams for ei:{} get exception,",ei,  e);
            return Result.newError("-1", e.getMessage());
        }
    }
}
