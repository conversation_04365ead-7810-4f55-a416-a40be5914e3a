package com.fxiaoke.open.erpsyncdata.web.controller.open;

import cn.hutool.core.map.multi.ListValueMap;
import com.fxiaoke.open.erpsyncdata.admin.service.WebhookService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.Webhook;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PushIdentifyManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;

/**
 * 外部回调接口
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@RestController("WebhookController")
@RequestMapping("erp/syncdata/open")
public class WebhookController {
    @Autowired
    private WebhookService webhookService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private PushIdentifyManager pushIdentifyManager;

    @ControllerMethodProxy()
    @RequestMapping(value = {"webhook/{tenantId}/{datacenterId}/{token}"}, method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseEntity<String> webhook(@PathVariable String tenantId,
                                          @PathVariable String datacenterId,
                                          @PathVariable String token,
                                          @RequestHeader(required = false) HttpHeaders headers,
                                          @RequestParam(required = false) LinkedHashMap<String, String> params,
                                          @RequestBody(required = false) String body) {
        //验证token
        if (!pushIdentifyManager.checkIdentify(tenantId, token)) {
            return ResponseEntity.badRequest().body("token is invalid");
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, datacenterId);
        if (connectInfo == null) {
            return ResponseEntity.badRequest().body("datacenterId is invalid");
        }
        Webhook.Arg arg = new Webhook.Arg().setHeaders(new ListValueMap<>(headers))
                .setParams(params)
                .setBody(body);
        Result<Webhook.Result> result = webhookService.webhook(tenantId, datacenterId, arg);
        if (!result.isSuccess()) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(result.getErrMsg());
        }
        return result.getData().toResponse();
    }
}
