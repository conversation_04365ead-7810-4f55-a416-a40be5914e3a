package com.fxiaoke.open.erpsyncdata.web.controller.inner;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.OuterConnectorManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 外部连接器接口
 * 暂时只支持内部调用
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@RestController
@RequestMapping("inner/outerConnector")
public class OuterConnectorManagerController {
    @Autowired
    private OuterConnectorManager outerConnectorManager;

    /**
     * 注册
     *
     * @return
     */
    @PostMapping("register")
    public Result<Void> register(@RequestBody HubInfo hubInfo) {
        outerConnectorManager.temporaryRegister(hubInfo);
        return Result.newSuccess();
    }


    /**
     * 获取列表
     *
     */
    @GetMapping("getHubInfoList")
    public Result<List<HubInfo>> getHubInfoList() {
        return Result.newSuccess(outerConnectorManager.getHubInfoList());
    }
}
