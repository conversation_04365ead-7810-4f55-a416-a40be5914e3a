package com.fxiaoke.open.erpsyncdata.web.controller.inner;

import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.model.inner.StdConnectInfo;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 内部接口：连接器管理
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@RestController
@RequestMapping("inner/connectInfo")
public class InnerConnectInfoController extends BaseInnerController {
    @Autowired
    private ConnectInfoService connectInfoService;

    @ApiOperation(value = "获取企业连接信息列表，无连接参数(仅标准连接器)")
    @RequestMapping(value = "/listStdConnectInfos", method = RequestMethod.POST)
    public Result<List<StdConnectInfo.Result>> listStdConnectInfos(@RequestBody StdConnectInfo.ListArg arg) {
        String tenantId = getTenantId();
        Integer userId = getUserId();
        List<DataCenterInfoResult> dataCenterInfoResults = connectInfoService.getAllDCInfo(tenantId, userId, getLocale()).safeData();
        List<StdConnectInfo.Result> resultList = dataCenterInfoResults.stream()
                .filter(v -> StrUtil.equals(v.getConnectorKey(), arg.getConnectorKey()))
                .map(v -> {
                    StdConnectInfo.Result result = new StdConnectInfo.Result();
                    return result.setId(v.getId())
                            .setConnectorKey(v.getConnectorKey())
                            .setDataCenterName(v.getDataCenterName())
                            .setConnectorName(v.getConnectorName());
                }).collect(Collectors.toList());
        return Result.newSuccess(resultList);
    }

    @ApiOperation(value = "获取企业连接信息详情(仅标准连接器)")
    @RequestMapping(value = "/getStdConnectInfo", method = RequestMethod.POST)
    public Result<StdConnectInfo.Result> getStdConnectInfo(@RequestBody StdConnectInfo.IdArg arg) {
        String tenantId = getTenantId();
        Integer userId = getUserId();
        Result<ConnectInfoResult> connectInfoByDataCenterId = connectInfoService.getConnectInfoByDataCenterId(tenantId, userId, arg.getId());
        return buildResult(connectInfoByDataCenterId);
    }


    @ApiOperation(value = "删除企业连接信息(仅标准连接器)")
    @RequestMapping(value = "/deleteStdConnectInfo", method = RequestMethod.POST)
    public Result<Void> deleteStdConnectInfo(@RequestBody StdConnectInfo.IdArg arg) {
        String tenantId = getTenantId();
        Integer userId = getUserId();
        Result<Void> voidResult = connectInfoService.deleteConnectInfo(tenantId, userId, arg.getId(), getLocale());
        return voidResult;
    }


    @ApiOperation(value = "新建或更新企业连接信息(仅标准连接器)")
    @RequestMapping(value = "/upsertStdConnectInfo", method = RequestMethod.POST)
    public Result<StdConnectInfo.Result> upsertStdConnectInfo(@RequestBody StdConnectInfo.UpsertArg arg) {
        String tenantId = getTenantId();
        Integer userId = getUserId();
        Result<ConnectInfoResult> connectInfoResultResult = connectInfoService.upsertConnectInfoAndCopySetting(tenantId, userId, buildArg(arg), I18nUtil.getLocaleFromTrace(), true, arg.isCopySettingsFromDefault());
        Result<StdConnectInfo.Result> upsertResult = buildResult(connectInfoResultResult);
        return upsertResult;
    }

    /**
     * 填充一些默认值
     */
    private ConnectInfoResult buildArg(StdConnectInfo.UpsertArg arg) {
        ConnectInfoResult connectInfoResult = new ConnectInfoResult();
        connectInfoResult.setId(arg.getId());
        connectInfoResult.setChannel(ErpChannelEnum.STANDARD_CHANNEL);
        connectInfoResult.setConnectorKey(arg.getConnectorKey());
        connectInfoResult.setDataCenterName(arg.getDataCenterName());
        connectInfoResult.setConnectParams(ErpChannelEnum.STANDARD_CHANNEL.getNewConnectParam(arg.getConnectParam()));
        connectInfoResult.setEnterpriseName(getEnterpriseName());
        return connectInfoResult;
    }

    /**
     * 填充一些默认值
     */
    private Result<StdConnectInfo.Result> buildResult(Result<ConnectInfoResult> connectInfoResultR) {
        if (!connectInfoResultR.isSuccess()) {
            return Result.copy(connectInfoResultR);
        }
        ConnectInfoResult data = connectInfoResultR.getData();
        StdConnectInfo.Result result = new StdConnectInfo.Result();
        result.setId(data.getId())
                .setConnectorKey(data.getConnectorKey())
                .setDataCenterName(data.getDataCenterName())
                .setConnectParam(data.getConnectParams().getStandard())
                .setConnectorName(data.getConnectorName())
                .setNumber(data.getNumber())
                .setBindExceptionMsg(data.getBindExceptionMsg())
        ;
        return Result.newSuccess(result);
    }

}
