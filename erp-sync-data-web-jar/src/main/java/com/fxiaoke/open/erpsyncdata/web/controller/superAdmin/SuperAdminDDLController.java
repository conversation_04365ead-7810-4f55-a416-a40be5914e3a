package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.AmisResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据库DDL操作
 * 请谨慎使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/8/23
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/ddl")
@Slf4j
public class SuperAdminDDLController extends SuperAdminBaseController {
    private final String mappingPreFix = "sync_data_mappings_";
    @Autowired
    private ErpTableDao erpTableDao;
    @Resource(name = "erpSyncDataMongoStore")
    private DatastoreExt tempStore;
    @Resource(name = "erpSyncDataLogMongoStore")
    private DatastoreExt logStore;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    private String tempDbName = "erp_sync_data2";
    private String logDbName;

    @PostConstruct
    public void init() {
        this.logDbName = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }

    @PostMapping("pg/listIndexes")
    public Result<Dict> listIndexes(@RequestBody IndexArg param) {
        String query = String.format("select indexname, indexdef from pg_indexes where tablename = '%s'", param.getTable());
        List<Dict> res = erpTableDao.setTenantId(param.getTenantId()).superQuerySql2(query, param.getTable());
        List<String> indexdefs = res.stream().map(v -> v.getStr("indexdef")).collect(Collectors.toList());
        return AmisResult.list(indexdefs);
    }

    @PostMapping("pg/createIndexes")
    public DeferredResult<Result<Dict>> createPgIndexes(@RequestBody IndexArg indexArg) {
        return asyncExecute(() -> createPgIndexesSync(indexArg), 5, true, "创建索引" + indexArg.getIndexPrefix(),null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> createPgIndexesSync(IndexArg indexArg) {
        if (StrUtil.isBlank(indexArg.getIndexPrefix())) {
            return Result.newError("索引前缀不允许为空");   // ignoreI18n   实施和开发自用
        }
        List<String> failedTenants = new ArrayList<>();
        Map<String, String> tenantTableMap = new LinkedHashMap<>();
        String tablePrefix = mappingPreFix;
        if (StrUtil.isNotBlank(indexArg.getTable())) {
            if (!indexArg.getTable().startsWith(mappingPreFix)) {
                return Result.newError("仅允许修改mapping表");   // ignoreI18n   实施和开发自用
            }
            tablePrefix = indexArg.getTable();
        }
        //所有mapping表
        List<String> allMappings = erpTableDao.setTenantId(indexArg.tenantId).listAllTableLeftMatching(tablePrefix);
        for (String table : allMappings) {
            String tenantId = StrUtil.removePrefix(table, mappingPreFix);
            if (NumberUtil.isInteger(tenantId)) {
                tenantTableMap.put(tenantId, table);
            }
        }

        if (StrUtil.isNotBlank(indexArg.getTenantIdStr())) {
            //筛选企业
            HashSet<String> tenantIds = Sets.newHashSet(Splitter.on(",").split(indexArg.getTenantIdStr()));
            tenantTableMap.keySet().retainAll(tenantIds);
        }
        String indexSqlPrefix;
        if(indexArg.uniqueIndex!=null&&indexArg.uniqueIndex){
            indexSqlPrefix="create unique index concurrently if not exists %s on %s %s";
        }else{
            indexSqlPrefix="create index concurrently if not exists %s on %s %s";
        }
        for (Map.Entry<String, String> entry : tenantTableMap.entrySet()) {
            String tenantId = entry.getKey();
            String table = entry.getValue();
            String sql = String.format(indexSqlPrefix, indexArg.getIndexPrefix() + tenantId, table, indexArg.getIndex());
            try {
                int i = erpTableDao.setTenantId(indexArg.tenantId).superUpdateSql(sql);
                log.info("{},{}", sql, i);
            } catch (Exception e) {
                failedTenants.add(tenantId);
                log.info("failed,{}", sql, e);
                if (!indexArg.isSkipFailed()) {
                    return Result.newError("失败" + tenantId + ":" + e);   // ignoreI18n   实施和开发自用
                }
            }
        }
        String res = "失败企业：" + failedTenants;   // ignoreI18n   实施和开发自用
        return AmisResult.of(res);

    }

    @PostMapping("pg/dropIndexes")
    public DeferredResult<Result<Dict>> dropPgIndexes(@RequestBody IndexArg indexArg) {
        return asyncExecute(() -> dropPgIndexesSync(indexArg), 5, true, "删除索引" + indexArg.getIndexPrefix(),null);   // ignoreI18n   实施和开发自用
    }

    @PostMapping("pg/execute")
    public DeferredResult<Result<Integer>> pgExecute(@RequestBody Dict arg) {
        String sql = arg.getStr("sql");
        String tenantId = arg.getStr("tenantId");
        return asyncExecute(() -> {
            int i = erpTableDao.setTenantId(tenantId).superUpdateSql(sql);
            return Result.newSuccess(i);
        }, 5, true, "执行sql" + sql,null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> dropPgIndexesSync(IndexArg indexArg) {
        if (StrUtil.isBlank(indexArg.getIndexPrefix())) {
            return Result.newError("索引前缀不允许为空");   // ignoreI18n   实施和开发自用
        }
        List<String> failedTenants = new ArrayList<>();
        Map<String, String> tenantTableMap = new LinkedHashMap<>();
        String tablePrefix = mappingPreFix;
        if (StrUtil.isNotBlank(indexArg.getTable())) {
            if (!indexArg.getTable().startsWith(mappingPreFix)) {
                return Result.newError("仅允许修改mapping表");   // ignoreI18n   实施和开发自用
            }
            tablePrefix = indexArg.getTable();
        }
        //所有mapping表
        List<String> allMappings = erpTableDao.setTenantId(indexArg.tenantId).listAllTableLeftMatching(tablePrefix);
        for (String table : allMappings) {
            String tenantId = StrUtil.removePrefix(table, mappingPreFix);
            if (NumberUtil.isInteger(tenantId)) {
                tenantTableMap.put(tenantId, table);
            }
        }

        if (StrUtil.isNotBlank(indexArg.getTenantIdStr())) {
            //筛选企业
            HashSet<String> tenantIds = Sets.newHashSet(Splitter.on(",").split(indexArg.getTenantIdStr()));
            tenantTableMap.keySet().retainAll(tenantIds);
        }
        for (Map.Entry<String, String> entry : tenantTableMap.entrySet()) {
            String tenantId = entry.getKey();
            String sql = String.format("drop index if exists %s", indexArg.getIndexPrefix() + tenantId);
            try {
                int i = erpTableDao.setTenantId(indexArg.tenantId).superUpdateSql(sql);
                log.info("{},{}", sql, i);
            } catch (Exception e) {
                failedTenants.add(tenantId);
                log.info("failed,{}", sql, e);
                if (!indexArg.isSkipFailed()) {
                    return Result.newError("失败" + tenantId + ":" + e);   // ignoreI18n   实施和开发自用
                }
            }
        }
        String res = "失败企业：" + failedTenants;   // ignoreI18n   实施和开发自用
        return AmisResult.of(res);

    }

    /**
     * @param indexArg db.erp_temp_84307.createIndex({'tenant_id':1,'dc_id':1,"obj_api_name":1,"task_num":1,"_id":-1},{"background":true,"name":"idx_task"});
     * @return
     */
    @PostMapping("mongo/createIndexes")
    public DeferredResult<Result<Dict>> createMongoIndex(@RequestBody CreateMongoIndexesArg indexArg) {
        return asyncExecute(() -> createMongoIndexesSync(indexArg), 5, true, "创建mongo索引(或删除)" + indexArg.getIndexName(),null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> createMongoIndexesSync(CreateMongoIndexesArg indexArg) {
        MongoDatabase db;
        Bson index;
        String indexName = indexArg.getIndexName();
        List<Bson> indexes = new ArrayList<>();
        LinkedHashMap<String, Integer> indexMap = JacksonUtil.fromJson(indexArg.getIndex(), new TypeReference<LinkedHashMap<String, Integer>>() {
        });
        Bson partialFilterExpression = null;
        if (StringUtils.isNotBlank(indexArg.getPartialFilterExpression())){
            LinkedHashMap<String, Object> filterMap = JacksonUtil.fromJson(indexArg.getPartialFilterExpression(), new TypeReference<LinkedHashMap<String, Object>>() {
            });
            List<Bson> filters = new ArrayList<>();
            filterMap.forEach((k,v)->{
                filters.add(Filters.eq(k,v));
            });
            if (!filters.isEmpty()){
                partialFilterExpression = Filters.and(filters);
            }
        }

        indexMap.forEach((field, v) -> {
            if (v == 1) {
                indexes.add(Indexes.ascending(field));
            } else if (v == -1) {
                indexes.add(Indexes.descending(field));
            } else {
                throw new ErpSyncDataException("indexes value only can be 1 or -1",null,null);
            }
        });
        if (indexes.size() == 0) {
            throw new ErpSyncDataException("indexes can not be empty",null,null);
        } else if (indexes.size() == 1) {
            index = indexes.get(0);
        } else {
            index = Indexes.compoundIndex(indexes);
        }
        if (indexArg.getType().equals("log")) {
            db = logStore.getMongo().getDatabase(logDbName);
        } else if (indexArg.getType().equals("temp")) {
            db = tempStore.getMongo().getDatabase(tempDbName);
        } else {
            return Result.newError("type can only be log or temp");
        }
        List<String> tenantIds;
        if (StringUtils.isNotBlank(indexArg.getTenantIdStr())) {
            tenantIds = Splitter.on(",").splitToList(indexArg.getTenantIdStr());
        } else {
            //处理 所有企业
            tenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0")).listTenantId();
        }
        Set<String> collectionNames = new HashSet<>();
        Set<String> collectionNameLimitSet = tenantIds.stream().map(v -> indexArg.getCollection() + "_" + v).collect(Collectors.toSet());
        MongoIterable<String> colNameIter = db.listCollectionNames();
        for (String colName : colNameIter) {
            if (collectionNameLimitSet.contains(colName)) {
                collectionNames.add(colName);
            }
        }
        List<String> successCols = new ArrayList<>();
        List<String> failMsg = new ArrayList<>();
        for (String collectionName : collectionNames) {
            //逐个集合处理
            try {
                MongoCollection<Document> collection = db.getCollection(collectionName);
                if (indexArg.isDropIndexes()) {
                    //删除索引
                    collection.dropIndex(index);
                } else {
                    IndexOptions indexOptions = new IndexOptions().name(indexName).partialFilterExpression(partialFilterExpression).background(true);
                    String index1 = collection.createIndex(index, indexOptions);
                    log.info("create index:{},{}",collectionName,index1);
                }
                successCols.add(collectionName);
            } catch (Exception e) {
                failMsg.add(collectionName + " " + e.getMessage());
                log.info("create mongo index exception,col:{}", collectionName, e);
                if (indexArg.isSkipFailed()) {
                    //跳过异常
                    continue;
                }
                break;
            }
        }
        String msg = String.format("成功：%s,失败：%s", successCols, failMsg);   // ignoreI18n   实施和开发自用
        AmisResult result = AmisResult.of(msg);
        result.setErrMsg(msg);
        return result;
    }


    /**
     *
     */
    @Data
    public static class CreateMongoIndexesArg {
        /**
         * 集合前缀
         */
        private String collection;

        /**
         * store,log或者temp
         */
        private String type;

        /**
         * 索引前缀
         */
        private String indexName;

        /**
         * 范围，只支持eq，如：{"operation_type": null}
         */
        private String partialFilterExpression;

        /**
         * 索引
         */
        private String index;

        /**
         * 限定企业,逗号分隔
         */
        private String tenantIdStr;

        /**
         * 跳过失败的
         */
        private boolean skipFailed;

        /**
         * 删除索引
         */
        private boolean dropIndexes = false;
    }


    /**
     *
     */
    @Data
    public static class IndexArg {
        private String tenantId;
        /**
         * 表名前缀
         */
        private String table;

        /**
         * 索引前缀
         */
        private String indexPrefix;

        /**
         * 索引
         */
        private String index;

        /**
         * 限定企业
         */
        private String tenantIdStr;

        /**
         * 跳过失败的
         */
        private boolean skipFailed;
        /**
         * 是否唯一索引，默认是false
         */
        private Boolean uniqueIndex=false;
    }

}
