package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.arg.DeleteSyncDataAndSyncDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.admin.model.UpdateMapping2Success;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date: 10:03 2021/7/5
 * @Desc:
 */
@Slf4j
@Api(tags = "数据映射相关管理接口")
@RestController("syncDataMappingAdminController")
@RequestMapping("/erp/syncdata/superadmin/syncDataMapping")
public class SyncDataMappingAdminController extends SuperAdminBaseController {
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private AdminSyncDataMappingManager adminSyncDataMappingManager;

    @ApiOperation(value = "通过策略明细id删除syncData和syncDataMapping")
    @RequestMapping(value = "/deleteByPloyDetailId", method = RequestMethod.GET)
    public Result<Void> deleteByPloyDetailId(@RequestParam String tenantId,@RequestParam String ployDetailId) {
        if(StringUtils.isBlank(tenantId)||StringUtils.isBlank(ployDetailId)){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        DeleteSyncDataAndSyncDataMappingArg arg=new DeleteSyncDataAndSyncDataMappingArg();
        arg.setPloyDetailId(ployDetailId);
        arg.setTenantId(tenantId);
        log.info("user={} deleteByPloyDetailId arg={}",getLoginUserId(),arg);
        return adminSyncDataMappingService.deleteSyncDataAndSyncDataMapping(arg);
    }

    @ApiOperation(value = "删除syncData和syncDataMapping")
    @RequestMapping(value = "/deleteSyncDataAndSyncDataMapping", method = RequestMethod.POST)
    public Result<Void> deleteSyncDataAndSyncDataMapping(@RequestBody DeleteSyncDataAndSyncDataMappingArg arg) {
        if(StringUtils.isBlank(arg.getDestObjApiName())&&StringUtils.isBlank(arg.getSourceObjApiName())
                &&StringUtils.isBlank(arg.getPloyDetailId())&&!arg.getDeleteAll()){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        log.info("user={} deleteByPloyDetailId arg={}",getLoginUserId(),arg);
        return adminSyncDataMappingService.deleteSyncDataAndSyncDataMapping(arg);
    }

    @ApiOperation(value = "更新中间表为成功状态")
    @RequestMapping(value = "/updateStatus2Success", method = RequestMethod.POST)
    public Result<UpdateMapping2Success.Record> updateStatus2Success(@RequestBody UpdateMapping2Success.Arg arg) {
        UpdateMapping2Success.Record record = adminSyncDataMappingManager.updateMapping2Success(arg.getTenantId(), arg.getSourceObjApiName(), arg.getDestObjApiName(), arg.isOnlyCreated(), arg.getLimit());
        SendMsgHelper sendMsgHelper = initSendMsgHelper(arg.getTenantId(), "updateStatus2Success");
        sendMsgHelper.append("arg:").append(arg).append("\n");
        sendMsgHelper.sendMsg();
        return Result.newSuccess(record);
    }
}
