package com.fxiaoke.open.erpsyncdata.web.aop;

import com.fxiaoke.open.erpsyncdata.admin.remote.LicenseService;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AbstractReplaceEnterpriseAspect;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.web.annontation.CheckProduct;
import com.fxiaoke.open.erpsyncdata.web.interceptor.UserContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/21 14:42:15
 */
@Component
@Aspect
public class ControllerCheckProductAspect extends AbstractReplaceEnterpriseAspect {
    @Autowired
    private LicenseService licenseService;

    /**
     * 校验是否有购买产品
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp..*.*(..)) ")
    public Object verifyProduct(ProceedingJoinPoint jp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final Class<?> declaringType = methodSignature.getDeclaringType();
        final CheckProduct annotation = AnnotationUtils.findAnnotation(declaringType, CheckProduct.class);
        if (Objects.isNull(annotation)) {
            return jp.proceed();
        }

        final String tenantId = UserContextHolder.getUserVo().getTenantId();
        if (licenseService.hasProduct(tenantId, annotation.value())) {
            return jp.proceed();
        }

        final Method method = methodSignature.getMethod();
        return errorResult(tenantId, method.getReturnType(), ResultCodeEnum.UNPURCHASED_PRODUCT);
    }
}
