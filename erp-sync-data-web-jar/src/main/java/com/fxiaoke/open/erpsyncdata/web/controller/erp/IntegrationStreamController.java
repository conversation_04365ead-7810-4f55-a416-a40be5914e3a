package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperationTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SyncDataMappingFieldKey;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ListObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmObjectName;
import com.fxiaoke.open.erpsyncdata.preprocess.result.GetCrmObjectApiNameByErpObjApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.result.IntegrationSimpleViewInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SmsNotificationService;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.fxiaoke.otherrestapi.marketingsms.arg.CheckSmsStatusArg;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 19:11 2022/1/15
 * @Desc:
 */
@Slf4j
@Api(tags = "集成流相关接口")
@RestController("integrationStreamController")
@RequestMapping("cep/integrationStream")
@ManagedTenantIntercept
public class IntegrationStreamController extends AsyncSupportController {
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private SmsNotificationService smsNotificationService;

    @ApiOperation(value = "创建集成流对象映射")
    @RequestMapping("/createObjectMapping")
    public Result<String> createObjectMapping(@RequestBody CreateObjectMappingArg arg,
                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Integer loginUserId = getLoginUserId();
        Result<String> result = integrationStreamService.createObjectMapping(tenantId, arg,lang);
        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId,dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
          result.getData(),loginUserId,UserOperationTypeEnum.CREATE.name(),i18NStringManager.get(I18NStringEnum.s752,lang,getLoginUserTenantId())+arg.getIntegrationStreamName(),null));
        return result;
    }

    @ApiOperation(value = "更新集成流")
    @RequestMapping("/updateIntegrationStream")
    public Result<UpdateIntegrationStreamResult> updateIntegrationStream(@RequestBody UpdateIntegrationStreamArg arg,
                                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {

        String tenantId = getLoginUserTenantId();
        IdArg idArg=new IdArg();
        idArg.setId(arg.getId());
        Result<QueryIntegrationDetailResult> integrationStreamDetail = integrationStreamService.getIntegrationStreamDetail(tenantId,idArg,lang);
        String snapshotData = JacksonUtil.toJson(integrationStreamDetail.getData());

        String dcId = getDcId();
        Integer loginUserId = getLoginUserId();
        Result<UpdateIntegrationStreamResult> result =
          integrationStreamService.allUpdateIntegrationStream(tenantId, arg,lang);

        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId,dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
          arg.getId(),loginUserId,UserOperationTypeEnum.EDIT.name(),i18NStringManager.get(I18NStringEnum.s753,lang,tenantId),snapshotData));

        return result;
    }

//    @ApiOperation(value = "获取集成流列表")
//    @RequestMapping("/queryIntegrationStreamList")
//    public Result<QueryResult<List<IntegrationStreamResult>>> queryIntegrationStreamList(@RequestBody ListIntegrationStreamArg arg, HttpServletResponse response) {
//        log.info("integration stream arg:{}",arg);
//        String tenantId = getLoginUserTenantId();
//        if(ObjectUtils.isEmpty(arg.getDcId())&&!arg.getFilterAll()){
//            String dcId = getDcId();
//            arg.setDcId(dcId);
//        }
//        if(ObjectUtils.isNotEmpty(arg.getDcId())){
//            //设置cookie
//            String cookieValue = DataCenterCookieUtils.buildDataCenterCookie(tenantId, arg.getDcId());
//            Cookie cookie = new Cookie("syncDC", cookieValue);
//            cookie.setMaxAge((int) TimeUnit.DAYS.toSeconds(31));
//            cookie.setPath("/erp/");
//            response.addCookie(cookie);
//        }
//        log.info("integration stream arg finish:{}",arg);
//        return integrationStreamService.queryIntegrationStreamList(tenantId, arg,false);
//    }

    @ApiOperation(value = "数据维护列表页")
    @RequestMapping("/queryIntegrationDataViewLists")
    public Result<QueryResult<List<IntegrationStreamResult>>> queryIntegrationDataViewLists(@RequestBody ListIntegrationStreamArg arg,
                                                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        if(ObjectUtils.isEmpty(arg.getDcId())&&!arg.getFilterAll()){
            String dcId = getDcId();
            arg.setDcId(dcId);
        }
        return integrationStreamService.queryIntegrationStreamList(tenantId, arg,true,lang);
    }


    @ApiOperation(value = "获取集成流详情")
    @RequestMapping("/getIntegrationStreamDetail")
    public Result<QueryIntegrationDetailResult> getIntegrationStreamDetail(@RequestBody IdArg arg,
                                                                           @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        //查询集成流详情
        Result<QueryIntegrationDetailResult> integrationStreamDetail = integrationStreamService.getIntegrationStreamDetail(tenantId, arg,lang);
        //校验集成流错误信息
        return integrationStreamDetail;
    }



    @ApiOperation(value = "获取集成流crm对象")
    @RequestMapping("/getIntegrationStreamCrmObjList")
    public Result<List<SyncObjectResult>> getIntegrationStreamCrmObjList(@RequestBody CepArg cepArg,
                                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return integrationStreamService.getIntegrationStreamCrmObjList(tenantId,lang);
    }


    @ApiOperation(value = "获取集成流erp对象")
    @RequestMapping("/getIntegrationStreamErpObjList")
    public Result<List<SyncObjectResult>> getIntegrationStreamErpObjList(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        final String dcId = getDcId();
        return integrationStreamService.getIntegrationStreamErpObjList(tenantId, dcId);
    }

    @ApiOperation(value = "更新集成流名字")
    @RequestMapping("/updateIntegrationStreamName")
    public Result<Integer> updateIntegrationStreamName(@RequestBody UpdateIntegrationStreamArg.UpdateStreamNameArg arg,
                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        IdArg idArg=new IdArg();
        idArg.setId(arg.getId());
        Result<QueryIntegrationDetailResult> integrationStreamDetail = integrationStreamService.getIntegrationStreamDetail(tenantId,idArg,lang);
        String dcId = getDcId();
        Integer loginUserId = getLoginUserId();
        Result<Integer> result = integrationStreamService.updateIntegrationStreamName(tenantId, arg);
        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId,dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
          arg.getId(),loginUserId,UserOperationTypeEnum.EDIT.name(),i18NStringManager.get2(I18NStringEnum.s771.getI18nKey(),
                        lang,
                        tenantId,
                        String.format(I18NStringEnum.s771.getI18nValue(), integrationStreamDetail.getData().getIntegrationStreamName(),arg.getIntegrationStreamName()),
                        Lists.newArrayList(integrationStreamDetail.getData().getIntegrationStreamName(),arg.getIntegrationStreamName())),null));
        return result;
    }

    @ApiOperation(value = "新版数据集成流页面")
    @RequestMapping(value = {"/newQueryIntegrationStreamList"})
    public Result<QueryResult<List<IntegrationViewResult>>> newQueryIntegrationStreamList(@RequestBody ListIntegrationStreamArg arg,
                                                                                          @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        if(ObjectUtils.isEmpty(arg.getDcId())&&!arg.getFilterAll()){
            String dcId = getDcId();
            arg.setDcId(dcId);
        }
        Result<QueryResult<List<IntegrationViewResult>>> queryResultResult = integrationStreamService.newQueryIntegrationStreamList(tenantId, arg, true,lang);
        return queryResultResult;
    }

    @ApiOperation(value = "获取集成流状态与最后更新时间")
    @RequestMapping(value = {"/singleQueryIntegrationStreamList"})
    public Result<QueryResult<List<IntegrationViewResult>>> singleQueryIntegrationStreamList(@RequestBody IdArg arg,
                                                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();

        Result<QueryResult<List<IntegrationViewResult>>> queryResultResult = integrationStreamService.singleQueryIntegrationStreamList(tenantId, arg,lang);
        return queryResultResult;
    }

    @ApiOperation(value = "获取集成流状态与最后更新时间")
    @RequestMapping("/queryStreamStatusAndFailCount")
    public DeferredResult<Result<List<IntegrationViewResult.InvalidInfoResult>>> queryStreamStatusAndFailCount(@RequestBody CepListArg<String> arg,
                                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        List<String> streamIds = arg.getCepListArg();
        String tenantId = getLoginUserTenantId();
        String actionName = i18NStringManager.get(I18NStringEnum.s5100,lang,tenantId);
        return asyncExecute(() ->integrationStreamService.queryStreamStatusAndLastSyncTime(tenantId, streamIds,lang),10,false,actionName,lang);
    }

    @ApiOperation(value = "获取集成流失败的数量，超时的获取缓存的结果")
    @RequestMapping("/querySyncFailCount")
    public DeferredResult<Result<List<IntegrationViewResult.SyncFailResult>>> querySyncFailCount(@RequestBody CepListArg<String> arg,
                                                                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        log.info("IntegrationStreamController.querySyncFailCount,lang={}",lang);
        List<String> streamIds = arg.getCepListArg();
        String tenantId = getLoginUserTenantId();
        Result<List<IntegrationViewResult.SyncFailResult>> timeoutResult = integrationStreamService.querySyncFailCountByCache(tenantId,streamIds);
        String actionName = i18NStringManager.get(I18NStringEnum.s63,lang,tenantId);
        return asyncExecute(() ->integrationStreamService.querySyncFailCount(tenantId, streamIds),10,false,actionName,timeoutResult,lang);
    }

    @ApiOperation(value = "强制刷新获取失败数量")
    @RequestMapping("/refreshFailCount")
    public DeferredResult<Result<List<IntegrationViewResult.SyncFailResult>>> refreshFailCount(@RequestBody CepListArg<String> arg,
                                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        List<String> streamIds = arg.getCepListArg();
        String tenantId = getLoginUserTenantId();
        Result<List<IntegrationViewResult.SyncFailResult>> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = i18NStringManager.get(I18NStringEnum.s63,lang,tenantId);
        return asyncExecute(() ->integrationStreamService.querySyncFailCount(tenantId, streamIds),10,false,actionName,timeoutResult,lang);
    }

    @ApiOperation(value = "获取反写crm源对象字段apiName")
    @RequestMapping("/getReverseWriteObjFieldApiNames")
    public Result<ListObjectFieldsResult> getReverseWriteObjFieldApiNames(@RequestBody ListObjectFieldsArg arg,
                                                                          @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Integer userId = getLoginUserId();
        return integrationStreamService.getReverseWriteObjFieldApiNames(tenantId,dcId,userId,arg.getObjectApiName(),lang);
    }
    @ApiOperation(value = "根据对象apiName返回关联的集成流")
    @RequestMapping("/queryPloyDetailByApiName")
    public Result<List<IntegrationSimpleViewInfoResult>> queryPloyDetailByApiName(@RequestBody ListObjectFieldsArg arg,
                                                                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        List<SyncPloyDetailEntity> allPloyDetailBySplitName = syncPloyDetailManager.getAllPloyDetailBySplitName(tenantId, arg.getObjectApiName());
        List<IntegrationSimpleViewInfoResult> integrationSimpleViewInfoResults=Lists.newArrayList();
        allPloyDetailBySplitName.stream().forEach(item ->{
            IntegrationSimpleViewInfoResult integrationSimpleViewInfoResult=new IntegrationSimpleViewInfoResult();
            integrationSimpleViewInfoResult.setIntegrationStreamName(item.getIntegrationStreamName());
            integrationSimpleViewInfoResult.setId(item.getId());
            integrationSimpleViewInfoResults.add(integrationSimpleViewInfoResult);
        });
        return Result.newSuccess(integrationSimpleViewInfoResults);
    }



    @ApiOperation(value = "集成流操作日志")
    @RequestMapping("/queryStreamOpeartorLog")
    public Result<List<UserOperatorLogResult>> queryStreamOpeartorLog(@RequestBody UserOperatorArg arg,
                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Result<List<UserOperatorLogResult>> result = integrationStreamService.queryStreamOpeartorLog(tenantId,dcId,arg.getModuleId(),(arg.getPageNum() - 1) * arg.getPageSize(),arg.getPageSize(),lang);
        return result;
    }
    @ApiOperation(value = "修改GetById接口状态")
    @RequestMapping("/updateGetByIdStatus")
    public Result<Boolean> updateGetByIdStatus(@RequestBody GetByIdApiStatusArg arg) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Result<Boolean> result = integrationStreamService.updateGetByIdStatus(tenantId,dcId,arg);
        return result;
    }
    @ApiOperation(value = "查询GetById接口状态")
    @RequestMapping("/queryGetByIdStatus")
    public Result<Boolean> queryGetByIdStatus(@RequestBody GetByIdApiStatusArg arg) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Result<Boolean> result = integrationStreamService.queryGetByIdStatus(tenantId,dcId,arg);
        return result;
    }

    @ApiOperation(value = "修改数据为同步中，功能测试")
    @RequestMapping("/updateSyncDataStatus")
    public Result<Boolean> updateSyncDataStatus(@RequestBody IdArg arg,
                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Result<Boolean> result = integrationStreamService.updateSyncDataStatus(tenantId,dcId,arg,lang);
        return result;
    }

    @ApiOperation(value = "修改数据为同步中，功能测试")
    @RequestMapping("/updateMappingStatus")
    public Result<Boolean> updateMappingStatus(@RequestBody IdArg arg) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Result<Boolean> result = integrationStreamService.updateMappingStatus(tenantId,dcId,arg);
        return result;
    }


    @ApiOperation(value = "检查和更新集成流明细对象映射关系以及附加的关系")
    @RequestMapping("/checkAndUpdateDetailObjMapping")
    public Result<Void> checkAndUpdateDetailObjMapping(@RequestBody UpdateIntegrationStreamArg.CheckAndUpdateDetailObjMappingArg arg,
                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        Integer loginUserId = getLoginUserId();
        if(StringUtils.isEmpty(arg.getTenantId())) {
            arg.setTenantId(tenantId);
        }
        if(StringUtils.isEmpty(arg.getDcId())) {
            arg.setDcId(dcId);
        }
        Result<Void> result = integrationStreamService.checkAndUpdateDetailObjMapping(arg,lang);

        return result;
    }

    @ApiOperation(value = "获取中间表字段apiName")
    @RequestMapping("/getSyncDataMappingFieldApiNames")
    public Result<ListObjectFieldsResult> getSyncDataMappingFieldApiNames(@RequestBody ListObjectFieldsArg arg,
                                                                          @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        //目前只有固定的这几个
        ListObjectFieldsResult result=new ListObjectFieldsResult();
        result.setObjectApiName(arg.getObjectApiName());
        result.setFields(Lists.newArrayList());

        ObjectFieldResult sourceDataId=new ObjectFieldResult();
        sourceDataId.setApiName(SyncDataMappingFieldKey.sourceDataId);
        sourceDataId.setLabel(i18NStringManager.get(I18NStringEnum.s69,lang,getLoginUserTenantId()));
        sourceDataId.setDefineType(ErpFieldTypeEnum.text.name());
        sourceDataId.setType(ErpFieldTypeEnum.text.name());
        result.getFields().add(sourceDataId);

        ObjectFieldResult sourceDataName=new ObjectFieldResult();
        sourceDataName.setApiName(SyncDataMappingFieldKey.sourceDataName);
        sourceDataName.setLabel(i18NStringManager.get(I18NStringEnum.s70,lang,getLoginUserTenantId()));
        sourceDataName.setDefineType(ErpFieldTypeEnum.text.name());
        sourceDataName.setType(ErpFieldTypeEnum.text.name());
        result.getFields().add(sourceDataName);

        ObjectFieldResult destDataId=new ObjectFieldResult();
        destDataId.setApiName(SyncDataMappingFieldKey.destDataId);
        destDataId.setLabel(i18NStringManager.get(I18NStringEnum.s71,lang,getLoginUserTenantId()));
        destDataId.setDefineType(ErpFieldTypeEnum.text.name());
        destDataId.setType(ErpFieldTypeEnum.text.name());
        result.getFields().add(destDataId);

        ObjectFieldResult destDataName=new ObjectFieldResult();
        destDataName.setApiName(SyncDataMappingFieldKey.destDataName);
        destDataName.setLabel(i18NStringManager.get(I18NStringEnum.s72,lang,getLoginUserTenantId()));
        destDataName.setDefineType(ErpFieldTypeEnum.text.name());
        destDataName.setType(ErpFieldTypeEnum.text.name());
        result.getFields().add(destDataName);


        ObjectFieldResult masterDataId=new ObjectFieldResult();
        masterDataId.setApiName(SyncDataMappingFieldKey.masterDataId);
        masterDataId.setLabel(i18NStringManager.get(I18NStringEnum.s73,lang,getLoginUserTenantId()));
        masterDataId.setDefineType(ErpFieldTypeEnum.text.name());
        masterDataId.setType(ErpFieldTypeEnum.text.name());
        result.getFields().add(masterDataId);

        return Result.newSuccess(result);
    }

    @ApiOperation(value = "根据erpObjApiName获取crmObjApiName")
    @PostMapping("/getCrmObjectApiNameByErpObjApiName")
    public Result<GetCrmObjectApiNameByErpObjApiName.Result> getCrmObjectApiNameByErpObjApiName(@RequestBody GetCrmObjectApiNameByErpObjApiName.Arg arg) {
        final String tenantId = getLoginUserTenantId();
        List<SyncPloyDetailEntity> allPloyDetailBySplitName = syncPloyDetailManager.getAllPloyDetailBySplitName(tenantId, arg.getObjectApiName());
        final List<String> crmApiNames = allPloyDetailBySplitName.stream()
                .map(SyncPloyDetailEntity::getDestObjectApiName)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, String> crmObjectApiNameDisplayMap = crmRemoteManager.listObjectNamesByApiNames(tenantId, crmApiNames);
        final List<CrmObjectName> collect = crmObjectApiNameDisplayMap.entrySet().stream()
                .map(entry -> new CrmObjectName(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        return Result.newSuccess(new GetCrmObjectApiNameByErpObjApiName.Result(collect));
    }
    @ApiOperation(value = "检查企业短信服务sms状态")
    @PostMapping("/checkSmsStatus")
    public Result<Boolean> checkSmsStatus() {
        final String tenantId = getLoginUserTenantId();
        CheckSmsStatusArg arg=new CheckSmsStatusArg();
        arg.setTenantId(Integer.valueOf(tenantId));
        return smsNotificationService.checkSmsStatus(tenantId,arg);
    }
}