package com.fxiaoke.open.erpsyncdata.web.controller.customFunction;


import com.fxiaoke.open.erpsyncdata.admin.arg.CreateErpFieldMappingArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.OAConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date: 15:20 2020/9/22
 * @Desc:
 *
 * 人员，部门，国家省市区等 erp字段映射中间表，开放给自定义函数访问。
 */


@Slf4j
@Api(tags = "erp系统字段表相关接口")
@RestController("ErpFieldMappingController")
@RequestMapping("inner/erp/syncdata/customfunction/ErpFieldMapping")
public class ErpFieldMappingController {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private IdGenerator idGenerator;

    @ApiOperation(value = "新建一条映射")
    @RequestMapping(value = "/create",method = RequestMethod.POST)
    @ControllerMethodProxy
    public Result<Boolean> create(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") String tenantId, @RequestBody CreateErpFieldMappingArg arg) {
        if (arg == null || tenantId == null || arg.getDataCenterId() == null||arg.getChannel()==null||arg.getDataType()==null
        ||arg.getFsDataId()==null||arg.getErpDataId()==null) {
            log.info("tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s59, null);
        }
        if(ErpChannelEnum.OA.equals(arg.getChannel())){
            OAConnectInfoEntity oaConnectInfoEntity=oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
            if(!arg.getDataCenterId().equals(oaConnectInfoEntity.getId())){
                return Result.newSystemError(I18NStringEnum.s60);
            }
            if(!ErpFieldTypeEnum.employee_oa.equals(arg.getDataType())){//oa渠道只有employee_oa
                return Result.newSystemError(I18NStringEnum.s61);
            }
        }else{
            ErpConnectInfoEntity erpConnectInfoEntity=erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId,arg.getDataCenterId());
            if(erpConnectInfoEntity==null){
                return Result.newSystemError(I18NStringEnum.s60);
            }
        }
        try {
            List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch(tenantId, arg.getDataCenterId(), arg.getDataType(), arg.getFsDataId(), arg.getErpDataId());
            if(CollectionUtils.isNotEmpty(erpFieldDataMappingEntities)){
                return Result.newSystemError(I18NStringEnum.s62);
            }
            ErpFieldDataMappingEntity entity = new ErpFieldDataMappingEntity();
            BeanUtils.copyProperties(arg, entity);
            entity.setTenantId(tenantId);
            entity.setId(idGenerator.get());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(System.currentTimeMillis());

            if(arg.getDataType()== ErpFieldTypeEnum.employee||arg.getDataType()== ErpFieldTypeEnum.employee_oa) {
                //员工类型数据，自动填充扩展字段
                Map map = Maps.newHashMap();
                map.put("id", entity.getId());
                map.put("channel", entity.getChannel());
                map.put("fsEmployeePhone", "");
                map.put("fsEmployeeStatus", 1);
                map.put("fsEmployeeId", entity.getFsDataId());
                map.put("fsEmployeeName", entity.getFsDataName());
                map.put("erpEmployeeId", entity.getErpDataId());
                map.put("erpEmployeeName", entity.getErpDataName());
                map.put("erpEmployeePhone", "");
                entity.setFieldDataExtendValue(GsonUtil.toJson(map));
            }

            int ret = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(entity);
            return Result.newSuccess(1 == ret);
        }catch (Exception e) {
            log.error("tenantid:{} createFieldMapping get exception, ", tenantId, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }
}