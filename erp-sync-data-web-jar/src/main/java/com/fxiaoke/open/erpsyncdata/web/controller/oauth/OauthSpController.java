package com.fxiaoke.open.erpsyncdata.web.controller.oauth;

import com.facishare.open.erp.connertor.sdk.model.GetUserToken;
import com.facishare.open.erp.connertor.service.OauthLoginService;
import com.fxiaoke.open.erpsyncdata.admin.model.GetOauthLoginDialogUrl;
import com.fxiaoke.open.erpsyncdata.admin.model.OauthCallback;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.Oauth2ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/20 17:54:42
 */
@Slf4j
@Api(tags = "sso相关接口")
@RestController()
@RequestMapping("cep/oauth/sp")
public class OauthSpController extends BaseController {

    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Autowired
    private OauthLoginService oauthLoginService;

    /**
     * @param channel 渠道,暂时只支持facebook,linkedin
     */
    @ApiOperation(value = "获取登录跳转链接")
    @PostMapping(value = "/{channel}/loginUrl")
    public Result<GetOauthLoginDialogUrl.Result> getOauthLoginDialogUrl(@PathVariable String channel, @RequestBody(required = false) GetOauthLoginDialogUrl.Arg arg) {
        String config = Objects.isNull(arg) || Objects.isNull(arg.getType()) || Objects.equals(arg.getType(), 1) ?
                channel :
                channel + "_" + arg.getType();
        final String oauthLoginDialogUrl = ConfigCenter.getOauthLoginDialogUrl(config);
        if (StringUtils.isBlank(oauthLoginDialogUrl)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s95, channel);
        }
        return Result.newSuccess(new GetOauthLoginDialogUrl.Result(oauthLoginDialogUrl));
    }

    /**
     * @param channel 渠道,暂时只支持facebook,linkedin
     */
    @ApiOperation(value = "跳转回来获取用户口令")
    @PostMapping(value = "/{channel}/callback")
    public Result<Oauth2ConnectParam> oauthCallback(@PathVariable String channel, @RequestBody OauthCallback.Arg arg) {
        if (StringUtils.isBlank(arg.getCode())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String config = Objects.isNull(arg.getType()) || Objects.equals(arg.getType(), 1) ?
                channel :
                channel + "_" + arg.getType();

        final GetUserToken.Arg tokenArg = new GetUserToken.Arg();
        tokenArg.setAppId(ConfigCenter.getOauthAppId(config));
        tokenArg.setAppSecret(ConfigCenter.getOauthAppSecret(config));
        tokenArg.setRedirectUri(ConfigCenter.getOauthRedirectUri(config));
        tokenArg.setVerificationCode(arg.getCode());

        final GetUserToken.Result userToken = oauthLoginService.getUserToken(channel, tokenArg);

        Oauth2ConnectParam param = getOauth2ConnectParam(channel, arg, userToken);

        return Result.newSuccess(param);
    }

    private Oauth2ConnectParam getOauth2ConnectParam(final String channel, final OauthCallback.Arg arg, final GetUserToken.Result userToken) {
        Integer type = null;
        if (Objects.equals(channel, "facebook")) {
            type = Objects.isNull(arg.getType()) ? 1 : arg.getType();
        }
        return new Oauth2ConnectParam(userToken.getName(), userToken.getConnectParam(), userToken.getExpireTime(), null, type);
    }
}
