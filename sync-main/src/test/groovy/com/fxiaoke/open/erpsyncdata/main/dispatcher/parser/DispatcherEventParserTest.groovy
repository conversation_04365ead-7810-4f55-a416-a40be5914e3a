package com.fxiaoke.open.erpsyncdata.main.dispatcher.parser

import com.alibaba.fastjson.JSONObject
import com.alicp.jetcache.support.Kryo5ValueDecoder
import com.alicp.jetcache.support.Kryo5ValueEncoder
import com.esotericsoftware.kryo.kryo5.Kryo
import com.fasterxml.jackson.core.type.TypeReference
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmDataManager
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmObjAndFieldManager
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjDispatchPriorityConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpTenantConfiguration
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.TenantConfigurationService
import org.apache.rocketmq.common.message.MessageExt
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date: 11:34 2023/2/24
 * @Desc:
 */
class DispatcherEventParserTest extends Specification {

    def tenantId = "123456"

    static {
        System.setProperty("config.mode", "localNoUpdate")
    }

    private PlusTenantConfigManager plusTenantConfigManager = Mock()
    private CrmObjAndFieldManager crmObjAndFieldManager = Mock()


    def "test parse"() {
        given:
        def tenantConfigurationService = Mock(TenantConfigurationService) {
            queryConfig(*_) >> {
                def m = ["test_api_name": 500]
                def m_s = JacksonUtil.toJson(m)
                ErpTenantConfiguration conf = new ErpTenantConfiguration(configuration: m_s)
                Result.newSuccess(conf)
            }
        }
        def crmObjAndFieldManager = Mock(CrmObjAndFieldManager) {
            getCrmObjMasterObjApiName(*_) >> "master_obj_api_name"
            objFieldIsCountOrFormulaOrQuoteField(*_) >> true
        }
        def tenantConfigurationManager = Mock(TenantConfigurationManager) {
            inWhiteList(*_) >> true
            getMergeMasterAndDetailTenantObj(*_) >> {
                def res = ["123456": ["master_obj_api_name"]]
            }
            getCrmDetailObjDefaultAggregationTime(*_) >> 2500L
        }
        DispatcherEventParser parser = new DispatcherEventParser(tenantConfigurationService: tenantConfigurationService,
                crmObjAndFieldManager: crmObjAndFieldManager, tenantConfigurationManager: tenantConfigurationManager,
                plusTenantConfigManager: plusTenantConfigManager
        )
        def src_data = JSONObject.newInstance()
        src_data.put("object_describe_api_name", "object_describe_api_name")
        src_data.put("tenant_id", tenantId)
        src_data.put("_id", UUID.randomUUID().toString())
        src_data.put("test", "test")
        def body = JSONObject.newInstance()
        body.put("sourceTenantType", TenantTypeEnum.CRM.getType())
        body.put("sourceEventType", 0)
        body.put("ployDetailSnapshotId", "testId")
        body.put("dataVersion", 1000L)
        body.put("sourceData", src_data)
        body.put("delayDispatcherTime", 500L)
        MessageExt msg = new MessageExt()
        msg.setBody(JacksonUtil.toJson(body).getBytes())
        when:
//        parser.init()
        def res = parser.parse(msg)
        then:
        res != null
        println(res)
    }

    @Unroll
    def "getTempAggregationTime"() {
        Result<ErpTenantConfiguration> queryConfigResult = new Result<>()
        queryConfigResult.setData(new ErpTenantConfiguration())
        queryConfigResult.getData().setConfiguration("{}")
        DispatcherEventParser dispatcherEventParser = new DispatcherEventParser(
                aggregationTime: aggregationTime,
                tenantConfigurationManager: Mock(TenantConfigurationManager) {
                    getCrmDetailObjDefaultAggregationTime(*_) >> CrmDetailObjDefaultAggregationTime
                },
                crmObjAndFieldManager: Mock(CrmObjAndFieldManager) {
                    getCrmObjMasterObjApiName(*_) >> mainObjApiName
                },
                tenantConfigurationService: Mock(TenantConfigurationService) {
                    queryConfig(*_) >> queryConfigResult
                },
        );
        when:
        def time = dispatcherEventParser.getTempAggregationTime(sourceTenantType, "81243", objApiName);
        then:
        time == result

        where:
        sourceTenantType | objApiName | mainObjApiName | CrmDetailObjDefaultAggregationTime | aggregationTime | result
        1                | "obj"      | "obj"          | 10000                              | 500             | 500
        1                | "obj11"    | "obj"          | 10000                              | 500             | 10000
        2                | "obj11"    | "obj"          | 10000                              | 500             | 500
    }

    @Unroll
    def "getPriority"() {
        Result<ErpTenantConfiguration> queryConfigResult = new Result<>()
        queryConfigResult.setData(new ErpTenantConfiguration())
        queryConfigResult.getData().setConfiguration("{}")
        DispatcherEventParser dispatcherEventParser = new DispatcherEventParser(
                tenantConfigurationService: Mock(TenantConfigurationService) {
                    queryConfig(*_) >> queryConfigResult
                },
                plusTenantConfigManager: plusTenantConfigManager
        );
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("object_describe_api_name", "obj");
        jsonObject.put("tenant_id", "81243");

        when:
        def order = dispatcherEventParser.getPriority(sourceTenantType, "81243", objApiName, jsonObject);
        then:
        order == result

        where:
        sourceTenantType | objApiName             | result
        1                | "SalesOrderProductObj" | 1
        1                | "SalesOrderObj"        | 1
        2                | "SalesOrderProductObj" | 1
        2                | "SalesOrderObj"        | 1
        2                | "testObj"              | 50
    }


    @Unroll
    def "test expr get Priority"() {
        Result<ErpTenantConfiguration> queryConfigResult = new Result<>()
        queryConfigResult.setData(new ErpTenantConfiguration())
        queryConfigResult.getData().setConfiguration("{}")
        DispatcherEventParser dispatcherEventParser = new DispatcherEventParser(
                tenantConfigurationService: Mock(TenantConfigurationService) {
                    queryConfig(*_) >> queryConfigResult
                },
                plusTenantConfigManager: plusTenantConfigManager,
                crmObjAndFieldManager: crmObjAndFieldManager,
                crmDataManager: Mock(CrmDataManager){
                    getByIdSelective(*_) >> {
                        def data = new ObjectData()
                        data.put("p",5)
                        return data
                    }
                }
        );
        crmObjAndFieldManager.objFieldIsCountOrFormulaOrQuoteField(_, _, "f1") >> true
        crmObjAndFieldManager.objFieldIsCountOrFormulaOrQuoteField(*_) >> false
        //测试expr
        plusTenantConfigManager.getObjConfig(_, _, TenantConfigurationTypeEnum.OBJ_DISPATCH_PRIORITY_CONFIG_MAP, _) >> {
            if (objApiName == "testExprObj") {
                return ["testExprObj": new ObjDispatchPriorityConfig(
                        priority: 30,
                        priorityExprList: [
                                new ObjDispatchPriorityConfig.PriorityExpr(20, 'p=="1"'),
                                new ObjDispatchPriorityConfig.PriorityExpr(60, 'p=="2"')
                        ],
                        fields: ['p']
                )]
            }
            return null
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("object_describe_api_name", "obj");
        jsonObject.put("tenant_id", "81243");

        when:
        if (addMap != null) {
            jsonObject.putAll(addMap)
        }
        def order = dispatcherEventParser.getPriority(sourceTenantType, "81243", objApiName, jsonObject);
        then:
        order == result

        where:
        sourceTenantType | objApiName      | addMap        | result
        1                | "SalesOrderObj" | [:]           | 1
        1                | "testObj"       | ['f1': 'fff'] | 1001
        1                | "testExprObj"   | [:]           | 30
        1                | "testExprObj"   | ['p': '1']    | 20
        1                | "testExprObj"   | ['p': '2']    | 60
        1                | "testExprObj"   | ['p': '3']    | 30
    }



    def "testSerialize"() {
        def objConfigMap = ["testExprObj": new ObjDispatchPriorityConfig(
                priority: 30,
                priorityExprList: [
                        new ObjDispatchPriorityConfig.PriorityExpr(20, 'p=="1"'),
                        new ObjDispatchPriorityConfig.PriorityExpr(60, 'p=="2"')
                ],
                fields: ['p']
        )]
        def json = JacksonUtil.toJson(objConfigMap)
        print(json)
        def obj = JacksonUtil.fromJson(json, new TypeReference<Map<String, ObjDispatchPriorityConfig>>() {})
        print(obj)

        def encodeVal = Kryo5ValueEncoder.INSTANCE.apply(objConfigMap)
        def decodeVal = Kryo5ValueDecoder.INSTANCE.apply(encodeVal)

        expect:
        true
    }
}
