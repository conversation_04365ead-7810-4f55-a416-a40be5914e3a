package com.fxiaoke.open.erpsyncdata.main.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataManager;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.OpsToolService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldTypeContants;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.main.manager.SyncLockManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HandleDeleteArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpSyncService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.*;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/17
 */
@Service("erpSyncService")
@Slf4j
public class ErpSyncServiceImpl implements ErpSyncService {
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private AdminSyncDataManager adminSyncDataManager;
    @Autowired
    private OpsToolService opsToolService;
    @Autowired
    private AllModelDubboService allModelDubboService;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ErpTenantConfigurationDao tenantConfigurationDao;
    @Autowired
    private SyncDataMappingService syncDataMappingService;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private SyncLockManager syncLockManager;

    /**
     * @param tenantId
     * @param endUpdateTime
     * @return
     */
    @Override
    public Result<Void> syncDataTimeOutUpdateStatusToError(String tenantId, Long startUpdateTime, Long endUpdateTime) {

        //获取需要被扫描的集成流的对象apiName
        List<Pair<String, String>> nameList = syncPloyDetailManager.queryEnableIntegrationStream(tenantId);
        for (Pair<String, String> sourceDestApiName : nameList) {
            List<Integer> idDoingStatus = Lists.newArrayList(SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.WAITTING.getStatus());
            int limit = 1000;
            for (int i = 0; i < 10; i++) {
                //一次最多1000*10条数据
                if (i == 5) {
                    log.warn("code has error ,or too many waiting data");
                }
                //里面如果更新不当有可能死循环，注意
                List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(tenantId).listByStatusTimeOutMappings(tenantId, startUpdateTime, endUpdateTime, sourceDestApiName.getLeft(), sourceDestApiName.getRight(), limit, 0);
                 if (CollectionUtils.isEmpty(syncDataMappingsEntities)) {
                    break;
                }
                //需要忽略新增的状态
                List<SyncDataMappingsEntity> ignoreToSuccess = syncDataMappingsEntities.stream().filter(item -> item.getLastSyncStatus().equals(SyncDataStatusEnum.IGNORE.getStatus())).collect(Collectors.toList());
                 if(CollectionUtils.isNotEmpty(ignoreToSuccess)){
                     //更新忽略状态的为成功
                     List<String> mappingIds = ignoreToSuccess.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
                     syncDataMappingsDao.setTenantId(tenantId).batchUpdateByIds(tenantId,
                             Lists.newArrayList(mappingIds),
                             SyncDataStatusEnum.WRITE_SUCCESS.getStatus(),
                             null,
                             i18NStringManager.getByEi(I18NStringEnum.s6,tenantId),
                             System.currentTimeMillis());
                 }

                syncDataMappingsEntities= syncDataMappingsEntities.stream().filter(item ->!item.getLastSyncStatus().equals(SyncDataStatusEnum.IGNORE.getStatus())).collect(Collectors.toList());
                 if(CollectionUtils.isEmpty(syncDataMappingsEntities)){
                     break;
                 }
                 List<String> syncDataEntityId = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getLastSyncDataId).collect(Collectors.toList());
                List<String> mappingIds = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
                List<SyncDataEntity> syncDataEntityList = adminSyncDataDao.setTenantId(tenantId).listByIds(tenantId, syncDataEntityId);
                //批量更新中间表失败
                syncDataMappingsDao.setTenantId(tenantId).batchUpdateByIds(tenantId,
                        Lists.newArrayList(mappingIds),
                        SyncDataStatusEnum.PROCESS_FAILED.getStatus(),
                        null,
                        i18NStringManager.getByEi(I18NStringEnum.s689,tenantId),
                        System.currentTimeMillis());
//                List<SyncDataEntity> syncDataEntityList = adminSyncDataDao.setTenantId(tenantId).listByStatusListAndEndUpdateTime(tenantId, idDoingStatus, 0L, endUpdateTime, 0, limit);
                if (syncDataEntityList.size() == 0) {
                    break;
                }
                for (SyncDataEntity entity : syncDataEntityList) {
                    int newStatus;
                    String remark = i18NStringManager.getByEi(I18NStringEnum.s987,tenantId);
                    newStatus = SyncDataStatusEnum.PROCESS_FAILED.getStatus();
                    if (StringUtils.isNotBlank(entity.getRemark())) {
                        remark += ":" + entity.getRemark();
                    }
                    entity.setRemark(remark);
                    entity.setErrorCode(I18NStringEnum.s987.name());
                    adminSyncDataManager.updateToError(tenantId, entity.getId(), newStatus, -1, remark, entity.getSyncPloyDetailSnapshotId());
                }
                monitorReportManager.send2SyncDataErrorMonitor(syncDataEntityList);
                //重试
                reSyncTimeOutSyncData(tenantId, syncDataEntityList);
                try {
                    //更新接口需要等待
                    Thread.sleep(10*1000L);
                } catch (Exception e) {
                    log.warn("sync mappings  Exception e={}",e);
                }
            }

        }
        return Result.newSuccess();
    }
    private void reSyncTimeOutSyncData(String tenantId, List<SyncDataEntity> syncDataEntityList) {
        if (tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NOT_AUTO_SYNC_TENANTS)){
            //不自动重试黑名单，企业级别
            return;
        }
        List<SyncDataContextEvent> eventDataList=Lists.newArrayList();
        if(ConfigCenter.TIME_OUT_SYNC_DATA_TIME<0){//如果超时时间小于0，不重试
            return;
        }
        SetParams params=SetParams.setParams().ex(ConfigCenter.TIME_OUT_SYNC_DATA_TIME).nx();//ex超时时间，nx不存在才set
        for (SyncDataEntity entity : syncDataEntityList) {
            StringBuilder redisKey=new StringBuilder();
            redisKey.append(tenantId).append("_").append(entity.getSourceData().getApiName()).append("_").append(entity.getSourceData().getId());
            String result = redisDataSource.get(this.getClass().getSimpleName()).set(redisKey.toString(), "1", params);
            if(result==null){//为空说明没有设置上，已存在
                continue;
            }

            // 检查数据是否还在同步过程中
            if (syncLockManager.checkDataIsBeingSynchronized(tenantId, entity.getSourceData().getApiName(), entity.getSourceData().getId())) {
                continue;
            }

            SyncDataContextEvent eventData = new SyncDataContextEvent();
            eventData.setSourceData(entity.getSourceData());
            if(eventData.getSourceData()!=null){//如果为空设置为系统
                eventData.getSourceData().putIfAbsent("last_modified_by",Lists.newArrayList("-10000"));
            }
            eventData.setSourceEventType(entity.getSourceEventType());
            eventData.setSourceTenantType(entity.getSourceTenantType());
            eventData.setPloyDetailSnapshotId(entity.getSyncPloyDetailSnapshotId());
            eventData.setSyncLogId(entity.getSyncLogId());
            eventDataList.add(eventData);
        }
        if(CollectionUtils.isNotEmpty(eventDataList)){
            eventTriggerService.batchSendEventData2DispatcherMqByContext(eventDataList);
        }
    }

    /**
     * 同步等待数据目前是直接执行的，需要考虑接入分发框架
     * @return
     */
    @Override
    public Result<Void> syncWaitingData(String tenantId) {
//        Long beginCreateTime = DateTime.now().minusSeconds(60 * 60).getMillis();
//        Long endCreateTime = DateTime.now().minusSeconds(10).getMillis();
//        Integer limit = 1000;
//        //每次最多处理一千条，有多的下次会处理。
//        List<SyncDataEntity> wattingData = adminSyncDataDao.setTenantId(tenantId).listByStatusAndCreateTime(tenantId, beginCreateTime, endCreateTime, SyncDataStatusEnum.WAITTING.getStatus(), limit);
//        String tranceId = TraceUtil.get();
//        for (SyncDataEntity syncDataEntity : wattingData) {
//            TraceUtil.initTrace(tranceId);
//            TraceUtil.addChildTrace(syncDataEntity.getSourceDataId());
//            CompleteEventTriggerArg completeEventTriggerMqData = new CompleteEventTriggerArg();
//            completeEventTriggerMqData.setSyncDataId(syncDataEntity.getId());
//            completeEventTriggerMqData.setSourceEventType(EventTypeEnum.WAITTING.getType());
//            completeEventTriggerMqData.setTenantId(tenantId);
//            syncMainService.singleSync(completeEventTriggerMqData);
//            TraceUtil.removeChildTrace(syncDataEntity.getSourceDataId());
//        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncSingletonData(ErpIdArg erpIdArg, String destObjApiName) {
        return adminSyncDataMappingService.syncSingletonData(erpIdArg, destObjApiName);
    }


    @Override
    public Result<Void> sendDoDispatchMq(String tenantId, List<SyncDataContextEvent> eventDataList){
        eventDataList.forEach(SyncDataContextEvent::fillCrmCleanFields);
        return allModelDubboService.batchSendEventData2DispatcherMqByContext(eventDataList);
    }

    @Override
    public Result<Void> scanTempNotTrigger(String tenantId) {
        //扫描前一天的数据
        DateTime beginDt = DateUtil.beginOfDay(DateUtil.yesterday());
        DateTime endDt = DateUtil.beginOfDay(new DateTime());
        Result<Integer> result = opsToolService.scanErpTempBetweenId(tenantId, beginDt.getTime(), endDt.getTime());
        return Result.copy(result);
    }

    @Override
    public void handleDelete(HandleDeleteArg arg) {
        final String tenantId = arg.getTenantId();
        final String dataId = arg.getDataId();
        final String objectApiName = arg.getObjectApiName();

        //支持双向策略的清除，根据crm的object-apiName匹配
        deleteErp2CrmData(tenantId, objectApiName, dataId);
        deleteCrm2ErpData(tenantId, objectApiName, dataId);
    }

    private void deleteCrm2ErpData(String tenantId, String sourceObjectApiName, String dataId) {
        Result2<List<SyncPloyDetailSnapshotData2>> ployDetailsSnapshots = syncPloyDetailSnapshotService.listNewestEnableSyncPloyDetailsSnapshots(tenantId, sourceObjectApiName, TenantTypeEnum.CRM.getType(), Lists.newArrayList(tenantId));
        if (CollectionUtils.isEmpty(ployDetailsSnapshots.getData())) {
            return;
        }
        log.debug("deleteErp2CrmData message tenantId:{} sourceObjectApiName:{} dataId:{}", tenantId, sourceObjectApiName, dataId);
        //检查策略是否勾选作废事件
//        List<SyncPloyDetailSnapshotData> filterSnapshots
//                = ployDetailsSnapshots.getData().stream().filter(item -> item.getSyncPloyDetailData().getSyncRules().getEvents().contains(EventTypeEnum.INVALID.getType())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(filterSnapshots)) {
//            return;
//        }
        for (SyncPloyDetailSnapshotData2 filterSnapshot : ployDetailsSnapshots.getData()) {
            String destObjectApiName = null;
            SyncDataEntity lastSyncData = null;
            //主对象处理
            if (filterSnapshot.getSourceObjectApiName().equals(sourceObjectApiName)) {
                destObjectApiName = filterSnapshot.getDestObjectApiName();
                Result2<SyncDataMappingData> masterDataMapping = syncDataMappingService.getSyncDataMapping(tenantId, tenantId, sourceObjectApiName, dataId, tenantId, destObjectApiName);
                if(ObjectUtils.isEmpty(masterDataMapping.getData())) return;
                String erpDataId=masterDataMapping.getData().getDestDataId();
                //这里是crm-erp的方向
                String dataCenterId=filterSnapshot.getSyncPloyDetailData().getDestDataCenterId();
                lastSyncData = adminSyncDataDao.setTenantId(tenantId).getSimple(tenantId, masterDataMapping.getData().getLastSyncDataId());
                if (ObjectUtils.isNotEmpty(lastSyncData)&&ObjectUtils.isNotEmpty(lastSyncData.getSourceData())) {
                    SyncDataContextEvent doWriteMqData = new SyncDataContextEvent();
                    doWriteMqData.setDestTenantId(tenantId);
                    doWriteMqData.setTenantId(tenantId);
                    doWriteMqData.setSourceTenantId(tenantId);
                    doWriteMqData.setDestObjectApiName(destObjectApiName);
                    erpDataId= ObjectUtils.isEmpty(lastSyncData.getDestDataId())?lastSyncData.getDestData().getId():lastSyncData.getDestDataId();
                    doWriteMqData.setDestDataId(erpDataId);
                    doWriteMqData.setSyncPloyDetailSnapshotId(filterSnapshot.getId());
                    doWriteMqData.setDestEventType(EventTypeEnum.DELETE_DIRECT.getType());
                    doWriteMqData.setSyncDataId(lastSyncData.getId());
                    doWriteMqData.setMasterMappingsData(null);
                    erpDataPreprocessService.delete2InvalidErpObjData(doWriteMqData);
                }else{
                    erpDataPreprocessService.deleteCrm2erpDataDao(tenantId, erpDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
                    erpDataPreprocessService.deleteErp2CrmDataDao(tenantId, erpDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
                }
                //可能有些从对象不会触发删除事件，需要根据主对象删除从对象
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : filterSnapshot.getSyncPloyDetailData().getDetailObjectMappings()) {
                    Result2<List<SyncDataMappingData>> dataByMasterId = syncDataMappingService.getDataByMasterId(tenantId, tenantId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName(), dataId);
                    log.debug("crm2erp query dataByMasterId:{} ",dataByMasterId);
                    for (SyncDataMappingData datum : dataByMasterId.getData()) {
                        dealWithDetailSyncDataFromCrm(filterSnapshot, detailObjectMapping.getSourceObjectApiName(), tenantId, datum.getSourceDataId());
                    }
                }
            } else {
                //从对象处理
                dealWithDetailSyncDataFromCrm(filterSnapshot, sourceObjectApiName, tenantId, dataId);
            }

        }
    }

    private void deleteErp2CrmData(String tenantId, String destObjectApiName, String dataId) {
        Result2<List<SyncPloyDetailSnapshotData2>> ployDetailsSnapshots = syncPloyDetailSnapshotService.listEnableSyncPloyDetailByDestApiName(tenantId, destObjectApiName, TenantTypeEnum.CRM.getType(), Lists.newArrayList(tenantId));
        if (CollectionUtils.isEmpty(ployDetailsSnapshots.getData())) {
            return;
        }
        log.debug("deleteErp2CrmData message tenantId:{} destObjectApiName:{} dataId:{}", tenantId, destObjectApiName, dataId);
        //检查策略是否勾选作废事件
//        List<SyncPloyDetailSnapshotData> filterSnapshots
//                = ployDetailsSnapshots.getData().stream().filter(item -> item.getSyncPloyDetailData().getSyncRules().getEvents().contains(EventTypeEnum.INVALID.getType())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(filterSnapshots)) {
//            return;
//        }
        for (SyncPloyDetailSnapshotData2 filterSnapshot : ployDetailsSnapshots.getData()) {
            String sourceObjectApiName = null;
            SyncDataEntity lastSyncData = null;
            //主对象处理
            if (filterSnapshot.getDestObjectApiName().equals(destObjectApiName)) {
                sourceObjectApiName = filterSnapshot.getSourceObjectApiName();
                Result2<SyncDataMappingData> masterDataMapping = syncDataMappingService.getSyncDataMappingByDest(tenantId, tenantId, sourceObjectApiName, dataId, tenantId, destObjectApiName);
                if(ObjectUtils.isEmpty(masterDataMapping.getData())) return;

                lastSyncData = adminSyncDataDao.setTenantId(tenantId).getSimple(tenantId, masterDataMapping.getData().getLastSyncDataId());
                String erpDataId=masterDataMapping.getData().getSourceDataId();
                //这里是erp-crm的方向
                String dataCenterId=filterSnapshot.getSyncPloyDetailData().getSourceDataCenterId();
                if (ObjectUtils.isNotEmpty(lastSyncData)&&ObjectUtils.isNotEmpty(lastSyncData.getSourceData())) {
                    SyncDataContextEvent doWriteMqData = new SyncDataContextEvent();
                    doWriteMqData.setDestTenantId(tenantId);
                    doWriteMqData.setTenantId(tenantId);
                    doWriteMqData.setSourceTenantId(tenantId);
                    doWriteMqData.setDestObjectApiName(sourceObjectApiName);
                    doWriteMqData.setDestDataId(erpDataId);
                    doWriteMqData.setSyncPloyDetailSnapshotId(filterSnapshot.getId());
                    doWriteMqData.setDestEventType(EventTypeEnum.DELETE_DIRECT.getType());
                    doWriteMqData.setSyncDataId(lastSyncData.getId());
                    //doWriteMqData.setDestMasterDataId(masterId);
                    doWriteMqData.setMasterMappingsData(null);
                    Result<String> stringResult = erpDataPreprocessService.delete2InvalidErpObjData(doWriteMqData);
                }else{
                    erpDataPreprocessService.deleteCrm2erpDataDao(tenantId, erpDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
                    erpDataPreprocessService.deleteErp2CrmDataDao(tenantId, erpDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
                }

                //可能有些从对象不会触发删除事件，需要根据主对象删除从对象
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : filterSnapshot.getSyncPloyDetailData().getDetailObjectMappings()) {
                    Result2<List<SyncDataMappingData>> dataByMasterId = syncDataMappingService.getDataByMasterId(tenantId, tenantId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName(), masterDataMapping.getData().getSourceDataId());
                    log.debug("erp2crm query dataByMasterId:{} ",dataByMasterId);
                    for (SyncDataMappingData datum : dataByMasterId.getData()) {
                        dealWithDetailSyncDataFromErp(filterSnapshot, detailObjectMapping.getSourceObjectApiName(), tenantId, datum.getDestDataId());
                    }
                }
            } else {
                //从对象处理
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : filterSnapshot.getSyncPloyDetailData().getDetailObjectMappings()) {
                    if(detailObjectMapping.getDestObjectApiName().equals(destObjectApiName)){
                        dealWithDetailSyncDataFromErp(filterSnapshot, detailObjectMapping.getSourceObjectApiName(), tenantId, dataId);
                    }
                }
            }
        }
    }

    private void dealWithDetailSyncDataFromCrm(SyncPloyDetailSnapshotData2 filterSnapshot, String sourceObjectApiName, String tenantId, String dataId) {
        String masterFieldApiName = null;
        String destObjectApiName = null;
        SyncDataEntity lastSyncData = null;
        String masterId = null;
        List<FieldMappingData> fieldMappingDataList = new ArrayList<>(0);
        OuterService outerService = outerServiceFactory.get(TenantTypeEnum.CRM.getType());
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : filterSnapshot.getSyncPloyDetailData().getDetailObjectMappings()) {
            //拿到从映射字段集合与目标从对象apiName
            if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                fieldMappingDataList = detailObjectMapping.getFieldMappings();
                destObjectApiName = detailObjectMapping.getDestObjectApiName();
                break;
            }
        }
        Result2<SyncDataMappingData> syncDataMapping = syncDataMappingService.getSyncDataMapping(tenantId, tenantId, sourceObjectApiName, dataId, tenantId, destObjectApiName);
        if(ObjectUtils.isEmpty(syncDataMapping.getData()))return;
        lastSyncData = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, syncDataMapping.getData().getLastSyncDataId());
        String erpDataId=syncDataMapping.getData().getDestDataId();
        String dataCenterId=filterSnapshot.getSyncPloyDetailData().getDestDataCenterId();

        log.debug("dealWithDetailSyncDataFromCrm getlastSyncData:{}",lastSyncData);
        if(ObjectUtils.isNotEmpty(lastSyncData)&&ObjectUtils.isNotEmpty(lastSyncData.getSourceData())){
            //拿取主对象apiName(detailObjectMapping会构造主对象的mapping以供使用)
            for (FieldMappingData fieldMapping : fieldMappingDataList) {
                if (fieldMapping.getSourceType().equals(FieldTypeContants.MASTER_DETAIL)) {
                    //如规格值的masterFieldApiName值为specification_id，specification_id为从对象数据sourceData中主对象数据id的key
                    masterFieldApiName = fieldMapping.getSourceApiName();
                    break;
                }
            }
            //通过从对象的masterFieldApiName值为key，获取主对象数据id
            masterId = lastSyncData.getSourceData().getString(masterFieldApiName);
            ObjectData remoteSourceObjectData = null;
            if (StringUtils.isEmpty(masterId)) {
                remoteSourceObjectData = outerService.getObjectData(tenantId, TenantType.CRM, sourceObjectApiName, lastSyncData.getSourceDataId(),  true).getData();
                if (remoteSourceObjectData != null) {
                    masterId = remoteSourceObjectData.getString(masterFieldApiName);
                }
                if (StringUtils.isEmpty(masterId)) {
                    log.info("{}detail data masterId is null,masterId:{},data={}", LogUtil.BREAK_KEY, masterId, lastSyncData);
                    return;
                }
            }
            SyncDataContextEvent doWriteMqData = new SyncDataContextEvent();
            doWriteMqData.setDestTenantId(tenantId);
            doWriteMqData.setTenantId(tenantId);
            doWriteMqData.setSourceTenantId(tenantId);
            doWriteMqData.setDestObjectApiName(destObjectApiName);
            String destDataId= ObjectUtils.isEmpty(lastSyncData.getDestDataId())?lastSyncData.getDestData().getId():lastSyncData.getDestDataId();
            doWriteMqData.setDestDataId(destDataId);
            doWriteMqData.setSyncPloyDetailSnapshotId(filterSnapshot.getId());
            doWriteMqData.setDestEventType(EventTypeEnum.DELETE_DIRECT.getType());
            doWriteMqData.setSyncDataId(lastSyncData.getId());
            MasterMappingsData masterMappingsData = new MasterMappingsData();
            masterMappingsData.setDestDataId(masterId);
            //doWriteMqData.setDestMasterDataId(masterId);
            doWriteMqData.setMasterMappingsData(masterMappingsData);
            erpDataPreprocessService.delete2InvalidErpObjData(doWriteMqData);
        }else {
            erpDataPreprocessService.deleteCrm2erpDataDao(tenantId, erpDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
            erpDataPreprocessService.deleteErp2CrmDataDao(tenantId, erpDataId, dataCenterId, sourceObjectApiName, destObjectApiName);

        }

    }

    private void dealWithDetailSyncDataFromErp(SyncPloyDetailSnapshotData2 filterSnapshot, String sourceObjectApiName, String tenantId, String destDataId) {
        String masterFieldApiName = null;
        String destObjectApiName = null;
        SyncDataEntity lastSyncData = null;
        String masterId = null;
        List<FieldMappingData> fieldMappingDataList = new ArrayList<>(0);
        OuterService outerService = outerServiceFactory.get(TenantTypeEnum.ERP.getType());
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : filterSnapshot.getSyncPloyDetailData().getDetailObjectMappings()) {
            //拿到从映射字段集合与目标从对象apiName
            if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                fieldMappingDataList = detailObjectMapping.getFieldMappings();
                destObjectApiName = detailObjectMapping.getDestObjectApiName();
                break;
            }
        }
        Result2<SyncDataMappingData> syncDataMapping = syncDataMappingService.getSyncDataMappingByDest(tenantId, tenantId, sourceObjectApiName, destDataId, tenantId, destObjectApiName);
        if(ObjectUtils.isEmpty(syncDataMapping.getData()))return;
        //这里是erp-crm的方向
        String dataCenterId=filterSnapshot.getSyncPloyDetailData().getSourceDataCenterId();
        lastSyncData = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, syncDataMapping.getData().getLastSyncDataId());
        log.debug("dealWithDetailSyncDataFromErp getlastSyncData:{}",lastSyncData);
        if(ObjectUtils.isNotEmpty(lastSyncData)&&ObjectUtils.isNotEmpty(lastSyncData.getSourceData())){
            //拿取主对象apiName(detailObjectMapping会构造主对象的mapping以供使用)
            for (FieldMappingData fieldMapping : fieldMappingDataList) {
                if (fieldMapping.getSourceType().equals(FieldTypeContants.MASTER_DETAIL)) {
                    //如规格值的masterFieldApiName值为specification_id，specification_id为从对象数据sourceData中主对象数据id的key
                    masterFieldApiName = fieldMapping.getSourceApiName();
                    break;
                }
            }
            //通过从对象的masterFieldApiName值为key，获取主对象数据id
            masterId = lastSyncData.getSourceData().getString(masterFieldApiName);
            ObjectData remoteSourceObjectData = null;
            if (StringUtils.isEmpty(masterId)) {
                remoteSourceObjectData = outerService.getObjectData(tenantId, TenantType.ERP, sourceObjectApiName, lastSyncData.getSourceDataId(), true).getData();
                if (remoteSourceObjectData != null) {
                    masterId = remoteSourceObjectData.getString(masterFieldApiName);
                }
                if (StringUtils.isEmpty(masterId)) {
                    log.info("{}detail data masterId is null,masterId:{},data={}", LogUtil.BREAK_KEY, masterId, lastSyncData);
                    return;
                }
            }
            SyncDataContextEvent doWriteMqData = new SyncDataContextEvent();
            doWriteMqData.setDestTenantId(tenantId);
            doWriteMqData.setTenantId(tenantId);
            doWriteMqData.setSourceTenantId(tenantId);
            doWriteMqData.setDestObjectApiName(sourceObjectApiName);
            doWriteMqData.setDestDataId(lastSyncData.getSourceDataId());
            doWriteMqData.setSyncPloyDetailSnapshotId(filterSnapshot.getId());
            doWriteMqData.setDestEventType(EventTypeEnum.DELETE_DIRECT.getType());
            doWriteMqData.setSyncDataId(lastSyncData.getId());

            MasterMappingsData masterMappingsData = new MasterMappingsData();
            masterMappingsData.setDestDataId(masterId);
            //doWriteMqData.setDestMasterDataId(masterId);
            doWriteMqData.setMasterMappingsData(masterMappingsData);
            erpDataPreprocessService.delete2InvalidErpObjData(doWriteMqData);
        }else{
            erpDataPreprocessService.deleteCrm2erpDataDao(tenantId, destDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
            erpDataPreprocessService.deleteErp2CrmDataDao(tenantId, destDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
        }
    }
}
