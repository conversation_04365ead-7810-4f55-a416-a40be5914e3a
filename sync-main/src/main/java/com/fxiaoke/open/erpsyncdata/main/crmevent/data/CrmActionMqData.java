package com.fxiaoke.open.erpsyncdata.main.crmevent.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import java.io.Serializable;
import lombok.Data;

@Data
public class CrmActionMqData implements Serializable {
    /**
     * { "TenantID": 1, "TenantAccount": "fs", "AppID":"CRM", "Package":"CRM", "ObjectApiName": "AccountObj", "ObjectID": "a5f11f885b1f400eb8a9357629760e67", "ActionCode": "Add" "ActionContent": { "OwnerID": 1000 }, "OperatorID": 1000, "ActionTime": **********, "Source": "CRM" }
     */

    public static final String topic = "TOPIC_CRM_OPENAPI";
    public static final int flag = 1;

    @JSONField(name = "TenantID")
    private int ei;  //企业ID
    @JSONField(name = "TenantAccount")
    private String ea;     //企业帐号
    @JSONField(name = "AppID")
    private String appId;
    @JSONField(name = "Package")
    private String packageName;
    @JSONField(name = "ObjectApiName")
    private String objectApiName;//对象类型，如客户、联系人
    @JSONField(name = "ObjectID")
    private String objectId;
    @JSONField(name = "ActionCode")
    private String actionCode; //操作名称，如Add（增加）、Delete（删除）
    @JSONField(name = "ActionContent")
    private ObjectData actionContent;//事件内容
    @JSONField(name = "OperatorID")
    private int operatorId;//操作者ID
    @JSONField(name = "ActionTime")
    private long actionTime;//操作时间
    @JSONField(name = "Source")
    private String source;
}