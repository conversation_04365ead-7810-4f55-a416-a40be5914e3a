package com.fxiaoke.open.erpsyncdata.main.service;

import com.fxiaoke.open.erpsyncdata.common.annotation.CompareSyncField;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncStepException;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.converter.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ChangeToWaitingMqData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.main.crmevent.DataCenterMqProducer;
import com.fxiaoke.open.erpsyncdata.main.manager.StreamManager;
import com.fxiaoke.open.erpsyncdata.main.manager.SyncLockManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg.EventData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.open.erpsyncdata.writer.manager.NodeCompleteDataWriteManager;
import com.fxiaoke.open.erpsyncdata.writer.manager.NodeReverseWrite2CrmManager;
import com.fxiaoke.open.erpsyncdata.writer.manager.NodeSyncWriteMainManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service("syncMainService")
public class SyncMainServiceImpl implements SyncMainService {
    @Autowired
    private DataCenterMqProducer dataCenterMqProducer;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private NodeSyncPreManager nodeSyncPreManager;
    @Autowired
    private ChangeToWaitingManager changeToWaitingManager;
    @Autowired
    private NodeCompleteDataProcessManager nodeCompleteDataProcessManager;
    @Autowired
    private NodeCompleteDataWriteManager nodeCompleteDataWriteManager;
    @Autowired
    private NodeCompleteEventTriggerManager nodeCompleteEventTriggerManager;
    @Autowired
    private NodeDoProcessManager nodeDoProcessManager;
    @Autowired
    private NodeSyncWriteMainManager nodeSyncWriteMainManager;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private NodeReverseWrite2CrmManager nodeReverseWrite2CrmManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ReSyncDataNodeManager reSyncDataNodeManager;
    @Autowired
    private NodeQueryCrmObject2DestManager nodeQueryCrmObject2DestManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncPloyManager syncPloyManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private StreamManager streamManager;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncLockManager syncLockManager;

    /**
     * 分发后，数据同步主方法.
     * 无论是CRM, 还是ERP，数据都会进入mq聚合框架，
     * 这里就是分发数据的总入口。
     *
     */
    @LogLevel(LogLevelEnum.TRACE)
    @Override
    @CompareSyncField(syncType = SyncCompareConstant.SYNC_DATA_MAIN)
    public Result2<ObjectDataSyncMsg> syncDataMain(SyncDataContextEvent originEventData) {
        // 检查是否在同步中
        final ObjectData sourceData = originEventData.getSourceData();
        boolean dataLock = syncLockManager.tryLock(sourceData.getTenantId(), sourceData.getApiName(), sourceData.getId());
        if (!dataLock) {
            // 抢不到锁放回聚合框架重试
            LogIdUtil.setNeedRetry(true);
            return Result2.newError(ResultCodeEnum.DATA_SYNCHRONIZING);
        }

        try {
            if (StringUtils.isNotBlank(originEventData.getLocale())) {
                TraceUtil.setLocale(originEventData.getLocale());
            } else {
                // 同步过程中错误信息需多语,没有多语按企业多语处理
                TraceUtil.setLocale(i18NStringManager.getDefaultLang(sourceData.getTenantId()));
            }
            return syncDataMain2(originEventData);
        } finally {
            TraceUtil.cleanLocale();
            syncLockManager.unlock(sourceData.getTenantId(), sourceData.getApiName(), sourceData.getId());
        }
    }

    private Result2<ObjectDataSyncMsg> syncDataMain2(SyncDataContextEvent originEventData) {
        String sourceTenantId = originEventData.getSourceData().getTenantId();
        TraceUtil.setEi(sourceTenantId);
        originEventData.setTenantId(sourceTenantId);
        //logback增加类型
        addMDCData(originEventData.getDataReceiveType(), sourceTenantId);
        //一条数据，可以经过多个集成流处理。 把所有集成流都查出来。
        ObjectDataSyncMsg objectDataSyncMsg = new ObjectDataSyncMsg(Lists.newArrayList());
        List<SyncPloyDetailSnapshotData2> syncPloyDetailSnapshotData2List = streamManager.queryIntegrationStreamSnapshot(originEventData, originEventData.getPloyDetailSnapshotId(), Lists.newArrayList());

        //判断源数据在集成流快照中是否应该同步
        String preLogId = LogIdUtil.get();
        ObjectData sourceData = originEventData.getSourceData();
        Integer sourceTenantType = originEventData.getSourceTenantType();
        boolean isERP2CRM = Objects.equals(sourceTenantType, TenantType.ERP);
        if (!isERP2CRM && CollectionUtils.isNotEmpty(syncPloyDetailSnapshotData2List)) {//crm->erp且快照不为空，日志可能会很多
            //上报crm源数据触发日志
            sendCrmTriggerLog(sourceTenantId, originEventData);
        }
        for (int i = 0; i < syncPloyDetailSnapshotData2List.size(); i++) {
            //集成流处理开始
            SyncDataContextEvent eventData=originEventData;
            if(syncPloyDetailSnapshotData2List.size()>1){
                //多个集成流处理数据，需要进行深拷贝event.避免数据污染
                eventData=BeanUtil.deepCopy(originEventData,SyncDataContextEvent.class);
            }
            SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotData2List.get(i);
            SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();
            eventData.setSourceDataCenterId(syncPloyDetailData.getSourceDataCenterId());
            eventData.setDestDataCenterId(syncPloyDetailData.getDestDataCenterId());
            String erpDataCenterId = isERP2CRM ? syncPloyDetailData.getSourceDataCenterId() : syncPloyDetailData.getDestDataCenterId();
            String syncPloyDetailId = syncPloyDetailSnapshotData.getSyncPloyDetailId();
            LogIdUtil.setDataCenterId(erpDataCenterId,isERP2CRM);
            String mainObjApiName = syncPloyDetailSnapshotData.getSourceObjectApiName();
            addChildLogId(preLogId, sourceTenantId, isERP2CRM, i, syncPloyDetailId, mainObjApiName);
            eventData.setSyncLogId(LogIdUtil.get());
            eventData.setBasic(sourceTenantId, erpDataCenterId)
                    .setStreamId(syncPloyDetailId);
            eventData.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotData.getId());
            eventData.setPloyDetailSnapshotId(syncPloyDetailSnapshotData.getId());
            TraceUtil.initTrace(eventData.getSyncLogId());

            //单个集成流的处理
            Result2<List<ObjectDataSyncMsg.SingleDataSyncMsg>> dataSyncResultList = runOneIntegrationStream(eventData);
            if (CollectionUtils.isNotEmpty(dataSyncResultList.getData())) {
                objectDataSyncMsg.getDataSyncResultList().addAll(dataSyncResultList.getData());
            } else {
                ObjectDataSyncMsg.SingleDataSyncMsg singleDataSyncMsg = buildSingleDataSyncMsg(false,
                        sourceData,
                        syncPloyDetailSnapshotData,
                        dataSyncResultList.getErrMsg(),
                        DataNodeNameEnum.Exception);
                objectDataSyncMsg.getDataSyncResultList().add(singleDataSyncMsg);
            }
        }
        if(!CollectionUtils.isNotEmpty(syncPloyDetailSnapshotData2List)){//算正常结束
            ObjectDataSyncMsg.SingleDataSyncMsg singleDataSyncMsg = buildSingleDataSyncMsg(true,
                    sourceData,
                    null,
                    i18NStringManager.getByEi(I18NStringEnum.s2032,sourceTenantId),
                    DataNodeNameEnum.DataTriggerProcess);
            objectDataSyncMsg.getDataSyncResultList().add(singleDataSyncMsg);
        }
        LogIdUtil.reset(preLogId);
        TimePointRecorderStatic.record("allFinish");
        return Result2.newSuccess(objectDataSyncMsg);
    }

    private void sendCrmTriggerLog(String tenantId,SyncDataContextEvent originEventData) {
        try{
            ObjectData sourceData = originEventData.getSourceData();
            ErpTempData erpTempData=ErpTempData.builder().dataId(sourceData.getId()).dataNumber(sourceData.getName()).dataBody(JacksonUtil.toJson(sourceData))
                    .operationType(originEventData.getSourceEventType()).lastSyncTime(sourceData.getVersion()).build();
            if(StringUtils.isBlank(erpTempData.getDataNumber())){
                Result2<ObjectData> objectDataResult2 = outerServiceFactory.get(TenantType.CRM).getObjectData(tenantId, TenantType.CRM, sourceData.getApiName(), sourceData.getId());
                if(objectDataResult2!=null&&objectDataResult2.isSuccess()&&objectDataResult2.getData()!=null){
                    erpTempData.setDataNumber(objectDataResult2.getData().getName());
                }
            }
            syncLogManager.saveLogWithLogId(tenantId, SyncLogTypeEnum.CRM_TRIGGER,SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),null, erpTempData);
        }catch (Exception e){
            log.warn("sendCrmTriggerLog error={}",e);
        }
    }

    private void addMDCData(Integer dataReceiveType, String tenantId) {
        Set<String> colorEiLogSet = tenantConfigurationManager.getColorLogTenantIds();
        LogConsoleStrategyFilter.putDataReceiveType(dataReceiveType);
        if (StringUtils.isNotEmpty(tenantId) && colorEiLogSet.contains(tenantId)) {
            LogConsoleStrategyFilter.putTenantId(tenantId);
        }
    }

    private void addChildLogId(String preLogId, String sourceTenantId, boolean isERP2CRM, int i, String syncPloyDetailId, String mainObjApiName) {
        //增加子logId
        //判断如果是非主对象作为主对象对接，则不直接使用序号
        int streamSeq = i;
        if (isERP2CRM) {
            Integer objSeq = erpObjManager.getMainSeqMap(sourceTenantId).getOrDefault(mainObjApiName, 0);
            //假设i不会大于10，就不会产生重复的seq
            streamSeq += objSeq * 10;
        }
        LogIdUtil.buildChildLogIdRestId(preLogId, streamSeq);
        LogIdUtil.setStreamId(syncPloyDetailId);
    }


    //TODO 同步过程的数据体
    @Override
    public Result2<List<EventData>> sendDependEventData(List<EventData> eventDatas, String destTenantId, Boolean syncDependForce) {
        //改为发送到分发框架
        eventDatas.forEach(v -> {v.setSyncDependForce(syncDependForce);v.setDataReceiveType(DataReceiveTypeEnum.DEPEND_DATA.getType());});
        BatchSendEventDataArg arg = new BatchSendEventDataArg(eventDatas);
        List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
        eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
        //不先走一遍数据范围校验，直接返回所有依赖数据未创建映射
        return Result2.newError(ResultCodeEnum.DEPEND_DATA__NOT_SYNC, eventDatas);
    }

    /**
     * 同步单条数据, 运行一个集成流
     */
    @CompareSyncField(syncType = SyncCompareConstant.SYNC_DATA_RUN)
    public Result2<List<ObjectDataSyncMsg.SingleDataSyncMsg>> runOneIntegrationStream(SyncDataContextEvent syncDataContextEvent) {
        //final，限定不允许更换
        //准备参数,以下值，在后面使用都是安全不为空的了。
        //tenantId在前面设置了
        String tenantId = syncDataContextEvent.getTenantId();

        String dataId = syncDataContextEvent.getSourceData().getId();
        Long version = syncDataContextEvent.getDataVersion() == null ? 0L : syncDataContextEvent.getDataVersion();

        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, syncDataContextEvent.getSyncPloyDetailSnapshotId()).getData();
        SyncPloyDetailData2 syncPloyDetailData = syncPloyDetailSnapshotData.getSyncPloyDetailData();

        syncDataContextEvent.setSourceTenantType(syncPloyDetailData.getSourceTenantType());
        syncDataContextEvent.setDestTenantType(syncPloyDetailData.getDestTenantType());
        syncDataContextEvent.setSyncLogId(LogIdUtil.get());
        syncDataContextEvent.setObjectApiName(syncDataContextEvent.getSourceData().getApiName());
        syncDataContextEvent.setMainObjApiName(syncPloyDetailData.getSourceObjectApiName());
        syncDataContextEvent.setDestObjectApiName(syncPloyDetailData.getDestObjectApiName());
        syncDataContextEvent.setDataId(dataId);
        syncDataContextEvent.setVersion(version);
        syncDataContextEvent.setDataVersion(version);

        syncPloyManager.fillScreenByPloyDetailId(tenantId, syncDataContextEvent);

        List<ObjectDataSyncMsg.SingleDataSyncMsg> dataSyncMsgList = Lists.newArrayList();
        //前面就不要出现异常了。。。
        try {
            //事件过滤、数据范围过滤 等操作
            nodeSyncPreManager.processMessage(syncDataContextEvent);

            if (nodeCompleteEventTriggerManager.needProcess(syncDataContextEvent)) {
                //同步前函数 创建syncData
                nodeCompleteEventTriggerManager.processMessage(syncDataContextEvent);
            }
            if (nodeDoProcessManager.needProcess(syncDataContextEvent)) {
                //字段转换
                nodeDoProcessManager.processMessage(syncDataContextEvent);
            }
            if (nodeQueryCrmObject2DestManager.needProcess(syncDataContextEvent)) {
                //查询crm，赋值给目标
                nodeQueryCrmObject2DestManager.processMessage(syncDataContextEvent);
            }
            if (nodeCompleteDataProcessManager.needProcess(syncDataContextEvent)) {
                //同步中函数
                nodeCompleteDataProcessManager.processMessage(syncDataContextEvent);
            }
            if (nodeSyncWriteMainManager.needProcess(syncDataContextEvent)) {
                //写处理
                nodeSyncWriteMainManager.processMessage(syncDataContextEvent);
            }
            if (nodeReverseWrite2CrmManager.needProcess(syncDataContextEvent)) {
                //反写crm
                nodeReverseWrite2CrmManager.processMessage(syncDataContextEvent);
            }
            if (nodeCompleteDataWriteManager.needProcess(syncDataContextEvent)) {
                //同步后函数
                nodeCompleteDataWriteManager.processMessage(syncDataContextEvent);
            }
            if (syncDataContextEvent.getStop()) {
                //某一步骤中断结束
                log.info("sync stop at {},msg:{}", syncDataContextEvent.getCurrentDataNodeName(), syncDataContextEvent.getMsg());
            }
            if (syncDataContextEvent.isFinish()) {
                //完成写结束
                log.info("sync finish at {},msg:{}", syncDataContextEvent.getCurrentDataNodeName(), syncDataContextEvent.getMsg());
            }
            syncDataContextEvent.setCurrentDataNodeName(DataNodeNameEnum.DataEnd);
            monitorReportManager.sendEndNodeMsgByCtx(syncDataContextEvent, DataNodeNameEnum.DataEnd, 1000, I18NStringEnum.s2040.getText());
        } catch (SyncStepException syncStepException) {
            syncDataContextEvent.setMsg(syncStepException.getMessage());
            //某一步骤发生异常而中断
            TimePointRecorderStatic.setSyncStepExceptionMsg(syncDataContextEvent.getSyncDataId(), true, syncStepException.getMessage());
            syncDataContextEvent.setCurrentDataNodeName(DataNodeNameEnum.SyncStepException);
            monitorReportManager.sendEndNodeMsgByCtx(syncDataContextEvent, DataNodeNameEnum.SyncStepException, 1000, syncStepException.getMessage());
            log.info("sync stop at {},exception,{}", syncDataContextEvent.getCurrentDataNodeName(), syncStepException.getMessage());
        } catch (Throwable th) {
            //main方法流程异常。
            syncDataContextEvent.setMsg(th.getMessage());
            log.error("sync error at {}", syncDataContextEvent.getCurrentDataNodeName(), th);
            TimePointRecorderStatic.setThrowMsg(syncDataContextEvent.getSyncDataId(), true, th.getMessage());
            extractedCatchStatus(syncDataContextEvent, tenantId, th.getMessage());
            syncDataContextEvent.setCurrentDataNodeName(DataNodeNameEnum.Exception);
            monitorReportManager.sendEndNodeMsgByCtx(syncDataContextEvent, DataNodeNameEnum.Exception, 1000, th.getMessage());
        } finally {
            //从缓存取出SyncData，保存错误的SyncData到重试集合,删除成功的重试数据
            Map<String, SyncDataEntity> tenantSyncDataCache = syncDataFixDao.getTenantSyncDataCache(syncDataContextEvent.getTenantId());
            if (tenantSyncDataCache != null) {
                List<ObjectDataSyncMsg.SingleDataSyncMsg> singleDataSyncMsgList = buildSingleDataSyncMsgBySyncData(tenantId, Lists.newArrayList(tenantSyncDataCache.values()), syncDataContextEvent.getCurrentDataNodeName());
                dataSyncMsgList.addAll(singleDataSyncMsgList);
                reSyncDataNodeManager.saveErrorSyncDataByCache(syncDataContextEvent, Lists.newArrayList(tenantSyncDataCache.values()));
            } else {
                ObjectDataSyncMsg.SingleDataSyncMsg singleDataSyncMsg = buildSingleDataSyncMsg(tenantId, false, syncDataContextEvent.getSourceData(), syncDataContextEvent.getPloyDetailSnapshotId(), syncDataContextEvent.getMsg(), syncDataContextEvent.getCurrentDataNodeName());
                dataSyncMsgList.add(singleDataSyncMsg);
            }
            //将syncData落库
            syncDataFixDao.removeCacheAndInsertDb(syncDataContextEvent.getTenantId());
            TimePointRecorderStatic.record("finish");
        }
        return Result2.newSuccess(dataSyncMsgList);
    }

    private void extractedCatchStatus(SyncDataContextEvent syncDataContextEvent, String tenantId, String msg) {
        List<String> syncDataIds=Lists.newArrayList();
        SyncDataMappingsEntity bySourceDataMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, syncDataContextEvent.getSourceData().getApiName(), syncDataContextEvent.getDestObjectApiName(), syncDataContextEvent.getSourceData().getId());
        if(ObjectUtils.isNotEmpty(bySourceDataMapping)){
            syncDataIds.add(bySourceDataMapping.getLastSyncDataId());
            //更新状态为失败
            syncDataMappingsDao.setTenantId(tenantId).updateLastSyncStatusById(tenantId,bySourceDataMapping.getId(),SyncDataStatusEnum.PROCESS_FAILED.getStatus(), msg,System.currentTimeMillis());
            //是主对象的，需要更新相关的从对象
            if(syncDataContextEvent.getMasterUpdateEvent()){
                SyncPloyDetailEntity ployDetail = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, syncDataContextEvent.getStreamId());
                //从对象apiName映射
                List<SyncObjectAndTenantMappingData> objectApiNameMappingDatas = new ArrayList<>();
                for (com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : ployDetail.getDetailObjectMappings()) {
                    objectApiNameMappingDatas.add(SyncObjectAndTenantMappingData.newInstance(tenantId,
                            detailObjectMapping.getSourceObjectApiName(),
                            tenantId,
                            detailObjectMapping.getDestObjectApiName()));
                }
                if(CollectionUtils.isNotEmpty(objectApiNameMappingDatas)){
                    List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(tenantId).listByMasterDataId(tenantId, objectApiNameMappingDatas, syncDataContextEvent.getSourceData().getId());
                    if(CollectionUtils.isNotEmpty(syncDataMappingsEntities)){
                        //更新从对象的状态为失败。
                        List<String> entities = new ArrayList<>();
                        syncDataMappingsEntities.forEach(entity -> {
                            entities.add(entity.getId());
                            syncDataIds.add(entity.getLastSyncDataId());
                        });
                        syncDataMappingsDao.setTenantId(tenantId).batchUpdateStatusByIds(tenantId,entities, msg,SyncDataStatusEnum.PROCESS_FAILED.getStatus());
                    }
                }

            }
            //批量更新快照的失败信息
            syncDataFixDao.updateStatusByIds(tenantId,syncDataIds,SyncDataStatusEnum.PROCESS_FAILED.getStatus(), msg,I18NStringEnum.s392.name());
        }
    }

    private ObjectDataSyncMsg.SingleDataSyncMsg buildSingleDataSyncMsg(String tenantId, Boolean isSyncEd, ObjectData sourceData, String snapshotDataId, String msg,DataNodeNameEnum lastSyncNode) {
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData = syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, snapshotDataId).getData();
        return buildSingleDataSyncMsg(isSyncEd, sourceData, syncPloyDetailSnapshotData, msg,lastSyncNode);
    }

    private ObjectDataSyncMsg.SingleDataSyncMsg buildSingleDataSyncMsg(Boolean isSyncEd, ObjectData sourceData, SyncPloyDetailSnapshotData2 snapshotData, String msg,DataNodeNameEnum lastSyncNode) {
        ObjectDataSyncMsg.SingleDataSyncMsg singleDataSyncMsg = new ObjectDataSyncMsg.SingleDataSyncMsg();
        if (sourceData != null) {
            singleDataSyncMsg.setSourceObjectApiName(sourceData.getApiName());
            singleDataSyncMsg.setSourceDataId(sourceData.getId());
            if (snapshotData != null) {
                if (sourceData.getApiName().equals(snapshotData.getSourceObjectApiName())) {
                    singleDataSyncMsg.setDestObjectApiName(snapshotData.getDestObjectApiName());
                } else {
                    if (CollectionUtils.isNotEmpty(snapshotData.getSyncPloyDetailData().getDetailObjectMappings())) {
                        for (DetailObjectMappingsData.DetailObjectMappingData detail : snapshotData.getSyncPloyDetailData().getDetailObjectMappings()) {
                            if (sourceData.getApiName().equals(detail.getSourceObjectApiName())) {
                                singleDataSyncMsg.setDestObjectApiName(detail.getDestObjectApiName());
                            }
                        }
                    }
                }
            }
        }
        if(lastSyncNode!=null){
            singleDataSyncMsg.setLastSyncNode(lastSyncNode.getName());
        }
        singleDataSyncMsg.setMsg(msg);
        singleDataSyncMsg.setWriteDestSucc(isSyncEd);
        return singleDataSyncMsg;
    }

    private List<ObjectDataSyncMsg.SingleDataSyncMsg> buildSingleDataSyncMsgBySyncData(String tenantId, List<SyncDataEntity> tenantSyncDataList,DataNodeNameEnum lastSyncNode) {
        List<ObjectDataSyncMsg.SingleDataSyncMsg> dataSyncMsgList = Lists.newArrayList();
        for (SyncDataEntity syncDataEntity : tenantSyncDataList) {
            if(syncDataEntity==null){
                continue;
            }
            ObjectDataSyncMsg.SingleDataSyncMsg singleDataSyncMsg = new ObjectDataSyncMsg.SingleDataSyncMsg();
            singleDataSyncMsg.setSourceObjectApiName(syncDataEntity.getSourceObjectApiName());
            singleDataSyncMsg.setSourceDataId(syncDataEntity.getSourceDataId());
            singleDataSyncMsg.setDestObjectApiName(syncDataEntity.getDestObjectApiName());
            singleDataSyncMsg.setDestDataId(syncDataEntity.getDestDataId());
            singleDataSyncMsg.setDestEventType(syncDataEntity.getDestEventType());
            singleDataSyncMsg.setMsg(syncDataEntity.getRemark());
            singleDataSyncMsg.setNeedReturnDestObjectData(syncDataEntity.getNeedReturnDestObjectData());
            Boolean writeDestSucc=syncDataEntity.getStatus()!=null&&syncDataEntity.getStatus().equals(SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
            singleDataSyncMsg.setWriteDestSucc(writeDestSucc);
            if(lastSyncNode!=null){
                singleDataSyncMsg.setLastSyncNode(lastSyncNode.getName());
            }
            dataSyncMsgList.add(singleDataSyncMsg);
        }
        return dataSyncMsgList;
    }


    @Override
    public Result2<Void> changeToWatting(String tenantId, String syncDataId, Integer oldSyncDataStatus, Integer tenantType, List<EventData> eventDataList) {
        ChangeToWaitingMqData doDependMqData = new ChangeToWaitingMqData();
        doDependMqData.setTenantId(tenantId);
        doDependMqData.setSyncDataId(syncDataId);
        doDependMqData.setOldSyncDataStatus(oldSyncDataStatus);
        doDependMqData.setEventDataList(eventDataList);
        TimePointRecorderStatic.recordSync("changeToWaiting", doDependMqData.getSyncDataId());
        changeToWaitingManager.processMessage(doDependMqData);
        return Result2.newSuccess();
    }

    @Override
    @LogLevel(LogLevelEnum.TRACE)
    public Result2<String> sendEventData2DispatcherMq(SyncDataContextEvent eventData) {
        String msgId = dataCenterMqProducer.sendDoDispatcher(eventData);
        return Result2.newSuccess(msgId);
    }
}
