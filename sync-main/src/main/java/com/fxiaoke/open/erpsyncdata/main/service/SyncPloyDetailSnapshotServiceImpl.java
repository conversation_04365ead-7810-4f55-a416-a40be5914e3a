package com.fxiaoke.open.erpsyncdata.main.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SyncPloyDetailSnapshotServiceImpl implements SyncPloyDetailSnapshotService {
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;


    @Override
    @Cached(expire = 30, cacheType = CacheType.LOCAL, localLimit = 1000)
    @LogLevel(LogLevelEnum.TRACE)
    public Result2<Boolean> isMatchDestCondition(String destTenantId, String destObjectApiName, Integer tenantType) {
        String destMasterObjectApiName = outerServiceFactory.get(tenantType).getMasterObjectApiName(destTenantId, tenantType, destObjectApiName).getData();
        if (!Strings.isNullOrEmpty(destMasterObjectApiName)) {
            destObjectApiName = destMasterObjectApiName;
        }
        List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(destTenantId))
                .listNewestByDestTenantIdAndDestObjectApiName(destTenantId, destObjectApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        Boolean flag = list.size() > 0;
        return Result2.newSuccess(flag);
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Override
    @Cached(expire = 30, cacheType = CacheType.LOCAL, localLimit = 1000)
    public Result2<List<SyncPloyDetailSnapshotData2>> listNewestEnableSyncPloyDetailsSnapshots(String sourceTenantId, String sourceObjectApiName, Integer tenantType, List<String> destTenantIds) {
        String sourceMasterObjectApiName = outerServiceFactory.get(tenantType).getMasterObjectApiName(sourceTenantId, tenantType, sourceObjectApiName).getData();
        List<SyncPloyDetailSnapshotEntity> resultData;
        if (Strings.isNullOrEmpty(sourceMasterObjectApiName)) {
            resultData = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId))
                    .listNewestBySourceTenantIdAndSrouceObjectApiName(sourceTenantId, sourceObjectApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        } else {
            resultData = new ArrayList<>();
            List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId))
                .listNewestBySourceTenantIdAndSrouceObjectApiName(sourceTenantId, sourceMasterObjectApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            for (SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity : list) {
                SyncPloyDetailData syncPloyDetailData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData();
                if (syncPloyDetailData == null) {
                    continue;
                }
                DetailObjectMappingsData detailObjectMappings = syncPloyDetailData.getDetailObjectMappings();
                if (detailObjectMappings == null) {
                    continue;
                }
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : detailObjectMappings) {
                    if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName)) {
                        resultData.add(syncPloyDetailSnapshotEntity);
                        break;
                    }
                }
            }
        }
        //试一下允许价目表明细直接按主对象对接，
        if (CollectionUtils.isEmpty(resultData) && ObjectApiNameEnum.FS_PRICEBOOKPRODUCT_OBJ.getObjApiName().equals(sourceObjectApiName)) {
            resultData = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId))
                    .listNewestBySourceTenantIdAndSrouceObjectApiName(sourceTenantId, sourceObjectApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        }
        if (CollectionUtils.isNotEmpty(resultData) && CollectionUtils.isNotEmpty(destTenantIds)){
            resultData = resultData.stream().filter(val-> destTenantIds.contains(val.getDestTenantId())).collect(Collectors.toList());
        }
        return Result2.newSuccess(BeanUtil2.deepCopyList(resultData, SyncPloyDetailSnapshotData2.class));
    }

    @Override
    @Cached(expire = 30, cacheType = CacheType.LOCAL, localLimit = 1000)
    public Result2<List<SyncPloyDetailSnapshotData2>> listEnableSyncPloyDetailByDestApiName(String sourceTenantId, String dest_object_api_name, Integer tenantType, List<String> destTenantIds) {
        String destMasterObjectApiName = outerServiceFactory.get(tenantType).getMasterObjectApiName(sourceTenantId, tenantType, dest_object_api_name).getData();
        List<SyncPloyDetailSnapshotEntity> resultData;
        if (Strings.isNullOrEmpty(destMasterObjectApiName)) {
            resultData = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId))
                    .listNewestByDestTenantIdAndDestObjectApiName(sourceTenantId, dest_object_api_name, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        } else {
            resultData = new ArrayList<>();
            List<SyncPloyDetailSnapshotEntity> list = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId))
                    .listNewestByDestTenantIdAndDestObjectApiName(sourceTenantId, destMasterObjectApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            for (SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity : list) {
                SyncPloyDetailData syncPloyDetailData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData();
                if (syncPloyDetailData == null) {
                    continue;
                }
                DetailObjectMappingsData detailObjectMappings = syncPloyDetailData.getDetailObjectMappings();
                if (detailObjectMappings == null) {
                    continue;
                }
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : detailObjectMappings) {
                    if (detailObjectMapping.getDestObjectApiName().equals(dest_object_api_name)) {
                        resultData.add(syncPloyDetailSnapshotEntity);
                        break;
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(resultData) && CollectionUtils.isNotEmpty(destTenantIds)){
            resultData = resultData.stream().filter(val-> destTenantIds.contains(val.getDestTenantId())).collect(Collectors.toList());
        }
        return Result2.newSuccess(BeanUtil2.deepCopyList(resultData, SyncPloyDetailSnapshotData2.class));
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Override
    public Result2<SyncPloyDetailSnapshotData2> getSyncPloyDetailSnapshotBySnapshotId(String tenantId, String snapshotId) {
        SyncPloyDetailSnapshotEntity entity = syncPloyDetailSnapshotManager.getEntryBySnapshotId(tenantId,snapshotId);
        return Result2.newSuccess(BeanUtil2.deepCopy(entity, SyncPloyDetailSnapshotData2.class));
    }
}
