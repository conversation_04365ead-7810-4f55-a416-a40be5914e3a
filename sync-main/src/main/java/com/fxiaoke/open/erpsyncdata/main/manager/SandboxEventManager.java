package com.fxiaoke.open.erpsyncdata.main.manager;

import cn.hutool.core.collection.CollUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MigrateTableManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SandboxEventManager {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private ErpU8EaiConfigDao erpU8EaiConfigDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private MigrateTableManager migrateTableManager;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpDbProxyConfigDao erpDbProxyConfigDao;
    @Autowired
    private ErpCustomInterfaceDao erpCustomInterfaceDao;
    @Autowired
    private ErpObjInterfaceCheckedDao erpObjInterfaceCheckedDao;
    @Autowired
    private ErpAlarmRuleDao erpAlarmRuleDao;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;

    public void copyErpSyncConfig(String from, String to) throws SQLException {
        log.info("copy erp sync config begin,from:{},to:{}", from, to);
        Map<String, String> dcIdMap = new HashMap<>();
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).listByTenantId(from);
        if (CollectionUtils.isNotEmpty(erpConnectInfoEntities)) {
            erpConnectInfoEntities = erpConnectInfoEntities.stream().
                    filter(entity -> entity.getChannel().getConnectorType() != ConnectorTypeEnum.OA)
                    .collect(Collectors.toList());
        }
        if (erpConnectInfoEntities.isEmpty()) {
            log.info("copy erp sync config end, empty, from:{},to:{}", from, to);
            return;
        }
        // 飞书连接器监听license创建,可能已经创建了,为了防止触发唯一索引,不创建同名连接器
        final List<String> dcNames = erpConnectInfoEntities.stream()
                .map(ErpConnectInfoEntity::getDataCenterName)
                .collect(Collectors.toList());
        final List<ErpConnectInfoEntity> oldErpConnectInfos = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).queryInfoByNames(to, dcNames);
        final Map<String, String> dcNameIdMap = oldErpConnectInfos.stream()
                .collect(Collectors.toMap(ErpConnectInfoEntity::getDataCenterName, ErpConnectInfoEntity::getId));

        final List<ErpConnectInfoEntity> insertInfos = erpConnectInfoEntities.stream()
                .filter(v -> {
                    final String oldId = dcNameIdMap.get(v.getDataCenterName());
                    if (Objects.nonNull(oldId)) {
                        dcIdMap.put(v.getId(), oldId);
                    }
                    return Objects.isNull(oldId);
                }).peek(v -> {
                    String newId = IdGenerator.get();
                    dcIdMap.put(v.getId(), newId);
                    v.setId(newId);
                    v.setTenantId(to);
                    //置空连接信息,pushApiNames可能出问题，暂时不解决。
                    v.setConnectParams(null);
                }).collect(Collectors.toList());

        int i1 = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(insertInfos);
        log.info("copy erp sync config,insert erp connect info ,{}", i1);
        //创建专表
        migrateTableManager.initTenantTable(to);
        copyErpObject(from, to, dcIdMap);
        copyErpObjectRelationship(from, to, dcIdMap);
        copyErpObjectField(from, to, dcIdMap);
        copyErpFieldExtend(from, to, dcIdMap);
        copyErpObjGroovy(from, to, dcIdMap);
        copyErpU8EaiConfig(from, to, dcIdMap);
        final Map<String, String> ployIdMap = copySyncPloyDetail(from, to, dcIdMap);
        copyErpCustomInterface(from, to, dcIdMap);
        copyErpObjInterfaceChecked(from, to, dcIdMap);
        copyErpDBProxyConfig(from, to, dcIdMap);
        copyErpAlarmRule(from, to, dcIdMap, ployIdMap);
        copyTenantConfig(from, to, dcIdMap, ployIdMap);
        log.info("copy erp sync config end,from:{},to:{}", from, to);
    }

    public void copyErpSyncData(final String from, final String to) {
        log.info("copy erp sync data begin,from:{},to:{}", from, to);
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).listByTenantId(from);
        if (CollectionUtils.isNotEmpty(erpConnectInfoEntities)) {
            erpConnectInfoEntities = erpConnectInfoEntities.stream().
                    filter(entity -> entity.getChannel().getConnectorType() != ConnectorTypeEnum.OA)
                    .collect(Collectors.toList());
        }
        if (erpConnectInfoEntities.isEmpty()) {
            log.info("copy erp sync data end, empty, from:{},to:{}", from, to);
            return;
        }

        copyConnectParams(to, erpConnectInfoEntities);
        log.info("copy erp sync ConnectParams end,from:{},to:{}", from, to);

        copyDataMapping(from, to);

        log.info("copy erp sync data end,from:{},to:{}", from, to);
    }

    private void copyConnectParams(final String to, final List<ErpConnectInfoEntity> erpConnectInfoEntities) {
        // 同步连接器连接参数
        final Map<Integer, String> numberMap = erpConnectInfoEntities.stream()
                .filter(v -> Objects.nonNull(v.getConnectParams()) && Objects.nonNull(v.getNumber()))
                .collect(Collectors.toMap(ErpConnectInfoEntity::getNumber, ErpConnectInfoEntity::getConnectParams));
        final List<ErpConnectInfoEntity> oldErpConnectInfos = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).listByTenantId(to);
        oldErpConnectInfos.stream()
                .filter(v -> numberMap.containsKey(v.getNumber()))
                .forEach(v -> {
                    v.setConnectParams(numberMap.get(v.getNumber()));
                    erpConnectInfoManager.updateById(to, v);
                });
    }

    private void copyDataMapping(final String from, final String to) {
        // 同步中间表
        // 分页获取中间表数据并插入
        String maxId = null;
        while (true) {
            final List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.pageTenantIdByMaxId(from, maxId, 1000);
            if (syncDataMappingsEntities.isEmpty()) {
                break;
            }
            maxId = syncDataMappingsEntities.get(syncDataMappingsEntities.size() - 1).getId();
            // 插入数据,先不考虑有中间表数据的情况
            syncDataMappingsEntities.forEach(v -> {
                v.setId(IdGenerator.get());
                v.setTenantId(to);
                v.setSourceTenantId(to);
                v.setDestTenantId(to);
            });
            syncDataMappingsDao.batchInsert(to, syncDataMappingsEntities);
        }
    }

    public void deleteErpSyncConfig(String tenantId) {
        log.info("delete erp sync config begin,tenantId:{}", tenantId);
        boolean allowDelete = true;
        try {
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
            if (!ConfigCenter.ERP_DOMAIN_URL.contains("ceshi112.com") && !ea.endsWith("_sandbox")) {
                //查找企业状态
                GetSimpleEnterpriseDataArg getSimpleEnterpriseDataArg = new GetSimpleEnterpriseDataArg();
                getSimpleEnterpriseDataArg.setEnterpriseAccount(ea);
                GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(getSimpleEnterpriseDataArg);
                if (simpleEnterpriseData != null && simpleEnterpriseData.getEnterpriseData() != null) {
                    allowDelete = simpleEnterpriseData.getEnterpriseData().getRunStatus() == 5;
                }
            }
        } catch (Exception ignore) {
            //转换异常的，允许处理
        }
        if (!allowDelete) {
            throw new ErpSyncDataException(I18NStringEnum.s169, tenantId);
        }
        deleteErpSettingNotJudgeSandbox(tenantId);
    }

    public void deleteErpSettingNotJudgeSandbox(String tenantId) {
        List<ErpConnectInfoEntity> erpConnectInfos = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
        if (erpConnectInfos.isEmpty()) {
            log.info("delete erp sync config end,tenantId:{}", tenantId);
            return;
        }
        for (ErpConnectInfoEntity erpConnectInfo : erpConnectInfos) {
            String dcId = erpConnectInfo.getId();
            int i = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, dcId);
            log.info("delete erp sync config,erpConnectInfo:{},row:{}", erpConnectInfo, i);
            i = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantIdAndDcId(tenantId, dcId);
            log.info("delete erp sync config,erpObjectDao,row:{}", i);
            i = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantIdAndDcId(tenantId, dcId);
            log.info("delete erp sync config,erpObjectRelationshipDao,row:{}", i);
            i = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantIdAndDcId(tenantId, dcId);
            log.info("delete erp sync config,erpObjectFieldDao,row:{}", i);
        }
        int i = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantId(tenantId);
        log.info("delete erp sync config,erpObjGroovyDao,row:{}", i);
        i = erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantId(tenantId);
        log.info("delete erp sync config,erpU8EaiConfigDao,row:{}", i);
        i = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantId(tenantId);
        log.info("delete erp sync config,erpFieldExtendDao,row:{}", i);
        syncDataMappingsDao.setTenantId(tenantId).truncateTable(tenantId);
        log.info("delete erp data mapping end,syncDataMappingsDao");
        i = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantId(tenantId);
        log.info("delete erp sync config,adminSyncPloyDetailDao,row:{}", i);
        i = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).invalidAllByTenantId(tenantId);
        log.info("delete erp sync config,adminSyncPloyDetailSnapshotDao,row:{}", i);
        migrateTableManager.renameTables(tenantId);
        log.info("delete erp sync config,renameTables");
        log.info("delete erp sync config end");
    }

    private void copyErpObject(String from, String to, Map<String, String> dcIdMap) {
        ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
        erpObjectEntity.setTenantId(from);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).queryList(erpObjectEntity);
        erpObjectEntities.forEach(v -> {
            v.setId(IdGenerator.get());
            v.setTenantId(to);
            v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int i2 = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(erpObjectEntities);
        log.info("copy erp sync config,insert ErpObjectEntity ,{}", i2);
    }

    private void copyErpObjectField(String from, String to, Map<String, String> dcIdMap) {
        ErpObjectFieldEntity entity2 = new ErpObjectFieldEntity();
        entity2.setTenantId(from);
        List<ErpObjectFieldEntity> entities2 = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).queryList(entity2);
        entities2.forEach(v -> {
            v.setId(IdGenerator.get());
            v.setTenantId(to);
            v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
        });
        //每次插入100条
        List<List<ErpObjectFieldEntity>> split = CollUtil.split(entities2, 100);
        for (List<ErpObjectFieldEntity> entities : split) {
            int i2 = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(entities);
            log.info("copy erp sync config,insert ErpObjectFieldEntity ,{}", i2);
        }
    }

    private void copyErpObjectRelationship(String from, String to, Map<String, String> dcIdMap) {
        ErpObjectRelationshipEntity entity = new ErpObjectRelationshipEntity();
        entity.setTenantId(from);
        List<ErpObjectRelationshipEntity> entities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).queryList(entity);
        entities.forEach(v -> {
            v.setId(IdGenerator.get());
            v.setTenantId(to);
            v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int i2 = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(entities);
        for (String dcId : dcIdMap.values()) {
            erpObjectRelationshipDao.invalidCacheErpObj(to, dcId);
        }
        log.info("copy erp sync config,insert ErpObjectRelationshipEntity ,{}", i2);
    }

    private void copyErpFieldExtend(String from, String to, Map<String, String> dcIdMap) {
        ErpFieldExtendEntity entity = new ErpFieldExtendEntity();
        entity.setTenantId(from);
        List<ErpFieldExtendEntity> entities = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).queryList(entity);
        entities.forEach(v -> {
            v.setId(IdGenerator.get());
            v.setTenantId(to);
            v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        //每次插入100条
        List<List<ErpFieldExtendEntity>> split = CollUtil.split(entities, 100);
        for (List<ErpFieldExtendEntity> entities2 : split) {
            int i2 = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(entities2);
            log.info("copy erp sync config,insert ErpFieldExtendEntity ,{}", i2);
        }
    }

    private void copyTenantConfig(String from, String to, Map<String, String> dcIdMap, Map<String, String> ployIdMap) {
        final ErpTenantConfigurationEntity record = new ErpTenantConfigurationEntity();
        record.setTenantId(from);
        final List<ErpTenantConfigurationEntity> entities = erpTenantConfigurationDao.queryList(record);
        long now = System.currentTimeMillis();
        Set<String> commonConfig = Sets.newHashSet("0", "ALL");
        final List<ErpTenantConfigurationEntity> collect = entities.stream()
                .filter(v -> {
                    String toId = null;
                    if (commonConfig.contains(v.getDataCenterId())) {
                        toId = v.getDataCenterId();
                    } else if (dcIdMap.containsKey(v.getDataCenterId())) {
                        toId = dcIdMap.get(v.getDataCenterId());
                    } else if (ployIdMap.containsKey(v.getDataCenterId())) {
                        toId = ployIdMap.get(v.getDataCenterId());
                    }

                    if (Objects.isNull(toId)) {
                        return false;
                    }

                    v.setDataCenterId(toId);
                    v.setId(IdGenerator.get());
                    v.setTenantId(to);
                    v.setCreateTime(now);
                    v.setUpdateTime(now);
                    return true;
                }).collect(Collectors.toList());
        // 理论上是新企业,不会有缓存,所以直接存到数据库,不需要清缓存
        int i2 = erpTenantConfigurationDao.batchInsert(collect);
        log.info("copy tenant_config,insert {}", i2);
    }

    private void copyErpCustomInterface(String from, String to, Map<String, String> dcIdMap) {
        final ErpCustomInterfaceEntity record = new ErpCustomInterfaceEntity();
        record.setTenantId(from);
        final List<ErpCustomInterfaceEntity> entities = erpCustomInterfaceDao.queryList(record);
        long now = System.currentTimeMillis();
        final List<ErpCustomInterfaceEntity> collect = entities.stream()
                .filter(v -> dcIdMap.containsKey(v.getDataCenterId()))
                .peek(v -> {
                    v.setId(IdGenerator.get());
                    v.setTenantId(to);
                    v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
                    v.setCreateTime(now);
                    v.setUpdateTime(now);
                }).collect(Collectors.toList());
        int i2 = erpCustomInterfaceDao.batchInsert(collect);
        log.info("copy erp_custom_interface,insert {}", i2);
    }

    private void copyErpObjInterfaceChecked(String from, String to, Map<String, String> dcIdMap) {
        final ErpObjInterfaceCheckedEntity record = new ErpObjInterfaceCheckedEntity();
        record.setTenantId(from);
        final List<ErpObjInterfaceCheckedEntity> entities = erpObjInterfaceCheckedDao.queryList(record);
        long now = System.currentTimeMillis();
        final List<ErpObjInterfaceCheckedEntity> collect = entities.stream()
                .filter(v -> dcIdMap.containsKey(v.getDataCenterId()))
                .peek(v -> {
                    v.setId(IdGenerator.get());
                    v.setTenantId(to);
                    v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
                    v.setCreateTime(now);
                    v.setUpdateTime(now);
                }).collect(Collectors.toList());
        int i2 = erpObjInterfaceCheckedDao.batchInsert(collect);
        log.info("copy erp_obj_interface_checked,insert {}", i2);
    }

    private void copyErpObjGroovy(String from, String to, Map<String, String> dcIdMap) {
        ErpObjGroovyEntity entity = new ErpObjGroovyEntity();
        entity.setTenantId(from);
        List<ErpObjGroovyEntity> entities = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).queryList(entity);
        entities.forEach(v -> {
            v.setId(IdGenerator.get());
            v.setTenantId(to);
            v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int i2 = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(entities);
        log.info("copy erp sync config,insert ErpObjGroovyEntity ,{}", i2);
    }

    private void copyErpAlarmRule(String from, String to, Map<String, String> dcIdMap, Map<String, String> ployIdMap) {
        final ErpAlarmRuleEntity record = new ErpAlarmRuleEntity();
        record.setTenantId(from);
        final List<ErpAlarmRuleEntity> entities = erpAlarmRuleDao.queryList(record);
        long now = System.currentTimeMillis();
        final List<ErpAlarmRuleEntity> collect = entities.stream()
                .filter(entity -> dcIdMap.containsKey(entity.getDataCenterId()))
                .peek(entity -> {
                    entity.setId(IdGenerator.get());
                    entity.setTenantId(to);
                    entity.setDataCenterId(dcIdMap.get(entity.getDataCenterId()));
                    final String oldPloyDetailIds = entity.getPloyDetailIds();
                    if (StringUtils.isNotBlank(oldPloyDetailIds) && !"all".equals(oldPloyDetailIds)) {
                        final String newPloyIds = Splitter.on(";").splitToList(oldPloyDetailIds)
                                .stream()
                                .map(ployIdMap::get)
                                .collect(Collectors.joining(";"));
                        entity.setPloyDetailIds(newPloyIds);
                    }
                    entity.setCreateTime(now);
                    entity.setUpdateTime(now);
                }).collect(Collectors.toList());
        int i2 = erpAlarmRuleDao.batchInsert(collect);
        log.info("copy erp_alarm_rule,insert {}", i2);
    }

    private void copyErpDBProxyConfig(String from, String to, Map<String, String> dcIdMap) {
        final List<ErpDBProxyConfigEntity> erpDBProxyConfigEntities = erpDbProxyConfigDao.queryByTenantId(from);
        long now = System.currentTimeMillis();
        final List<ErpDBProxyConfigEntity> collect = erpDBProxyConfigEntities.stream()
                .filter(v -> dcIdMap.containsKey(v.getDataCenterId()))
                .peek(entity -> {
                    entity.setId(IdGenerator.get());
                    entity.setTenantId(to);
                    entity.setDataCenterId(dcIdMap.get(entity.getDataCenterId()));
                    entity.setCreateTime(now);
                    entity.setUpdateTime(now);
                }).collect(Collectors.toList());
        int i2 = erpDbProxyConfigDao.batchInsert(collect);
        log.info("copy erp_dbproxy_config,insert {}", i2);
    }

    private void copyErpU8EaiConfig(String from, String to, Map<String, String> dcIdMap) {
        ErpU8EaiConfigEntity entity = new ErpU8EaiConfigEntity();
        entity.setTenantId(from);
        List<ErpU8EaiConfigEntity> entities = erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).queryList(entity);
        final List<ErpU8EaiConfigEntity> collect = entities.stream()
                .filter(v -> dcIdMap.containsKey(v.getDataCenterId()))
                .peek(v -> {
                    v.setId(IdGenerator.get());
                    v.setTenantId(to);
                    v.setDataCenterId(dcIdMap.get(v.getDataCenterId()));
                    long now = System.currentTimeMillis();
                    v.setCreateTime(now);
                    v.setUpdateTime(now);
                }).collect(Collectors.toList());
        int i2 = erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).batchInsert(collect);
        log.info("copy erp sync config,insert ErpU8EaiConfigEntity ,{}", i2);
    }

    private Map<String, String> copySyncPloyDetail(String from, String to, Map<String, String> dcIdMap) {
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(from)).listByTenantId(from);
        Map<String, String> ployIdMap = new HashMap<>();
        if (syncPloyDetailEntities.isEmpty()) {
            log.info("copy erp sync config,insert SyncPloyDetailEntity empty");
            return ployIdMap;
        }
        syncPloyDetailEntities.forEach(v -> {
            final String oldId = v.getId();
            v.setId(IdGenerator.get());
            ployIdMap.put(oldId, v.getId());

            v.setTenantId(to);
            v.setSourceDataCenterId(dcIdMap.get(v.getSourceDataCenterId()));
            v.setDestDataCenterId(dcIdMap.get(v.getDestDataCenterId()));
            v.setStatus(SyncPloyDetailStatusEnum.DISABLE.getStatus());
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int i = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(to)).insertList(syncPloyDetailEntities);
        log.info("copy erp sync config,insert SyncPloyDetailEntity ,{}", i);
        return ployIdMap;
    }
}
