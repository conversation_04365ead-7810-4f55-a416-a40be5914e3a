package com.fxiaoke.open.erpsyncdata.main.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/14 16:30:08
 */
@Component
@Slf4j
public class SyncLockManager {
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ConfigCenterConfig configCenterConfig;

    private static final String lockDataFormat = "erpdss_lock:sync_data:%s:%s:%s";

    private String getLockKey(String tenantId, String objectApiName, String syncDataId)
    {
        return String.format(lockDataFormat, tenantId, objectApiName, syncDataId);
    }

    public boolean tryLock(String tenantId, String objectApiName, String syncDataId) {
        if (!configCenterConfig.needLockSyncData(tenantId)) {
            return true;
        }
        final String syncLockName = getLockKey(tenantId, objectApiName, syncDataId);
        try {
            return redissonClient.getLock(syncLockName).tryLock(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("tryLock error, lockName:{}", syncLockName, e);
            return false;
        }
    }

    public void unlock(String tenantId, String objectApiName, String syncDataId)
    {
        if (!configCenterConfig.needLockSyncData(tenantId)) {
            return;
        }
        final String syncLockName = getLockKey(tenantId, objectApiName, syncDataId);
        try {
            redissonClient.getLock(syncLockName).unlock();
        } catch (Exception e) {
            log.warn("unlock error key:{}", syncLockName, e);
        }
    }

    /**
     * 检测数据是否正在同步
     */
    public boolean checkDataIsBeingSynchronized(String tenantId, String objectApiName, String syncDataId) {
        if (!configCenterConfig.needLockSyncData(tenantId)) {
            return false;
        }
        // 检查是否有对应的key
        final String syncLockName = getLockKey(tenantId, objectApiName, syncDataId);
        return redissonClient.getLock(syncLockName).isLocked();
    }
}
