package com.fxiaoke.open.erpsyncdata.main.service;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AutoBindEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDepartmentOrPersonnelService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("allModelDubboService")
public class AllModelDubboServiceImpl implements AllModelDubboService {
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private SyncMainService syncMainService;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;


    @Override
    public Result2<Void> batchSendEventData2DispatcherMq(BatchSendEventDataArg arg) {
        // 因为dubbo-rest用的FastJson,不支持传null的参数,这里需要将cleanFields值放到ObjectData中
        arg.getEventDatas().stream()
                .filter(event -> CollectionUtils.isNotEmpty(event.getCleanFields()))
                .forEach(event -> {
                    event.getCleanFields().forEach(field -> event.getSourceData().put(field, null));
                    event.getCleanFields().clear();
                });

        return eventTriggerService.batchSendEventData2DispatcherMq(arg);
    }

    @Override
    public Result2<Void> batchSendEventData2DispatcherMqByContext(List<SyncDataContextEvent> syncDataContextEvents) {
        // 因为dubbo-rest用的FastJson,不支持传null的参数,这里需要将cleanFields值放到ObjectData中
        syncDataContextEvents.forEach(SyncDataContextEvent::addCrmCleanFields);

        return eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
    }

    @Override
    public Result2<ObjectDataSyncMsg> syncDataMain(SyncDataContextEvent eventData) {
        //避免组装logId出现错误
        try {
            TraceUtil.initTrace(eventData.getSyncLogId());
            LogIdUtil.reset(eventData.getSyncLogId());
            LogIdUtil.setSourceObjApiName(eventData.getSourceData().getApiName());
            LogIdUtil.setDataCenterId(eventData.getSourceDataCenterId(), true);
            String realApiName= idFieldConvertManager.getRealObjApiName(eventData.getSourceData().getTenantId(), eventData.getSourceData().getApiName());
            LogIdUtil.setRealObjApiName(realApiName);
        } catch (Exception e) {
            log.warn("sync dispatch processor logId exception:{}",e.getMessage());
        }
        return syncMainService.syncDataMain(eventData);
    }

    @Override
    public void autoBindEmployeeMapping(AutoBindEmployeeMapping.Arg arg) {
        syncDepartmentOrPersonnelService.autoBindEmployeeMapping(arg);
    }
}
