package com.fxiaoke.open.erpsyncdata.main.manager.init;

import cn.hutool.core.collection.ListUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 全网发布后，该类可删除。
 * <AUTHOR> (^_−)☆
 * @date 2023-08-08
 */
@Component
@Slf4j
public class BOMV1WhiteGroupInitializer extends Initializer {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;

    @Override
    void init() {
        ErpTenantConfigurationEntity global = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.BOM_V1_TENANTS.name());
        if (global != null) {
            log.info("BOMV1WhiteGroup has bean init before {}", global.getConfiguration());
            return;
        }
        //查找对接到crm的预设BOM的企业,非K3的也在里面了但是不管，不然不好判断。两个方向都算
        List<String> tenantIdList = adminSyncPloyDetailDao.queryTenantIdByDestCRMObjectApiName("BOMObj");
        List<String> tenantIdList2 = adminSyncPloyDetailDao.queryTenantIdBySourceCRMObjectApiName("BOMObj");
        tenantIdList.addAll(tenantIdList2);
        tenantIdList = tenantIdList.stream().distinct().collect(Collectors.toList());
        String tenantStr = Joiner.on(";").join(tenantIdList);
        tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.BOM_V1_TENANTS.name(), tenantStr);
        log.info("BOMV1WhiteGroup init {}", tenantStr);
    }

    public BOMV1WhiteGroupInitializer() {
        super(true);
    }
}
