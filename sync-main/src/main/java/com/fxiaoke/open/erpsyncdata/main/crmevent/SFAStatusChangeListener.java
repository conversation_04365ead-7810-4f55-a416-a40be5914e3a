package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.common.mq.AbstractMqConsumer;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.main.crmevent.data.SfaMultiEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


@Slf4j
public class SFAStatusChangeListener extends AbstractMqConsumer<SfaMultiEvent> {
    //erp-sync-listen-enterprise
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    public static String SPU_VALUE="spu";

    public SFAStatusChangeListener(String rocketMQConsumerConfigName, String nameserver, String consumerGroup, String topic) {
       super(rocketMQConsumerConfigName, nameserver, consumerGroup, topic);
    }

    @Override
    public void processMessage(SfaMultiEvent enterpriseRunStatusEvent) throws Throwable {
        try {
                log.info("body msg :{}",enterpriseRunStatusEvent);
                if(!SPU_VALUE.equals(enterpriseRunStatusEvent.getModuleCode())){
                    log.info("sfaStatusChangeListener erp value not equals spu,tenantId:{}", enterpriseRunStatusEvent);
                    return;
                }
                String tenantIdValue=enterpriseRunStatusEvent.getTenantId();
                List<ErpConnectInfoEntity> erpConnectInfos = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantIdValue))
                        .listByTenantId(tenantIdValue);
                if (erpConnectInfos.isEmpty()) {//moduleCode为spu表示商品开关有变化
                    log.info("sfaStatusChangeListener erp sync config end,tenantId:{}", enterpriseRunStatusEvent);
                    return;
                }
                sfaApiManager.querySpuOpenStatus(enterpriseRunStatusEvent.getTenantId(),true);
        } catch (Exception e) {
            log.error("eServiceChangeListener consumeMessage failed. msgs:{}, ", enterpriseRunStatusEvent);
        }
    }
}
