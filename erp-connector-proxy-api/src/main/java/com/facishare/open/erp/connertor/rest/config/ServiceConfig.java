package com.facishare.open.erp.connertor.rest.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:16:23
 */
@Data
public class ServiceConfig {
    private String serviceKey;

    private String desc;

    private String address;

    // private String codec;

    private int socketTimeOut=10000;

    private int connectionTimeOut=2000;

    // private String serviceConfigName;

    public void setAddress(final String address) {
        // 固定结尾为'/'
        this.address = address.endsWith("/") ? address : (address + "/");
    }
}
