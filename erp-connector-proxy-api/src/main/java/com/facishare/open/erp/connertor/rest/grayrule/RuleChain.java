package com.facishare.open.erp.connertor.rest.grayrule;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * 基于多个规则进行匹配
 * Created by lirui on 2016-08-05 13:09.
 */
@ToString
public class RuleChain {
    /**
     * 是否是默认禁止访问
     */
    private final boolean defaultDeny;
    /**
     * 多个企业配置信息
     */
    private final Multimap<String, MatchRule> rules;

    public RuleChain(boolean defaultDeny) {
        this.defaultDeny = defaultDeny;
        this.rules = ArrayListMultimap.create();
    }

    public void add(String rule) {
        String key = StringUtils.substringBefore(rule, ".");
        MatchRule subRule = new MatchRule(StringUtils.substringAfter(rule, "."));
        this.rules.put(key, subRule);
    }

    /**
     * 循环遍历所有的rule，找到第一个匹配的就退出，所以在配置规则的时候，尽量匹配范围大的放在前面
     *
     * @param rules 所有的规则
     */
    private boolean matches(final String[] keys, final Collection<MatchRule> rules) {
        return rules.stream().anyMatch(matchRule -> matchRule.matches(keys, 1));
    }

    public boolean isAllow(final String[] keys) {
        Collection<MatchRule> eidRules = rules.get("*");
        if (CollectionUtils.isNotEmpty(eidRules) && matches(keys, eidRules)) {
            return defaultDeny;
        }

        if (Objects.equals("*", keys[0]) || StringUtils.isBlank(keys[0])) {
            return !defaultDeny;
        }

        eidRules = rules.get(keys[0]);
        if (CollectionUtils.isNotEmpty(eidRules) && matches(keys, eidRules)) {
            return defaultDeny;
        }
        return !defaultDeny;
    }
}
