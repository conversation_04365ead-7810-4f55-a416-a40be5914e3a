package com.facishare.open.erp.connertor.rest.grayrule;

import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 15:04:40
 */
@Slf4j
@ToString
public class RouteGrayRule {
    private static final CharMatcher INVISIBLE = new CharMatcher() {
        @Override
        public boolean matches(char c) {
            if (c <= ' ') {
                return true;
            } else if (c == 160 || c == '\ufeff') {
                return true;
            } else if (c >= '\u202a' && c <= '\u202e') {
                return true;
            } else if (c >= '\u200b' && c <= '\u200f') {
                return true;
            } else if (c >= '\u2060' && c <= '\u206f') {
                return true;
            } else {
                return false;
            }
        }
    };
    private final String rule;
    private final RuleChain chain;

    public RouteGrayRule(String rule) {
        this.chain = initChain(rule);
        // 避免大段文本占据几M内存
        this.rule = rule.length() > 512 ? rule.substring(0, 512) + "..." : rule;
    }

    private RuleChain initChain(final String rule) {
        String gray = INVISIBLE.removeFrom(rule);
        boolean defaultDeny = true;
        if ("deny".equals(gray) || gray.startsWith("black")) {
            defaultDeny = false;
        }
        RuleChain rules = new RuleChain(defaultDeny);
        int index = CharMatcher.anyOf(";:").indexIn(gray);
        if (index > 0) {
            String access = gray.substring(index + 1);
            Splitter.on(CharMatcher.anyOf("|;")).omitEmptyStrings().split(access)
                    .forEach(rules::add);
        } else {
            List<String> words = Lists.newArrayList("black", "white", "deny", "allow");
            if (words.stream().anyMatch(gray::contains)) {
                rules.add("*");
            }
        }
        return rules;
    }

    public boolean isAllow(String... keys) {
        if (keys == null || keys.length == 0) {
            return false;
        }
        try {
            return chain.isAllow(keys);
        } catch (Exception e) {
            log.error("error when check keys={}", Arrays.toString(keys), e);
            return false;
        }
    }
}
