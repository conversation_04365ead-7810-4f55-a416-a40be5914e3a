package com.facishare.open.erp.connertor.service;

import com.facishare.open.erp.connertor.codec.AesCodec;
import com.facishare.open.erp.connertor.rest.annotation.RestResource;
import com.facishare.open.erp.connertor.sdk.model.GetById;
import com.facishare.open.erp.connertor.sdk.model.ListByTime;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/3/27 19:50:43
 */
@RestResource(value = "ErpConnectorProxy", desc = "调用国外的erp", codec = AesCodec.class)
public interface ConnectorService {
    @RequestMapping("/{channel}/getById")
    GetById.Result getById(@PathVariable("channel") String channel, @RequestBody GetById.Arg arg);

    @RequestMapping("/{channel}/listByTime")
    ListByTime.Result listByTime(@PathVariable("channel") String channel, @RequestBody ListByTime.Arg arg);
}
