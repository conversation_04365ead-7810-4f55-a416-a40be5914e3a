package com.facishare.open.erp.connertor.linkedin;

import com.facishare.open.erp.connertor.codec.AesCodec;
import com.facishare.open.erp.connertor.rest.annotation.RestResource;
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetAllForm;
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetFormMetaData;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/5/10 19:53:38
 */
@RestResource(value = "ErpConnectorProxy", desc = "调用国外的erp", codec = AesCodec.class)
public interface LinkedinService {

    @RequestMapping("/linkedin/getAllForm")
    GetAllForm.Result getAllForm(@RequestBody GetAllForm.Arg arg);

    @RequestMapping("/linkedin/getFormMetaData")
    GetFormMetaData.Result getFormMetaData(@RequestBody GetFormMetaData.Arg arg);
}
