package com.facishare.open.erp.connertor.rest.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/3/22 09:31:01
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.PARAMETER})
public @interface GrayKeys {
    /**
     * 在方法上的时候使用,按顺序写参数名(暂时没有spel需求)
     */
    String[] value() default {};
}
