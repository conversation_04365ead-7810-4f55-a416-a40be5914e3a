package com.fxiaoke.open.erpsyncdata.common.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.common.util.NeedRetryCenterUtils;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.List;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;

@Slf4j
public abstract class AbstractBatchMqConsumer<T> {
    protected Class<T> messageClass;
    protected String rocketMQConsumerConfigName;

    @Setter
    protected String sectionNames;
    private String consumerGroup;
    private AutoConfMQPushConsumer autoConfRocketMQProcessor;

    public AbstractBatchMqConsumer(Class<T> messageClass) {
        this.messageClass = messageClass;
    }

    public void init() {
        MessageListenerOrderly listener = (msgs, context) -> {
            Thread.currentThread().setName("Comsumer-" + this.getClass().getSimpleName() + "-" + IdUtil.generateId().substring(0, 6));
            String topic = null;
            String tags = null;
            MessageHelper.fillContextFromMessage(TraceContext.get(), msgs.get(0));
            List<T> messages = new ArrayList<>(100);
            try {
                for (MessageExt message : msgs) {
                    T messageObject = null;
                    checkOrSetTraceId();
                    messageObject = parseObject(message);
                    if (message.getTopic().equals("TOPIC_CRM_OPENAPI") || message.getTopic().equals("object-data") || message.getTopic().equals("object-describe")) {
                        log.trace("comsumer msg msgId={} topic={} tags={} flag={} messageObject={}", message.getMsgId(), message.getTopic(), message.getTags(), message.getFlag(), messageObject);
                    } else {
                        log.info("comsumer msg msgId={} topic={} tags={} flag={} messageObject={}", message.getMsgId(), message.getTopic(), message.getTags(), message.getFlag(), messageObject);
                    }
                    messages.add(messageObject);
                }
                batchProcessMessage(messages);
            } catch (Throwable e) {
               // 这里拦截pg的报错。上报bizlog。记录重试
                //配置项的报错才记录重试。
                handlerException(messages, e);
                Object infoMsg = msgs;
                if (messages != null) {
                    infoMsg = messages;
                }
                log.warn(
                    "error " + e.getMessage() + " comsumer msg first msgId=" + msgs.get(0).getMsgId() + " topic=" + msgs.get(0).getTopic() + " tags=" + msgs.get(0).getTags() + " flag=" + msgs.get(0)
                        .getFlag() + " msgs=" + infoMsg + "", e);
            } finally {
                removeTrace();
            }
            return ConsumeOrderlyStatus.SUCCESS;
        };
        autoConfRocketMQProcessor = new AutoConfMQPushConsumer(rocketMQConsumerConfigName, sectionNames, listener);
        if (!Strings.isNullOrEmpty(consumerGroup)) {
            autoConfRocketMQProcessor.setGroupNameKey(consumerGroup);
        }
        autoConfRocketMQProcessor.start();
        log.info("[" + this.getClass().getName() + "] [init] [success] [autoConfRocketMQProcessor:{}] ", autoConfRocketMQProcessor);
    }

    protected T parseObject(Message message) throws Throwable {
        return JSON.parseObject(message.getBody(), messageClass, Feature.IgnoreNotMatch);
    }

    protected abstract int batchProcessMessage(List<T> messages) throws Throwable;

    protected abstract void handlerException(List<T> messages,Throwable throwable) ;

    public void setRocketMQConsumerConfigName(String rocketMQConsumerConfigName) {
        this.rocketMQConsumerConfigName = rocketMQConsumerConfigName;
    }

    public void setConsumerGroup(String consumerGroup) {
        this.consumerGroup = consumerGroup;
    }

    private void checkOrSetTraceId() {
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "mq_" + System.currentTimeMillis();
            context.setTraceId(traceId);
        }
        MDC.put("traceId", traceId);
    }

    public void removeTrace() {
        TraceContext.remove();
        MDC.remove("traceId");
    }
}
