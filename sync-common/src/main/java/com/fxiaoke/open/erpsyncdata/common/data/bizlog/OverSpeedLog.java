package com.fxiaoke.open.erpsyncdata.common.data.bizlog;

import io.protostuff.Tag;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;


/**
 * 限速日志记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OverSpeedLog {
    @Tag(1)
    @Builder.Default
    private String logType = "erpdss-overspeed";
    @Tag(2)
    private long stamp;
    @Tag(3)
    @Builder.Default
    private String appName="erpdss";
    @Tag(7)
    private String tenantId;

    @Tag(51)
     /*** 写入类型：TO_CRM 或 TO_ERP */
    private String writeType;
    @Tag(52)
    private String configType;
    @Tag(151)
     /** 是否批量写入*/
    private int isBatch;   
    @Tag(152)
    /*** 是否对象独立限速 */
    private int isIndependentLimit; 

} 