package com.fxiaoke.open.erpsyncdata.common.data.bizlog;

import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.fxiaoke.log.GenericBizLog.*;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServerStatusLog {
    @Tag(LOGTYPE_FIELD_NUMBER)
    @Builder.Default
    private String logType = "erpdss-server-status";
    @Tag(STAMP_FIELD_NUMBER)
    @Builder.Default
    private long stamp = System.currentTimeMillis();
    @Tag(APP_FIELD_NUMBER)
    @Builder.Default
    private String app = ProcessInfo2.appName;
    /**
     *
     */
    @Tag(PROFILE_FIELD_NUMBER)
    @Builder.Default
    private String profile = ProcessInfo2.profile;
    /**
     * 发起检测的服务ip
     */
    @Tag(SERVERIP_FIELD_NUMBER)
    @Builder.Default
    private String serverIp = ProcessInfo2.serverIp;
    /**
     * 地址
     */
    @Tag(STRING1_FIELD_NUMBER)
    private String url;

    /**
     * 地址
     */
    @Tag(STRING2_FIELD_NUMBER)
    private String name;
    /**
     * 地址
     */
    @Tag(TYPE1_FIELD_NUMBER)
    private String type;
    /**
     * 状态码
     */
    @Tag(INT1_FIELD_NUMBER)
    private Integer statusCode;
}
