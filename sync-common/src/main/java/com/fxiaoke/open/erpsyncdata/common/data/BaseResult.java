package com.fxiaoke.open.erpsyncdata.common.data;

import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;

import java.io.Serializable;

import lombok.Data;

@Data
public abstract class BaseResult implements Serializable {
    protected int errCode = ResultCodeEnum.SUCCESS.getErrCode();
    protected String errMsg = "success";

    public boolean isSuccess() {
        return this.errCode == ResultCodeEnum.SUCCESS.getErrCode();
    }
}
