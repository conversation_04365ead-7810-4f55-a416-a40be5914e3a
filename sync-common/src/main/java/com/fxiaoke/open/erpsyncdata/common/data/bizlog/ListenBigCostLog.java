package com.fxiaoke.open.erpsyncdata.common.data.bizlog;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListenBigCostLog {

        @Tag(1)
        @Builder.Default
        private String logType = "erpdss-listen-cost";
        @Tag(2)
        private long stamp;
        @Tag(3)
        @Builder.Default
        private String appName="erpdss";
        @Tag(7)
        private String tenantId;

        @Tag(51)
        private String podName;//(System.getenv("HOSTNAME"))  // 获取当前pod名称
        @Tag(52)
        private String threadId;//(String.valueOf(Thread.currentThread().getId())) // 获取当前线程ID
        @Tag(201)
        private long listenCost = 0;
}
