package com.fxiaoke.open.erpsyncdata.common.ibatis;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * 仅有一个insert方法
 *
 * @param <T>
 */
public interface TenantBaseMapper<T> {
    /**
     * 插入记录,返回值不是id, 使用object的getId方法获得id
     *
     * @return 影响行数
     */
    @InsertProvider(type = CrudProvider.class, method = "insert")
    int insert(T t);

    /**
     * 忽略结果
     *
     * @param t
     * @return
     */
    @InsertProvider(type = CrudProvider.class, method = "insertIngore")
    int insertIgnore(T t);

    /**
     * 批量插入记录
     *
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = CrudProvider.class, method = "batchInsert")
    @Result(javaType = int.class)
    int batchInsert(@Param("tenantId") String tenantId, @Param(CrudProvider.VALUE_KEY) List<T> record);

    @UpdateProvider(type = CrudProvider.class, method = "update")
    @Result(javaType = int.class)
    int update(T t);
}
