package com.fxiaoke.open.erpsyncdata.common.data.bizlog;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncDataErrLog {
    @Tag(1)
    @Builder.Default
    private String logType = "erpdss-sync-stat";
    @Tag(2)
    private long stamp;
    @Tag(3)
    private String appName="erpdss";
    @Tag(7)
    private String tenantId;
    @Tag(10)
    private String objectApiName;
    //string1
    @Tag(51)
    private String syncErrMsg;
    @Tag(151)
    //0-no error, -1-other error  1-写入目标系统失败, 2-调用同步前中后函数失败
    private int errType;
}
