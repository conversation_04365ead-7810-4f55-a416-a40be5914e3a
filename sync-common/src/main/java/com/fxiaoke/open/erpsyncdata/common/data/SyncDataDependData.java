package com.fxiaoke.open.erpsyncdata.common.data;

import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:23 2021/11/11
 * @Desc:
 */
@Data
@Builder
public class SyncDataDependData implements Serializable {
    public String ployDetailId;
    public String tenantId;
    public String objApiName;
    public String dataId;
    public String syncDataId;
    /**
     * 多语
     */
    private String locale;

    public List<DependMappingData> dependMappingDataList;

    @Data
    @Builder
    public static class DependMappingData implements Serializable{
        public List<String> dependFields;
        public String dependSourceApiName;
        public String dependDataId;
        public String dependDestApiName;
        public SyncDataException exception;
    }

}
