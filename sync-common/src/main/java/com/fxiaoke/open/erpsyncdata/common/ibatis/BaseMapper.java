package com.fxiaoke.open.erpsyncdata.common.ibatis;

import com.github.mybatis.annotation.AutoResultMap;
import java.io.Serializable;
import java.util.List;

import org.apache.ibatis.annotations.*;

public interface BaseMapper<T> {

    /**
     * 插入记录,返回值不是id, 使用object的getId方法获得id
     *
     * @return 影响行数
     */
    @InsertProvider(type = CrudProvider.class, method = "insert")
    int insert(T t);

    @InsertProvider(type = CrudProvider.class, method = "insertIngore")
    int insertIngore(T t);

    /**
     * 插入记录并设置t的id属性为数据库的自增ID,返回值是修改的记录行数 使用object的getId方法获得id
     *
     * @return 影响行数
     */
    @InsertProvider(type = CrudProvider.class, method = "insert")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertAndSetObjectId(T t);

    /**
     * 根据主键删除记录
     */
    @DeleteProvider(type = CrudProvider.class, method = "deleteById")
    @AutoResultMap
    int deleteById(Serializable id);

    @DeleteProvider(type = CrudProvider.class, method = "deleteByIds")
    @AutoResultMap
    int deleteByIds(@Param(CrudProvider.PARA_KEY) List<? extends Serializable> ids);

    @UpdateProvider(type = CrudProvider.class, method = "update")
    int update(T var1);
}
