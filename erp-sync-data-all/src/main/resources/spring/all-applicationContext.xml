<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <import resource="all-common.xml"/>
    <import resource="all-dubbo-provider.xml"/>
    <import resource="all-dubbo-consumer.xml"/>
    <import resource="erp-sync-data-all-rest-client.xml"/>
    <!--mq生产者配置-->
    <import resource="all-mq-producer.xml"/>
    <!--paasMq消费者-->
    <import resource="classpath*:spring/fseventtrigger-mq.xml"/>
    <!--db连接信息-->
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/syncmain-context.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <import resource="classpath*:spring/dispatcher-context.xml"/>
    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>
</beans>