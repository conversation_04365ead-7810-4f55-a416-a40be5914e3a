<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://code.alibabatech.com/schema/dubbo
           http://code.alibabatech.com/schema/dubbo/dubbo.xsd">


    <dubbo:consumer timeout="30000" filter="tracerpc" retries="0"/>

    <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService"/>

    <dubbo:reference id="ssoLoginService" interface="com.facishare.userlogin.api.service.SSOLoginService"/>

    <dubbo:reference id="oAConnParamService" interface="com.fxiaoke.open.oasyncdata.service.OAConnParamService" mock="return null"/>
</beans>