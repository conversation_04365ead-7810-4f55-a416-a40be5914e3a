<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--    turboFilter会按照配置的顺序执行。作用于全局-->
    <turboFilter class="com.fxiaoke.open.erpsyncdata.dbproxy.manager.LogConsoleStrategyFilter" ></turboFilter>
    <!--ThreadContext中如果有colorEiLog,,且带的值是YES,就会打印所有日志.相当于对EI染色 -->
    <turboFilter class="com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErrLogFilter">
        <Key>colorized_tenant</Key>
        <DefaultThreshold>ERROR</DefaultThreshold>
        <OnHigherOrEqual>ACCEPT</OnHigherOrEqual>
        <OnLower>NEUTRAL</OnLower>
        <MDCValueLevelPair>
            <value>1</value>
            <level>DEBUG</level>
        </MDCValueLevelPair>
    </turboFilter>
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/fs-app.log</file>
        <!-- 滚动生成日志，最多 20 个，最大500M，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fs-app-%d{yyyyMMdd}.%i.log</fileNamePattern>
            <!-- 单个文件最大500MB，最多存留30天，最大存留8GB -->
            <maxFileSize>500MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <!--去掉class打印，保留logger-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{20}]:[%line] %X{traceId} %X{colorEi} %msg%rEx{
                full,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!--记录warn级别以上日志-->
    <appender name="ErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <File> ${catalina.home}/logs/error.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread]- %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log</FileNamePattern>
            <!-- 单个文件最大500MB，最多存留30天，最大存留8GB -->
            <maxFileSize>500MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 配置基础组件为ERROR级别，避免打印过多影响服务自己日志 -->
    <logger name="org.apache" level="ERROR"/>
    <logger name="com.github" level="ERROR"/>
    <logger name="Access" level="INFO"/>
    <logger name="com.facishare.uc.api.cache" level="ERROR"/>

    <logger level="ERROR" name="com.fxiaoke.open.erpsyncdata.apiproxy.k3cloud" additivity="true"/>
    <logger level="INFO" name="com.fxiaoke.open.erpsyncdata" additivity="true"/>

    <logger name="com.fxiaoke.dispatcher.mq" level="WARN"/>
    <logger name="com.fxiaoke.dispatcher.processor" level="WARN"/>
    <logger name="com.fxiaoke.dispatcher.store" level="WARN"/>
    <logger name="com.fxiaoke.dispatcher.processor.LockService" level="WARN"/>
    <logger name="com.facishare.function" level="ERROR"/>
    <logger name="org.mongodb.driver" level="ERROR"/>
    <logger name="com.fxiaoke.i18n" level="ERROR"/>
    <logger name="com.xxl.job" level="ERROR"/>
    <logger name="com.alibaba" level="ERROR"/>
    <logger name="com.facishare.paas" level="ERROR"/>
    <logger name="com.fxiaoke.common.StopWatch" level="ERROR"/>
    <logger name="com.facishare.uc" level="ERROR"/>
    <logger name="org.apache.kafka" level="ERROR"/>
    <logger name="xxl-job logger" level="ERROR"/>
    <logger name="com.fxiaoke.notifier" level="ERROR"/>
    <logger name="redis." level="ERROR"/>

    <logger name="com.fxiaoke.common.http." level="WARN"/>
    <logger name="com.alicp.jetcache.support.BroadcastManager" level="ERROR"/>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC_ROLLING" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFile"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC_ROLLING"/>
        <appender-ref ref="ErrorLog"/>
    </root>

</configuration>