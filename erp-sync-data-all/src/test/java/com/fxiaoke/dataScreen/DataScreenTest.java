package com.fxiaoke.dataScreen;

import com.alibaba.druid.pool.DataSourceNotAvailableException;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.GetConnectionTimeoutException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.organization.adapter.api.model.EnterpriseConfigKey;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.log.dto.DataScreenSyncLogDTO;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.BIDashboardEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen.BIManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen.DataScreenArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.converter.manager.ReSyncDataNodeManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataMonitorScreenDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import com.fxiaoke.open.erpsyncdata.writer.manager.NodeCompleteDataWriteManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Ignore;
import org.junit.Test;

import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/9/24 17:40
 * @Version 1.0
 */
@Ignore
@Slf4j
public class DataScreenTest extends BaseTest {
    @Autowired
    private BIManager biManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private NodeCompleteDataWriteManager completeDataWriteManager;
    @Autowired
    private ReSyncDataNodeManager reSyncDataNodeManager;
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private EnterpriseConfigService enterpriseConfigService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ConnectInfoService connectInfoService;



//    @Ignore
//    @Test
//    public void testSqlQuery() throws IllegalAccessException {
//        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).listErpDcExcludeOAByTenantId("88521");
//        erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).listByTenantIdAndIdsExcludeCrm("88521", null);
//
//        String dataCenterId="643f7322b54ea80001767d86";
//        List<String> crmObj=Lists.newArrayList("ProductObj");
//        List<SyncPloyDetailEntity> detailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).queryByDcIdByCrmObjApiNames("88521", dataCenterId, crmObj, 1);
//
//
//        //创建表达式解析器
//        ExpressionParser expressionParser = new SpelExpressionParser();
//        //创建数据上下文
//        StandardEvaluationContext evaluationContext = new StandardEvaluationContext();
//        //设置变量
////        evaluationContext.setVariable("a", 12);
////        evaluationContext.setVariable("b", 34);
////        evaluationContext.setVariable("c", 56);
//        SyncDataContextEvent erpContext=new SyncDataContextEvent();
////        List<SyncDataContextEvent.WriteResult> writeResults=Lists.newArrayList();
////        SyncDataContextEvent.WriteResult writeResult=new SyncDataContextEvent.WriteResult();
////        writeResult.setDestDataId("xxxxxxxxxxxxxxx");
////        writeResults.add(writeResult);
////        erpContext.setDetailWriteResults(writeResults);
////        Result<Object> objectResult = Result.newSuccess();
////        Map<String,Object> dataMap=Maps.newHashMap();
////        dataMap.put("dataSize",200);
////        objectResult.setData(dataMap);
////        DataIntegrationNotificationEntity dataIntegrationNotificationEntity=new DataIntegrationNotificationEntity();
//        evaluationContext.setVariable("syncDataContextEvent", erpContext);
//        Object value = expressionParser.parseExpression("#syncDataContextEvent?.writeResult == null || syncDataContextEvent.writeResult.isSuccess()? 0 : 1").getValue(evaluationContext);
//        //解析表达式
//        System.out.println(expressionParser.parseExpression("#result?.data==null?1:#result?.data[dataSize]==null?1:#result?.data[dataSize]").getValue(evaluationContext));
//
//
//    }

    @Test
    public void testDataScreenBizLog(){
        /**
         * @see com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen
         */
        for (int i = 0; i < 10; i++) {
            DataScreenSyncLogDTO dataScreenSyncLogDTO=new DataScreenSyncLogDTO();
            dataScreenSyncLogDTO.setDataCenterId("643f7322b54ea80001767d86");
            dataScreenSyncLogDTO.setExecuteCost(20);
            dataScreenSyncLogDTO.setOutSideObjId(String.valueOf(i));
            dataScreenSyncLogDTO.setOperationType(CommonConstant.READ_OPERATE_TYPE);
            dataScreenSyncLogDTO.setOutSideObjApiName("AR_receivable");
            dataScreenSyncLogDTO.setTenantId("88521");
            dataScreenSyncLogDTO.setCreateTime(System.currentTimeMillis());
            dataScreenSyncLogDTO.setUpdateTime(System.currentTimeMillis());
            dataScreenSyncLogDTO.setOperateStatus("0");
            MonitorBizLogUtil.sendDataScreen(dataScreenSyncLogDTO);
        }

    }
    @Test
    public void testBIManager(){
        List<DataCenterInfoResult> dataCenterInfoResults = biManager.queryConnectInfos("88521", null, false);
        Integer integer = biManager.queryBusiness("88521", dataCenterInfoResults);
        //biManager.extractedBrushFiled("88521");
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(1710777600000L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setObjAPIName(K3CloudForm.BD_MATERIAL);
        timeFilterArg.setTenantId("88521");
        timeFilterArg.setOperationType(1);
        timeFilterArg.setLimit(1000);
        ErpConnectInfoEntity byIdAndTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).getByIdAndTenantId("88521", "643f7322b54ea80001767d86");
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(ErpChannelEnum.ERP_K3CLOUD,byIdAndTenantId.getConnectParams());

        Result<StandardListData> standardListDataResult = erpDataManager.listErpObjDataByTime(timeFilterArg, byIdAndTenantId);
//        BIFieldDescArg biFieldDescArg=new BIFieldDescArg();
//
////        Result<String> stringResult = biManager.insertFieldDesc("88521", ERPBITypeEnum.OUT_SYSTEM, Lists.newArrayList(biFieldDescArg));
//        Result<String> stringResult = biManager.upsertFieldDesc("88521", ERPBITypeEnum.OUT_SYSTEM, Maps.newHashMap());
        log.info("result");
    }

    @Test
    public void testInitDesc(){
        //指定企业查询集成流，刷描述
        String tenantId="88521";
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
        Map<String, String> connectInfo = erpConnectInfoEntities.stream().collect(Collectors.toMap(ErpConnectInfoEntity::getId, ErpConnectInfoEntity::getDataCenterName, (u, v) -> u));
        List<BIFieldDescArg> connectArgList=Lists.newArrayList();
        String crmDataCenterId="";
        Map<String,String> connectMap=Maps.newHashMap();
        for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
            if(erpConnectInfoEntity.getChannel().name().equals(ErpChannelEnum.CRM.name())){
                crmDataCenterId=erpConnectInfoEntity.getId();
            }
            BIFieldDescArg biFieldDescArg=new BIFieldDescArg();
            connectMap.put(erpConnectInfoEntity.getId(),erpConnectInfoEntity.getDataCenterName());
        }
        Result<String> stringResult = biManager.upsertFieldDesc(tenantId, ERPBITypeEnum.OUT_SYSTEM, connectMap);
        //BIFieldDescArg.BICRUDFiledResult bicrudFiledResult = biManager.queryFieldDesc("88521", ERPBITypeEnum.OUT_SYSTEM);
        BIFieldDescArg.BICRUDFiledResult bicrudFiledResult = biManager.queryFieldDesc("88521", ERPBITypeEnum.OUT_SYSTEM);


        log.info("connectResult:{}",stringResult.getErrMsg());
        //根据企业刷描述
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
        List<BIFieldDescArg> erpArgList=Lists.newArrayList();
        Map<String, String> objMap = erpObjectEntities.stream().filter(item -> item.getErpObjectType().equals(ErpObjectTypeEnum.REAL_OBJECT)).
                collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getErpObjectName, (u, v) -> u));
        Map<String,String> objectMap=Maps.newHashMap();
        for (Map.Entry<String, String> objEntry : objMap.entrySet()) {
            BIFieldDescArg biFieldDescArg=new BIFieldDescArg();
            objectMap.put(objEntry.getKey(),objEntry.getValue());
        }
        Result<String> objResult = biManager.upsertFieldDesc(tenantId, ERPBITypeEnum.OUT_SIDE_OBJ_APINAME, objectMap);
        log.info("objResult:{}",objResult.getErrMsg());
        //集成流上函数异常
        List<SyncPloyDetailEntity> detailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).listByTenantId("88521");
        Map<String,String> funcionName=Maps.newHashMap();
        for (SyncPloyDetailEntity detailEntity : detailEntities) {
            String beforeFuncApiName = detailEntity.getBeforeFuncApiName();
            if(StringUtils.isNotEmpty(beforeFuncApiName)){
                funcionName.put(beforeFuncApiName,beforeFuncApiName);
            }
            String duringFuncApiName = detailEntity.getDuringFuncApiName();
            if(StringUtils.isNotEmpty(duringFuncApiName)){
                funcionName.put(duringFuncApiName,duringFuncApiName);
            }
            String afterFuncApiName = detailEntity.getAfterFuncApiName();
            if(StringUtils.isNotEmpty(afterFuncApiName)){
                funcionName.put(afterFuncApiName,afterFuncApiName);
            }
        }
        //api函数
        List<ErpObjGroovyEntity> erpObjGroovyEntities = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).queryGroovyTenantId("88521");
        if(CollectionUtils.isNotEmpty(erpObjGroovyEntities)){
            Map<String, String> collect = erpObjGroovyEntities.stream().filter(item -> item.getFuncApiName() != null).collect(Collectors.toMap(ErpObjGroovyEntity::getFuncApiName, ErpObjGroovyEntity::getFuncApiName,(u,v)->u));
            funcionName.putAll(collect);
        }
        //刷crm对象
        List<ObjectDescribe> objectDescribes = crmRemoteManager.listMasterObjects(tenantId);
        Map<String,String> crmObjMap=Maps.newHashMap();
        for (ObjectDescribe objectDescribe : objectDescribes) {
            crmObjMap.put(objectDescribe.getApiName(),objectDescribe.getDisplayName());
        }
        crmObjMap.putAll(funcionName);
        Result<String> crmResult = biManager.upsertFieldDesc(tenantId, ERPBITypeEnum.CRM_OBJ_APINAME, crmObjMap);
        log.info("objResult:{}",crmResult.getErrMsg());

    }
    @Test
    public void testFieldQuery(){
        DataScreenArg dataScreenArg=JSONObject.parseObject("\n" +
                "{\n" +
                "    \"screenName\": \"集成平台大屏\",\n" +
                "    \"dateRangeEnum\": \"LAST_SEVEN_DAYS\",\n" +
                "    \"dataCenterList\": [\n" +
                "        {\n" +
                "            \"value\": \"643f7322b54ea80001767d86\",\n" +
                "            \"label\": \"88521金蝶云·星空\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"value\": \"65d1bec7e0909a00016591ea\",\n" +
                "            \"label\": \"泛微e-office\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"value\": \"6544b4c4763ecb0001ad8185\",\n" +
                "            \"label\": \"线上真实企业连接器--sunquick2023\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"crmObjectList\": [\n" +
                "        {\n" +
                "            \"value\": \"PriceBookObj\",\n" +
                "            \"label\": \"价目表\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"value\": \"DeliveryNoteProductObj\",\n" +
                "            \"label\": \"发货单产品\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"currentDcId\": \"6488111b7b1fdb0001f04ef7\"\n" +
                "}",DataScreenArg.class);
//        ScreenFilterInfoResult screenFilterInfoResult = biManager.queryTenantConfig("88521");
//        biManager.saveFilter(dataScreenArg,"88521");

//        String data="{\"code\":200,\"data\":{\"displayFields\":[{\"fieldName\":\"执行时间（日）\",\"dim\":false,\"dimensionConfig\":{\"groupType\":1,\"dimType\":\"0\",\"dimensionConfigAnalysisType\":\"0\"},\"canShowDetail\":false,\"fieldId\":\"BI_213150709142159360\"},{\"fieldName\":\"CRM对象\",\"dim\":false,\"dimensionConfig\":{\"groupType\":2,\"dimType\":\"0\",\"dimensionConfigAnalysisType\":\"0\"},\"canShowDetail\":false,\"fieldId\":\"BI_65e05930aaba3400015babf7\"},{\"fieldName\":\"CRM数据同步量\",\"dim\":false,\"canShowDetail\":false,\"fieldId\":\"BI_65f2a920b04b200001768d70\"}],\"dataSet\":[[{\"formattedValue\":\"20240301\",\"formattedShowValue\":\"20240301\",\"valueCode\":\"20240301\",\"value\":\"20240301\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240302\",\"formattedShowValue\":\"20240302\",\"valueCode\":\"20240302\",\"value\":\"20240302\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240303\",\"formattedShowValue\":\"20240303\",\"valueCode\":\"20240303\",\"value\":\"20240303\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240304\",\"formattedShowValue\":\"20240304\",\"valueCode\":\"20240304\",\"value\":\"20240304\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240305\",\"formattedShowValue\":\"20240305\",\"valueCode\":\"20240305\",\"value\":\"20240305\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240306\",\"formattedShowValue\":\"20240306\",\"valueCode\":\"20240306\",\"value\":\"20240306\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240307\",\"formattedShowValue\":\"20240307\",\"valueCode\":\"20240307\",\"value\":\"20240307\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240308\",\"formattedShowValue\":\"20240308\",\"valueCode\":\"20240308\",\"value\":\"20240308\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240309\",\"formattedShowValue\":\"20240309\",\"valueCode\":\"20240309\",\"value\":\"20240309\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240310\",\"formattedShowValue\":\"20240310\",\"valueCode\":\"20240310\",\"value\":\"20240310\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240311\",\"formattedShowValue\":\"20240311\",\"valueCode\":\"20240311\",\"value\":\"20240311\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240317\",\"formattedShowValue\":\"20240317\",\"valueCode\":\"20240317\",\"value\":\"20240317\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240318\",\"formattedShowValue\":\"20240318\",\"valueCode\":\"20240318\",\"value\":\"20240318\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240319\",\"formattedShowValue\":\"20240319\",\"valueCode\":\"20240319\",\"value\":\"20240319\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240320\",\"formattedShowValue\":\"20240320\",\"valueCode\":\"20240320\",\"value\":\"20240320\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240321\",\"formattedShowValue\":\"20240321\",\"valueCode\":\"20240321\",\"value\":\"20240321\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240322\",\"formattedShowValue\":\"20240322\",\"valueCode\":\"20240322\",\"value\":\"20240322\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240323\",\"formattedShowValue\":\"20240323\",\"valueCode\":\"20240323\",\"value\":\"20240323\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240324\",\"formattedShowValue\":\"20240324\",\"valueCode\":\"20240324\",\"value\":\"20240324\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240325\",\"formattedShowValue\":\"20240325\",\"valueCode\":\"20240325\",\"value\":\"20240325\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"发货单产品\",\"formattedShowValue\":\"发货单产品\",\"valueCode\":\"DeliveryNoteProductObj\",\"value\":\"发货单产品\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"产品\",\"formattedShowValue\":\"产品\",\"valueCode\":\"ProductObj\",\"value\":\"产品\"},{\"formattedValue\":\"5\",\"formattedShowValue\":\"5.00\",\"valueCode\":\"5\",\"value\":\"5\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"合同\",\"formattedShowValue\":\"合同\",\"valueCode\":\"ContractObj\",\"value\":\"合同\"},{\"formattedValue\":\"98\",\"formattedShowValue\":\"98.00\",\"valueCode\":\"98\",\"value\":\"98\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"发货单\",\"formattedShowValue\":\"发货单\",\"valueCode\":\"DeliveryNoteObj\",\"value\":\"发货单\"},{\"formattedValue\":\"7\",\"formattedShowValue\":\"7.00\",\"valueCode\":\"7\",\"value\":\"7\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"合同\",\"formattedShowValue\":\"合同\",\"valueCode\":\"ContractObj\",\"value\":\"合同\"},{\"formattedValue\":\"1\",\"formattedShowValue\":\"1.00\",\"valueCode\":\"1\",\"value\":\"1\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240329\",\"formattedShowValue\":\"20240329\",\"valueCode\":\"20240329\",\"value\":\"20240329\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240330\",\"formattedShowValue\":\"20240330\",\"valueCode\":\"20240330\",\"value\":\"20240330\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240331\",\"formattedShowValue\":\"20240331\",\"valueCode\":\"20240331\",\"value\":\"20240331\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240301\",\"formattedShowValue\":\"20240301\",\"valueCode\":\"20240301\",\"value\":\"20240301\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240302\",\"formattedShowValue\":\"20240302\",\"valueCode\":\"20240302\",\"value\":\"20240302\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240303\",\"formattedShowValue\":\"20240303\",\"valueCode\":\"20240303\",\"value\":\"20240303\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240304\",\"formattedShowValue\":\"20240304\",\"valueCode\":\"20240304\",\"value\":\"20240304\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240305\",\"formattedShowValue\":\"20240305\",\"valueCode\":\"20240305\",\"value\":\"20240305\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240306\",\"formattedShowValue\":\"20240306\",\"valueCode\":\"20240306\",\"value\":\"20240306\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240307\",\"formattedShowValue\":\"20240307\",\"valueCode\":\"20240307\",\"value\":\"20240307\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240308\",\"formattedShowValue\":\"20240308\",\"valueCode\":\"20240308\",\"value\":\"20240308\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240309\",\"formattedShowValue\":\"20240309\",\"valueCode\":\"20240309\",\"value\":\"20240309\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240310\",\"formattedShowValue\":\"20240310\",\"valueCode\":\"20240310\",\"value\":\"20240310\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240311\",\"formattedShowValue\":\"20240311\",\"valueCode\":\"20240311\",\"value\":\"20240311\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240317\",\"formattedShowValue\":\"20240317\",\"valueCode\":\"20240317\",\"value\":\"20240317\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240318\",\"formattedShowValue\":\"20240318\",\"valueCode\":\"20240318\",\"value\":\"20240318\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240319\",\"formattedShowValue\":\"20240319\",\"valueCode\":\"20240319\",\"value\":\"20240319\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240320\",\"formattedShowValue\":\"20240320\",\"valueCode\":\"20240320\",\"value\":\"20240320\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240321\",\"formattedShowValue\":\"20240321\",\"valueCode\":\"20240321\",\"value\":\"20240321\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240322\",\"formattedShowValue\":\"20240322\",\"valueCode\":\"20240322\",\"value\":\"20240322\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240323\",\"formattedShowValue\":\"20240323\",\"valueCode\":\"20240323\",\"value\":\"20240323\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240324\",\"formattedShowValue\":\"20240324\",\"valueCode\":\"20240324\",\"value\":\"20240324\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240325\",\"formattedShowValue\":\"20240325\",\"valueCode\":\"20240325\",\"value\":\"20240325\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"114\",\"formattedShowValue\":\"114.00\",\"valueCode\":\"114\",\"value\":\"114\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"1\",\"formattedShowValue\":\"1.00\",\"valueCode\":\"1\",\"value\":\"1\"}],[{\"formattedValue\":\"20240329\",\"formattedShowValue\":\"20240329\",\"valueCode\":\"20240329\",\"value\":\"20240329\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240330\",\"formattedShowValue\":\"20240330\",\"valueCode\":\"20240330\",\"value\":\"20240330\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240331\",\"formattedShowValue\":\"20240331\",\"valueCode\":\"20240331\",\"value\":\"20240331\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"发货单产品\",\"formattedShowValue\":\"发货单产品\",\"valueCode\":\"DeliveryNoteProductObj\",\"value\":\"发货单产品\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"产品\",\"formattedShowValue\":\"产品\",\"valueCode\":\"ProductObj\",\"value\":\"产品\"},{\"formattedValue\":\"5\",\"formattedShowValue\":\"5.00\",\"valueCode\":\"5\",\"value\":\"5\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"发货单\",\"formattedShowValue\":\"发货单\",\"valueCode\":\"DeliveryNoteObj\",\"value\":\"发货单\"},{\"formattedValue\":\"7\",\"formattedShowValue\":\"7.00\",\"valueCode\":\"7\",\"value\":\"7\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"4\",\"formattedShowValue\":\"4.00\",\"valueCode\":\"4\",\"value\":\"4\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"合同\",\"formattedShowValue\":\"合同\",\"valueCode\":\"ContractObj\",\"value\":\"合同\"},{\"formattedValue\":\"99\",\"formattedShowValue\":\"99.00\",\"valueCode\":\"99\",\"value\":\"99\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"117\",\"formattedShowValue\":\"117.00\",\"valueCode\":\"117\",\"value\":\"117\"}]]},\"message\":\"success\"}";
//        BIDataResult biDataResult= JSONObject.parseObject(data,BIDataResult.class);
//        //指定筛选日期
//        BIDashboardEnum biDashboardEnum=BIDashboardEnum.COUNT_CRM_READ_WRITE;
//        //本周内 指定日期
//        List<BIFieldDescArg.DashboardFilterEnum> conditionFilterValues = BIDashboardEnum.getConditionFilterValues(biDashboardEnum);
//        Map<String, BIFieldDescArg.DashboardFilterEnum> collect = conditionFilterValues.stream().collect(Collectors.toMap(BIFieldDescArg.DashboardFilterEnum::getFieldName, Function.identity(), (v1, v2) -> v1));
//        List<BIFieldDescArg.FilterList> list=Lists.newArrayList();
//        BIFieldDescArg.FilterList item=new BIFieldDescArg.FilterList();
//        item.setFilterId("BI_65f2a9adb04b200001768dd7");
//        item.setDateRangeId("5");
//        DataScreenArg dataScreenArg=new DataScreenArg();
        dataScreenArg.setDateRangeEnum(BIDateRangeEnum.YESTERDAY);
        dataScreenArg.setTenantId("88521");
        Result<BIDataResult> result = biManager.rangeDateFilter(dataScreenArg.getDateRangeEnum(), dataScreenArg, BIDashboardEnum.ANALYZE_BUSINESS_FLOW);
        Result<Map<String, List<DataDashboardResult>>> mapResult = biManager.convertFilterResult(result);
        BIDataResult biDataResult=result.getData();
        Map<String,List<DataDashboardResult>> dataResult=Maps.newHashMap();
        for (List<BIDataResult.BIDataSetResult> biDataSetResults : biDataResult.getData().getDataSet()) {
            DataDashboardResult valueResult=new DataDashboardResult();
            String formattedShowValue = biDataSetResults.get(0).getFormattedShowValue();
            for (int i = 0; i < biDataSetResults.size(); i++) {
                BIDataResult.BIDataSetResult biDataSetResult = biDataSetResults.get(i);
                DataDashboardResult dataDashboardResult=new DataDashboardResult();

                 if(i==1&&!biDataSetResult.getFormattedShowValue().contains("f00")){
                     valueResult.setApiName(biDataSetResult.getFormattedShowValue());
                 }
                if(i==2){
                    valueResult.setDataCount(biDataSetResult.getFormattedShowValue());
                }
            }
            dataResult.computeIfAbsent(formattedShowValue,v -> Lists.newArrayList()).add(valueResult);

        }
//        Result<String> stringResult = biManager.queryFieldDesc("88521", ERPBITypeEnum.OUT_SYSTEM);
//        log.info("result");
    }
    @Test
    public void testFieldQuery2(){
        biManager.queryFieldDesc("88521", ERPBITypeEnum.OUT_SYSTEM);
        String data="{\"code\":200,\"data\":{\"displayFields\":[{\"fieldName\":\"执行时间（日）\",\"dim\":false,\"dimensionConfig\":{\"groupType\":1,\"dimType\":\"0\",\"dimensionConfigAnalysisType\":\"0\"},\"canShowDetail\":false,\"fieldId\":\"BI_213150709142159360\"},{\"fieldName\":\"CRM对象\",\"dim\":false,\"dimensionConfig\":{\"groupType\":2,\"dimType\":\"0\",\"dimensionConfigAnalysisType\":\"0\"},\"canShowDetail\":false,\"fieldId\":\"BI_65e05930aaba3400015babf7\"},{\"fieldName\":\"CRM数据同步量\",\"dim\":false,\"canShowDetail\":false,\"fieldId\":\"BI_65f2a920b04b200001768d70\"}],\"dataSet\":[[{\"formattedValue\":\"20240301\",\"formattedShowValue\":\"20240301\",\"valueCode\":\"20240301\",\"value\":\"20240301\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240302\",\"formattedShowValue\":\"20240302\",\"valueCode\":\"20240302\",\"value\":\"20240302\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240303\",\"formattedShowValue\":\"20240303\",\"valueCode\":\"20240303\",\"value\":\"20240303\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240304\",\"formattedShowValue\":\"20240304\",\"valueCode\":\"20240304\",\"value\":\"20240304\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240305\",\"formattedShowValue\":\"20240305\",\"valueCode\":\"20240305\",\"value\":\"20240305\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240306\",\"formattedShowValue\":\"20240306\",\"valueCode\":\"20240306\",\"value\":\"20240306\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240307\",\"formattedShowValue\":\"20240307\",\"valueCode\":\"20240307\",\"value\":\"20240307\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240308\",\"formattedShowValue\":\"20240308\",\"valueCode\":\"20240308\",\"value\":\"20240308\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240309\",\"formattedShowValue\":\"20240309\",\"valueCode\":\"20240309\",\"value\":\"20240309\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240310\",\"formattedShowValue\":\"20240310\",\"valueCode\":\"20240310\",\"value\":\"20240310\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240311\",\"formattedShowValue\":\"20240311\",\"valueCode\":\"20240311\",\"value\":\"20240311\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240317\",\"formattedShowValue\":\"20240317\",\"valueCode\":\"20240317\",\"value\":\"20240317\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240318\",\"formattedShowValue\":\"20240318\",\"valueCode\":\"20240318\",\"value\":\"20240318\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240319\",\"formattedShowValue\":\"20240319\",\"valueCode\":\"20240319\",\"value\":\"20240319\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240320\",\"formattedShowValue\":\"20240320\",\"valueCode\":\"20240320\",\"value\":\"20240320\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240321\",\"formattedShowValue\":\"20240321\",\"valueCode\":\"20240321\",\"value\":\"20240321\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240322\",\"formattedShowValue\":\"20240322\",\"valueCode\":\"20240322\",\"value\":\"20240322\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240323\",\"formattedShowValue\":\"20240323\",\"valueCode\":\"20240323\",\"value\":\"20240323\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240324\",\"formattedShowValue\":\"20240324\",\"valueCode\":\"20240324\",\"value\":\"20240324\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240325\",\"formattedShowValue\":\"20240325\",\"valueCode\":\"20240325\",\"value\":\"20240325\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"发货单产品\",\"formattedShowValue\":\"发货单产品\",\"valueCode\":\"DeliveryNoteProductObj\",\"value\":\"发货单产品\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"产品\",\"formattedShowValue\":\"产品\",\"valueCode\":\"ProductObj\",\"value\":\"产品\"},{\"formattedValue\":\"5\",\"formattedShowValue\":\"5.00\",\"valueCode\":\"5\",\"value\":\"5\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"合同\",\"formattedShowValue\":\"合同\",\"valueCode\":\"ContractObj\",\"value\":\"合同\"},{\"formattedValue\":\"98\",\"formattedShowValue\":\"98.00\",\"valueCode\":\"98\",\"value\":\"98\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"发货单\",\"formattedShowValue\":\"发货单\",\"valueCode\":\"DeliveryNoteObj\",\"value\":\"发货单\"},{\"formattedValue\":\"7\",\"formattedShowValue\":\"7.00\",\"valueCode\":\"7\",\"value\":\"7\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"合同\",\"formattedShowValue\":\"合同\",\"valueCode\":\"ContractObj\",\"value\":\"合同\"},{\"formattedValue\":\"1\",\"formattedShowValue\":\"1.00\",\"valueCode\":\"1\",\"value\":\"1\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240329\",\"formattedShowValue\":\"20240329\",\"valueCode\":\"20240329\",\"value\":\"20240329\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240330\",\"formattedShowValue\":\"20240330\",\"valueCode\":\"20240330\",\"value\":\"20240330\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240331\",\"formattedShowValue\":\"20240331\",\"valueCode\":\"20240331\",\"value\":\"20240331\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240301\",\"formattedShowValue\":\"20240301\",\"valueCode\":\"20240301\",\"value\":\"20240301\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240302\",\"formattedShowValue\":\"20240302\",\"valueCode\":\"20240302\",\"value\":\"20240302\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240303\",\"formattedShowValue\":\"20240303\",\"valueCode\":\"20240303\",\"value\":\"20240303\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240304\",\"formattedShowValue\":\"20240304\",\"valueCode\":\"20240304\",\"value\":\"20240304\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240305\",\"formattedShowValue\":\"20240305\",\"valueCode\":\"20240305\",\"value\":\"20240305\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240306\",\"formattedShowValue\":\"20240306\",\"valueCode\":\"20240306\",\"value\":\"20240306\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240307\",\"formattedShowValue\":\"20240307\",\"valueCode\":\"20240307\",\"value\":\"20240307\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240308\",\"formattedShowValue\":\"20240308\",\"valueCode\":\"20240308\",\"value\":\"20240308\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240309\",\"formattedShowValue\":\"20240309\",\"valueCode\":\"20240309\",\"value\":\"20240309\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240310\",\"formattedShowValue\":\"20240310\",\"valueCode\":\"20240310\",\"value\":\"20240310\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240311\",\"formattedShowValue\":\"20240311\",\"valueCode\":\"20240311\",\"value\":\"20240311\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240317\",\"formattedShowValue\":\"20240317\",\"valueCode\":\"20240317\",\"value\":\"20240317\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240318\",\"formattedShowValue\":\"20240318\",\"valueCode\":\"20240318\",\"value\":\"20240318\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240319\",\"formattedShowValue\":\"20240319\",\"valueCode\":\"20240319\",\"value\":\"20240319\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240320\",\"formattedShowValue\":\"20240320\",\"valueCode\":\"20240320\",\"value\":\"20240320\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240321\",\"formattedShowValue\":\"20240321\",\"valueCode\":\"20240321\",\"value\":\"20240321\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240322\",\"formattedShowValue\":\"20240322\",\"valueCode\":\"20240322\",\"value\":\"20240322\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240323\",\"formattedShowValue\":\"20240323\",\"valueCode\":\"20240323\",\"value\":\"20240323\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240324\",\"formattedShowValue\":\"20240324\",\"valueCode\":\"20240324\",\"value\":\"20240324\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240325\",\"formattedShowValue\":\"20240325\",\"valueCode\":\"20240325\",\"value\":\"20240325\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"114\",\"formattedShowValue\":\"114.00\",\"valueCode\":\"114\",\"value\":\"114\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"********\",\"formattedShowValue\":\"********\",\"valueCode\":\"********\",\"value\":\"********\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"1\",\"formattedShowValue\":\"1.00\",\"valueCode\":\"1\",\"value\":\"1\"}],[{\"formattedValue\":\"20240329\",\"formattedShowValue\":\"20240329\",\"valueCode\":\"20240329\",\"value\":\"20240329\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240330\",\"formattedShowValue\":\"20240330\",\"valueCode\":\"20240330\",\"value\":\"20240330\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"20240331\",\"formattedShowValue\":\"20240331\",\"valueCode\":\"20240331\",\"value\":\"20240331\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"发货单产品\",\"formattedShowValue\":\"发货单产品\",\"valueCode\":\"DeliveryNoteProductObj\",\"value\":\"发货单产品\"},{\"formattedValue\":\"2\",\"formattedShowValue\":\"2.00\",\"valueCode\":\"2\",\"value\":\"2\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"产品\",\"formattedShowValue\":\"产品\",\"valueCode\":\"ProductObj\",\"value\":\"产品\"},{\"formattedValue\":\"5\",\"formattedShowValue\":\"5.00\",\"valueCode\":\"5\",\"value\":\"5\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"发货单\",\"formattedShowValue\":\"发货单\",\"valueCode\":\"DeliveryNoteObj\",\"value\":\"发货单\"},{\"formattedValue\":\"7\",\"formattedShowValue\":\"7.00\",\"valueCode\":\"7\",\"value\":\"7\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"客户\",\"formattedShowValue\":\"客户\",\"valueCode\":\"AccountObj\",\"value\":\"客户\"},{\"formattedValue\":\"4\",\"formattedShowValue\":\"4.00\",\"valueCode\":\"4\",\"value\":\"4\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"合同\",\"formattedShowValue\":\"合同\",\"valueCode\":\"ContractObj\",\"value\":\"合同\"},{\"formattedValue\":\"99\",\"formattedShowValue\":\"99.00\",\"valueCode\":\"99\",\"value\":\"99\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"--\",\"formattedShowValue\":\"--\",\"valueCode\":\"--\",\"value\":\"--\"},{\"formattedValue\":\"0\",\"formattedShowValue\":\"0.00\",\"valueCode\":\"0\",\"value\":\"0\"}],[{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"sy#f00>Feb*\",\"formattedShowValue\":\"sy#f00>Feb*\",\"valueCode\":\"sy#f00>Feb*\",\"value\":\"sy#f00>Feb*\"},{\"formattedValue\":\"117\",\"formattedShowValue\":\"117.00\",\"valueCode\":\"117\",\"value\":\"117\"}]]},\"message\":\"success\"}";
        BIDataResult biDataResult= JSONObject.parseObject(data,BIDataResult.class);
//        Result<BIDataResult> result = biManager.queryDashboard("88521", "BI_65f2a91eb04b200001768d6f", null);
//        Map<String,Map<String,List<Integer>>> dataSourceSet=Maps.newHashMap();
        Map<String,List<DataDashboardResult>> dataResult=Maps.newHashMap();
        for (List<BIDataResult.BIDataSetResult> biDataSetResults : biDataResult.getData().getDataSet()) {
            DataDashboardResult valueResult=new DataDashboardResult();
            String formattedShowValue = biDataSetResults.get(0).getFormattedShowValue();
            for (int i = 0; i < biDataSetResults.size(); i++) {
                BIDataResult.BIDataSetResult biDataSetResult = biDataSetResults.get(i);
                if(i==1&&!biDataSetResult.getFormattedShowValue().contains("f00")){
                    valueResult.setApiName(biDataSetResult.getFormattedShowValue());
                }
                if(i==2){
                    valueResult.setDataCount(biDataSetResult.getFormattedShowValue());
                }
            }
            dataResult.computeIfAbsent(formattedShowValue,v -> Lists.newArrayList()).add(valueResult);

        }

        log.info("result");
    }

    @Test
    public void viewAll(){
        BIFieldDescArg.FilterList thisMonthFilter=new BIFieldDescArg.FilterList();
        //拿到对应的报表的筛选id.
        List<BIFieldDescArg.DashboardFilterEnum> conditionFilterValues = BIDashboardEnum.getConditionFilterValues(BIDashboardEnum.ANY_SYSTEM_INFO);
        BIFieldDescArg.DashboardFilterEnum executeTime = conditionFilterValues.stream().filter(item -> item.getFieldName().contains("executeTime")).findFirst().get();
        thisMonthFilter.setFilterId(executeTime.getFilterId());
        thisMonthFilter.setDateRangeId(BIDateRangeEnum.THIS_MONTH.getDataRangeId());
        Result<BIDataResult> biDataResultResult = biManager.queryDashboard("88521", BIDashboardEnum.ANY_SYSTEM_INFO.getFilterId(), Lists.newArrayList(thisMonthFilter));
        log.info("result");
    }

    @Test
    public void testCountData(){

        biManager.queryConnectInfos("88423",null,true);
        String percent = "0";
        String totalCount="0.00";
        String currencyPloyCount="0.00";
        if (Double.valueOf(totalCount) != 0) {
            //当前的同步数量
            //计算占比
            percent = String.format("%.2f%%", Double.valueOf(currencyPloyCount) / Double.valueOf(totalCount) * 100);
        }
//        ErpIdArg erpIdArg=new ErpIdArg();
//        erpIdArg.setDataId("CH3602");
//        erpIdArg.setObjAPIName("BD_MATERIAL.BillHead");
//        erpIdArg.setTenantId("88521");
//        erpIdArg.setSyncPloyDetailSnapshotId("d1a81eab1edf4951ba8f34670b9eec9c");
//        erpDataPreprocessService.getReSyncObjDataById(erpIdArg);
        String tenantId="88521";
//        //配置了筛选系统dataScreenArg
//        ScreenFilterInfoResult screenFilterInfoResult = biManager.queryTenantConfig(tenantId);
//        List<String> filterDataCenterInfos=Lists.newArrayList();
//        if(ObjectUtils.isNotEmpty(screenFilterInfoResult)&& CollectionUtils.isNotEmpty(screenFilterInfoResult.getDataCenterList())){
//            Set<DataScreenFilterResult.Option> dataCenterIds=screenFilterInfoResult.getDataCenterList();
//            filterDataCenterInfos = dataCenterIds.stream().map(DataScreenFilterResult.Option::getValue).collect(Collectors.toList());
//        }
//        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
//        List<DataCenterInfoResult> screenDataCenterInfos= Lists.newArrayList();
//        for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
//            if(!ConnectorTypeEnum.OA.equals(erpConnectInfoEntity.getChannel().getConnectorType())){
//                if(ErpChannelEnum.CRM.equals(erpConnectInfoEntity.getChannel())||filterDataCenterInfos.contains(erpConnectInfoEntity.getId())){
//                    DataCenterInfoResult crmInfo = BeanUtil.copy(erpConnectInfoEntity, DataCenterInfoResult.class);
//                    screenDataCenterInfos.add(crmInfo);
//                }
//            }
//        }
//        List<DataCenterInfoResult> dataCenterInfoResults = BeanUtil.copyList(screenDataCenterInfos, DataCenterInfoResult.class);
//
//
//        String json="{\n" +
//                "    \"tenantId\":\"88521\",\n" +
//                "    \"dataCenterId\":[{\"653623d5b4660c00014c129a\":\"云星辰\"},{\"643f7322b54ea80001767d86\":\"88521金蝶云星空\"}],\n" +
//                "    \"crmObjApiName\":[{\"AccountObj\":\"客户\"}],\n" +
//                "    \"executeTime\":\"THIS_WEEK\",\n" +
//                "    \"currentDcId\":\"654a25ce94431f000129b877\"\n" +
//                "}";
//        DataScreenArg testData=JSONObject.parseObject(json,DataScreenArg.class);
        DataScreenArg dataScreenArg=new DataScreenArg();
        //dataScreenArg=JSONObject.parseObject("{\"crmObjectList\":[],\"dateRangeEnum\":\"LAST_SEVEN_DAYS\",\"crmSystem\":false,\"dataCenterList\":[{\"label\":\"自建业务系统\",\"value\":\"64d34e5a5dba9300010dd999\"}],\"currentDcId\":\"643f7322b54ea80001767d86\"}",DataScreenArg.class);

        dataScreenArg= JSONObject.parseObject("{\n" +
                "    \"crmObjectList\": [],\n" +
                "    \"dateRangeEnum\": \"LAST_FOURTEEN_DAYS\",\n" +
                "    \"pollingInterval\": \"15\",\n" +
                "    \"crmSystem\": false,\n" +
                "    \"dataCenterList\": [\n" +
                "        {\n" +
                "            \"value\": \"643f7322b54ea80001767d86\",\n" +
                "            \"label\": \"云星空\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"currentDcId\": \"643f7322b54ea80001767d86\"\n" +
                "}",DataScreenArg.class);
        dataScreenArg.setTenantId("88521");
//        biManager.queryIntervalSyncDataDetail(dataScreenArg);
//        biManager.incrementDataResult(dataScreenArg);
//        dataScreenArg.setCrmSystem(true);
//        biManager.systemBusinessFlowCount(dataScreenArg);
         biManager.calculateFlowResult(dataScreenArg);

//        biManager.screenMultiDashboard(dataScreenArg);
////        dataScreenArg.setExecuteTime(BIDateRangeEnum.TODAY);
//        List<DataScreenArg.ScreenOptional> dataCenter=Lists.newArrayList();
//        DataScreenArg.ScreenOptional optional=new DataScreenArg.ScreenOptional();
//        optional.setOptionalKey("653623d5b4660c00014c129a");
//        optional.setOptionalLabel("云星辰");
//        DataScreenArg.ScreenOptional optional2=new DataScreenArg.ScreenOptional();
//        optional.setOptionalKey("654a25ce94431f000129b877");
//        optional.setOptionalLabel("金蝶云·星空1");
//        dataCenter.add(optional);
//        dataCenter.add(optional2);
//        dataScreenArg.setDataCenterIds(dataCenter);
        List<Pair<String,String>> crmObjApiName=Lists.newArrayList();
        crmObjApiName.add(Pair.of("AccountObj","客户"));
//        dataScreenArg.setCrmObjApiName(crmObjApiName);
        dataScreenArg.setTenantId("88521");
        List<DataScreenArg.ScreenOptional> screenOptionals=Lists.newArrayList();
        DataScreenArg.ScreenOptional screenOptional=new DataScreenArg.ScreenOptional();
        screenOptional.setValue("643f7322b54ea80001767d86");
        screenOptional.setLabel("88521金蝶云·星空");
        dataScreenArg.setDataCenterList(Lists.newArrayList(screenOptional));
//        DataScreenResult dataScreenResult = biManager.allViewData(dataScreenArg);
        log.info("datascreen");
    }

    @Test
    public void testData(){
        DataMonitorScreenDTO dataMonitorScreenDTO=new DataMonitorScreenDTO();
        syncLogManager.initLogId("88521","ContactObj");
        Boolean result=StringUtils.isEmpty(dataMonitorScreenDTO.getCrmObjApiName());

        LogIdUtil.setRealObjApiName("ContactObj");
        LogIdUtil.setStreamId("2c72f8d5076f4f12a001bcde820be47e");
        LogIdUtil.setDataId("655d70fc60d8b50001518e5d");
        String data="{\"completeDataProcess\":true,\"completeDataWrite\":true,\"crmObjApiName\":\"ContactObj\",\"dataId\":\"655d70fc60d8b50001518e5d\",\"dataReceiveType\":2,\"debugRecordIfDetailCauseMaterSync\":false,\"destData\":{\"object_describe_api_name\":\"contactTest_1hgshoasa\",\"tenant_id\":\"88521\",\"name\":\"测试联系人\",\"_id\":\"661e1d5640aabd0007b93fba\"},\"destDataCenterId\":\"64d34e5a5dba9300010dd999\",\"destDataId\":\"661e1d5640aabd0007b93fba\",\"destDetailObjMasterDetailFieldApiName\":{},\"destDetailSyncDataIdAndDestDataMap\":{},\"destEventType\":2,\"destObjectApiName\":\"contactTest_1hgshoasa\",\"destTenantId\":\"88521\",\"destTenantType\":2,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":true,\"doWrite\":true,\"errCode\":0,\"errMsg\":\"成功\",\"finish\":false,\"isMatchUpdateData\":true,\"mainObjApiName\":\"ContactObj\",\"objectApiName\":\"ContactObj\",\"outSideObjApiName\":\"contactTest\",\"ployDetailSnapshotId\":\"b35aae7515da479c9a58c1c94dd92bf9\",\"queryCrmObject2DestNode\":true,\"reverseWrite2Crm\":true,\"sourceData\":{\"lock_rule\":\"default_lock_rule\",\"enable_partner_view\":false,\"owner_department_id\":\"1000\",\"owner_department\":\"研发中心\",\"tel\":\"***********\",\"searchAfterId\":[\"*************\",\"655d70fc60d8b50001518e5d\"],\"add\":\"深圳\",\"is_relevance_wechat\":false,\"lock_status\":\"1\",\"package\":\"CRM\",\"create_time\":1700623642723,\"version\":\"3\",\"created_by\":[\"-10000\"],\"data_own_department\":[\"1000\"],\"owner_changed_time\":*************,\"name\":\"测试联系人\",\"name_order\":\"C#B2E2#CAD4#C1AA#CFB5#C8CB\",\"_id\":\"655d70fc60d8b50001518e5d\",\"tel1\":\"***********\",\"tenant_id\":\"88521\",\"lock_user\":[\"-10000\"],\"is_deleted\":false,\"object_describe_api_name\":\"ContactObj\",\"owner\":[\"1019\"],\"last_modified_time\":*************,\"life_status\":\"under_review\",\"contact_status\":\"0\",\"last_modified_by\":[\"-10000\"],\"record_type\":\"default__c\",\"account_id\":\"652d072cadc42500016eafbc\",\"account_id__relation_ids\":\"652d072cadc42500016eafbc\"},\"sourceDataCenterId\":\"643f7326b54ea8000176a191\",\"sourceEventType\":2,\"sourceTenantId\":\"88521\",\"sourceTenantType\":1,\"stop\":false,\"streamId\":\"2c72f8d5076f4f12a001bcde820be47e\",\"success\":true,\"syncDataData\":{\"destDataId\":\"661e1d5640aabd0007b93fba\",\"destEventType\":2,\"destObjectApiName\":\"contactTest_1hgshoasa\",\"destTenantId\":\"88521\",\"destTenantType\":2,\"id\":\"661e4441c2a5fb69180b252c\",\"sourceData\":{\"tenant_id\":\"88521\",\"owner\":[\"1019\"],\"object_describe_api_name\":\"ContactObj\",\"name\":\"测试联系人\",\"_id\":\"655d70fc60d8b50001518e5d\"},\"sourceDataId\":\"655d70fc60d8b50001518e5d\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"ContactObj\",\"sourceTenantId\":\"88521\",\"sourceTenantType\":1,\"status\":2,\"syncPloyDetailSnapshotId\":\"b35aae7515da479c9a58c1c94dd92bf9\",\"tenantId\":\"88521\"},\"syncDataId\":\"661e4441c2a5fb69180b252c\",\"syncDataMap\":{\"661e4441c2a5fb69180b252c\":{\"destDataId\":\"661e1d5640aabd0007b93fba\",\"destDataName\":\"测试联系人\",\"destObjectApiName\":\"contactTest_1hgshoasa\",\"sourceDataId\":\"655d70fc60d8b50001518e5d\",\"sourceObjectApiName\":\"ContactObj\",\"syncDataId\":\"661e4441c2a5fb69180b252c\"}},\"syncLogId\":\"J-E.88521.0.ContactObj.27vaWWw29Us.3.0\",\"syncPloyDetailId\":\"2c72f8d5076f4f12a001bcde820be47e\",\"syncPloyDetailSnapshotId\":\"b35aae7515da479c9a58c1c94dd92bf9\",\"tenantId\":\"88521\",\"updateData\":{\"lock_rule\":\"default_lock_rule\",\"modifiedTime\":1713259492258,\"enable_partner_view\":false,\"owner_department_id\":\"1000\",\"owner_department\":\"研发中心\",\"tel\":\"***********\",\"add\":\"深圳\",\"is_relevance_wechat\":false,\"lock_status\":\"1\",\"package\":\"CRM\",\"create_time\":1700623642723,\"version\":\"3\",\"created_by\":[\"-10000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"1019\"],\"teamMemberType\":\"0\",\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\",\"teamMemberDeptCascade\":\"0\"}],\"data_own_department\":[\"1000\"],\"owner_changed_time\":*************,\"name\":\"测试联系人\",\"name_order\":\"C#B2E2#CAD4#C1AA#CFB5#C8CB\",\"_id\":\"655d70fc60d8b50001518e5d\",\"tel1\":\"***********\",\"tenant_id\":\"88521\",\"lock_user\":[\"-10000\"],\"is_deleted\":false,\"object_describe_api_name\":\"ContactObj\",\"owner\":[\"1019\"],\"last_modified_time\":*************,\"life_status\":\"under_review\",\"contact_status\":\"0\",\"last_modified_by\":[\"-10000\"],\"record_type\":\"default__c\",\"account_id\":\"652d072cadc42500016eafbc\",\"account_id__relation_ids\":\"652d072cadc42500016eafbc\"},\"writeResult\":{\"errCode\":0,\"errMsg\":\"成功\",\"simpleSyncData\":{},\"success\":true,\"syncDataId\":\"661e4441c2a5fb69180b252c\"}}";
        SyncDataContextEvent syncDataContextEvent=JSONObject.parseObject(data,new TypeReference<SyncDataContextEvent>(){});
        completeDataWriteManager.processMessage(syncDataContextEvent);
    }

    @Test
    public void testDataError(){
        GetConfigDto.Argument argument=new GetConfigDto.Argument();
        argument.setKey(EnterpriseConfigKey.CONFIG_KEY_ENTERPRISE_LOGO.getKey());
        argument.setEmployeeId(-10000);
        argument.setEnterpriseId(88521);
        GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
        getSimpleEnterpriseArg.setEnterpriseId(Integer.valueOf(88521));
        GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(getSimpleEnterpriseArg);
        GetConfigDto.Result config = enterpriseConfigService.getConfig(argument);
        String dataEvent="{\"completeDataProcess\":false,\"completeDataWrite\":false,\"crmObjApiName\":\"object_YAwO0__c\",\"dataReceiveType\":1,\"dataVersion\":1713171466744,\"dataVersionList\":[1713171466744],\"debugRecordIfDetailCauseMaterSync\":false,\"destDataCenterId\":\"643f7326b54ea8000176a191\",\"destEventType\":1,\"destObjectApiName\":\"object_YAwO0__c\",\"destTenantId\":\"88521\",\"destTenantType\":1,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":-139,\"errMsg\":\"同步前自定义函数执行失败：运算条件异常 Division by zero\",\"finish\":false,\"mainObjApiName\":\"BD_MATERIAL.BillHead\",\"msg\":\"119\",\"outSideObjApiName\":\"BD_MATERIAL\",\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"sourceData\":{\"tenant_id\":\"88521\",\"FMaterialGroup\":738522,\"SubHeadEntity3.FPurchaserId.FNumber__r\":[\"1049\"],\"modifiedTime\":1713171466774,\"erp_id\":\"CH3294\",\"FDescription\":\"123\",\"FMaterialGroup.FNumber\":\"1700216839357\",\"SubHeadEntity3.FPurchaserId.FNumber\":[\"1049\"],\"SubHeadEntity1.FIsSNManage\":false,\"last_modified_by\":[\"-10000\"],\"mongo_id\":\"65e199726d8dda28d26075f0\",\"SubHeadEntity1.FIsBatchManage\":false,\"FSpecification\":\" \",\"Number\":\"CH3294\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"FForbidStatus\":\"A\",\"name\":\"1941自动化产品20231023-73-edit\",\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"VirtualHasBatchAndSerial\":\"1\",\"comName\":\"CH3294#1941自动化产品20231023-73-edit\",\"_id\":\"CH3294\",\"erp_num\":\"CH3294\",\"FNumber\":\"CH3294\"},\"sourceDataCenterId\":\"643f7322b54ea80001767d86\",\"sourceEventType\":1,\"sourceTenantId\":\"88521\",\"sourceTenantType\":2,\"stop\":true,\"streamId\":\"e89f3c7a82ab4719bc1c3efc7be333e6\",\"success\":false,\"syncLogId\":\"J-E.88521.0.BD_MATERIAL.27tu5H3HF2E.115.0\",\"syncPloyDetailSnapshotId\":\"99c49601409f44c3af3733605a16f1c0\",\"tenantId\":\"88521\"}";
        SyncDataContextEvent syncDataContextEvent=JSONObject.parseObject(dataEvent,SyncDataContextEvent.class);
        List<SyncDataEntity> dataEntities= JSONArray.parseArray("[{\"createTime\":1713171479224,\"dataReceiveType\":1,\"destObjectApiName\":\"object_YAwO0__c\",\"destTenantId\":\"88521\",\"destTenantType\":1,\"errorCode\":\"-139\",\"id\":\"661cec171ba22a28fb891d60\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"同步前自定义函数执行失败：运算条件异常 Division by zero\",\"sourceData\":{\"tenant_id\":\"88521\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"name\":\"1941自动化产品20231023-73-edit\",\"_id\":\"CH3294\"},\"sourceDataId\":\"CH3294\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":1,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"88521\",\"sourceTenantType\":2,\"status\":1,\"syncLogId\":\"J-E.88521.0.BD_MATERIAL.27tu5H3HF2E.115.0\",\"syncPloyDetailSnapshotId\":\"99c49601409f44c3af3733605a16f1c0\",\"tenantId\":\"88521\",\"updateTime\":1713171479224}]",
                SyncDataEntity.class);
        reSyncDataNodeManager.saveErrorSyncDataByCache(syncDataContextEvent,dataEntities);
        //        reSyncDataNodeManager.saveErrorSyncData("88521",TenantType.ERP, DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType(),)
    }

    @Test
    public void testPGeError(){

        List<String> includeExceptionList=Lists.newArrayList("com.alibaba.druid.pool.GetConnectionTimeoutException","com.alibaba.druid.pool.DataSourceNotAvailableException","");
        // 创建 Druid 数据源并进行基本配置
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl("**************************************************");
        dataSource.setUsername("erp_sync_data_user");
        dataSource.setPassword("co2POC67VFIUHYQW");

        // 设置连接超时时间为 1 秒
        dataSource.setMaxWait(10);

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement("SELECT pg_sleep(30)")) {
            statement.execute();
            // 如果连接成功，则不会抛出连接超时异常
            System.out.println("Query executed successfully");
        } catch (Throwable e) {
            if(includeExceptionList.contains(e.getClass().getName())){
                System.out.println("Connection timeout error DataSourceNotAvailableException");
            }
            System.out.println("Connection timeout error occurred");
        }
    }

}
