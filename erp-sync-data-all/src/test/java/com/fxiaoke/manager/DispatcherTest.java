package com.fxiaoke.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData;
import com.fxiaoke.open.erpsyncdata.main.dispatcher.processor.DispatcherEventListen;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/20 16:46
 * @Version 1.0
 */
@Ignore
@Slf4j
public class DispatcherTest extends BaseTest {
    @Autowired
    private DispatcherEventListen dispatcherEventListen;


    @Test
    public  void convertSql(){
        Integer sourceTenantType= TenantType.CRM;
        Integer dataReceiveType= sourceTenantType.equals(TenantType.CRM) ? DataReceiveTypeEnum.PAAS_META_EVENT.getType() : DataReceiveTypeEnum
                .AUTO_POLLING_ERP.getType();
        System.out.println();
        List<DispatcherEventData> events = Lists.newArrayList();
        DispatcherEventData dispatcherEventData=new DispatcherEventData();
        dispatcherEventData.setApiName("SalesOrderObj");
        dispatcherEventData.setTenantType(TenantTypeEnum.CRM.getType());
        dispatcherEventData.setDataId("644202a28d392e00019e3133");
        dispatcherEventData.setTenantId("84801");
        ObjectData objectData=new ObjectData();
        objectData.put("remark","备注测试");
        objectData.put("object_describe_api_name","AccountObj");
        objectData.put("_id","644202a28d392e00019e3133");
        objectData.put("modifiedTime",System.currentTimeMillis()-100);
        List<String> dataJson=Lists.newArrayList(JSONObject.toJSONString(objectData));
        dispatcherEventData.setUpdateJson(dataJson);
        dispatcherEventData.setModifiedTime(System.currentTimeMillis());
        dispatcherEventData.setEventType(EventTypeEnum.UPDATE.getType());
        dispatcherEventData.setEventTypeList(Lists.newArrayList(EventTypeEnum.UPDATE.getType()));
        events.add(dispatcherEventData);
        dispatcherEventListen.listen(events);
    }

}
