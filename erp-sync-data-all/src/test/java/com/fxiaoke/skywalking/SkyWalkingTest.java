package com.fxiaoke.skywalking;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.qixin.api.constant.AuthSourceType;
import com.facishare.qixin.api.model.EnterpriseEnv;
import com.facishare.qixin.api.model.message.content.AdvanceText;
import com.facishare.qixin.api.model.message.content.TextInfo;
import com.facishare.qixin.api.model.open.arg.OpenSendMessageBatchAsyncArg;
import com.facishare.qixin.api.model.session.InternationalInfo;
import com.facishare.qixin.api.open.OpenMessageBatchService;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.util.RandomUtil;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncWalkingNodeResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialWayDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseLinkWalkingService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.SyncLogManagerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.NewSaveResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpConnectService;
import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityObj;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.MongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.vo.SyncDataNotifyExcelVo;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.main.service.CRMOuterServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.RefreshSettingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SimpleSyncDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TaskNumInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpTempDataMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2CrmManager;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/24 17:40
 * @Version 1.0
 */

@Slf4j
public class SkyWalkingTest extends BaseTest {

    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private DoWrite2CrmManager doWrite2CrmManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ReadWalkingServiceImpl readWalkingService;
    @Autowired
    private BeforeFunctionWalkingServiceImpl functionWalkingService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private TempDataWalkingServiceImpl tempDataWalkingService;
    @Autowired
    private DataFilterServiceImpl dataFilterService;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;
    @Autowired
    private SfaApiManager sfaApiManager;

    @Autowired
    private MidFunctionWalkingServiceImpl midFunctionWalkingService;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private MongoStore mongoStore;
    @Autowired
    private MigrateTableManager migrateTableManager;
    @Autowired
    private FileService fileService;
    @Autowired
    private OpenMessageBatchService openMessageBatchService;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private CRMOuterServiceImpl crmOuterService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ErpConnectService erpConnectService;
    @Autowired
    private SpecialWayDataService specialWayDataService;
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Test
    public void testOa() {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj=new com.fxiaoke.crmrestapi.common.data.HeaderObj(88521,1000);
        ActionEditArg actionEditArg=JSONObject.parseObject("{\"allowEditLeadsStatus\":null,\"details\":{},\"fillOutOwner\":false,\"objectData\":{\"tenant_id\":\"88521\",\"name\":\"推送客户\",\"remark4__c\":\"1092\",\"_id\":\"667bb5ee4c593c0006f4c6fd\",\"record_type\":\"default__c\",\"object_describe_api_name\":\"AccountObj\"},\"optionInfo\":null}",ActionEditArg.class);


        metadataActionService.edit(headerObj, "AccountObj", false, false, null, null, actionEditArg);

        String dataId="{\n" +
                "    \"status\": 1,\n" +
                "    \"filterAll\": \"780777150699143168\",\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 20,\n" +
                "    \"currentDcId\": \"780777150699143168\"\n" +
                "}";
        ListIntegrationStreamArg listIntegrationStreamArg = JSONObject.parseObject(dataId, ListIntegrationStreamArg.class);

        String s = LogIdUtil.get();
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setTenantId("1111");
        ObjectId objectId = ObjectId.get();
        String ObjectId = objectId.toString();
        syncLogManager.initLogId("81243", "objapiname");

        interfaceMonitorManager.saveErpInterfaceMonitor("81243", "100811", "objapiname", "create",
                "saveArg", "result", 1, 111111l, 100l, "备注", "J.E.11.111", 100l, timeFilterArg);
    }
    @Test
    public  void convertSql(){
        try {
            String result = migrateTableManager.initTenantTable("84141");
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testMongoProjection(){
        Result<List<InterfaceMonitorData>> result=chInterfaceMonitorManager.listInterfaceMonitorByResult("84801","780777150699143168","BD_Customer",
                Lists.newArrayList("queryMasterBatch"),null,null,null,0,100,null,null,"1");
        chInterfaceMonitorManager.listInterfaceMonitorByInterfaceMonitorDataInType("81961","766345561596624896","BD_Customer",
                Lists.newArrayList("queryMasterBatch"),null,null,null,null,0,100,null,null);
    }

    @Test
    public void testId(){

        ErpTempData erpTempData=new ErpTempData();
        erpTempData.setRemark("准备成功");
        syncLogManager.saveErpTempLog("81243", SyncLogTypeEnum.DATA_SYNC_FILTER, SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),null, erpTempData);

        System.out.println(11);
    }

    @Test
    public void testFunction() {
        SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.valueOf(SyncLogTypeEnum.AFTER_FUNCTION.getType());
        StreamLogQueryArg streamLogQueryArg = new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("db48ee1d3b864371ab7fcf3666995ba0");
        BaseLinkWalkingService instance = SyncLogManagerFactory.getInstance(syncLogTypeEnum);
        Result result1 = instance.queryListLogByType("81243", streamLogQueryArg,null);
//        StreamLogQueryArg streamLogQueryArg=new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("d28e030c7c1b427e999a140181101aa7");
        Result result = functionWalkingService.queryListLogByType("81961", streamLogQueryArg,null);
        log.info("result");
    }

    @Test
    public void testRead() {
        LogIdUtil.clear();
        String preLogId=LogIdUtil.get();
        String s = LogIdUtil.buildChildLogId(preLogId, 1);
        StreamLogQueryArg streamLogQueryArg = new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("d28e030c7c1b427e999a140181101aa7");
        streamLogQueryArg.setNodeEnum(SyncLogTypeEnum.READ.getType());
        streamLogQueryArg.setPageNum(1);
        streamLogQueryArg.setPageSize(100);
        streamLogQueryArg.setSyncLogId("J-E.81961.0.BD_MATERIAL.c75f207ac46842a0b34a57ebc5f23022");
        streamLogQueryArg.setDcId("766345561596624896");
        Result<Page<ErpInterfaceMonitorResult>> pageResult1 = readWalkingService.queryListLogByType("81961", streamLogQueryArg,null);
        Result<Page<ErpInterfaceMonitorResult>> pageResult = dataFilterService.queryListLogByType("81243", streamLogQueryArg,null);

//        streamLogQueryArg.setPloyDetailId("d28e030c7c1b427e999a140181101aa7");
//        streamLogQueryArg.setNodeEnum(SyncLogTypeEnum.TEMP.getType());
//        Result<Page<ErpTempDataMonitorResult>> pageResult1 = tempDataWalkingService.queryListLogByType("81961", streamLogQueryArg);

//        log.info("result:{}",pageResult1);
    }

    @Test
    public void testMessage(){
        String xOrderNo="\"单据编号为“YFKJ202207S000353_V001”的销售订单新变更单，保存成功！\"";
        String xOrderNo23="";
        xOrderNo=xOrderNo.substring(xOrderNo.indexOf("“")+1,xOrderNo.indexOf("”"));

        log.info("eeeee");

    }

    @Test
    public void testLogSize() {
        SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.valueOf("READ");
        String sizeByType = LogIdUtil.getLogIdByType("81961","J-E.81961.0.SAL_SaleOrder.WyBZzfVYqZ", SyncLogTypeEnum.AFTER_SYSTEM_PROCESS);
        log.info("sizeByType");
    }

    @Test
    public void testQueryListLogByType() {
        queryListLogByType(SyncLogTypeEnum.READ);
        queryListLogByType(SyncLogTypeEnum.TEMP);
        queryListLogByType(SyncLogTypeEnum.WRITE);
    }


    private void queryListLogByType(SyncLogTypeEnum typeEnum) {
        StreamLogQueryArg streamLogQueryArg = new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("6bb1da9c34fa48efba5b3cc3bbf723e5");
        streamLogQueryArg.setNodeEnum(typeEnum.getType());
        streamLogQueryArg.setPageNum(1);
        streamLogQueryArg.setPageSize(20);
        BaseLinkWalkingService instance = SyncLogManagerFactory.getInstance(typeEnum);
        Result<?> result = instance.queryListLogByType("83952", streamLogQueryArg,null);
        log.info("result:{}", result);
    }

    @Test
    public void testERP() {
//        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj=new com.fxiaoke.crmrestapi.common.data.HeaderObj(84801,1000);
//
//        ActionAddArg actionArg=JSONObject.parseObject("{\"details\":{\"PriceBookAccountObj\":[{\"tenant_id\":\"84801\",\"owner\":[\"1000\"],\"_id\":\"642e6fc614969900014c5636\",\"object_describe_api_name\":\"PriceBookAccountObj\",\"price_book_id\":\"642e6fc614969900014c5632\"}]},\"fillOutOwner\":false,\"objectData\":{\"tenant_id\":\"84801\",\"owner\":[\"1000\"],\"end_date\":*************,\"active_status\":\"1\",\"apply_account_range\":\"{\\\"type\\\":\\\"CONDITION\\\",\\\"value\\\":\\\"[{\\\\\\\"filters\\\\\\\":[{\\\\\\\"field_name\\\\\\\":\\\\\\\"account_type\\\\\\\",\\\\\\\"operator\\\\\\\":\\\\\\\"IN\\\\\\\",\\\\\\\"field_values\\\\\\\":[\\\\\\\"lA00s9g93\\\\\\\"],\\\\\\\"connector\\\\\\\":\\\\\\\"AND\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"select_one\\\\\\\"}],\\\\\\\"connector\\\\\\\":\\\\\\\"OR\\\\\\\"}]\\\"}\",\"object_describe_api_name\":\"PriceBookObj\",\"name\":\"测试客户\",\"_id\":\"642e6fc614969900014c5632\",\"record_type\":\"default__c\",\"start_date\":*************}}",ActionAddArg.class);
//        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> priceBookObj = metadataActionService.add(headerObj, "PriceBookObj", actionArg);
//
//
//        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> priceBookObj1 = metadataActionService.add(headerObj, "PriceBookObj", actionArg);
        String json="{\n" +
                "        \"dataId\":\"829932\",\n" +
                "        \"destData\":{\n" +
                "            \"object_describe_api_name\":\"PriceBookObj\",\n" +
                "            \"tenant_id\":\"84801\",\n" +
                "            \"apply_account_range\":\"{\\\"type\\\":\\\"CONDITION\\\",\\\"value\\\":\\\"[{\\\\\\\"filters\\\\\\\":[{\\\\\\\"field_name\\\\\\\":\\\\\\\"account_type\\\\\\\",\\\\\\\"operator\\\\\\\":\\\\\\\"IN\\\\\\\",\\\\\\\"field_values\\\\\\\":[\\\\\\\"lA00s9g93\\\\\\\"],\\\\\\\"connector\\\\\\\":\\\\\\\"AND\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"select_one\\\\\\\"}],\\\\\\\"connector\\\\\\\":\\\\\\\"OR\\\\\\\"}]\\\"}\",\n" +
                "            \"owner\":[\n" +
                "                \"1000\"\n" +
                "            ],\n" +
                "            \"record_type\":\"default__c\",\n" +
                "            \"active_status\":\"1\",\n" +
                "            \"name\":\"测试客户1\",\n" +
                "            \"start_date\":*************,\n" +
                "            \"end_date\":*************,\n" +
                "            \"_id\":\"642e6fc614969900014c5633\"\n" +
                "        },\n" +
                "        \"destDataId\":\"642e6fc614969900014c5632\",\n" +
                "        \"destDetailSyncDataIdAndDestDataMap\":{\n" +
                "            \"642ea91f2cf7593a7935c3dd\":{\n" +
                "                \"object_describe_api_name\":\"PriceBookProductObj\",\n" +
                "                \"tenant_id\":\"84801\",\n" +
                "                \"pricebook_id\":\"642e6fc614969900014c5632\",\n" +
                "                \"product_id\":\"64240bd4c9c5f10001511a7c\",\n" +
                "                \"stand_price\":10,\n" +
                "                \"_id\":\"642e6fc614969900014c5633\",\n" +
                "                \"owner\":[\n" +
                "                    \"1000\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"642ea91f2cf7593a7935c3de\":{\n" +
                "                \"object_describe_api_name\":\"PriceBookAccountObj\",\n" +
                "                \"tenant_id\":\"84801\",\n" +
                "                \"price_book_id\":\"642e6fc614969900014c5632\",\n" +
                "                \"owner\":[\n" +
                "                    \"1000\"\n" +
                "                ],\n" +
                "                \"_id\":\"642e6fc614969900014c5699\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"destEventType\":1,\n" +
                "        \"destObjectApiName\":\"PriceBookObj\",\n" +
                "        \"destTenantId\":\"84801\",\n" +
                "        \"destTenantType\":1,\n" +
                "        \"mainObjApiName\":\"BD_SAL_PriceList.BillHead\",\n" +
                "        \"objectApiName\":\"BD_SAL_PriceList.BillHead\",\n" +
                "        \"sourceTenantId\":\"84801\",\n" +
                "        \"syncDataId\":\"642ea91f2cf7593a7935c3dc\",\n" +
                "        \"syncDataMap\":{\n" +
                "            \"642ea91f2cf7593a7935c3dc\":{\n" +
                "                \"destDataId\":\"642e6fc614969900014c5632\",\n" +
                "                \"destDataName\":\"测试客户\",\n" +
                "                \"destObjectApiName\":\"PriceBookObj\",\n" +
                "                \"sourceDataId\":\"829932\",\n" +
                "                \"sourceObjectApiName\":\"BD_SAL_PriceList.BillHead\",\n" +
                "                \"syncDataId\":\"642ea91f2cf7593a7935c3dc\"\n" +
                "            },\n" +
                "            \"642ea91f2cf7593a7935c3de\":{\n" +
                "                \"destObjectApiName\":\"PriceBookAccountObj\",\n" +
                "                \"sourceDataId\":\"100174\",\n" +
                "                \"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\n" +
                "                \"syncDataId\":\"642ea91f2cf7593a7935c3de\"\n" +
                "            },\n" +
                "            \"642ea91f2cf7593a7935c3dd\":{\n" +
                "                \"destObjectApiName\":\"PriceBookProductObj\",\n" +
                "                \"sourceDataId\":\"108197\",\n" +
                "                \"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\n" +
                "                \"syncDataId\":\"642ea91f2cf7593a7935c3dd\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"syncPloyDetailId\":\"28fc765ad17d45098b08d67f3b4dd9b2\",\n" +
                "        \"syncPloyDetailSnapshotId\":\"9a89d5fd0f444b8d82a2234de4dc5582\",\n" +
                "        \"tenantId\":\"84801\",\n" +
                "        \"version\":1680779547193\n" +
                "}";
        SyncDataContextEvent data = JSONObject.parseObject(json, SyncDataContextEvent.class);
        SyncDataContextEvent doWriteResultData = doWrite2CrmManager.oneDataWrite(data);
    }

    @Test
    public void testPage() {
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();
        String data = JSONObject.toJSONString(pageResult);
    }

    @Test
    public void testNode() {
        String tenantId = "81961";
        String streamId = "d28e030c7c1b427e999a140181101aa7";
        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, streamId,null);
        SyncPloyDetailResult syncPloyDetailResult = byIdWithCache.getData();
        List<SyncWalkingNodeResult> results = Lists.newArrayList();
        for (SyncLogTypeEnum value : SyncLogTypeEnum.values()) {

            if (value.getType().equals(SyncLogTypeEnum.COMMON.getType()) || value.getType().equals(SyncLogTypeEnum.AFTER_SYSTEM_PROCESS.getType())) {
                continue;
            }
            if (value.getType().equals(SyncLogTypeEnum.PRE_FUNCTION.getType()) && ObjectUtils.isEmpty(syncPloyDetailResult.getBeforeFuncApiName())) {
                continue;
            }
            if (value.getType().equals(SyncLogTypeEnum.MID_FUNCTION.getType()) && ObjectUtils.isEmpty(syncPloyDetailResult.getDuringFuncApiName())) {
                continue;
            }
            if (value.getType().equals(SyncLogTypeEnum.AFTER_FUNCTION.getType()) && ObjectUtils.isEmpty(syncPloyDetailResult.getAfterFuncApiName())) {
                continue;
            }

            SyncWalkingNodeResult nodeResult = SyncWalkingNodeResult.builder().nodeName(value.getDescription())
                    .nodeType(value.getType()).nodeOrder(value.getOrder()).build();
            results.add(nodeResult);
        }
        List<SyncWalkingNodeResult> collect = results.stream().sorted(Comparator.comparing(SyncWalkingNodeResult::getNodeOrder)).collect(Collectors.toList());
        log.info("result");

    }

    @Test
    public void testRead2() {
        StreamLogQueryArg streamLogQueryArg = new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("936dbf22e1294752a80a4e272df9b916");
        streamLogQueryArg.setNodeEnum(SyncLogTypeEnum.READ.getType());
        streamLogQueryArg.setPageNum(1);
        streamLogQueryArg.setPageSize(100);
        streamLogQueryArg.setDcId("780777150699143168");
//        streamLogQueryArg.setSyncLogId("J-E.81243.0.SalesOrderObj.WB3dW42ufS.0.0");
        Result<Page<ErpInterfaceMonitorResult>> pageResult = readWalkingService.queryListLogByType("84801", streamLogQueryArg,null);
        log.info("result:{}", pageResult);
    }

    @Test
    public void testTemp() {
        StreamLogQueryArg streamLogQueryArg = new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("492b449a89e944d098c8552f9b42a418");
        streamLogQueryArg.setNodeEnum(SyncLogTypeEnum.DATA_SYNC_FILTER.getType());
        streamLogQueryArg.setDataId("6380a09f6fd6b60001475432");
        streamLogQueryArg.setPageNum(1);
        streamLogQueryArg.setPageSize(20);
        streamLogQueryArg.setDcId("766345561596624896");
        Result<Page<ErpTempDataMonitorResult>> pageResult = dataFilterService.queryListLogByType("81961", streamLogQueryArg,null);
        log.info("result:{}", pageResult);
    }

    @Test
    public void testFilter() {

    }

    @Test
    public void functionById(){

    }


    @Test
    public void testFunctionDetail(){
        HeaderObj headerObj=new HeaderObj(81243,1000);
        ObjectData objectData = sfaApiManager.queryFunctionRunDetail("88466", "FuncSyncBefore__c", "J-E.88466.0.STK_Inventory.2901mNUxWIo.0.0");
        boolean errCode = objectData.getBoolean("success");
        String resultMessage = objectData.getString("results");
        List<ObjectData> results1 = JSONArray.parseArray(resultMessage, ObjectData.class);
        Object success = results1.get(0).get("success");
        log.info("objectData");
    }

    @Test
    public void filterType(){
//        String srcObjApiName="SAL_SaleOrder.BillHead";
//        String srcObjApiName2="SAL_SaleOrder.SaleOrderEntry";
//        String realObjApiName = idFieldConvertManager.getRealObjApiName("81243", srcObjApiName);
//        String realObjApiName2 = idFieldConvertManager.getRealObjApiName("81243", srcObjApiName2);
//        String realApiNameFromPreApiName = baseLinkService.getRealApiNameFromPreApiName("81243", "8874a6724d06464aa00538256fe4fecb", "SAL_SaleOrder.SaleOrderEntry");


        StreamLogQueryArg streamLogQueryArg = new StreamLogQueryArg();
        streamLogQueryArg.setPloyDetailId("8874a6724d06464aa00538256fe4fecb");
        streamLogQueryArg.setNodeEnum("DATA_SYNC_FILTER");
        streamLogQueryArg.setObjApiName("SAL_SaleOrder.BillHead");
        streamLogQueryArg.setDataId("CH1214");
//        streamLogQueryArg.setSyncLogId("J-E.81243.0.SalesOrderObj.WBcMm6iQ4E.0.0");
//        streamLogQueryArg.setStartTime(1656148244000L);
//        streamLogQueryArg.setEndTime(1656321044191L);

        Result<Page<ErpTempDataMonitorResult>> pageResult = dataFilterService.queryListLogByType("81243", streamLogQueryArg,null);
    }

    @Test
    public void testLogId() {
        String logId = "";
        boolean b = LogIdUtil.inValidLogId(logId);
        if (ObjectUtils.isEmpty(logId) || LogIdUtil.inValidLogId(logId)) {
            System.out.println(1);
        }


        logId = null;
        if (ObjectUtils.isEmpty(logId) || LogIdUtil.inValidLogId(logId)) {
            System.out.println(2);
        }
        System.out.println();
    }

    @Test
    public void testDetail() {

    }


    @Test
    public void testDetail1() {

        String data="{\"IsSuccess\":true,\"Message\":\"单据编号为“YFKJ202207S000376_V001”的销售订单新变更单，保存成功！\",\"Datas\":[{\"FID\":127504,\"FBillNo\":\"YFKJ202207S000376_V001\",\"SaleOrderEntry\":[{\"FEntryID\":\"133009\",\"FSeq\":\"1\",\"FChangeType\":\"B\",\"FRowType\":\"Standard\",\"FMaterialId\":745045,\"FMaterialName\":\"QingCloud桌面云系统\",\"FQtyX\":1,\"FQty\":1,\"FPriceX\":1327.433628,\"FPrice\":1327.433628,\"FTaxPricex\":1500,\"FTaxPrice\":1500,\"FAmount\":1061.95,\"FAllAmount\":1200,\"FPKIDX\":159516,\"FRowId\":\"52549efb-f50b-80f2-11ed-098cdc64f64a\",\"FParentRowId\":\" \"},{\"FEntryID\":\"133010\",\"FSeq\":\"2\",\"FChangeType\":\"B\",\"FRowType\":\"Standard\",\"FMaterialId\":748826,\"FMaterialName\":\"蔷薇灵动蜂巢自适应微隔离安全平台V2.0--基础软件平台（物理机、虚拟机）-单机版\",\"FQtyX\":1,\"FQty\":1,\"FPriceX\":884955.752212,\"FPrice\":884955.752212,\"FTaxPricex\":1000000,\"FTaxPrice\":1000000,\"FAmount\":884.96,\"FAllAmount\":1000,\"FPKIDX\":159517,\"FRowId\":\"52549efb-f50b-80f2-11ed-098cdc64f64b\",\"FParentRowId\":\" \"}]}]}";
        String data2="{\"Result\":{\"ResponseStatus\":{\"IsSuccess\":false,\"Errors\":[{\"Message\":\"订单已存在未生效的变更单信息，不能再次执行变更操作！\"}]}}}";
        NewSaveResult newSaveResult21=JsonUtil.fromJson(data,NewSaveResult.class);
        NewSaveResult newSaveResult2=JsonUtil.fromJson(data2,NewSaveResult.class);
        int compare = VersionComparator.INSTANCE.compare("8.0.277.101", "8.0.277.10");

        String json="{\"IsSuccess\":true,\"Message\":\"单据编号为“YFKJ202207S000353_V001”的销售订单新变更单，保存成功！\",\"Datas\":[{\"FID\":127342,\"FBillNo\":\"YFKJ202207S000353_V001\",\"SaleOrderEntry\":[{\"FEntryID\":\"132949\",\"FSeq\":\"1\",\"FChangeType\":\"B\",\"FRowType\":\"Standard\",\"FMaterialId\":745045,\"FMaterialName\":\"QingCloud桌面云系统\",\"FQtyX\":1.0,\"FQty\":1.0,\"FPriceX\":1327.433628,\"FPrice\":1327.433628,\"FTaxPricex\":1500.0,\"FTaxPrice\":1500.0,\"FAmount\":1061.95,\"FAllAmount\":1200.0,\"FPKIDX\":158786,\"FRowId\":\"52549efb-f50b-80f0-11ed-03efb70b7743\",\"FParentRowId\":\" \"},{\"FEntryID\":\"132950\",\"FSeq\":\"2\",\"FChangeType\":\"B\",\"FRowType\":\"Standard\",\"FMaterialId\":748826,\"FMaterialName\":\"蔷薇灵动蜂巢自适应微隔离安全平台V2.0--基础软件平台（物理机、虚拟机）-单机版\",\"FQtyX\":1.0,\"FQty\":1.0,\"FPriceX\":884955.752212,\"FPrice\":884955.752212,\"FTaxPricex\":1000000.0,\"FTaxPrice\":1000000.0,\"FAmount\":884.96,\"FAllAmount\":1000.0,\"FPKIDX\":158787,\"FRowId\":\"52549efb-f50b-80f0-11ed-03efb70b7746\",\"FParentRowId\":\" \"}]}]}";
        NewSaveResult newSaveResult = JsonUtil.fromJson(json, NewSaveResult.class);



        StreamLogQueryArg.SyncLogDataArg syncLogDataArg = new StreamLogQueryArg.SyncLogDataArg();
        syncLogDataArg.setDcId("766345561596624896");
        syncLogDataArg.setId("62b0e411d265683825b3b097");
        syncLogDataArg.setNodeEnum("TEMP");

        Result<?> result = tempDataWalkingService.queryLogDataById("81961", syncLogDataArg,null,null);
        System.out.println(result);
    }

    @Test
    public void testLogIdFix(){
        try {
            LogIdUtil.reset(null);
            LogIdUtil.setStreamId(null);
            TraceUtil.initTrace(null);
            throw new ErpSyncDataException("111",null,null);
        } catch (Exception e) {
            log.info("syncMain service impl init logId error:{}","");
        }
        String s = LogIdUtil.get();
        LogIdUtil.reset("");
        String s1 = LogIdUtil.get();

        String logId = syncLogManager.initLogId("test001", "apiname");
        LogIdUtil.reset(null);
        String afterLogId=LogIdUtil.get();
        log.info("afterLogId");
    }

    @Test
    public void testLogIdUtil(){
        String s = LogIdUtil.get();
        LogIdUtil.reset("slogId");
        log.info("s");
    }
    @Test
    public void testFunction2(){
        String response="{\n" +
                "\t\"errCode\": 0,\n" +
                "\t\"errMessage\": \"OK\",\n" +
                "\t\"result\": {}\n" +
                "}";
        DocumentContext documentContext =
                JsonPath.using(Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL,
                        Option.SUPPRESS_EXCEPTIONS)).parse(response);

        Object object=documentContext.read("$.result.function.is_active");

        System.out.println("result");
    }

    @Test
    public void testTaskNum(){
        TaskNumInfo baseLog = TaskUtil.getTaskNumInfo();
        TaskUtil.setTaskNumInfo(TaskNumInfo.generateInfo("okkkkk",1));
        TaskNumInfo baseLog1 = TaskUtil.getTaskNumInfo();
    }

    @Test
    public void testJson(){
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(81031, -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ListObjectDescribeResult> resultList = objectDescribeService.list(headerObj, true, Lists.newArrayList("MultiUnitRelatedObj"));



        String result = FileUtil.readString(new File("D:/data/D.js"), Charset.defaultCharset());
        String result2 = JSONObject.toJSONString(result, true);
//        Bson filer = Filters.and(Filters.eq("_id", new ObjectId("63510838bb6c8641938489de")));
//        List<Bson> updates = new ArrayList<>();
//        updates.add(set("result",result));
//
//        UpdateResult updateResult = mongoStore.getInterfaceMonitorCollection("interface_monitor_81961").updateOne(filer, Updates.combine(updates));

        System.out.println("SkyWalkingTest.testJson");

    }



    private static Pattern numberPattern= Pattern.compile("^[0-9]+(.[0-9]+)?$");
    @Test
    public void testMatch(){

        boolean b = numberPattern.matcher("1e-2").find();
        System.out.println("SkyWalkingTest.testMatch");
    }

    @Test
    public void testUpdateQueryCode(){



        adminSyncPloyDetailService.updateUsedQueryField("88521","643f7322b54ea80001767d86","BD_Customer.BillHead", ErpChannelEnum.ERP_K3CLOUD);
//        adminSyncPloyDetailService.updateUsedQueryFieldByObjectApiName("81243","BOS_ASSISTANTDATA_DETAIL.BillHead");
    }

    @Test
    public void testUploadFile(){
        String sss;
        try {
            sss= URLEncoder.encode("object_x0df1__c", "utf-8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<SyncDataNotifyExcelVo> tempData = getData();
        BuildExcelFile.Arg<SyncDataNotifyExcelVo> arg = new BuildExcelFile.Arg<>();
        arg.setTenantId("88521");
        arg.setFileName("数据通知");
        arg.setDataList(tempData);
        arg.setSheetNames(Lists.newArrayList("数据同步列表"));
        Result<BuildExcelFile.Result> resultResult = fileService.buildExcelFile(arg,null);
        log.info("resultResult");
    }

    private List<SyncDataNotifyExcelVo> getData(){
        List<SyncDataNotifyExcelVo> voList=Lists.newArrayList();
        for (int i = 0; i < 100; i++) {
            SyncDataNotifyExcelVo syncDataNotifyExcelVo=new SyncDataNotifyExcelVo();
            syncDataNotifyExcelVo.setDataName(RandomUtil.randomString("测试"));
            syncDataNotifyExcelVo.setRemark("状态详情");
            syncDataNotifyExcelVo.setStatusMessage(SyncStatusEnum.SUCCESS.getName());
            voList.add(syncDataNotifyExcelVo);
        }
        return voList;
    }

    @Test
    public void testConditionParse(){
        IntegrationStreamNodesData integrationStreamNodesData=new IntegrationStreamNodesData();
        IntegrationStreamNodesData.NotifyComplementNode notifyComplementNode=new IntegrationStreamNodesData.NotifyComplementNode();
        notifyComplementNode.setNotifyStatus(Lists.newArrayList(1,2));

        List<IntegrationStreamNodesData.NotifyConditionFilter> notifyConditionFilters=Lists.newArrayList();
        IntegrationStreamNodesData.NotifyConditionFilter notifyConditionFilter=new IntegrationStreamNodesData.NotifyConditionFilter();
        notifyConditionFilter.setOperator("LIKE");
        notifyConditionFilter.setFieldValue("字段填写错误");
        IntegrationStreamNodesData.NotifyConditionFilter notifyConditionFilter_1=new IntegrationStreamNodesData.NotifyConditionFilter();
        notifyConditionFilter_1.setOperator("LIKE");
        notifyConditionFilter_1.setFieldValue("字段失败");
        notifyConditionFilters.add(notifyConditionFilter);
        notifyConditionFilters.add(notifyConditionFilter_1);
        notifyComplementNode.setNotifyConditionFilters(notifyConditionFilters);
        integrationStreamNodesData.setNotifyComplementNode(notifyComplementNode);

        List<List<FilterData>> rulesWithOr=Lists.newArrayList();

        for (IntegrationStreamNodesData.NotifyConditionFilter conditionFilter : integrationStreamNodesData.getNotifyComplementNode().getNotifyConditionFilters()) {
            FilterData filterData=new FilterData();
            filterData.setFieldValue(Lists.newArrayList(conditionFilter.getFieldValue()));
            filterData.setOperate(conditionFilter.getOperator());
            filterData.setFieldType(FieldType.STRING);
            filterData.setFieldApiName(conditionFilter.getFieldApiName());
            List<FilterData> andFilterData=Lists.newArrayList(filterData);
            rulesWithOr.add(andFilterData);
        }

        String expression = ConditionUtil.parseToOrExpression(rulesWithOr);
        Map<String,Object> envMap= Maps.newHashMap();
        envMap.put("remark","数据上报");

        Object execute = AviatorEvaluator.execute(expression, envMap, true);

        log.info("expression");
    }

    @Test
    public void testSendAppMessage(){
        AdvanceText advanceText = new AdvanceText();
        TextInfo textInfo1 = new TextInfo();
        textInfo1.setActionType(TextInfo.None);
        textInfo1.setContent("同步数据1"+"\n"+"换行了");
        textInfo1.setNewLine(true);//换行

        TextInfo textInfo2 = new TextInfo();
        textInfo2.setActionType(TextInfo.URL);
        textInfo2.setContent("可以点击跳转");
        textInfo2.setUrl("https://www.ceshi112.com/");
        textInfo2.setNewLine(true);//换行
        TextInfo textInfo3 = new TextInfo();
        textInfo3.setActionType(TextInfo.URL);
        textInfo3.setContent("可以点击跳转<>");
        textInfo3.setUrl("https://www.ceshi112.com/");
        textInfo3.setNewLine(true);//换行
        advanceText.setTextInfoList(Arrays.asList(textInfo1, textInfo2,textInfo3));


        OpenSendMessageBatchAsyncArg asyncArg = new OpenSendMessageBatchAsyncArg();
        asyncArg.setPostId( UUID.randomUUID().toString());//postId
        asyncArg.setToUserList(Arrays.asList(1007));//接收人员
        asyncArg.setMessageType("AT");//消息类型
        asyncArg.setMessageContent(JSON.toJSONString(advanceText));
        asyncArg.setInnerApp(false);
        asyncArg.setSource(AuthSourceType.system);
        asyncArg.setEnv(EnterpriseEnv.INNER);//内部企业
        asyncArg.setEnterpriseAccount("82777");//企业EA
        asyncArg.setAppId(ConfigCenter.ERP_SYNC_DATA_APP_ID);//服务号ID
        asyncArg.setContentInfo(new InternationalInfo());//内容国际化信息
        asyncArg.setSummaryInfo(new InternationalInfo());//推送描述国际化信息
        boolean b = openMessageBatchService.sendMessageBatchAsyncV2(asyncArg);
        System.out.println("SkyWalkingTest.testSendAppMessage");


    }


    @Test
    public void testPushATMessage() throws IllegalAccessException {
        Map<String, Object> objectMap = Maps.newHashMap();
        objectMap.put("IsDeleteEntry", "false");
        objectMap.put("ValidateFlag", "false");
        SaveArg saveArg = new SaveArg();
        saveArg.put("IsDeleteEntry","false");
        saveArg.setNeedReturnFields(Lists.newArrayList("11111"));
        String json =JSONObject.toJSONString(saveArg);
        String jsonBody = JacksonUtil.toJson(saveArg);
        for (Field field : SaveArg.class.getFields()) {
            field.setAccessible(true);
            Object value = objectMap.get(field.getName());
            if (Objects.nonNull(value)) {
                field.set(saveArg, value);
            }
        }


        AdvanceText advanceText = new AdvanceText();
        TextInfo textInfo1 = new TextInfo();
        textInfo1.setActionType(TextInfo.None);
        textInfo1.setContent("第一行");

        TextInfo textInfo2 = new TextInfo();
        textInfo2.setActionType(TextInfo.None);
        textInfo2.setContent("第二行");
        TextInfo textInfo3 = new TextInfo();
        textInfo3.setActionType(TextInfo.None);
        textInfo3.setContent("第三行");
        textInfo3.setNewLine(true);

        advanceText.setTextInfoList(Arrays.asList(textInfo1, textInfo2, textInfo3));


        OpenSendMessageBatchAsyncArg asyncArg = new OpenSendMessageBatchAsyncArg();
        asyncArg.setPostId( UUID.randomUUID().toString());//postId
        asyncArg.setToUserList(Arrays.asList(1007));//接收人员
        asyncArg.setMessageType("AT");//消息类型
        asyncArg.setMessageContent(JSON.toJSONString(advanceText));
        asyncArg.setInnerApp(true);
        asyncArg.setSource(AuthSourceType.system);
        asyncArg.setEnv(EnterpriseEnv.INNER);//内部企业
        asyncArg.setEnterpriseAccount("82777");//企业EA
        asyncArg.setAppId("open_FSAID_9897f5");//服务号ID
        asyncArg.setContentInfo(new InternationalInfo());//内容国际化信息
        asyncArg.setSummaryInfo(new InternationalInfo());//推送描述国际化信息
        openMessageBatchService.sendMessageBatchAsyncV2(asyncArg);

    }


    @Test
    public void testProduce(){
        Triple triple = ImmutableTriple.of("82777", "BD_MATERIAL.BillHead","ProductObj");
        String result = JSONObject.toJSONString(triple);
        SyncStatusMessageArg syncStatusMessageArg = generateTenantIdInfo(result);
        List<SimpleSyncDataArg> simpleSyncDataArgs = JSONArray.parseArray("[\n" +
                "    {\n" +
                "        \"sourceDataId\":\"103891#XSDD002598\",\n" +
                "        \"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\n" +
                "        \"receivers\":1007,\n" +
                "        \"destObjectApiName\":\"ProductObj\",\n" +
                "        \"destDataId\":\"63b558ef6f0cf60001c3de2c\",\n" +
                "        \"tenantId\":\"82777\",\n" +
                "        \"remark\":\"success3c732d57df1b4dc4bd7f5f4623d4974f\",\n" +
                "        \"status\":3\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceDataId\":\"103891#XSDD002598\",\n" +
                "        \"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\n" +
                "        \"receivers\":1007,\n" +
                "        \"destObjectApiName\":\"ProductObj\",\n" +
                "        \"destDataId\":\"63b558ef6f0cf60001c3de2c\",\n" +
                "        \"tenantId\":\"82777\",\n" +
                "        \"remark\":\"success3ac5caf9cc444e728cc262f424406b83\",\n" +
                "        \"status\":3\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceDataId\":\"103891#XSDD002598\",\n" +
                "        \"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\n" +
                "        \"receivers\":1007,\n" +
                "        \"destObjectApiName\":\"ProductObj\",\n" +
                "        \"destDataId\":\"63b558ef6f0cf60001c3de2c\",\n" +
                "        \"tenantId\":\"82777\",\n" +
                "        \"remark\":\"successea0c1dc4462d4289bc38eba3c73ac1ea\",\n" +
                "        \"status\":3\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceDataId\":\"103891#XSDD002598\",\n" +
                "        \"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\n" +
                "        \"receivers\":1007,\n" +
                "        \"destObjectApiName\":\"ProductObj\",\n" +
                "        \"destDataId\":\"63b558ef6f0cf60001c3de2c\",\n" +
                "        \"tenantId\":\"82777\",\n" +
                "        \"remark\":\"successea0c1dc4462d4289bc38eba3c73ac1ea\",\n" +
                "        \"status\":6\n" +
                "    }\n" +
                "]", SimpleSyncDataArg.class);
        sendSyncMessageToUser(simpleSyncDataArgs,syncStatusMessageArg);
    }

    private SyncStatusMessageArg generateTenantIdInfo(String tenantApiName){
        //SYNC_DATA_NOTIFY_企业id_sourceApiName_destApiName
        MutableTriple<String,String,String> triple=JSONObject.parseObject(tenantApiName,MutableTriple.class);
        String tenantId=triple.getLeft();
        String sourceApiName=triple.getMiddle();
        String destApiName=triple.getRight();
        SyncPloyDetailEntity entityByTenantIdAndObjApiName = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, sourceApiName, destApiName);
        SyncStatusMessageArg syncStatusMessageArg=new SyncStatusMessageArg();
        syncStatusMessageArg.setTenantId(tenantId);
        syncStatusMessageArg.setConnInfoName(entityByTenantIdAndObjApiName.getIntegrationStreamName());
        syncStatusMessageArg.setEndTime(System.currentTimeMillis());
        //减掉聚合时间
        syncStatusMessageArg.setStartTime(System.currentTimeMillis()- TimeUnit.MINUTES.toMillis(5));
        String direction=entityByTenantIdAndObjApiName.getSourceTenantType().equals(TenantTypeEnum.CRM.getType())?"CRM->ERP":"ERP->CRM";
        syncStatusMessageArg.setSyncDirection(direction);
        return syncStatusMessageArg;

    }

    public void sendSyncMessageToUser(List<SimpleSyncDataArg> syncDataString, SyncStatusMessageArg syncStatusMessageArg){
        log.info("syncDataString:{}",JSONObject.toJSONString(syncDataString));
        Map<Integer, List<SimpleSyncDataArg>> receiverUserMap = syncDataString.stream().collect(Collectors.groupingBy(SimpleSyncDataArg::getReceivers));
        //组装发布消息
        for (Integer received : receiverUserMap.keySet()) {
            List<SimpleSyncDataArg> simpleSyncDataArgs = receiverUserMap.get(received);
            Map<Integer, List<SyncStatusMessageArg.SyncDataStatusMessage>> statusAllList = simpleSyncDataArgs.stream().map(item -> {
                SyncStatusMessageArg.SyncDataStatusMessage syncDataStatusMessage = new SyncStatusMessageArg.SyncDataStatusMessage();
                String dataName=StringUtils.isNotEmpty(item.getSourceDataName())?item.getSourceDataName():item.getSourceDataId();
                syncDataStatusMessage.setDataName(dataName);
                syncDataStatusMessage.setStatus(SyncDataStatusEnum.isSyncDataStatusReturnInt(item.getStatus()));
                syncDataStatusMessage.setStatusMessage(SyncStatusEnum.getNameByStatus(SyncDataStatusEnum.isSyncDataStatusReturnInt(item.getStatus())));
                syncDataStatusMessage.setRemark(item.getRemark());
                return syncDataStatusMessage;
            }).collect(Collectors.groupingBy(item -> item.getStatus()));

            syncStatusMessageArg.setSuccessList(statusAllList.get(SyncStatusEnum.SUCCESS.getStatus()));
            syncStatusMessageArg.setErrorList(statusAllList.get(SyncStatusEnum.FAILED.getStatus()));
            Result<List<TextInfo>> messageResult = notificationService.generateDataSyncResult(syncStatusMessageArg);
            notificationService.sendErpSyncDataAppMultiNotice(messageResult.getData(),
                    syncStatusMessageArg.getTenantId(),
                    Lists.newArrayList(received),
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager,null,syncStatusMessageArg.getTenantId()),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL,null);
        }

    }


    @Test
    public void batchGetObject(){
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf("84801"), CrmConstants.SYSTEM_USER);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName("AccountObj");
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.setSearchSource("db");
        ArrayList<String> dataLists = Lists.newArrayList("6438f726c71def00018fc7a6", "6438f4b0c71def00018e9865", "6438f00fc71def00018c45e3", "6434c672c3dbba00012a153b");
        SearchTemplateQuery copy = BeanUtil.copy(searchQueryInfo, SearchTemplateQuery.class);
        searchQueryInfo.addFilter("_id", dataLists,FilterOperatorEnum.IN);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2","-1","0","1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> objectDataQueryListResultResult = objectDataServiceV3.queryList(headerObj, findV3Arg);
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2","-1","0","1"), FilterOperatorEnum.IN);
        copy.addFilter("_id", dataLists,FilterOperatorEnum.EQ);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(copy));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> objectDataQueryListResultResult1 = objectDataServiceV3.queryList(headerObj, findV3Arg);
        System.out.println("listObjectData"+objectDataQueryListResultResult1);
    }


    @Test
    public void syncList(){

        Result<Set<CheckAndUpdatePloyValidStatusDetailData>> setResult = adminSyncPloyDetailService.checkAndUpdatePloyStatus("88521", 1000, "65eae61485fc92000182469a", "65eae61685fc920001824712", 1, true,null);

        HashMap<Object, Object> objectHashMap = Maps.newHashMap();
        objectHashMap.put("cache","cacheValue");
        ArrayList<Integer> integers = Lists.newArrayList(0,1, 2, 3, 4, 5, 6, 7, 8);

        for (int i = 0; i < integers.size(); i++) {
            if(i%10==0){
                objectHashMap.clear();
                System.out.println("SkyWalkingTest.syncList"+i+JSONObject.toJSONString(objectHashMap));
                List<Integer> sub = CollectionUtil.sub(integers, i, i + 10);
                System.out.println("SkyWalkingTest.syncList"+JSONObject.toJSONString(sub));
                objectHashMap.put("cache","cacheValue"+i);
            }
            System.out.println(JSONObject.toJSONString(objectHashMap));
        }

    }

    @Test
    public  void testSqlInterceptor() throws IllegalAccessException {


        ErpConnectInfoEntity erpConnectInfoEntity=new ErpConnectInfoEntity();
        erpConnectInfoEntity.setConnectParams("{\"baseUrl\":\"http://172.31.100.60/k3cloud/\",\"dbId\":\"5bd164c806c6c8\",\"dbName\":\"接口环境（新）\",\"authType\":1,\"userName\":\"ces2\",\"password\":\"8888888\",\"lcid\":2052,\"useFsHttpClient\":true}");
        erpConnectInfoEntity.setTenantId("82712");

        erpConnectInfoEntity.setDataCenterName("金蝶K3Cloud");
        erpConnectInfoEntity.setEnterpriseName("库存专测3");
        erpConnectInfoEntity.setId("754388706949464064");
        //erpConnectInfoEntity.setConnectParams("B9c3q0Al55dST1HGKPdVyGCYiHuiQwQ7fb1gy9w/ZSKNs/48ZiFtV9ideNCY/ARtAz0drssjukL6yhvFBLKaMQzpDTKWav6VPQAC+B3XnA8t36NWHhFyIEsad/vHhGfXar8D1NdMLZuXHqWL1cD8GGOeuVHLcRmWAroE01xmcGub7SKFyid8SgwZVSW5YUMpJq3NqOKN2lDiLjhZZ60QVMe4E0y9xmy5OPNuqvrQAMDhrNe7ZrbNEgESo2N0p0Z1uzhmqAZzFOsM44DhIUUAgNMMQreYhZbmo/iZXgvo6M+PsA9h1B7Zzf3VjKlczk9ktGOqetOWbEQRn5AGEEz2LTVCevZ9asNOUINoHE9muuM=");
        //ErpConnectInfoEntity decrypt = SecurityUtil.decrypt(erpConnectInfoEntity);
        SecurityObj sensitiveData = AnnotationUtils.findAnnotation(erpConnectInfoEntity.getClass(), SecurityObj.class);
//        int updateResult = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdAdmin(erpConnectInfoEntity);
//        System.out.println("updateResult"+updateResult);
        ErpConnectInfoEntity erpConnectInfoEntity1 = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryInfoByName("82712", "金蝶K3Cloud");
        log.info("erpConnectInfoEntity1");

    }


    @Test
    public  void testConnect(){
        erpConnectService.actuatorSAPProxyStatus("http://*********:8081/");
    }

    @Test
    public  void testSqlQuery() throws IllegalAccessException {

        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId("81243", "63f82854a8bd974420925e83");

        RefreshSettingArg arg=new RefreshSettingArg();
        arg.setEnvType("GRAY_TENANTS");
        Set<String> set = new HashSet<>();
        if(ObjectUtils.isNotEmpty(arg.getEnvType())){
            ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.GRAY_TENANTS.name());
            if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
                set = ImmutableSet.copyOf(Splitter.on(";").split(configuration.getConfiguration()));
            }
        }
        if(!CollectionUtils.isEmpty(arg.getTenantIds())){
            set.addAll(arg.getTenantIds());
        }
        log.info("getEnv enterprsie:{}", JSONObject.toJSONString(set));
        for (String tenantValue : set) {
            try {
                List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listByTenantId(tenantValue);
                for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
                    erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(erpConnectInfoEntity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ErpConnectInfoEntity erpConnectInfoEntity1 = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryInfoByName("82712", "金蝶K3Cloud");
        log.info("erpConnectInfoEntity1");

    }

    @Test
    public void testSapFunctionCreate(){
        //函数入参
        Map objectImports=Maps.newHashMap();
        Map objectImport=Maps.newHashMap();
        Map sapFunctionParamsArgs=Maps.newHashMap();
        sapFunctionParamsArgs.put("functionName","BAPI_MATERIAL_SAVEDATA");
        objectImport.put("MATERIAL","P1P17LPLPLP");
        objectImport.put("IND_SECTOR","M");
        objectImport.put("MATL_TYPE","FERT");
        objectImport.put("BASIC_VIEW","X");
        objectImports.put("HEADDATA",objectImport);
        Map<String,Object> objectImportsClient= new HashMap<>();
        objectImportsClient.put("BASE_UOM","EA");
        objectImportsClient.put("BASE_UOM_ISO","EA");
        objectImports.put("CLIENTDATA",objectImportsClient);
        //CLIENTDATAX

        Map objectImportsClientX=Maps.newHashMap();
        objectImportsClientX.put("BASE_UOM","X");
        objectImportsClientX.put("BASE_UOM_ISO","X");
        objectImports.put("CLIENTDATAX",objectImportsClientX);
        Map<String, List<Object>> objectImportsTable= new HashMap<>();
        Map<String,Object> objectImportDesc= new HashMap<>();
        objectImportDesc.put("MATL_DESC","测试数据");
        objectImportDesc.put("LANGU","ZH");
        objectImportDesc.put("LANGU_ISO","ZH");
        objectImportsTable.put("MATERIALDESCRIPTION",Lists.newArrayList(objectImportDesc));

        sapFunctionParamsArgs.put("importParamsValues", objectImports);
        sapFunctionParamsArgs.put("tableParams", objectImportsTable);
        log.info("data:"+sapFunctionParamsArgs);
        String urlProxy="http://*********:8080/proxy/sapProxy/executeRfcMethod";
        Map<String,String> headerMap=Maps.newHashMap();
        headerMap.put("Content-Type","application/json");
        List<String> apinames=Lists.newArrayList("RETURN");
        sapFunctionParamsArgs.put("returnParamsField",apinames);

        Result result = proxyHttpClient.postUrl(urlProxy, sapFunctionParamsArgs, headerMap, new TypeReference<Result>() {
        });
//   String urlProxy="http://*********:8080/proxy/sapProxy/executeRfcMethod";
// def data= Fx.http.post( urlProxy,  [:],  sapFunctionParamsArgs,  20,  false,  0,  true);
//        Map bodyMap=[:];
//        bodyMap.put("sapFunctionParamsArgs",sapFunctionParamsArgs);
//        log.info("bodyMap:"+bodyMap)
//        StringBody body = StringBody.builder().content(bodyMap).build()
//        Request request = Request.builder()
//                .method("GET")
//                .url('http://172.31.100.26:8080/proxy/sapProxy/connectFunction')
//                .timeout(7000)
//                .retryCount(0)
//                .header("Content-Type", "application/json")
//
//                .build()
    }
}
