package com.fxiaoke.skywalking;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.K3RangerConvertManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.PriceListSpecialBusinessImpl;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataSyncNotifyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.FieldDbManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SimpleSyncDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.RangeConditionFieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.bson.Document;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/24 17:40
 * @Version 1.0
 */
@Ignore
@Slf4j
public class SyncDataNotifyTest extends BaseTest {

    @Autowired
    private DataSyncNotifyManager dataSyncNotifyManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private FieldDbManager fieldDbManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private K3RangerConvertManager rangerConvertManager;
    @Autowired
    private PriceListSpecialBusinessImpl priceListSpecialBusinessImpl;
    @Test
    public void testProduceData() throws InterruptedException {

        for (int index = 0; index < 20; index++) {
            Map<String, SyncDataEntity> syncDataEntityMap =Maps.newHashMap();
            String dataJson="{\"createTime\":1675131468680,\"destData\":{\"object_describe_api_name\":\"ProductObj\",\"tenant_id\":\"82777\",\"record_type\":\"default__c\",\"owner\":[\"1000\"],\"product_status\":\"1\",\"price\":\"20\",\"is_saleable\":true,\"batch_sn\":\"1\",\"name\":\"CH2037#拨测测试产品20230130-51\",\"category\":\"60\",\"is_package\":false,\"product_code\":\"CH2037\",\"_id\":\"63d78fa890232f0001df225b\"},\"destDataId\":\"63d78fa890232f0001df225b\",\"destEventType\":2,\"destObjectApiName\":\"ProductObj\",\"destTenantId\":\"82777\",\"destTenantType\":1,\"id\":\"63d87a4c3c69c51b0176624d\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"82777\",\"SubHeadEntity.FSuite\":\"0\",\"FDescription\":\" \",\"FMaterialGroup.FNumber\":\"60\",\"FSpecification\":\" \",\"Number\":\"CH2037\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"VirtualHasBatchAndSerial\":\"1\",\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"name\":\"拨测测试产品20230130-51\",\"comName\":\"CH2037#拨测测试产品20230130-51\",\"_id\":\"CH2037\"},\"sourceDataId\":\"CH2037\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"82777\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.82777.0.BD_MATERIAL.1VGJNM0KQZq.0.0\",\"syncPloyDetailSnapshotId\":\"4ec9604fc5234fe091a833485080b666\",\"tenantId\":\"82777\",\"updateTime\":1675131468680}";
            SyncDataEntity syncDataEntity= JSONObject.parseObject(dataJson,SyncDataEntity.class);
            syncDataEntity.setStatus(index/2+1);
            syncDataEntity.setSourceDataId(String.valueOf(index+ RandomUtil.randomInt(10)));
            syncDataEntityMap.put(String.valueOf(index),syncDataEntity);
            dataSyncNotifyManager.pushDataToNotify("82777",syncDataEntityMap.values());
        }
        Thread.sleep(1000*20);
    }

    @Test
    public void testDCName(){
        String apiName="{\"left\":\"88521\",\"middle\":\"SalesOrderObj\",\"right\":\"SAL_SaleOrder.BillHead\"}";
        SyncStatusMessageArg syncStatusMessageArg = dataSyncNotifyManager.generateTenantIdInfo(apiName);
        System.out.println("");
    }

    @Test
    public void testProduceDataCurrently() throws InterruptedException {


            Map<String, SyncDataEntity> syncDataEntityMap =Maps.newHashMap();
            String dataJson="{\"createTime\":1675687326531,\"destData\":{\"object_describe_api_name\":\"ProductObj\",\"tenant_id\":\"82777\",\"record_type\":\"default__c\",\"product_status\":\"1\",\"price\":\"20\",\"is_saleable\":true,\"owner\":[null],\"batch_sn\":\"1\",\"name\":\"CH1948#CH1948#product123003\",\"category\":\"72\",\"is_package\":false,\"product_code\":\"CH1948\",\"remark\":\"this is a remark_APL同步中函数_APL同步前函数_APL同步前函数\",\"_id\":\"63ae93a877c4450001d967b4\"},\"destDataId\":\"63ae93a877c4450001d967b4\",\"destEventType\":2,\"destObjectApiName\":\"ProductObj\",\"destTenantId\":\"82777\",\"destTenantType\":1,\"id\":\"63e0f59e5a9a695c214e8596\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"82777\",\"SubHeadEntity.FSuite\":\"0\",\"FDescription\":\"this is a remark_APL同步中函数_APL同步前函数_APL同步前函数\",\"FMaterialGroup.FNumber\":\"72\",\"FSpecification\":\" \",\"Number\":\"CH1948\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"VirtualHasBatchAndSerial\":\"1\",\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"name\":\"CH1948#product123003\",\"comName\":\"CH1948#CH1948#product123003\",\"_id\":\"CH1948\"},\"sourceDataId\":\"CH1948\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"82777\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.82777.0.BD_MATERIAL.1VRpPyFUe88.99.0\",\"syncPloyDetailSnapshotId\":\"df2a4b87d42744eda891f8c7ae137dd5\",\"tenantId\":\"82777\",\"updateTime\":1675687326531}";
            SyncDataEntity syncDataEntity= JSONObject.parseObject(dataJson,SyncDataEntity.class);
            syncDataEntityMap.put("112112",syncDataEntity);
            dataSyncNotifyManager.pushDataToNotify("82777",syncDataEntityMap.values());

        Thread.sleep(1000*20);
    }

    @Test
    public void testProduceDataRelation() throws InterruptedException {


        Map<String, SyncDataEntity> syncDataEntityMap =Maps.newHashMap();
        String dataJson="{\"createTime\":1675656492186,\"destData\":{\"object_describe_api_name\":\"ProductObj\",\"tenant_id\":\"82777\",\"record_type\":\"default__c\",\"product_status\":\"1\",\"price\":\"20\",\"is_saleable\":true,\"owner\":[\"-10000\"],\"batch_sn\":\"1\",\"name\":\"CH2063#1941自动化产品2023026-70\",\"category\":\"60\",\"is_package\":false,\"product_code\":\"CH2063\",\"_id\":\"63dfdb1df339260001f0953b\"},\"destDataId\":\"63dfdb1df339260001f0953b\",\"destEventType\":2,\"destObjectApiName\":\"ProductObj\",\"destTenantId\":\"82777\",\"destTenantType\":1,\"id\":\"63e07d2c01e0583872a2eee3\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"82777\",\"SubHeadEntity.FSuite\":\"0\",\"FDescription\":\" \",\"SubHeadEntity3.FPurchaserId.FNumber\":[\"1000\",\"1001\"],\"FMaterialGroup.FNumber\":\"60\",\"FSpecification\":\" \",\"Number\":\"CH2063\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"VirtualHasBatchAndSerial\":\"1\",\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"name\":\"1941自动化产品2023026-70\",\"comName\":\"CH2063#1941自动化产品2023026-70\",\"_id\":\"CH2063\"},\"sourceDataId\":\"CH2063\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"82777\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.82777.0.BD_MATERIAL.1VQP7zq9wSQ.0.0\",\"syncPloyDetailSnapshotId\":\"544aa873197845b5bf9d3310bb533e21\",\"tenantId\":\"82777\",\"updateTime\":1675656492186}";
        SyncDataEntity syncDataEntity= JSONObject.parseObject(dataJson,SyncDataEntity.class);
        syncDataEntityMap.put("112112",syncDataEntity);
        dataSyncNotifyManager.pushDataToNotify("82777",syncDataEntityMap.values());

        Thread.sleep(1000*20);
    }

    @Test
    public void testNotify(){
        SyncStatusMessageArg syncStatusMessageArg = generateTenantIdInfo("{\"left\":\"82777\",\"middle\":\"BD_MATERIAL.BillHead\",\"right\":\"ProductObj\"}");
        SimpleSyncDataArg simpleSyncDataArg=new SimpleSyncDataArg();
        simpleSyncDataArg.setTenantId("82777");
        simpleSyncDataArg.setSourceDataId("121267887");
        simpleSyncDataArg.setSourceObjectApiName("BD_MATERIAL.BillHead");
        simpleSyncDataArg.setDestObjectApiName("ProductObj");
        simpleSyncDataArg.setSourceDataId("CH2040");
        simpleSyncDataArg.setRemark("SUCCESS");
        simpleSyncDataArg.setStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
        simpleSyncDataArg.setReceivers(1007);
        String data="[{\"destDataId\":\"63dfdf4df339260001f09744\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"success\",\"sourceDataId\":\"CH2064\",\"sourceDataName\":\"1941自动化产品2023026-67-edit\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":6,\"tenantId\":\"82777\"},{\"destDataId\":\"63dcd1747fe30900015850f3\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH2062\",\"sourceDataName\":\"9999\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"63e0c75645e2330001a62ebe\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH2068\",\"sourceDataName\":\"erp产品020601\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"63e09e96f339260001f0ea6c\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH2067\",\"sourceDataName\":\"crm产品020602\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"63e0e223bf13ab000145407e\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH2069\",\"sourceDataName\":\"crm产品020603\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"63e09bc1f339260001f0e86a\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH2066\",\"sourceDataName\":\"crm产品020601\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"842009577095102468\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据[CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#84801产品121301]的字段[产品名称]超过了最大长度CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#84801产品121301\",\"sourceDataId\":\"CH1882\",\"sourceDataName\":\"CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#CH1882#84801产品121301\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"63dcb57c3d5993000173ed15\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH1901\",\"sourceDataName\":\"1428产品121501\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"842009712856334336\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH1895\",\"sourceDataName\":\"3265产品121301\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"628c8d3f1c511000019f791d\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"调用CRM接口错误:数据负责人不存在或已停用！\",\"sourceDataId\":\"CH1015\",\"sourceDataName\":\"84801新增产品同步\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":5,\"tenantId\":\"82777\"},{\"destDataId\":\"63dfdf4df339260001f09744\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"success\",\"sourceDataId\":\"CH2064\",\"sourceDataName\":\"1941自动化产品2023026-67-edit\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":6,\"tenantId\":\"82777\"},{\"destDataId\":\"842009720439635968\",\"destObjectApiName\":\"ProductObj\",\"receivers\":1007,\"remark\":\"success\",\"sourceDataId\":\"CH1879\",\"sourceDataName\":\"erp产品121302\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"status\":6,\"tenantId\":\"82777\"}]";
        List<SimpleSyncDataArg> simpleSyncDataArgs = JSONArray.parseArray(data, SimpleSyncDataArg.class);

        dataSyncNotifyManager.sendSyncMessageToUser(simpleSyncDataArgs,syncStatusMessageArg);
    }
    private SyncStatusMessageArg generateTenantIdInfo(String tenantApiName){
        //SYNC_DATA_NOTIFY_企业id_sourceApiName_destApiName
        log.info("generateTenantIdInfo key:{}",tenantApiName);
        MutableTriple<String,String,String> triple=JSONObject.parseObject(tenantApiName,MutableTriple.class);
        String tenantId=triple.getLeft();
        String sourceApiName=triple.getMiddle();
        String destApiName=triple.getRight();
        SyncPloyDetailEntity entityByTenantIdAndObjApiName = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, sourceApiName, destApiName);
        SyncStatusMessageArg syncStatusMessageArg=new SyncStatusMessageArg();
        syncStatusMessageArg.setTenantId(tenantId);
        syncStatusMessageArg.setConnInfoName(entityByTenantIdAndObjApiName.getIntegrationStreamName());
        syncStatusMessageArg.setEndTime(System.currentTimeMillis());
        //减掉聚合时间
        syncStatusMessageArg.setStartTime(System.currentTimeMillis()- TimeUnit.MINUTES.toMillis(5));
        String direction=entityByTenantIdAndObjApiName.getSourceTenantType().equals(TenantTypeEnum.CRM.getType())?"CRM->ERP":"ERP->CRM";
        syncStatusMessageArg.setSyncDirection(direction);
        return syncStatusMessageArg;

    }
    @Test
    public void testDataTask(){
        String dataJson = "{\"createTime\":1675051282798,\"destData\":{\"object_describe_api_name\":\"ProductObj\",\"tenant_id\":\"82777\",\"record_type\":\"default__c\",\"owner\":[\"1000\"],\"product_status\":\"1\",\"price\":\"20\",\"is_saleable\":true,\"batch_sn\":\"1\",\"name\":\"CH2032#产品013004\",\"category\":\"73\",\"is_package\":false,\"product_code\":\"CH2032\",\"remark\":\" erp->crm\",\"_id\":\"63d6a0952bfdcd0001ad6432\"},\"destDataId\":\"63d6a0952bfdcd0001ad6432\",\"destEventType\":2,\"destObjectApiName\":\"ProductObj\",\"destTenantId\":\"82777\",\"destTenantType\":1,\"id\":\"63d741125f3d9168cdaa6c31\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"82777\",\"SubHeadEntity.FSuite\":\"0\",\"FDescription\":\" erp->crm\",\"FMaterialGroup.FNumber\":\"73\",\"FSpecification\":\" \",\"Number\":\"CH2032\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"VirtualHasBatchAndSerial\":\"1\",\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"name\":\"产品013004\",\"comName\":\"CH2032#产品013004\",\"_id\":\"CH2032\"},\"sourceDataId\":\"CH2032\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"82777\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.82777.0.BD_MATERIAL.1VFcj0pgojS.0.0\",\"syncPloyDetailSnapshotId\":\"4ec9604fc5234fe091a833485080b666\",\"tenantId\":\"82777\",\"updateTime\":1675051282798}";
        SyncDataEntity dataEntity = JSONObject.parseObject(dataJson, SyncDataEntity.class);
        ErpIdArg erpIdArg = new ErpIdArg();
        String dataId = dataEntity.getSourceDataId();
        String tenantId = "82777";
        String sourceObjApiName = dataEntity.getSourceObjectApiName();
        String dataCenterId = "696453487420604416";
        erpIdArg.setDataId(dataId);
        erpIdArg.setObjAPIName(sourceObjApiName);
        erpIdArg.setTenantId("82777");
        ErpObjectRelationshipEntity relation = fieldDbManager.getRelation(tenantId, sourceObjApiName);
        String realObjApiName = relation.getErpRealObjectApiname();
        fieldDbManager.convertIdArg2Erp(erpIdArg);
        //这里用真实apiName
        erpIdArg.setObjAPIName(realObjApiName);
        boolean usedIdQuery = fieldDbManager.hasUsedIdQuery(erpIdArg,dataCenterId);
        Document erpObjDataById = null;
        if (usedIdQuery) {
            erpObjDataById = erpTempDataDao.getErpObjDataById(tenantId, dataCenterId, realObjApiName, dataId);
        } else {
            erpObjDataById = erpTempDataDao.getErpObjDataByNum(tenantId, dataCenterId, realObjApiName, dataId);
        }
        if (ObjectUtil.isNotEmpty(erpObjDataById) && CollectionUtils.isNotEmpty(erpObjDataById.getList("task_num", String.class))) {
            List<String> taskNum = erpObjDataById.getList("task_num", String.class);
            boolean ploy_auto_query = taskNum.stream().allMatch(str -> str.equals("ploy_auto_query"));
            if (!ploy_auto_query) {
                return;
            }
        }

    }

    @Test
    public void testCondition(){

        RangeConditionFieldMappingData rangeConditionFieldMappingData=new RangeConditionFieldMappingData();
        rangeConditionFieldMappingData.setSourceErpObjApiName("BD_SAL_PriceList.BillHead");
        rangeConditionFieldMappingData.setSourceRangeFieldApiName("FLimitCustomer");
        rangeConditionFieldMappingData.setSourceConditionObjectApiName("BD_SAL_PriceList.SAL_APPLYCUSTOMER");
        RangeConditionFieldMappingData.ConditionMapping conditionMapping=new RangeConditionFieldMappingData.ConditionMapping();
        List<RangeConditionFieldMappingData.ConditionMapping> conditionMappings=Lists.newArrayList();
        String conditionData="[\n" +
                "    {\n" +
                "        \"sourceApiName\":\"FCustGroup\",\n" +
                "        \"sourceType\":\"select_one\",\n" +
                "        \"destApiName\":\"field_600Jd__c\",\n" +
                "        \"destType\":\"select_one\",\n" +
                "        \"optionMappings\":[\n" +
                "            {\n" +
                "                \"sourceOption\":\"002\",\n" +
                "                \"destOption\":\"zL23AN6c8\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"sourceOption\":\"001\",\n" +
                "                \"destOption\":\"option1\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"mappingType\":1,\n" +
                "        \"value\":\"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceApiName\":\"FCustTypeID.FNumber\",\n" +
                "        \"sourceType\":\"select_one\",\n" +
                "        \"destApiName\":\"account_type\",\n" +
                "        \"destType\":\"select_one\",\n" +
                "        \"optionMappings\":[\n" +
                "            {\n" +
                "                \"sourceOption\":\"KHLB001_SYS\",\n" +
                "                \"destOption\":\"1\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"sourceOption\":\"0000001\",\n" +
                "                \"destOption\":\"lA00s9g93\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"sourceOption\":\"ge0z23ob\",\n" +
                "                \"destOption\":\"qS0dx5an2\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"mappingType\":1,\n" +
                "        \"value\":\"\"\n" +
                "    }\n" +
                "]";
        List<FieldMappingData> fieldMappingData = JSONArray.parseArray(conditionData, FieldMappingData.class);
        Map<String, FieldMappingData> fieldDestDataMap
                = fieldMappingData.stream().collect(Collectors.toMap(FieldMappingData::getDestApiName, v -> v, (u, v) -> u));

        conditionMapping.setSourceExpression("1=1");
        conditionMapping.setDestExpression("1=1");
        conditionMapping.setRangeFieldMapping(fieldMappingData);

        rangeConditionFieldMappingData.setMatchCondition(Lists.newArrayList(conditionMapping));
        rangeConditionFieldMappingData.setDestRangeFieldApiName("apply_account_range");
        rangeConditionFieldMappingData.setDestConditionObjectApiName("AccountObj");

        List<ObjectData> sourceDataLists=JSONArray.parseArray("[{\"DetailId\":100166," +
                "\"FCustID.Name\":null,\"FCustID.FNumber\":null,\"FCustIDFCUSTID\":0," +
                "\"FCustTypeID.FDataValue\":\"SSSVIP\",\"FCustTypeID.FNumber\":\"0000001\",\n" +
                "          \"FCustTypeID.Id\":\"617f91cd9bc165\"," +
                "\"FIsDefList\":false,\"FCustGroup\":0},\n" +
                "          {\"DetailId\":100167," +
                "\"FCustID.Name\":null,\"FCustID.FNumber\":null,\"FCustIDFCUSTID\":0,\n" +
                "         \"FCustTypeID.FDataValue\":" +
                "\"\\u91CD\\u8981\\u5BA2\\u6237\"," +
                "\"FCustTypeID.FNumber\":\"ge0z23ob\",\n" +
                "         \"FCustTypeID.Id\":" +
                "\"5f06c6179054e9\",\"FIsDefList\":false,\"FCustGroup\":0}]",ObjectData.class);
        List<ObjectData> convertObjData=Lists.newArrayList();

        for (ObjectData sourceDataList : sourceDataLists) {
            ObjectData objectData = rangerConvertManager.convertRangeData("84801", "780777150699143168",sourceDataList, EventTypeEnum.UPDATE.getType(), "84801",
                    "PriceBookObj", fieldMappingData, TenantTypeEnum.CRM.getType(), TenantTypeEnum.ERP.getType());
            convertObjData.add(objectData);
        }
        System.out.println("SyncDataNotifyTest.testCondition");
        Map<String,List<Object>> valuesMap=Maps.newHashMap();
        for (ObjectData convertObjDatum : convertObjData) {
            for (FieldMappingData fieldMappingDatum : fieldMappingData) {
                Object object = convertObjDatum.get(fieldMappingDatum.getDestApiName());
                if(ObjectUtils.isEmpty(object)){
                    continue;
                }
                valuesMap.computeIfAbsent(fieldMappingDatum.getDestApiName(), k -> new ArrayList<>()).add(object);
            }
        }
        //{"account_type":["lA00s9g93","qS0dx5an2"]}
        System.out.println("SyncDataNotifyTest.testCondition"+JSONObject.toJSONString(valuesMap));
        /**
         * [
         *     {
         *         "filters":[
         *             {
         *                 "field_name":"account_type",
         *                 "operator":"IN",
         *                 "field_values":[
         *                     "1",
         *                     "lA00s9g93"
         *                 ],
         *                 "connector":"AND",
         *                 "type":"select_one"
         *             }
         *         ],
         *         "connector":"OR"
         *     }
         * ]
         */
        RangeConditionFieldMappingData.CrmFilterData crmFilterData=new RangeConditionFieldMappingData.CrmFilterData();
        for (String dataKey : valuesMap.keySet()) {
            RangeConditionFieldMappingData.CrmConditionExpression crmConditionExpression=new RangeConditionFieldMappingData.CrmConditionExpression();
            crmConditionExpression.setConnector("AND");
            crmConditionExpression.setOperator("IN");
            crmConditionExpression.setField_name(dataKey);
            crmConditionExpression.setField_values(valuesMap.get(dataKey));
            FieldMappingData fieldMappingData1 = fieldDestDataMap.get(dataKey);
            crmConditionExpression.setType(fieldDestDataMap.get(dataKey).getDestType());
            crmFilterData.addFilterData(crmConditionExpression);
        }
        RangeConditionFieldMappingData.ExpressionFilterData orgFilterData=new RangeConditionFieldMappingData.ExpressionFilterData();

        orgFilterData.setType("CONDITION");
        orgFilterData.setValue(JSONObject.toJSONString(crmFilterData));
        System.out.println("SyncDataNotifyTest.testCondition"+JSONObject.toJSONString(orgFilterData));


    }

    @Test
    public void testConditionORG(){

        RangeConditionFieldMappingData rangeConditionFieldMappingData=new RangeConditionFieldMappingData();
        rangeConditionFieldMappingData.setSourceErpObjApiName("BD_SAL_PriceList.BillHead");
        rangeConditionFieldMappingData.setSourceRangeFieldApiName("FLimitCustomer");
        rangeConditionFieldMappingData.setSourceConditionObjectApiName("BD_SAL_PriceList.SAL_APPLYCUSTOMER");
        RangeConditionFieldMappingData.ConditionMapping conditionMapping=new RangeConditionFieldMappingData.ConditionMapping();
        List<RangeConditionFieldMappingData.ConditionMapping> conditionMappings=Lists.newArrayList();
        String conditionData="[\n" +
                "{\n" +
                "    \"sourceApiName\": \"FDeptId.FNumber\",\n" +
                "    \"sourceType\": \"department\",\n" +
                "    \"destApiName\": \"field_91ayn__c\",\n" +
                "    \"destType\": \"department\",\n" +
                "    \"optionMappings\": [],\n" +
                "    \"mappingType\": 2001,\n" +
                "    \"value\": \"\"\n" +
                "},"+
             "{\n" +
                "    \"sourceApiName\":\"FSalerId.FNumber\",\n" +
                "    \"sourceType\":\"employee\",\n" +
                "    \"destApiName\":\"field_lH170__c\",\n" +
                "    \"destType\":\"employee\",\n" +
                "    \"optionMappings\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"mappingType\":2001,\n" +
                "    \"value\":\"FNumber\"\n" +
                "}"+
                "]";
        List<FieldMappingData> fieldMappingData = JSONArray.parseArray(conditionData, FieldMappingData.class);
        Map<String, FieldMappingData> fieldDestDataMap
                = fieldMappingData.stream().collect(Collectors.toMap(FieldMappingData::getDestApiName, v -> v, (u, v) -> u));

        conditionMapping.setSourceExpression("1=1");
        conditionMapping.setDestExpression("1=1");
        conditionMapping.setRangeFieldMapping(fieldMappingData);

        rangeConditionFieldMappingData.setMatchCondition(Lists.newArrayList(conditionMapping));
        rangeConditionFieldMappingData.setDestRangeFieldApiName("apply_org_range");
        rangeConditionFieldMappingData.setDestConditionObjectApiName("AccountObj");

        List<ObjectData> sourceDataLists=JSONArray.parseArray("[\n" +
                "    {\n" +
                "        \"FEntity1_FENTRYID\":100005,\n" +
                "        \"FSalerId\":0,\n" +
                "        \"FSaleGroupId\":0,\n" +
                "        \"FDeptId.FName\":\"默认部门\",\n" +
                "        \"FDeptId.FNumber\":\"04.02.01\",\n" +
                "        \"FDeptId\":608902\n" +
                "    }\n" +
                "]",ObjectData.class);
        List<ObjectData> convertObjData=Lists.newArrayList();

        for (ObjectData sourceDataList : sourceDataLists) {
            ObjectData objectData = rangerConvertManager.convertRangeData("84801", "780777150699143168", sourceDataList, EventTypeEnum.UPDATE.getType(), "84801",
                    "PriceBookObj", fieldMappingData, TenantTypeEnum.CRM.getType(), TenantTypeEnum.ERP.getType());
            convertObjData.add(objectData);
        }
        System.out.println("SyncDataNotifyTest.testCondition");
        Map<String,List<Object>> valuesMap=Maps.newHashMap();
        for (ObjectData convertObjDatum : convertObjData) {
            for (FieldMappingData fieldMappingDatum : fieldMappingData) {
                Object object = convertObjDatum.get(fieldMappingDatum.getDestApiName());
                if(ObjectUtils.isEmpty(object)){
                    continue;
                }
                valuesMap.computeIfAbsent(fieldMappingDatum.getDestApiName(), k -> new ArrayList<>()).add(object);
            }
        }
        //{"account_type":["lA00s9g93","qS0dx5an2"]}
        System.out.println("SyncDataNotifyTest.testCondition"+JSONObject.toJSONString(valuesMap));


        RangeConditionFieldMappingData.CrmFilterData crmFilterData=new RangeConditionFieldMappingData.CrmFilterData();

        for (String dataKey : valuesMap.keySet()) {
            RangeConditionFieldMappingData.CrmConditionExpression crmConditionExpression=new RangeConditionFieldMappingData.CrmConditionExpression();
            crmConditionExpression.setConnector("AND");
            crmConditionExpression.setOperator("IN");
            crmConditionExpression.setField_name(dataKey);
            crmConditionExpression.setField_values(valuesMap.get(dataKey));
            FieldMappingData fieldMappingData1 = fieldDestDataMap.get(dataKey);
            crmConditionExpression.setType(fieldDestDataMap.get(dataKey).getDestType());
            crmFilterData.addFilterData(crmConditionExpression);
        }
        RangeConditionFieldMappingData.ExpressionFilterData orgFilterData=new RangeConditionFieldMappingData.ExpressionFilterData();
        if(rangeConditionFieldMappingData.getDestRangeFieldApiName().equals("apply_org_range")){
            orgFilterData.setType("ORG");
            List<Object> employees=Lists.newArrayList();
            List<Object> departments=Lists.newArrayList();
            for (RangeConditionFieldMappingData.CrmConditionExpression field_value : crmFilterData.getFilters()) {
                if(field_value.getType().equals("department")){
                    departments.addAll(field_value.getField_values());
                }else{
                    employees.addAll(field_value.getField_values());
                }
            }
            Map<String,Object> dataMap=Maps.newHashMap();
            dataMap.put("employees",employees);
            dataMap.put("departments",departments);
            orgFilterData.setValue(JSONObject.toJSONString(dataMap));
            System.out.println("SyncDataNotifyTest.testConditionORG"+orgFilterData);
        }
        orgFilterData.setType("CONDITION");
        orgFilterData.setValue(JSONObject.toJSONString(crmFilterData));
        System.out.println("SyncDataNotifyTest.testCondition"+JSONObject.toJSONString(orgFilterData));

    }




    @Test
    public void testPrice(){
        /**
         * PriceListSpecialBu
         * sinessImpl.afterRunView,erpIdArg=ErpIdArg(tenantId=81243, objAPIName=BD_SAL_PriceList,
         * dataId=828509, dataIdIsNumber=false,
         * includeDetail=true, syncPloyDetailSnapshotId=5fd416b5b1c54793b682e9b52d9cc668,
         * sourceEventType=null),
         * standardData={"objAPIName":n
         * ull,"syncLogId":null,"dataVersion":null,
         * "masterFieldVal":{"erp_id":"828509","erp_num":"XSJMB0160","id":828509,"FName":"\u6D4B\u8BD5\u7C7B\u522B","FNumber":"XSJMB0160","FDescription":" ","FCreateDate":"2023-03-16T17:58:36.443","FEffectiveDate":"2023-03-1
         * 6T00:00:00","FExpiryDate":"2100-01-01T00:00:00","FApproveDate":null},
         * "detailFieldVals":{"BD_SAL_PriceList.SAL_APPLYSALESMAN":[],
         * "BD_SAL_PriceList.SAL_PRICELISTENTRY":[{"DetailId":108185,"FPriceUnitId.Name":"\u4E2A","FPriceUnitId.FNumber":"002","FPriceUnitId.Id":103718,"FPriceBase":1.0,"FToQty":0.0,"FPrice":190.0,"FDownPrice":190.0,"FEntryEffectiveDate":"2023-03-16T00:00:00","FEntryExpiryDate":"2100-01-01T00:00:00","FEntryForbidStatus":"A","FForbiderId.Id":"0","FForbidDate":null,"FMaterialTypeId.Name":"\u539F\u6750\u6599","FMaterialTypeId.FNumber":"CHLB01_SYS","FMaterialTypeIdFCATEGORYID":237,"FMaterialId.Name":"\u57F9\u8BAD\u52A9\u624B\u8BFE\u65F6","FMaterialId.FNumber":"00007","FMaterialIdFMATERIALID":103724,"FRowAuditStatus":"U","FUnitID.Name":"\u4E2A","FUnitID.FNumber":"002","FUnitID.Id":103718,"FFromQty":0.0,"FBomId.Name":null,"FBomId.FNumber":null,"FBomId.Id":0,"FLot.Name":null,"FLot.FNumber":null,"FLotFLOTID":0,"FDefBaseDataO.Name":null,"FDefBaseDataO.FNumber":null,"FDefBaseDataOFStockId":0,"FDefBaseDataT.Name":null,"FDefBaseDataT.FNumber":null,"FDefBaseDataTFStockId":0,"FDefAssistantO.FDataValue":null,"FDefAssistantO.FNumber":null,"FDefAssistantO.Id":" ","FDefAssistantT.FDataValue":null,"FDefAssistantT.FNumber":null,"FDefAssistantT.Id":" ","FDefTextO":" ","FDefTextT":" ","FDefaultPriceO":0.0,"FDefaultPriceT":0.0,"FMaterialGroupId.Name":null,"FMaterialGroupId.FNumber":null,"FMaterialGroupId.Id":0,"FMapId.Name":null,"FMapId.FNumber":null,"FMapId.Id":" "}],
         * "BD_SAL_PriceList.SAL_APPLYCUSTOMER":[{"DetailId":100166,"FCustID.Name":null,"FCustID.FNumber":null,"FCustIDFCUSTID":0,"FCustTypeID.FDataValue":"SSSVIP","FCustTypeID.FNumber":"0000001",
         * "FCustTypeID.Id":"617f91cd9bc165","FIsDefList":false,"FCustGroup":0},
         * {"DetailId":100167,"FCustID.Name":null,"FCustID.FNumber":null,"FCustIDFCUSTID":0,
         * "FCustTypeID.FDataValue":"\u91CD\u8981\u5BA2\u6237","FCustTypeID.FNumber":"ge0z23ob",
         * "FCustTypeID.Id":"5f06c6179054e9","FIsDefList":false,"FCustGroup":0}]}
         */
        ErpIdArg erpIdArg=new ErpIdArg();
        erpIdArg.setTenantId("84801");
        erpIdArg.setObjAPIName("BD_SAL_PriceList");
        erpIdArg.setDataId("829570");
        erpIdArg.setDataIdIsNumber(false);
        erpIdArg.setIncludeDetail(true);
        erpIdArg.setSyncPloyDetailSnapshotId("cb787bde9358449b8e7c1fa3d93972b0");
        StandardData standardData=new StandardData();
        String data="{\"erp_id\":\"829570\",\"erp_num\":\"XSJMB0164\",\"id\":829570,\"FDocumentStatus\":\"D\",\"FForbidStatus\":\"A\",\"FName\":\"\\u6D4B\\u8BD5\\u9500\\u552E\\u6761\\u4EF6\",\"FNumber\":\"XSJMB0164\",\"FDescription\":\" \",\"FCreateOr\n" +
                "gId.Name\":\"\\u7EB7\\u4EAB\\u9500\\u5BA2\",\"FCreateOrgId.FNumber\":\"000\",\"FCreateOrgIdFOrgID\":1,\"FUseOrgId.Name\":\"\\u7EB7\\u4EAB\\u9500\\u5BA2\",\"FUseOrgId.FNumber\":\"000\",\"FUseOrgIdFOrgID\":1,\"FCreatorId.Id\":\"605440\",\"FModifierId.Id\":\"605440\",\"FCreateDate\":\"2023-03-\n" +
                "31T18:48:16.267\",\"FModifyDate\":\"2023-03-31T18:48:16.41\",\"FCarriageClause\":\" \",\"FChargeClause\":\" \",\"FIsIncludedTax\":true,\"FCurrencyId.Name\":\"\\u7F8E\\u5143\",\"FCurrencyId.FNumber\":\"PRE007\",\"FCurrencyIdFCURRENCYID\":7,\"FEffectiveDate\":\"2023-03-31T00:00:00\",\"F\n" +
                "ExpiryDate\":\"2100-01-01T00:00:00\",\"FApproverId.Id\":\"0\",\"FApproveDate\":null,\"FEntryForbiderId.Id\":\"0\",\"FEntryForbidDate\":null,\"FPriceObject\":\"A\",\"FLimitCustomer\":\"3\",\"FLimitSalesMan\":\" \",\"FAuditStatus\":\"R\",\"FPriceType.FDataValue\":null,\"FPriceType.FNumber\n" +
                "\":null,\"FPriceType.Id\":\" \",\"FIsNotCheckPrice\":false}";
        ObjectData objectData = JSONObject.parseObject(data, ObjectData.class);
        standardData.setObjAPIName("");
        standardData.setMasterFieldVal(objectData);




        String detailData="{\"BD_SAL_PriceList.SAL_APPLYSALESMAN\":[],\"BD_SAL_PriceList.SAL_PRICELISTENTRY\":[{\"DetailId\":108194,\"FPriceUnitId.Name\":\"Pcs\",\"FPriceUnitId.FNumber\":\"Pcs\",\"FPriceUnitId.Id\":10101,\"FPr\n" +
                "iceBase\":1.0,\"FToQty\":0.0,\"FPrice\":100.0,\"FDownPrice\":100.0,\"FEntryEffectiveDate\":\"2023-03-31T00:00:00\",\"FEntryExpiryDate\":\"2100-01-01T00:00:00\",\"FEntryForbidStatus\":\"A\",\"FForbiderId.Id\":\"0\",\"FForbidDate\":null,\"FMaterialTypeId.Name\":\"\\u539F\\u6750\\u6599\"\n" +
                ",\"FMaterialTypeId.FNumber\":\"CHLB01_SYS\",\"FMaterialTypeIdFCATEGORYID\":237,\"FMaterialId.Name\":\"fktest3759\\u4EA7\\u54C120230329-79\",\"FMaterialId.FNumber\":\"CH2198\",\"FMaterialIdFMATERIALID\":829373,\"FRowAuditStatus\":\"R\",\"FUnitID.Name\":\"Pcs\",\"FUnitID.FNumber\":\"\n" +
                "Pcs\",\"FUnitID.Id\":10101,\"FFromQty\":0.0,\"FBomId.Name\":null,\"FBomId.FNumber\":null,\"FBomId.Id\":0,\"FLot.Name\":null,\"FLot.FNumber\":null,\"FLotFLOTID\":0,\"FDefBaseDataO.Name\":null,\"FDefBaseDataO.FNumber\":null,\"FDefBaseDataOFStockId\":0,\"FDefBaseDataT.Name\":null,\n" +
                "\"FDefBaseDataT.FNumber\":null,\"FDefBaseDataTFStockId\":0,\"FDefAssistantO.FDataValue\":null,\"FDefAssistantO.FNumber\":null,\"FDefAssistantO.Id\":\" \",\"FDefAssistantT.FDataValue\":null,\"FDefAssistantT.FNumber\":null,\"FDefAssistantT.Id\":\" \",\"FDefTextO\":\" \",\"FDefTex\n" +
                "tT\":\" \",\"FDefaultPriceO\":0.0,\"FDefaultPriceT\":0.0,\"FMaterialGroupId.Name\":null,\"FMaterialGroupId.FNumber\":null,\"FMaterialGroupId.Id\":0,\"FMapId.Name\":null,\"FMapId.FNumber\":null,\"FMapId.Id\":\" \"}],\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\":[{\"DetailId\":100171,\"F\n" +
                "CustID.Name\":null,\"FCustID.FNumber\":null,\"FCustIDFCUSTID\":0,\"FCustTypeID.FDataValue\":null,\"FCustTypeID.FNumber\":null,\"FCustTypeID.Id\":\" \",\"FIsDefList\":false,\"FCustGroup\":\"001\"}]}";


        Map<String,Object> detailMap=JSONObject.parseObject(detailData,Map.class);
        Map<String,List<ObjectData>> detailDataList= Maps.newHashMap();
        for (String key : detailMap.keySet()) {
            List<ObjectData> objectDataList = JSONArray.parseArray(detailMap.get(key).toString(), ObjectData.class);
            detailDataList.put(key,objectDataList);
        }
        standardData.setDetailFieldVals(detailDataList);
        K3Model k3Model=new K3Model();
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");


        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "780777150699143168");
        priceListSpecialBusinessImpl.afterRunView(erpIdArg,standardData,k3Model,apiClient);
        System.out.println("SyncDataNotifyTest.testPrice");
    }


}
