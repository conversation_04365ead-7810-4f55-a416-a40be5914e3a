package com.fxiaoke.PureTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import lombok.Data;
import lombok.SneakyThrows;
import org.junit.Test;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/14
 */
public class JacksonTest {

    @SneakyThrows
    public static void main(String[] args) {

        String json = "{\n" +
                "  \"id\": \"1023456789123456789\",\n" +
                "  \"testAB\": \"lalal\",\n" +
                "  \"ALong\": 102345678912345,\n" +
                "  \"doubleTest\": 12.0,\n" +
                "  \"bigDecimal\": -12.03333333333333\n" +
                "}";
        ObjectMapper mapper = JacksonUtil.get();
        TestObj obj1 = mapper.readValue(json, TestObj.class);
        TestObj fastJsonObj = JSON.parseObject(json, TestObj.class);
        Object gsonObj = GsonUtil.fromJson(json, TestObj.class);
        System.out.println(obj1);
        System.out.println(gsonObj);
        System.out.println(fastJsonObj);
    }


    /**
     * 测试
     */
    @Data
    public static class TestObj implements Serializable {
        private static final long serialVersionUID = -8761645379239188125L;
        /**
         * 数据中心Id
         */
        private String iD;

        private String testAB;

        private Long aLong;

        private Double doubleTest;

        private BigDecimal bigDecimal;

    }
}
