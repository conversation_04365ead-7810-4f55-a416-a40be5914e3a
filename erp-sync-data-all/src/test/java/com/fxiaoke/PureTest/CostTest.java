package com.fxiaoke.PureTest;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CountDownLatch;

import static java.lang.Math.abs;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/3
 */
public class CostTest {
    protected static final String tenantId = "84801";

    private static void testNewClient() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://*************/K3Cloud/",
                "5ec229fad54306", "ces1", "8888888");
        String paramJson = JacksonUtil.toJson(connectParam);
        K3CloudApiClient.newInstance(tenantId, connectParam, "");
        StopWatch stopWatch = new StopWatch("test build api client cost");
        stopWatch.start("new");
        K3CloudApiClient apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
        apiClient.setConnectParam(connectParam);
        stopWatch.stop();
        for (int i = 0; i < 10; i++) {
            stopWatch.start("login with cookie" + i);
            apiClient.loginThrowException();
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());
        stopWatch = new StopWatch("test build api client cost");
        for (int i = 0; i < 10; i++) {
            stopWatch.start("login with cookie" + i);
            apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
            apiClient.setConnectParam(connectParam);
            apiClient.loginThrowException();
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());
        stopWatch = new StopWatch("test build api client cost");
        for (int i = 0; i < 10; i++) {
            stopWatch.start("login with wrap" + i);
            apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "");
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());
        stopWatch = new StopWatch("test build api client cost");
        for (int i = 0; i < 10; i++) {
            stopWatch.start("login with json" + i);
            apiClient = K3CloudApiClient.newInstance(tenantId, paramJson, "");
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());
    }

    private static void testLogin() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "",
                "", "", "");
        StopWatch stopWatch = new StopWatch("test build api client cost");
        K3CloudApiClient apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
        connectParam.setUseFsHttpClient(true);
        apiClient.setConnectParam(connectParam);
        for (int i = 0; i < 10; i++) {
            stopWatch.start("login" + i);
            apiClient.loginThrowException();
            stopWatch.stop();
        }
        for (int i = 0; i < 5; i++) {
            stopWatch.start("view" + i);
            ViewArg data = new ViewArg();
            data.setId("100079");
            Result<ViewResult> sal_saleOrder = apiClient.view("SAL_SaleOrder", data);
            stopWatch.stop();
        }
        System.out.print(stopWatch.prettyPrint());
    }

    private static void testhashcodd() {
        String s1 = "  ";
       // String s2 = null;
        String s3 = "abc^*";
        String s4 = ")bcdefdd--99*&22aaa";

        System.out.println("ret: " + abs(s1.hashCode() % 10));
        // System.out.println("ret: " + abs(s2.hashCode() % 10));
        System.out.println("ret: " + abs(s3.hashCode() % 10));
        System.out.println("ret: " + abs(s4.hashCode() % 10));
    }

    private static void testParallel() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "",
                "", "", "");
        connectParam.setUseFsHttpClient(true);
        int count = 1000;
        CountDownLatch latch = new CountDownLatch(count);
        K3CloudApiClient apiClient2 = K3CloudApiClient.newInstance(tenantId, connectParam,"");
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FID");
        queryArg.setFormId(K3CloudForm.SAL_SaleOrder);
        Result<List<List<Object>>> listResult = apiClient2.executeBillQuery(queryArg);
        ViewArg data = new ViewArg();
        data.setId("100201");
        Result<ViewResult> view = apiClient2.view(K3CloudForm.SAL_SaleOrder, data);
        for (int i = 0; i < count; i++) {
            int finalI = i;
            new Thread(() -> {
                long l = System.currentTimeMillis();
                try {
                    K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam,"");
                    apiClient.view(K3CloudForm.SAL_SaleOrder, data);
                } catch (Exception e) {
                    System.out.printf("%s error\n", finalI);
                } finally {
                    System.out.printf("%s %s\n", finalI, System.currentTimeMillis() - l);
                    latch.countDown();
                }
            }
            ).start();
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        testhashcodd();
    }
}
