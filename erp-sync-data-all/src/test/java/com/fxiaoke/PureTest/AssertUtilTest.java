package com.fxiaoke.PureTest;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/2
 */
public class AssertUtilTest {
    public static void main(String[] args) {
//        AssertUtil.notEmpty(null, "空");
//        AssertUtil.notEmpty("", "空");
//        System.out.println(AssertUtil.notEmpty("ab", "空"));
//        System.out.println(Arrays.toString(AssertUtil.notEmpty(new int[]{}, "空")));
//        AssertUtil.notEmpty(new int[]{1, 2, 3}, "空");
//        AssertUtil.notEmpty(1234, "空");
        System.out.println(String.valueOf(AssertUtil.notNull(111,"空",null,null)));
    }
}
