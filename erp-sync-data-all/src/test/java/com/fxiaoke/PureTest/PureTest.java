package com.fxiaoke.PureTest;

import cn.hutool.core.thread.ThreadUtil;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.Semaphore;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/24
 */
@Slf4j
public class PureTest {

    @Data
    private static class Demo{
        Map<String,String> map ;
        String name;
    }

    @SneakyThrows
    public static void main(String[] args) {
        String[] split = "lll;".split(";");
        System.out.println(Arrays.toString(split));
    }
}
