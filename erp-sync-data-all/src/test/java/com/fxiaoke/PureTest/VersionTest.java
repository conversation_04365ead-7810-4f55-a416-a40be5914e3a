package com.fxiaoke.PureTest;

import cn.hutool.core.comparator.VersionComparator;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/3/1
 */
public class VersionTest {

    public static void main(String[] args) {

        String version = "8.0.123.1";
        String version2 = "7.6.2122.8";
        String version3 = "7.6.2122.5";
        String version4 = "4.0.123.1";
        String version5 = "4.0.2123.1";
        String lowestVersion = "7.6.2122.7";
        int compare1 = VersionComparator.INSTANCE.compare(version, lowestVersion);
        int compare2 = VersionComparator.INSTANCE.compare(version2, lowestVersion);
        int compare3 = VersionComparator.INSTANCE.compare(version3, lowestVersion);
        int compare4 = VersionComparator.INSTANCE.compare(version4, lowestVersion);
        int compare5 = VersionComparator.INSTANCE.compare(version5, lowestVersion);
        System.out.println(compare1);
        System.out.println(compare2);
        System.out.println(compare3);
        System.out.println(compare4);
        System.out.println(compare5);
    }
}
