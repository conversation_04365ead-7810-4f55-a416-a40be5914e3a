package com.fxiaoke.PureTest;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.SerializeNull;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ValueNullableAdapterFactory;
import com.google.common.collect.Lists;
import com.google.gson.*;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/19
 */
public class GsonTest {

    public static void main(String[] args) {
        testObjectDataFactory();
    }


    @Data
    private static class testCls{
        @SerializedName("object_data")
        @SerializeNull
        private ObjectData objectData;
        private Map<String, List<ObjectData>> details;
        private boolean fillOutOwner;
        private String remark;
    }

    private static void testObjectDataFactory(){
//        Gson defaultGson = new GsonBuilder().create();
        Gson customGson = new GsonBuilder().registerTypeAdapterFactory(new ValueNullableAdapterFactory(ObjectData.class)).registerTypeAdapter(Double.class, new JsonSerializer<Double>() {
            public JsonElement serialize(Double src, Type typeOfSrc, JsonSerializationContext context) {
                if (src == null) {
                    return JsonNull.INSTANCE;
                } else {
                    BigDecimal plainExpressionBigDecimal = new BigDecimal(src) {
                        public String toString() {
                            return super.toPlainString();
                        }
                    };
                    return new JsonPrimitive(plainExpressionBigDecimal);
                }
            }
        }).create();
        Gson defaultGson = new GsonBuilder().registerTypeAdapter(Double.class, new JsonSerializer<Double>() {
            public JsonElement serialize(Double src, Type typeOfSrc, JsonSerializationContext context) {
                if (src == null) {
                    return JsonNull.INSTANCE;
                } else {
                    BigDecimal plainExpressionBigDecimal = new BigDecimal(src) {
                        public String toString() {
                            return super.toPlainString();
                        }
                    };
                    return new JsonPrimitive(plainExpressionBigDecimal);
                }
            }
        }).create();
        Gson serialNullGson = new GsonBuilder().serializeNulls().create();
        testCls testCls = new testCls();
        ObjectData main = new ObjectData();
        main.put("name","testWithoutDetails");
        main.put("remark",null);
        main.put("double",1.2);
        testCls.setObjectData(main);
        testCls.setRemark(null);
        System.out.println("默认                 :"+defaultGson.toJson(testCls));
        System.out.println("全支持null            :"+serialNullGson.toJson(testCls));
        System.out.println("objectData类型支持null:"+customGson.toJson(testCls));

        main.put("name","testWithDetails");
        ObjectData detail = new ObjectData();
        detail.put("name","detail");
        detail.put("remark",null);
        testCls.setDetails(new HashMap<>());
        testCls.getDetails().put("detalObj", Lists.newArrayList(detail));
        System.out.println("默认:"+defaultGson.toJson(testCls));
        System.out.println("全支持null:"+serialNullGson.toJson(testCls));
        System.out.println("objectData类型支持null:"+customGson.toJson(testCls));
    }
}
