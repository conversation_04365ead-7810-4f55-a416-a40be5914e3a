package com.fxiaoke.sendEvent

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RetrySendMqDao
import com.fxiaoke.open.erpsyncdata.main.dispatcher.processor.DispatcherEventListen
import com.fxiaoke.open.erpsyncdata.main.dispatcher.processor.ProcessLimiter
import com.fxiaoke.open.erpsyncdata.main.service.CRMOuterServiceImpl
import com.fxiaoke.open.erpsyncdata.main.service.EventTriggerServiceImpl
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService
import com.google.common.collect.Lists
import org.bson.types.ObjectId
import org.powermock.core.classloader.annotations.PrepareForTest
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
class SendEventSpec extends Specification {

    ThreadLocal<Map<String, ObjectData>> CRM_OBJECT_DATA_LOCAL = new ThreadLocal<>();
    def syncLogManager = Mock(com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager)
    def syncMainService = Mock(SyncMainService)
    def outerServiceFactory = Mock(com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory)
    def asyncReTryIfFailedManager = Mock(AsyncReTryIfFailedManager)
    def idFieldConvertManager = Mock(IdFieldConvertManager)
    def objectDataServiceV3 = Mock(ObjectDataServiceV3)
    def erpObjManager = Mock(ErpObjManager)
    def notificationService = Mock(NotificationService)


    def eventTriggerService = new EventTriggerServiceImpl(syncMainService: syncMainService, asyncReTryIfFailedManager: asyncReTryIfFailedManager, syncLogManager: syncLogManager, outerServiceFactory: outerServiceFactory);


    @Unroll
    def "测试发送mq失败"() {
        given:
        def map = []

        "mock logid"
        syncLogManager.getInitLogId(*_) >> "J-E.89090_sandbox.0.FGLMain02.290hHPXcv5u.0.0"
        "mock syncMainService sendEventData2DispatcherMq发送失败"
//        syncMainService.sendEventData2DispatcherMq(*_) >> { throw new com.alibaba.druid.pool.GetConnectionTimeoutException("测试的pg") }
        syncMainService.sendEventData2DispatcherMq(*_) >> Result2.newError(ResultCodeEnum.SYSTEM_ERROR)
        when:
        eventTriggerService.batchSendEventData2DispatcherMqByContext(eventValue)

        then:
        1 * asyncReTryIfFailedManager.reTryBatchSendEventData2DispatcherMqByContext(*_) >> {
            map.add("1")
            return Result2.newSuccess()
        }
        map.size() == result

        where:
        eventValue      | result
        getEventValue() | 1

    }

    @Unroll
    def "测试发送retryData"() {
        given:
        def map = []
        def retrySendMqDao=Mock(RetrySendMqDao)
        def retryFailManager =new AsyncReTryIfFailedManager(retrySendMqDao:retrySendMqDao);

        "mock logid"
        retrySendMqDao.createReTrySendMq(*_)>> new ObjectId();
        "mock syncMainService sendEventData2DispatcherMq发送失败"
//        syncMainService.sendEventData2DispatcherMq(*_) >> { throw new com.alibaba.druid.pool.GetConnectionTimeoutException("测试的pg") }

        when:
        retryFailManager.reTryBatchSendEventData2DispatcherMqByContext(eventValue,"test")

        then:
        map.size()==0

        where:
        eventValue      | result
        getEventValue() | 1

    }


    List<SyncDataContextEvent> getEventValue() {
        List<SyncDataContextEvent> syncDataContextEvent= JSONArray.parseArray("[{\"cleanFields\":[],\"completeDataProcess\":true,\"completeDataWrite\":false,\"crmObjApiName\":\"object_I21Wu__c\",\"dataId\":\"ERPNumber2024061180\",\"dataReceiveType\":4,\"dataVersion\":1718115312076,\"dataVersionList\":[1718115312076],\"debugRecordIfDetailCauseMaterSync\":false,\"destData\":{\"object_describe_api_name\":\"object_I21Wu__c\",\"tenant_id\":\"89090\",\"record_type\":\"default__c\",\"owner\":[\"1000\"],\"remark__c\":\"ERPNumber2024061180\",\"_id\":\"ERPNumber2024061180\",\"name\":\"ERPNumber2024061180\"},\"destDataCenterId\":\"64acfe4860fc520001981494\",\"destDataId\":\"66683958cbb21c00078376d3\",\"destDetailObjMasterDetailFieldApiName\":{},\"destDetailSyncDataIdAndDestDataMap\":{},\"destEventType\":1,\"destObjectApiName\":\"object_I21Wu__c\",\"destTenantId\":\"89090\",\"destTenantType\":1,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":true,\"doWrite\":true,\"errCode\":0,\"errMsg\":\"成功\",\"finish\":false,\"mainObjApiName\":\"FGLMain02_1hlkbc4pe\",\"masterUpdateEvent\":true,\"objectApiName\":\"FGLMain02_1hlkbc4pe\",\"outSideObjApiName\":\"FGLMain02\",\"queryCrmObject2DestNode\":true,\"reverseWrite2Crm\":false,\"sourceData\":{\"tenant_id\":\"89090\",\"modifiedTime\":1718115312821,\"creator\":[\"1000\"],\"object_describe_api_name\":\"FGLMain02_1hlkbc4pe\",\"name\":\"ERP推送名称2024061180\",\"remark\":\"备注\",\"id\":\"ERPNumber2024061180\",\"_id\":\"ERPNumber2024061180\",\"last_modified_by\":[\"-10000\"],\"mongo_id\":\"666839226d8dda28d2dbb6af\"},\"sourceDataCenterId\":\"64acfe4860fc520001981495\",\"sourceEventType\":2,\"sourceTenantId\":\"89090\",\"sourceTenantType\":2,\"stop\":false,\"streamId\":\"9065f504ad464beaa65511bb46f0188a\",\"success\":true,\"syncDataData\":{\"destDataId\":\"66683958cbb21c00078376d3\",\"destEventType\":1,\"destObjectApiName\":\"object_I21Wu__c\",\"destTenantId\":\"89090\",\"destTenantType\":1,\"id\":\"66685c21fd8814310c35d5ae\",\"sourceData\":{\"tenant_id\":\"89090\",\"creator\":[\"1000\"],\"object_describe_api_name\":\"FGLMain02_1hlkbc4pe\",\"name\":\"ERP推送名称2024061180\",\"id\":\"ERPNumber2024061180\",\"_id\":\"ERPNumber2024061180\"},\"sourceDataId\":\"ERPNumber2024061180\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"FGLMain02_1hlkbc4pe\",\"sourceTenantId\":\"89090\",\"sourceTenantType\":2,\"status\":2,\"syncPloyDetailSnapshotId\":\"282c811c3df74032944348a963ed43fe\",\"tenantId\":\"89090\"},\"syncDataId\":\"66685c21fd8814310c35d5ae\",\"syncDataMap\":{\"66685c21fd8814310c35d5ae\":{\"destDataId\":\"66683958cbb21c00078376d3\",\"destDataName\":\"ERPNumber2024061180\",\"destObjectApiName\":\"object_I21Wu__c\",\"sourceDataId\":\"ERPNumber2024061180\",\"sourceObjectApiName\":\"FGLMain02_1hlkbc4pe\",\"syncDataId\":\"66685c21fd8814310c35d5ae\"}},\"syncLogId\":\"J-E.89090_sandbox.0.FGLMain02.290hHPXcv5u.0.0\",\"syncPloyDetailId\":\"9065f504ad464beaa65511bb46f0188a\",\"syncPloyDetailSnapshotId\":\"282c811c3df74032944348a963ed43fe\",\"tenantId\":\"89090\",\"version\":1718115312076}]",SyncDataContextEvent.class);
        return Lists.newArrayList(syncDataContextEvent);
    }


}
