package com.fxiaoke.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.UnDistributeCustomerArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.DistributeCustomerUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
@Slf4j
public class UnDistributeCustomerServiceImplTest extends BaseTest {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ApiClientHolder apiClientHolder;

    @Test
    public void executeLogic2() {
        UnDistributeCustomerArg model = new UnDistributeCustomerArg();
        model.setTenantId("88466");
        model.setDataCenterId("6436278b3dcc6b0001e76652");
        model.setUseOrgNumber("003");
        model.setCustomerNumber("20200716-000178");
        model.setCustomerId("717934");

        CustomFunctionCommonArg arg = new CustomFunctionCommonArg();
        arg.setTenantId("88466");
        arg.setType("unDistributeCustomer");
        arg.setParams(JSONObject.toJSONString(model));
        executeLogic(arg);
    }

    private Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("UnDistributeCustomerServiceImpl.executeLogic,arg={}", arg);

        if (arg == null || StringUtils.isEmpty(arg.getTenantId()) || StringUtils.isEmpty(arg.getParams())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        UnDistributeCustomerArg model = JSONObject.parseObject(arg.getParams(), UnDistributeCustomerArg.class);
        if (model == null
                || StringUtils.isEmpty(model.getTenantId())
                || StringUtils.isEmpty(model.getDataCenterId())
                || StringUtils.isEmpty(model.getUseOrgNumber())
                || StringUtils.isEmpty(model.getCustomerNumber())
                || StringUtils.isEmpty(model.getCustomerId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(model.getTenantId(), model.getDataCenterId());
        if (connectInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(model.getTenantId(),
                connectInfoEntity.getConnectParams(),
                model.getDataCenterId());

        String customerId = model.getCustomerId();
        String useOrgCustomerId = DistributeCustomerUtils.getCustomerId(apiClient,model.getCustomerNumber(), model.getUseOrgNumber());
        if(StringUtils.isEmpty(useOrgCustomerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_CUSTOMER_IN_DISTRIBUTED_ORG);
        }
        Submit.BaseArg unSubmitArg = new Submit.BaseArg();
        unSubmitArg.setIds(useOrgCustomerId);
        Result<Submit.Result> unAudit = apiClient.unAudit("BD_Customer", unSubmitArg);
        log.info("UnDistributeCustomerServiceImpl.executeLogic,unAudit={}", unAudit);

        String orgId = DistributeCustomerUtils.getOrgId(apiClient, model.getUseOrgNumber());
        if (StringUtils.isEmpty(customerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_ORG);
        }

        UnDistributeArg unDistributeArg = new UnDistributeArg();
        unDistributeArg.setDataIds(customerId);
        unDistributeArg.setOrganizationIds(orgId);
        Result<String> result = apiClient.unDistribute("BD_Customer", unDistributeArg);
        log.info("UnDistributeCustomerServiceImpl.executeLogic,result={}", result);
        if(!result.isSuccess()) {
            return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
        }
        String newCustomerId = null;
        UnDistributeResult unDistributeResult = JSONObject.parseObject(result.getData(), UnDistributeResult.class);
        if (CollectionUtils.isNotEmpty(unDistributeResult.getResult().getResponseStatus().getSuccessEntitys())) {
            newCustomerId = unDistributeResult.getResult().getResponseStatus().getSuccessEntitys().get(0).getId();
        }
        return Result.newSuccess(newCustomerId);
    }
}
