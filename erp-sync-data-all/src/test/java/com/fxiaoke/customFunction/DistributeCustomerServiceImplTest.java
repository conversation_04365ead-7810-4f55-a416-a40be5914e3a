package com.fxiaoke.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.DistributeCustomerArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.DistributeArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.DistributeResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.DistributeCustomerUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
@Slf4j
public class DistributeCustomerServiceImplTest extends BaseTest {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private AplManager aplManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Test
    public void executeLogic2() {
        DistributeCustomerArg model = new DistributeCustomerArg();
        model.setTenantId("88466");
        model.setDataCenterId("6436278b3dcc6b0001e76652");
        model.setUseOrgNumber("003");
        model.setCustomerNumber("20200716-000178");

        CustomFunctionCommonArg arg = new CustomFunctionCommonArg();
        arg.setTenantId("88466");
        arg.setType("distributeCustomer");
        arg.setParams(JSONObject.toJSONString(model));
        executeLogic(arg);
    }

    private Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("DistributeCustomerServiceImpl.executeLogic,arg={}", arg);

        if (arg == null || StringUtils.isEmpty(arg.getTenantId()) || StringUtils.isEmpty(arg.getParams())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        DistributeCustomerArg model = JSONObject.parseObject(arg.getParams(), DistributeCustomerArg.class);
        if (model == null
                || StringUtils.isEmpty(model.getTenantId())
                || StringUtils.isEmpty(model.getDataCenterId())
                || StringUtils.isEmpty(model.getUseOrgNumber())
                || StringUtils.isEmpty(model.getCustomerNumber())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(model.getTenantId(), model.getDataCenterId());
        if (connectInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient(model.getTenantId(),
                connectInfoEntity.getConnectParams(),
                model.getDataCenterId());
        if (StringUtils.isNotEmpty(DistributeCustomerUtils.getCustomerId(apiClient, model.getCustomerNumber(), model.getUseOrgNumber()))) {
            return Result.newError(ResultCodeEnum.CUSTOMER_EXIST_IN_CURRENT_ORG);
        }

        String customerId = DistributeCustomerUtils.getCustomerId(apiClient, model.getCustomerNumber());
        if (StringUtils.isEmpty(customerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_CUSTOMER);
        }

        String orgId = DistributeCustomerUtils.getOrgId(apiClient, model.getUseOrgNumber());
        if (StringUtils.isEmpty(customerId)) {
            return Result.newError(ResultCodeEnum.CANNOT_FIND_K3C_ORG);
        }

        DistributeArg distributeArg = new DistributeArg();
        distributeArg.setDataIds(customerId);
        distributeArg.setOrganizationIds(orgId);
        Result<String> result = apiClient.distribute("BD_Customer", distributeArg);
        log.info("DistributeCustomerServiceImpl.executeLogic,result={}", result);
        if(!result.isSuccess()) {
            return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
        }
        String newCustomerId = null;
        DistributeResult distributeResult = JSONObject.parseObject(result.getData(), DistributeResult.class);
        if (CollectionUtils.isNotEmpty(distributeResult.getResult().getResponseStatus().getSuccessEntitys())) {
            newCustomerId = distributeResult.getResult().getResponseStatus().getSuccessEntitys().get(0).getId();
        }
        return Result.newSuccess(newCustomerId);
    }

    @Test
    public void testFunctionAPIName(){
//        String tenantId = "88521";
//        Integer userId = 1000;
//        //根据预置函数模板创建JDY。默认函数名
//        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), userId);
//        QueryFuncArg functionServiceFindArg=new QueryFuncArg();
//        functionServiceFindArg.setFunctionName(CommonConstant.JDY_SYSTEM_FUNC);
//        Result<QueryFunctionResult> functionResultResult = aplManager.queryRegularFunction(headerObj, functionServiceFindArg);
//        if(functionResultResult.isSuccess()&&ObjectUtils.isNotEmpty(functionResultResult.getData().getResult())){
//            QueryFunctionResult.ListFunctionResult result = functionResultResult.getData().getResult();
//            //判断是不是已经被其他连接器使用了
//            List<ErpConnectInfoEntity> listDcByTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getListDcByTenantId(tenantId, ErpChannelEnum.ERP_JDY);
//            List<String> aplClassName= Lists.newArrayList();
//            for (ErpConnectInfoEntity erpConnectInfoEntity : listDcByTenantId) {
//                JdyConnectParam connectParam=JSONObject.parseObject(erpConnectInfoEntity.getConnectParams(),JdyConnectParam.class);
//                if(StringUtils.isNotEmpty(connectParam.getAplClassApiName())){
//                    aplClassName.add(connectParam.getAplClassApiName());
//                }
//
//            }
//            if(result.getFunction().size()>0){
//                String aplName = result.getFunction().stream().map(FunctionServiceFindData::getApiName).filter(u -> !aplClassName.contains(u)).findFirst().get();
//
//            }
//        }

//
//        Map map=JSONObject.parseObject("{\n" +
//                "    \"binding_object_api_name\": \"\",\n" +
//                "    \"type\": \"\",\n" +
//                "    \"selectFieldName\": \"name\",\n" +
//                "    \"function_name\": \"JDY\",\n" +
//                "    \"pageSize\": 20,\n" +
//                "    \"pageNumber\": 1,\n" +
//                "    \"pageSizeOption\": [\n" +
//                "        20,\n" +
//                "        50,\n" +
//                "        100\n" +
//                "    ],\n" +
//                "    \"_isfilter\": false,\n" +
//                "    \"is_include_used\": true\n" +
//                "}", Map.class);
//        QueryFuncArg queryFuncArg=new QueryFuncArg();
//        queryFuncArg.setFunctionName("JDYxxxx");
//        Result<QueryFunctionResult> functionResultResult = sfaApiManager.queryRegularFunction(headerObj, queryFuncArg);
//        if(functionResultResult.isSuccess()&& ObjectUtils.isNotEmpty(functionResultResult.getData().getResult())){
//            List<String> functionName = functionResultResult.getData().getResult().getFunction().stream().map(FunctionServiceFindData::getApiName).collect(Collectors.toList());
//
//        }
        log.info("function");
    }
}
