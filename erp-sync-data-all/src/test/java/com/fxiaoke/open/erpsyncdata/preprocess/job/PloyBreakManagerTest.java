package com.fxiaoke.open.erpsyncdata.preprocess.job;

import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/11/18
 */
@Ignore
public class PloyBreakManagerTest extends BaseTest {
    @Autowired
    private PloyBreakManager ployBreakManager;


    @Test
    public void testAddFailedSyncDataNum() {
        ployBreakManager.incrFailedSyncDataNum("81772", "a99d946cd74c4d17b088a72e4bb0f83b");

        for (int i = 0; i < 5; i++) {

        }
        ThreadUtil.waitForDie();
    }
}