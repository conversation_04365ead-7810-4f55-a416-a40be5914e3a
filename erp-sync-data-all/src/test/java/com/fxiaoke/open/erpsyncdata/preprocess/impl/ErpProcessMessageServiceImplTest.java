package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.manager.SyncPloyDetailAdminManager;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmRequestBaseParam;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConvertFactory;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConverter;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CompleteDataWriteArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteResultData;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.json.JSONObject;
import org.json.XML;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:38 2020/10/21
 * @Desc:
 */
@Ignore
public class ErpProcessMessageServiceImplTest extends BaseTest {
    //    @Autowired
//    private SkuSpuService skuSpuService;
    @Autowired
    private SyncPloyDetailAdminManager syncPloyDetailAdminManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private SyncSkuSpuServiceImpl skuSpuService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private FieldConvertFactory fieldConvertFactory;

    @Test
    public void test() {
        DoWriteResultData message1 = new DoWriteResultData();
        DoWriteMqData doWriteMqData = new DoWriteMqData();
        doWriteMqData.setDestTenantId("71658");
        doWriteMqData.setDestObjectApiName(ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
        doWriteMqData.setSyncDataId("d34405eb4d5c472dbd94872bbbcf1d94");
        com.fxiaoke.open.erpsyncdata.common.data.ObjectData destData = new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
        destData.putApiName(ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
        destData.put("name", "1118guoyd测试商品0002");
        destData.putId(IdUtil.generateId());
        destData.putTenantId("71658");
        destData.put("price", 11);
        destData.put("owner", Lists.newArrayList("-10000"));
        destData.put("category", "277");
        destData.put("unit", "mpWl5XsC");
        doWriteMqData.setDestEventType(1);
        doWriteMqData.setDestData(destData);
        CompleteDataWriteArg completeDataWriteArg = new CompleteDataWriteArg();
        message1.setDoWriteMqData(doWriteMqData);
        message1.setCompleteDataWriteArg(completeDataWriteArg);
        //skuSpuService.handleSkuSpu2Crm(message1);
//        ObjectData test1 = new ObjectData();
//        test1.setTenantId(71658);
//        test1.put("object_describe_api_name", ObjectApiNameEnum.FS_SPU.getObjApiName());
//        test1.put("owner", Lists.newArrayList("-10000"));
//        test1.put("name", "1103guoyd测试商品0001");
//        //test1.put("batch_sn", "1");
//        test1.put("category", "277");
//        //test1.put("is_spec", true);
//        test1.put("standard_price", 0);
//        test1.put("unit", "so3BXkto");
//        test1.put("_id", "5fa10ff06de9e800018346fc");
//        Map<String, Object> objectDataMap=new HashMap<>();
//        objectDataMap.put("object_data",test1);
//        CrmRequestBaseParam baseParam = CrmRequestBaseParam.build(71658,null,ObjectApiNameEnum.FS_SPU.getObjApiName());
//        CrmObjectDataResult crmObjectDataResult = crmObjectDataManager.updateObjectData(baseParam,objectDataMap , Collections.EMPTY_MAP);

        Map<String, Object> sss = new Gson().fromJson("{\"tenant_id\":\"71658\",\"lock_rule\":null,\"hasSpecValueCreatePrivilege\":true,\"batch_sn\":\"1\",\"remark\":\"\",\"lock_user\":null,\"commodity_label\":null,\"extend_obj_data_id\":\"5fa10ff06de9e80001834737\",\"is_deleted\":false,\"created_by__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"life_status_before_invalid\":null,\"object_describe_api_name\":\"SPUObj\",\"owner__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"owner_department\":null,\"out_owner\":null,\"owner__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"owner\":[\"-10000\"],\"standard_price\":\"1\",\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"全公司\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"999999\",\"parentId\":null,\"status\":0},\"create_time\":1604390896844,\"life_status\":\"normal\",\"order_field\":null,\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"is_spec\":true,\"out_tenant_id\":null,\"created_by\":[\"-10000\"],\"record_type\":\"default__c\",\"picture\":[],\"field_Bf1um__c\":\"\",\"product_line\":\"\",\"unit\":\"jfWeQIY6\",\"field_61lb0__c\":\"\",\"data_own_department\":[\"999999\"],\"name\":\"1103guoyd测试商品0001\",\"order_by\":null,\"_id\":\"5fa10ff06de9e800018346fc\",\"category\":\"57\",\"data_own_department__l\":[{\"deptId\":\"999999\",\"deptName\":\"全公司\",\"status\":0}],\"object_describe_id\":\"5c904a0e319d19e745308d77\",\"sku\":[{\"lock_rule\":null,\"field_p24Zp__c\":null,\"batch_sn\":\"1\",\"field_RK6p5__c\":null,\"extend_obj_data_id\":\"5fa10ff16de9e80001834792\",\"spu_id__relation_ids\":\"5fa10ff06de9e800018346fc\",\"off_shelves_time\":null,\"created_by__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"price\":\"10.0000000000\",\"field_VzTg2__c\":null,\"life_status_before_invalid\":null,\"total_num\":1,\"model\":null,\"owner_department\":null,\"field_4CJb0__c\":null,\"spu_id\":\"5fa10ff06de9e800018346fc\",\"barcode\":null,\"maintenance_period\":null,\"field_2D94a__c\":null,\"spec_and_value\":[{\"spec_value_id\":\"5f8fdf5fc533e700018bad48\",\"spec_id\":\"5f8fdf5ec533e700018bad0a\",\"order_field\":\"1\",\"sku_id\":\"5fa10ff06de9e80001834727\",\"spec_name\":\"尺寸\",\"spec_value_name\":\"2*2\"},{\"spec_value_id\":\"5f0d19f35e5b04000120436f\",\"spec_id\":\"5f0d19f35e5b04000120430f\",\"order_field\":\"2\",\"sku_id\":\"5fa10ff06de9e80001834727\",\"spec_name\":\"面积1\",\"spec_value_name\":\"334\"}],\"lock_status\":\"0\",\"package\":\"CRM\",\"field_3a999__c\":null,\"data_own_department__r\":{\"deptName\":\"全公司\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"999999\",\"parentId\":null,\"status\":0},\"is_giveaway\":\"0\",\"create_time\":1604390897557,\"field_1b7Fh__c\":null,\"field_j2Ih2__c\":null,\"order_field\":null,\"field_N42TZ__c\":null,\"field_1kVdj__c\":null,\"version\":\"1\",\"created_by\":[\"-10000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"-10000\"],\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\"}],\"field_xj71y__c\":null,\"field_RNP22__c\":null,\"product_line\":null,\"unit\":\"xKMj8Dgt\",\"data_own_department\":[\"999999\"],\"name\":\"1103guoyd测试商品0001[2*2-334]\",\"_id\":\"5fa10ff06de9e80001834727\",\"data_own_department__l\":[{\"deptId\":\"999999\",\"deptName\":\"全公司\",\"status\":0}],\"tenant_id\":\"71658\",\"field_Jyjar__c\":null,\"field_3BQrn__c\":null,\"field_k68FE__c\":null,\"product_status\":\"1\",\"remark\":null,\"replacement_period\":null,\"product_code\":null,\"lock_user\":null,\"on_shelves_time\":1604390897037,\"safety_stock\":null,\"is_deleted\":false,\"field_7cIc5__c\":null,\"object_describe_api_name\":\"ProductObj\",\"owner__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"out_owner\":null,\"relevant_team__r\":\"系统\",\"spu_id__r\":\"1103guoyd测试商品0001\",\"owner__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"owner\":[\"-10000\"],\"picture_path\":null,\"last_modified_time\":1604390897557,\"life_status\":\"normal\",\"field_gKG6c__c\":null,\"field_OFRpz__c\":null,\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"field_0z3x3__c\":\"385\",\"field_bH5YS__c\":null,\"last_modified_by\":[\"-10000\"],\"Brand__c\":null,\"data_right_flag\":\"write\",\"technical_parameter\":null,\"out_tenant_id\":null,\"field_014tB__c\":null,\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"field_61lb0__c\":null,\"product_spec\":\"尺寸:2*2;面积1:334\",\"max_stock\":null,\"field_zKo91__c\":true,\"order_by\":null,\"category\":\"57\",\"尺寸\":\"5f8fdf5fc533e700018bad48\",\"面积1\":\"5f0d19f35e5b04000120436f\",\"is_removed\":false,\"status_flag\":1}]}", Map.class);
        ObjectData test = new ObjectData();
        for (String key : sss.keySet()) {
            test.put(key, sss.get(key));
        }
        HeaderObj headerObj = new HeaderObj(71658, CrmConstants.SYSTEM_USER);
        SearchTemplateQuery spuQuery = new SearchTemplateQuery();
        spuQuery.addFilter("name", Lists.newArrayList("1103guoyd测试商品0001"), "EQ");
        //Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(), spuQuery);

//        ControllerListArg arg=new ControllerListArg();
//        arg.setObjectDescribeApiName(ObjectApiNameEnum.FS_SPU.getObjApiName());
//        SearchQuery searchQuery = new SearchQuery();
//        searchQuery.setLimit(100);
//        searchQuery.setOffset(0);
//        Filter filter=new Filter();
//        filter.setFieldName("spu_id");
//        filter.setFieldValues(Lists.newArrayList("5fa10ff06de9e800018346fc"));
//        filter.setOperator("EQ");
//        searchQuery.setFilters(Lists.newArrayList(filter));
//        arg.setSearchQuery(searchQuery);
//        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), arg);
//        GetRelatedDataListArg arg=new GetRelatedDataListArg();
//        arg.setAssociateObjectDataId("5fa10ff06de9e800018346fc");
//        arg.setAssociatedObjectDescribeApiName("ProductObj");
//        arg.setAssociateObjectDescribeApiName("SPUObj");
//        arg.setAssociatedObjectFieldRelatedListName("spu_sku_list");
//        arg.setIncludeAssociated(true);
//        //{"fieldName":"last_modified_time","isAsc":false}
//        SyncSkuSpuServiceImpl.SearchQueryInfo queryInfo = new SyncSkuSpuServiceImpl.SearchQueryInfo();
//        arg.setSearchQueryInfo(new Gson().toJson(queryInfo));
//        ObjectDataQueryListByIdsResult relatedDataList = metadataControllerService.getRelatedDataList(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), arg);

        ObjectData spuObjData = new ObjectData();
        spuObjData.setTenantId(71658);
        spuObjData.put("object_describe_api_name", ObjectApiNameEnum.FS_SPU.getObjApiName());
        spuObjData.setOwner(-10000);
        spuObjData.put("name", "1124guoyd测试商品0003");
        spuObjData.put("batch_sn", "1");
        spuObjData.put("category", "277");
        spuObjData.put("is_spec", true);
        spuObjData.put("standard_price", "0");
        spuObjData.put("unit", "mpWl5XsC");
        spuObjData.put("created_by", "1000");

        ObjectData productObjData = new ObjectData();
        productObjData.put("_id", IdUtil.generateId());
        productObjData.setTenantId(71658);
        productObjData.put("object_describe_api_name", ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
        productObjData.setOwner(-10000);
        productObjData.put("name", "1124guoyd测试商品0003");
        productObjData.put("price", "10");
        productObjData.put("remark", "134123412");
//        productObjData.put("category", "260");
        productObjData.put("status_flag", 1);
        Map<String, Object> spec = new HashMap<>();
        spec.put("spec_id", "5f8fdf5ec533e700018bad0a");
        spec.put("spec_value_id", "5f8fdf5fc533e700018bad48");
        spec.put("order_field", "1");
//        spec.put("sku_id", "5fa10ff06de9e80001834727");
        Map<String, Object> spec1 = new HashMap<>();
        spec1.put("spec_id", "5f0d19f35e5b04000120430f");
        spec1.put("spec_value_id", "5f0d19f35e5b04000120436f");
        spec1.put("order_field", "2");
        //       spec1.put("sku_id", "5fa10ff06de9e80001834727");
//        Map<String, Object> spec2 = new HashMap<>();
//        spec2.put("spec_id", "5f9a840034b6f400012aa20a");
//        spec2.put("spec_value_id", "5f9a840034b6f400012aa248");
//        spec2.put("order_field", "3");
        //productObjData.put("spec_and_value", Lists.newArrayList(spec, spec1));
//        productObjData.put("unit", "CyRGoPWa");

        spuObjData.put("sku", Lists.newArrayList(productObjData));
//        Result<ActionAddResult> spuDataResult = skuSpuService.createCrmObjectData(headerObj, spuObjData, new HashMap<>());
//        System.out.println(spuDataResult);
//        productObjData.put("spu_id", "5fa10ff06de9e800018346fc");
//        Result<ActionAddResult> productResult = skuSpuManager.createCrmObjectData(headerObj, productObjData, new HashMap<>());
//        System.out.println(productResult);
        productObjData.put("spu_id", "5fbdba43fd39f40001606d9a");
        productObjData.put("_id", "9230f7da78df412da837ac0e62315039");
        spuObjData.put("_id", "5fbdba43fd39f40001606d9a");
        String str = new Gson().toJson(spuObjData);
//        Result<ActionEditResult> spuUpdateDataResult = skuSpuService.updateCrmObjectData(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(), spuObjData, null);
//        System.out.println(spuUpdateDataResult);
        Result<ActionEditResult> productUpdateDataResult = skuSpuService.updateCrmObjectData(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), productObjData, null);
        System.out.println(productUpdateDataResult);
    }

    @Test
    public void test11() {
        try {
            FileInputStream in = new FileInputStream(new File("D:\\text.xml"));
            BufferedReader br = new BufferedReader(new InputStreamReader(in));
            String line = "";
            StringBuffer buf = new StringBuffer();
            for (line = br.readLine(); line != null; line = br.readLine()) {
                buf.append(new String(line.getBytes(), "UTF-8"));
            }
            String retmsg = buf.toString();
            System.out.println("【xml】" + buf.toString());
            br.close();
            JSONObject xmlJSONObj = XML.toJSONObject(retmsg);
            System.out.println(xmlJSONObj);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCPQ() {
        int ei = 79675;
        String productId = "5fbf52bd58d2810001da19e3";
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("root_product_ids", Lists.newArrayList(productId));
        paramMap.put("extend_obj_desc_api_name", "ProductGroupObj");
        paramMap.put("bom_list", Lists.newArrayList());
        paramMap.put("include_constraint", false);
        paramMap.put("include_desc", false);
        paramMap.put("price_book_id", "");
        paramMap.put("child_search_query_info", "{\"limit\":2000,\"offset\":0}");
        CrmRequestBaseParam baseParam = CrmRequestBaseParam.build(ei, CrmConstants.SYSTEM_USER, null);
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<JsonObject> jsonObjectResult = crmRemoteService.BOMObjTreeRelatedListV1(baseParam, paramMap, null);
    }

    @Test
    public void testDepartment() {
        int ei = 79675;
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ei), CrmConstants.SYSTEM_USER);
        ObjectData fsObjectData = new ObjectData();
        fsObjectData.put("extend_obj_desc_api_name",ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName());
        fsObjectData.put("record_type", "default__c");
        fsObjectData.put("parent_id", Lists.newArrayList("999999"));
        fsObjectData.put("status", "0");
        fsObjectData.setTenantId(ei);
        fsObjectData.setName("测试部门1219005");
        ActionAddArg addArg = new ActionAddArg();
        fsObjectData.putAll(fsObjectData);
        addArg.setObjectData(fsObjectData);
        addArg.setDetails(new HashMap<>());
        Result<ActionAddResult> addResultResult = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName(),false,false, null, null, addArg);
        System.out.println("");
    }
    @Test
    public void testPersion() {
        FieldConverter fieldConverter = fieldConvertFactory.getConverter(2001);
        Integer fieldMappingType = fieldConverter.getFieldMappingType();
        int ei = 79675;
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ei), CrmConstants.SYSTEM_USER);
        ObjectData fsObjectData = new ObjectData();
        fsObjectData.put("_id",idGenerator.get());
        fsObjectData.put("extend_obj_desc_api_name",ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName());
        fsObjectData.put("record_type", "default__c");
        fsObjectData.put("status", "0");
        fsObjectData.setTenantId(ei);
        fsObjectData.setName("测试员工113");
        ActionAddArg addArg = new ActionAddArg();
        fsObjectData.putAll(fsObjectData);
        addArg.setObjectData(fsObjectData);
        addArg.setDetails(new HashMap<>());
        Result<ActionAddResult> addResultResult = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName(),false,false, null, null, addArg);
        System.out.println("");
    }

    @Test
    public void testPrice() {
        int ei = 84801;
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(ei), CrmConstants.SYSTEM_USER);

        String objData="{\n" +
                "    \"object_data\": {\n" +
                "        \"tenant_id\": \"84801\",\n" +
                "        \"owner\": [\n" +
                "            \"1000\"\n" +
                "        ],\n" +
                "        \"end_date\": *************,\n" +
                "        \"active_status\": \"1\",\n" +
                "        \"apply_account_range\": \"{\\\"type\\\":\\\"CONDITION\\\",\\\"value\\\":\\\"[{\\\\\\\"filters\\\\\\\":[{\\\\\\\"field_name\\\\\\\":\\\\\\\"account_type\\\\\\\",\\\\\\\"operator\\\\\\\":\\\\\\\"IN\\\\\\\",\\\\\\\"field_values\\\\\\\":[\\\\\\\"lA00s9g93\\\\\\\"],\\\\\\\"connector\\\\\\\":\\\\\\\"AND\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"select_one\\\\\\\"}],\\\\\\\"connector\\\\\\\":\\\\\\\"OR\\\\\\\"}]\\\"}\",\n" +
                "        \"object_describe_api_name\": \"PriceBookObj\",\n" +
                "        \"name\": \"测试客户\",\n" +
                "        \"_id\": \"642e6fc614969900014c5632\",\n" +
                "        \"record_type\": \"default__c\",\n" +
                "        \"start_date\": *************\n" +
                "    },\n" +
                "    \"details\": {\n" +
                "        \"PriceBookAccountObj\": [\n" +
                "            {\n" +
                "                \"tenant_id\": \"84801\",\n" +
                "                \"owner\": [\n" +
                "                    \"1000\"\n" +
                "                ],\n" +
                "                \"_id\": \"642e6fc614969900014c5636\",\n" +
                "                \"object_describe_api_name\": \"PriceBookAccountObj\",\n" +
                "                \"price_book_id\": \"642e6fc614969900014c5632\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"fillOutOwner\": false\n" +
                "}";
        ObjectData fsObjectData = com.alibaba.fastjson.JSONObject.parseObject(objData,ObjectData.class);
        ActionAddArg addArg = new ActionAddArg();
        fsObjectData.putAll(fsObjectData);
        addArg.setObjectData(fsObjectData);
        addArg.setDetails(new HashMap<>());
        Result<ActionAddResult> addResultResult = metadataActionService.add(headerObj, "PriceBookObj",false,false, null, null, addArg);
        System.out.println("");
    }
}