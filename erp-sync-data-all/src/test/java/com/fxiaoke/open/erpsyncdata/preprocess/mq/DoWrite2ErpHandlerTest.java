package com.fxiaoke.open.erpsyncdata.preprocess.mq;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2ErpManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 11:20 2021/12/30
 * @Desc:
 */
@Ignore
public class DoWrite2ErpHandlerTest extends BaseTest {
    @Autowired
    private DoWrite2ErpManager doWrite2ErpManager;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;

    @Test
    public void testData(){
//        ErpTenantConfigurationEntity entity= erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81243")).findOne("81243","1","2", TenantConfigurationTypeEnum.LISTEN_CRM_DELETE_DATA.name());

    }
    @Test
    public void handleDoWrite2Erp() {
        String data="{\"destData\":{\"object_describe_api_name\":\"SAL_SaleOrder.BillHead\",\"tenant_id\":\"81243\",\"FSaleOrderFinance.FSettleCurrId.FNumber\":\"PRE001\",\"FChangeReason\":\"124324\",\"FSaleOrderFinance.FExchangeRate\":\"1\",\"FBillTypeID.FNumber\":\"XSDD01_SYS\",\"FDate\":1.6407936E12,\"FSalerId.FNumber\":[\"1000\"],\"FSaleOrgId.FNumber\":\"000\",\"FCustId.FNumber\":\"20211213-015887\",\"FNote\":\"13365651212\",\"F_PAEZ_Remarks\":\"zsl022企业客户048\",\"_id\":\"61cd564a0347bd000132363c\"},\"destDataId\":\"61cd564a0347bd000132363c\",\"destDetailSyncDataIdAndDestDataMap\":{\"87a782f66f8544c09c09cbb8c7d88ea1\":{\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"81243\",\"fake_master_detail\":\"61cd564a0347bd000132363c\",\"FSettleOrgIds.FNumber\":\"000\",\"FUnitID.FNumber\":\"Pcs\",\"FMaterialId.FNumber\":\"CH661z\",\"FQty\":\"60.00\",\"FTaxPrice\":\"0.00\",\"FRowType\":\"Standard\",\"FEntryNote\":\"哈哈备注\",\"_id\":\"61cd564a0347bd000132363d\",\"owner\":[],\"created_by\":[]}},\"destEventType\":1,\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantId\":\"81243\",\"destTenantType\":2,\"sourceTenantId\":\"81243\",\"syncDataId\":\"f53e761e8b3c453f955ecc2c3eebc6c0\",\"syncPloyDetailSnapshotId\":\"0cf12a4396144ab7bb3e959e99602c9f\",\"tenantId\":\"81243\"}";
        SyncDataContextEvent doWriteMqData= JsonUtil.fromJson(data,SyncDataContextEvent.class);
        doWriteMqData.setRequestId("6d171d35bf12af985838a7363521c234");
        doWriteMqData.getDestData().put(CommonConstant.REQUEST_ID_KEY,"6d171d35bf12af985838a7363521c234");
        SyncDataContextEvent syncStepData = doWrite2ErpManager.handleDoWrite2Erp(doWriteMqData);
        System.out.println("");
    }

    @Test
    public void handleDoWrite2Erp2() {
        String data="{\"destData\":{\"object_describe_api_name\":\"ENG_BOM.BillHead\",\"tenant_id\":\"82777\",\"FUseOrgId.FNumber\":\"000\",\"FUNITID.FNumber\":\"Pcs\",\"FCreateOrgId.FNumber\":\"000\",\"FBOMUSE\":\"99\",\"FBOMCATEGORY\":\"1\",\"FBILLTYPE.FNumber\":\"WLQD01_SYS\",\"FMATERIALID.FNumber\":\"CH1055\",\"_id\":\"788928494032814080\"},\"destDataId\":\"788928494032814080\",\"destDetailSyncDataIdAndDestDataMap\":{},\"destEventType\":1,\"destObjectApiName\":\"ENG_BOM.BillHead\",\"destTenantId\":\"82777\",\"destTenantType\":2,\"masterMappingsData\":null,\"requestId\":null,\"sourceTenantId\":\"82777\",\"syncDataId\":\"62a88181f836470a4acbc403\",\"syncDataMap\":{\"62a88181f836470a4acbc403\":{\"destDataId\":\"788928494032814080\",\"destObjectApiName\":\"ENG_BOM.BillHead\",\"sourceDataId\":\"62a82f9cf516a5000107ded4\",\"sourceObjectApiName\":\"BomInstanceObj\",\"syncDataId\":\"62a88181f836470a4acbc403\"}},\"syncPloyDetailSnapshotId\":\"915e991f05a84b188b367b548a3fd016\",\"tenantId\":\"82777\"}";
        SyncDataContextEvent doWriteMqData= JsonUtil.fromJson(data,SyncDataContextEvent.class);
        doWriteMqData.setRequestId("6d171d35bf12af985838a7363521c234");
        doWriteMqData.getDestData().put(CommonConstant.REQUEST_ID_KEY,"6d171d35bf12af985838a7363521c234");
        SyncDataContextEvent syncStepData = doWrite2ErpManager.doWrite2Erp(doWriteMqData);
        System.out.println("");
    }
}