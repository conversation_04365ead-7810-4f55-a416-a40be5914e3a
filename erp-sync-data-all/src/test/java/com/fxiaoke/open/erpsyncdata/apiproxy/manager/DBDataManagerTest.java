package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 16:52 2020/11/23
 * @Desc:
 */
@Ignore
public class DBDataManagerTest extends BaseTest {
    @Autowired
    private DBDataManager dbDataManager;
    @Autowired
    private ErpPushDataDao erpPushDataDao;


    @Test
    public void listErpObjDataByTime() {
        TimeFilterArg timeFilterArg=new TimeFilterArg();
        timeFilterArg.setTenantId("80787");
        timeFilterArg.setObjAPIName("inventory");
        timeFilterArg.setOperationType(1);
        timeFilterArg.setStartTime(System.currentTimeMillis()-1000*60*10);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setOffset(0);
        timeFilterArg.setLimit(10);
        Result<StandardListData> standardListDataResult = dbDataManager.listErpObjDataByTime(timeFilterArg);
        System.out.println(standardListDataResult);
    }


    @Test
    public void batchInsert() {
        List<ErpPushDataEntity> erpPushDataEntityList=new ArrayList<>();
        ErpPushDataEntity entity1=new ErpPushDataEntity();
        entity1.setId("2");
        entity1.setTenantId("80787");
        entity1.setObjectApiName("inventory");
        entity1.setOperationType(1);
        entity1.setSourceDataId("2");
        entity1.setSourceData("222");
        entity1.setStandardFormat("222");
        entity1.setCreateTime(new Date().getTime());
        entity1.setUpdateTime(new Date().getTime());
        ErpPushDataEntity entity2=new ErpPushDataEntity();
        entity2.setId("3");
        entity2.setTenantId("80787");
        entity2.setObjectApiName("inventory");
        entity2.setOperationType(1);
        entity2.setSourceDataId("2");
        entity2.setSourceData("222");
        entity2.setStandardFormat("222");
        entity2.setCreateTime(new Date().getTime());
        entity2.setUpdateTime(new Date().getTime());
        erpPushDataEntityList.add(entity1);
        erpPushDataEntityList.add(entity2);
        int i = erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("80787")).batchInsert(erpPushDataEntityList);
        System.out.println(i);
    }



}