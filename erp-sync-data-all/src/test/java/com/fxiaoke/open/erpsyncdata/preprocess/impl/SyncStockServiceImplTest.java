package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/30
 */
@Ignore
public class SyncStockServiceImplTest extends BaseTest {
    @Autowired
    private SyncStockServiceImpl syncStockService;

    @Test
    public void afterWriteBatchStock2Crm() {
        String json = "{\"completeDataWriteArg\":{\"destEventType\":2,\"writeResult\":{\"destDataId\":\"608ba58fbf77530001e28e08\",\"errCode\":0,\"errMsg\":\"success\",\"success\":true,\"syncDataId\":\"a4f44407fa5a4d739364ed607ca4155a\"}},\"doWriteMqData\":{\"destData\":{\"tenant_id\":\"81243\",\"owner\":[\"1000\"],\"batch_id\":\"608ba58dbf77530001e28e06\",\"object_describe_api_name\":\"BatchStockObj\",\"product_id\":\"608a2f13f246f80001bb070f\",\"batch_real_stock\":456.0,\"_id\":\"608ba58fbf77530001e28e08\",\"record_type\":\"default__c\",\"warehouse_id\":\"6051ce361c08f00001f0a40c\"},\"destDataId\":\"608ba58fbf77530001e28e08\",\"destDetailSyncDataIdAndDestDataMap\":{},\"destEventType\":2,\"destObjectApiName\":\"BatchStockObj\",\"destTenantId\":\"81243\",\"destTenantType\":1,\"sourceTenantId\":\"81243\",\"syncDataId\":\"a4f44407fa5a4d739364ed607ca4155a\",\"syncPloyDetailSnapshotId\":\"f7b963c98b914975b01820faae94ffde\"},\"stop\":false}";
        SyncDataContextEvent arg = JacksonUtil.fromJson(json,SyncDataContextEvent.class);
        syncStockService.afterWriteBatchStock2Crm(arg);
    }
}