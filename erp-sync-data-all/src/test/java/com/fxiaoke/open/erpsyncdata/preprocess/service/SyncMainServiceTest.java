package com.fxiaoke.open.erpsyncdata.preprocess.service;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.writer.manager.NodeReverseWrite2CrmManager;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


@Slf4j
public class SyncMainServiceTest extends BaseTest {
    @Autowired
    private SyncMainService syncMainService;
    @Autowired
    private ErpSyncService erpSyncService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao syncDataFixDao;

    @Autowired
    private NodeReverseWrite2CrmManager reverseWrite2CrmManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Test
    public void localTest() {
        IChangeableConfig allConfig = ConfigFactory.getConfig("erp-sync-data-all");
        String serviceEnvironment = allConfig.get("ServiceEnvironment");
        log.info("begin local test,env:{}",serviceEnvironment);
        ThreadUtil.waitForDie();
        log.info("end local test");
    }
    @Test
    public void testReserve(){
        SyncDataContextEvent completeDataWriteMqData = JSONObject.parseObject("{\"destEventType\":1,\"destTenantType\":2,\"detailWriteResults\":[{\"errCode\":5001,\"errMsg\":\"调用ERP接口报错: 调用外部http接口失败,错误信息：FReceivePlanAmount,整单收款计划应收金额合计不等于整单价税合计，不允许保存。\",\"simpleSyncData\":{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceDataId\":\"63ad9bde2752cb0001ed82f9\",\"sourceObjectApiName\":\"SalesOrderProductObj\",\"syncDataId\":\"63ad9bf32ada8669677d1225\"},\"success\":false,\"syncDataId\":\"63ad9bf32ada8669677d1225\"},{\"errCode\":5001,\"errMsg\":\"调用ERP接口报错: 调用外部http接口失败,错误信息：FReceivePlanAmount,整单收款计划应收金额合计不等于整单价税合计，不允许保存。\",\"simpleSyncData\":{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderPlan\",\"sourceDataId\":\"63ad9bde2752cb0001ed82fa\",\"sourceObjectApiName\":\"object_36FOK__c\",\"syncDataId\":\"63ad9bf32ada8669677d1226\"},\"success\":false,\"syncDataId\":\"63ad9bf32ada8669677d1226\"}],\"errCode\":0,\"errMsg\":\"success\",\"success\":true,\"syncPloyDetailSnapshotId\":\"cac52871846e4c39806c76b315b42a23\",\"tenantId\":\"84801\",\"writeResult\":{\"destDetailSyncDataIdAndDestDataMap\":{\"63ad9bf32ada8669677d1225\":{\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"84801\",\"fake_master_detail\":\"63ad9bf3f704170001f60dca\",\"FIsFree\":false,\"FUnitID.FNumber\":\"Pcs\",\"FMaterialId.FNumber\":\"CH1694\",\"FQty\":\"20.00\",\"FTaxPrice\":\"5.00\",\"FAllAmount\":\"100.00\",\"_id\":\"63ad9bf3f704170001f60dcc\"},\"63ad9bf32ada8669677d1226\":{\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderPlan\",\"tenant_id\":\"84801\",\"fake_master_detail\":\"63ad9bf3f704170001f60dca\",\"FRecAdvanceAmount\":\"200.00\",\"FRecAdvanceRate\":\"100.00\",\"FMustDate\":1.6703424E12,\"_id\":\"63ad9bf3f704170001f60dce\"}},\"errCode\":5001,\"errMsg\":\"调用ERP接口报错:调用外部http接口失败,错误信息：FReceivePlanAmount,整单收款计划应收金额合计不等于整单价税合计，不允许保存。\",\"simpleSyncData\":{\"destDataId\":\"63ad9bf3f704170001f60dca\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"sourceDataId\":\"63ad9bde2752cb0001ed82f8\",\"sourceObjectApiName\":\"SalesOrderObj\",\"syncDataId\":\"63ad9bf32ada8669677d1224\"},\"success\":false,\"syncDataId\":\"63ad9bf32ada8669677d1224\"}}", SyncDataContextEvent.class);
        SyncDataContextEvent syncStepData = reverseWrite2CrmManager.processMessage(completeDataWriteMqData);

    }
    @Test
    public void testSync(){

        erpSyncService.syncDataTimeOutUpdateStatusToError("82777",1672244224671L, 1672244344671L);
    }

    @Test
    public void testUpdate(){
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).queryByDcId("88521", "653623d5b4660c00014c129a", null);

        int count = syncDataMappingsDao.setTenantId("82777").updateLastStatusById("82777", "63a9ef658d2fcb000143c1c1", SyncDataStatusEnum.BE_PROCESS.getStatus());
        System.out.println("SyncMainServiceTest.testUpdate");
    }

    @Test
    public void testUpdateSyncData(){
    }


    @Test
    public void testSyncMain() {



//        syncLogManager.initLogId("89090","AccountObj");
//        List<ObjectDescribe> object_okqMk__c = crmRemoteManager.listObjectAndFieldByApiNames("89090", Lists.newArrayList("object_OkqMk__c"));
//        syncTest(bodyMasterDetail);
//        SyncDataContextEvent syncDataContextEvent=new SyncDataContextEvent();
//        ArrayList<Long> dataVersions = Lists.newArrayList(1688718058324L, 1688718058324l);
//
//        syncDataContextEvent.setDataVersionList(dataVersions);
//        String dataToString=syncDataContextEvent.toString();
//
//        String data = JSON.toJSONString(syncDataContextEvent);
//
//        SyncDataContextEvent eventData = JSON.parseObject(data,SyncDataContextEvent.class);
        syncTest(body);

    }
    String bodyMasterDetail="{\n" +
            "    \"checkSyncDataMapping\":false,\n" +
            "    \"completeDataProcess\":false,\n" +
            "    \"completeDataWrite\":false,\n" +
            "    \"dataReceiveType\":2,\n" +
            "    \"debugRecordIfDetailCauseMaterSync\":false,\n" +
            "    \"detailData\":{\n" +
            "\n" +
            "    },\n" +
            "    \"detailObjectDatasMap\":{\n" +
            "\n" +
            "    },\n" +
            "    \"detailWriteResults\":[\n" +
            "\n" +
            "    ],\n" +
            "    \"doProcess\":false,\n" +
            "    \"doWrite\":false,\n" +
            "    \"errCode\":0,\n" +
            "    \"errMsg\":\"success\",\n" +
            "    \"finish\":false,\n" +
            "    \"queryCrmObject2DestNode\":false,\n" +
            "    \"reverseWrite2Crm\":false,\n" +
            "    \"sourceData\":{\n" +
            "        \"enable_partner_view\":false,\n" +
            "        \"owner_department_id\":\"1015\",\n" +
            "        \"owner_department\":\"研发部\",\n" +
            "        \"searchAfterId\":[\n" +
            "            \"1704186850968\",\n" +
            "            \"6593c63b1b8f530007e62828\"\n" +
            "        ],\n" +
            "        \"is_relevance_wechat\":false,\n" +
            "        \"lock_status\":\"0\",\n" +
            "        \"package\":\"CRM\",\n" +
            "        \"create_time\":1704183355484,\n" +
            "        \"version\":\"4\",\n" +
            "        \"created_by\":[\n" +
            "            \"1000\"\n" +
            "        ],\n" +
            "        \"data_own_department\":[\n" +
            "            \"1015\"\n" +
            "        ],\n" +
            "        \"owner_changed_time\":1704183355351,\n" +
            "        \"name\":\"联系人24010209edit\",\n" +
            "        \"name_order\":\"L#C1AA#CFB5#C8CB#0032#0034#0030#0031#0030#0032#0030#0039#0045#0044#0049#0054\",\n" +
            "        \"tenant_id\":\"88521\",\n" +
            "        \"remark\":\"姓名变更触发备注变更\",\n" +
            "        \"is_deleted\":false,\n" +
            "        \"_id\":\"64e3108aef47590001c9b5d3\",\n" +
            "        \"object_describe_api_name\":\"ContactObj\",\n" +
            "        \"owner\":[\n" +
            "            \"1000\"\n" +
            "        ],\n" +
            "        \"last_modified_time\":1704186850968,\n" +
            "        \"life_status\":\"normal\",\n" +
            "        \"contact_status\":\"0\",\n" +
            "        \"last_modified_by\":[\n" +
            "            \"-10000\"\n" +
            "        ],\n" +
            "        \"record_type\":\"default__c\"\n" +
            "    },\n" +
            "    \"sourceEventType\":2,\n" +
            "    \"sourceTenantType\":1,\n" +
            "    \"stop\":false,\n" +
            "    \"success\":true,\n" +
            "    \"syncLogId\":\"J-E.88521.ContactObj.20sQKFIpbDa.0\"\n" +
            "}";

    private void syncTest(String body) {
        log.info("188889810021212");
        SyncDataContextEvent eventData = JSON.parseObject(body,SyncDataContextEvent.class);
        eventData.setDataReceiveType(DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType());
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }

    private void syncMainHasNull() {
        String eventStr="{\n" +
                "    \"cleanFields\": [\n" +
                "\n" +
                "    ],\n" +
                "    \"completeDataProcess\": false,\n" +
                "    \"completeDataWrite\": false,\n" +
                "    \"dataReceiveType\": 1,\n" +
                "    \"dataVersion\": 1717658852819,\n" +
                "    \"dataVersionList\": [\n" +
                "        1717658852819\n" +
                "    ],\n" +
                "    \"debugRecordIfDetailCauseMaterSync\": false,\n" +
                "    \"detailData\": {\n" +
                "\n" +
                "    },\n" +
                "    \"detailObjectDatasMap\": {\n" +
                "\n" +
                "    },\n" +
                "    \"detailWriteResults\": [\n" +
                "\n" +
                "    ],\n" +
                "    \"doProcess\": false,\n" +
                "    \"doWrite\": false,\n" +
                "    \"errCode\": 0,\n" +
                "    \"errMsg\": \"成功\",\n" +
                "    \"finish\": false,\n" +
                "    \"queryCrmObject2DestNode\": false,\n" +
                "    \"reverseWrite2Crm\": false,\n" +
                "    \"sourceData\": {\n" +
                "        \"FBillNo\": \"XSDD015923\",\n" +
                "        \"FSaleOrderFinance.FBillAllAmount\": 0,\n" +
                "        \"tenant_id\": \"88521\",\n" +
                "        \"modifiedTime\": 1717658853128,\n" +
                "        \"erp_id\": \"118534\",\n" +
                "        \"FNote\": \"\",\n" +
                "        \"FSalerId.FNumber__r\": [\n" +
                "            \"1033\"\n" +
                "        ],\n" +
                "        \"last_modified_by\": [\n" +
                "            \"-10000\"\n" +
                "        ],\n" +
                "        \"FDate\": 1678204800000,\n" +
                "        \"FApproveDate\": 1717171200000,\n" +
                "        \"mongo_id\": \"6659f8c66d8dda28d2c43c7f\",\n" +
                "        \"FSignStatus\": \"A\",\n" +
                "        \"object_describe_api_name\": \"SAL_SaleOrder.BillHead\",\n" +
                "        \"ComId\": \"118534#XSDD015923\",\n" +
                "        \"name\": \"XSDD015923\",\n" +
                "        \"FSalerId.FNumber\": [\n" +
                "            \"1033\"\n" +
                "        ],\n" +
                "        \"FCustId.FNumber\": \"20230308-000006\",\n" +
                "        \"id\": 118534,\n" +
                "        \"_id\": \"118534#XSDD015923\",\n" +
                "        \"FBillTypeID.Id\": \"eacb50844fc84a10b03d7b841f3a6278\",\n" +
                "        \"erp_num\": \"XSDD015923\"\n" +
                "    },\n" +
                "    \"sourceEventType\": 2,\n" +
                "    \"sourceTenantType\": 2,\n" +
                "    \"stop\": false,\n" +
                "    \"success\": true,\n" +
                "    \"syncLogId\": \"J-E.88521.0.SAL_SaleOrder.28RGFhPSaGc.0\"\n" +
                "}";
        SyncDataContextEvent eventData = JSON.parseObject(eventStr,SyncDataContextEvent.class);
        eventData.setSyncPloyDetailSnapshotId("ced001e2c310400c95d93ea628e1942f");
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }
    private void syncMainErrorData() {
        String eventStr="{\"cleanFields\":[],\"completeDataProcess\":false,\"completeDataWrite\":false,\"dataReceiveType\":2,\"debugRecordIfDetailCauseMaterSync\":false,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":0,\"errMsg\":\"成功\",\"finish\":false,\"ployDetailSnapshotId\":\"b37d8b17fa1f47efb8c187dcf3b3c5a0\",\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"sourceData\":{\"modifiedTime\":1717664168118,\"is_saleable\":true,\"product_category_id\":\"646ca28ebe3218000161e2a8\",\"extend_obj_data_id\":\"666162a70fefe60001f6553f\",\"price\":\"5.00\",\"owner_department_id\":\"1015\",\"owner_department\":\"研发部\",\"lock_status\":\"0\",\"package\":\"CRM\",\"is_giveaway\":\"0\",\"create_time\":1717658279252,\"version\":\"2\",\"created_by\":[\"1000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"1000\"],\"teamMemberType\":\"0\",\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\",\"teamMemberDeptCascade\":\"0\"}],\"data_own_department\":[\"1015\"],\"name\":\"88521产品060601\",\"_id\":\"666162a70fefe60001f6551c\",\"tenant_id\":\"88521\",\"field_uU4t5__c\":[],\"product_status\":\"1\",\"on_shelves_time\":1717658279212,\"is_deleted\":false,\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"product_category_id__relation_ids\":\"646ca28ebe3218000161e2a8\",\"picture_path\":[],\"is_package\":false,\"last_modified_time\":1717658279390,\"life_status\":\"normal\",\"last_modified_by\":[\"1000\"],\"record_type\":\"default__c\",\"category\":\"1684841103143\"},\"sourceEventType\":2,\"sourceTenantType\":1,\"stop\":false,\"success\":true,\"syncLogId\":\"J-E.88521.0.ProductObj.28RMZOnBgac.0\"}";
        SyncDataContextEvent eventData = JSON.parseObject(eventStr,SyncDataContextEvent.class);
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }

    private void testMain(){
        String message="{\"cleanFields\":[],\"completeDataProcess\":false,\"completeDataWrite\":false,\"dataReceiveType\":2,\"debugRecordIfDetailCauseMaterSync\":false,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":0,\"errMsg\":\"成功\",\"finish\":false,\"ployDetailSnapshotId\":\"057907ff836f49a995e080c42d286c99\",\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"sourceData\":{\"modifiedTime\":1717677178772,\"enable_partner_view\":false,\"owner_department_id\":\"1015\",\"owner_department\":\"研发部\",\"tel\":\"5849841984\",\"add\":\"aSDgsdhfghadrtywertsdv\",\"is_relevance_wechat\":false,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1713235485043,\"version\":\"5\",\"created_by\":[\"1000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"1000\"],\"teamMemberType\":\"0\",\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\",\"teamMemberDeptCascade\":\"0\"}],\"data_own_department\":[\"1015\"],\"owner_changed_time\":1713235484907,\"name\":\"88521联系人041601\",\"name_order\":\"##0038#0038#0035#0032#0031#C1AA#CFB5#C8CB#0030#0034#0031#0036#0030#0031\",\"_id\":\"661de61c7d5ed8000183a919\",\"card\":[],\"tel1\":\"5849841984\",\"tenant_id\":\"88521\",\"is_deleted\":false,\"object_describe_api_name\":\"ContactObj\",\"email\":\"<EMAIL>\",\"owner\":[\"1000\"],\"last_modified_time\":1715064920534,\"life_status\":\"normal\",\"contact_status\":\"0\",\"last_modified_by\":[\"1000\"],\"record_type\":\"default__c\"},\"sourceEventType\":2,\"sourceTenantType\":1,\"stop\":false,\"success\":true,\"syncLogId\":\"J-E.88521.0.ContactObj.28S2uBE5aj6.0\"}";
        SyncDataContextEvent eventData = JSON.parseObject(message,SyncDataContextEvent.class);
        eventData.setPloyDetailSnapshotId("057907ff836f49a995e080c42d286c99");

        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
    }

    private void testSalesOrder(){
        String message="{\"cleanFields\":[],\"completeDataProcess\":false,\"completeDataWrite\":false,\"dataReceiveType\":2,\"dataVersion\":1717728311263,\"dataVersionList\":[1717728311263],\"debugRecordIfDetailCauseMaterSync\":false,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":0,\"errMsg\":\"成功\",\"finish\":false,\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"sourceContextUserId\":\"-10000\",\"sourceData\":{\"modifiedTime\":1717728320565,\"current_level\":\"0\",\"discount\":\"100\",\"order_time\":1717689600000,\"receivable_amount\":\"75.00\",\"logistics_status\":\"1\",\"order_status\":\"7\",\"extend_obj_data_id\":\"6662743665ef8a00019a4368\",\"order_amount\":\"75.00\",\"owner_department_id\":\"1015\",\"owner_department\":\"研发部\",\"searchAfterId\":[\"*************\",\"6662743665ef8a00019a4342\"],\"signature_attachment\":[],\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1717728310788,\"submit_time\":1717728310667,\"resource\":\"0\",\"payment_amount\":\"0.00\",\"version\":\"2\",\"created_by\":[\"1000\"],\"data_own_department\":[\"1015\"],\"name\":\"********-011891\",\"_id\":\"6662743665ef8a00019a4342\",\"invoice_status\":\"3\",\"tenant_id\":\"88521\",\"remark\":\"接口自动测试erp->crm\",\"invoice_amount\":\"0.00\",\"is_deleted\":false,\"returned_goods_amount\":\"0.00\",\"object_describe_api_name\":\"SalesOrderObj\",\"refund_amount\":\"0.00\",\"product_amount\":\"75.00\",\"owner\":[\"1000\"],\"last_modified_time\":*************,\"life_status\":\"normal\",\"last_modified_by\":[\"1000\"],\"record_type\":\"default__c\",\"account_id\":\"6493164ca8b140000128a2a4\",\"account_id__relation_ids\":\"6493164ca8b140000128a2a4\",\"no_invoice_amount\":\"75.00\",\"dynamic_amount\":\"0.00\",\"field_2k6k2__c\":[]},\"sourceEventType\":2,\"sourceTenantType\":1,\"stop\":false,\"success\":true,\"syncLogId\":\"J-E.88521.0.SalesOrderObj.28T1p5abzBm\"}";
        SyncDataContextEvent eventData = JSON.parseObject(message,SyncDataContextEvent.class);
        eventData.setPloyDetailSnapshotId("82fe675bed4c4ba0b51af75f18416a6a");

        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
    }

    @Test
    public void syncMain(){
        testSalesOrder();
//        testMain();
//        syncMainDelete();
//        syncMainHasNull();
    }

    private void syncMainDelete() {
        String sourceDataStr="{\"tenant_id\":\"88521\",\"modifiedTime\":\"*************\",\"object_describe_api_name\":\"AccountAddrObj\",\"country\":\"11\",\"_id\":\"657ac241c4b3260001c62436\"}";
        ObjectData sourceData = JSON.parseObject(sourceDataStr,ObjectData.class);
        String eventStr = "{\"ployDetailSnapshotId\":null,\"sourceEventType\":5,\"sourceTenantType\":1,\"syncDependForce\":null,\"syncLogId\":\"J-E.83952.0.AccountObj.1WBNKsX3kHK.0\",\"dataVersion\":\"*************\"}";
        SyncDataContextEvent eventData = JSON.parseObject(eventStr,SyncDataContextEvent.class);
        eventData.setDataVersionList(Lists.newArrayList(eventData.getDataVersion()));
        eventData.setSourceData(sourceData);
        eventData.setSourceContextUserId("1001");
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }


    @Test
    public void syncMainCPQ(){
        SyncDataContextEvent eventData = new SyncDataContextEvent();
        eventData.setSourceEventType(2);
        eventData.setSourceData(JacksonUtil.fromJson("{\"tenant_id\":\"84141\",\"modifiedTime\":*************,\"erp_id\":\"863939\",\"quantity\":1,\"FMATERIALID.FMaterialGroup\":862521,\"FBOMCATEGORY\":\"1\",\"last_modified_by\":[\"-10000\"],\"FMATERIALID.FNumber\":\"CH2958\",\"mongo_id\":\"64e819cc1cb35b087aa6e3bc\",\"object_describe_api_name\":\"ENG_BOM.BillHead\",\"name\":\"CH2958_V1.1\",\"id\":863939,\"_id\":\"863939\",\"FBOMUSE\":\"99\",\"erp_num\":\"CH2958_V1.1\",\"FNumber\":\"CH2958_V1.1\"}", ObjectData.class));
        eventData.setSyncLogId("J-E.84141_sandbox.0.ENG_BOM.TEST2.0");
        eventData.setDataReceiveType(1);
        eventData.setSourceTenantType(2);
        eventData.setDataVersion(1692932656212L);
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }


    @Test
    public void syncMainCPQ2ERp(){
        SyncDataContextEvent eventData = new SyncDataContextEvent();
        eventData.setSourceEventType(2);
        eventData.setSourceData(JacksonUtil.fromJson("{\"tenant_id\":\"84141\",\"modifiedTime\":1692949532157,\"last_modified_time\":1692949532061,\"object_describe_api_name\":\"BomCoreObj\",\"remark\":\"2\",\"_id\":\"64e85ad2f856d200011d087b\",\"version\":\"4\"}", ObjectData.class));
        eventData.setSyncLogId("J-E.84141_sandbox.0.BomCoreObj.TEST2.0");
        eventData.setDataReceiveType(2);
        eventData.setSourceTenantType(1);
        eventData.setDataVersion(1692932656212L);
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }

    @Test
    public void syncCrm2Erp() {
        String eventStr = "{\n" +
                "    \"ployDetailSnapshotId\": null,\n" +
                "    \"sourceEventType\": 2,\n" +
                "    \"sourceTenantType\": 1,\n" +
                "    \"sourceData\": {\n" +
                "        \"tenant_id\": 83952,\n" +
                "        \"modifiedTime\": \"*************\",\n" +
                "        \"completion_rate\": \"28.05%\",\n" +
                "        \"address\": 1,\n" +
                "        \"object_describe_api_name\": \"AccountObj\",\n" +
                "        \"last_follower\": [\n" +
                "            \"1000\"\n" +
                "        ],\n" +
                "        \"_id\": \"62345119b933130001747069\",\n" +
                "        \"completed_field_quantity\": 33,\n" +
                "        \"last_followed_time\": \"*************\"\n" +
                "    },\n" +
                "    \"syncDependForce\": null\n" +
                "}";
        SyncDataContextEvent eventData = JSON.parseObject(eventStr,SyncDataContextEvent.class);
        Result2<ObjectDataSyncMsg> voidResult2 = syncMainService.syncDataMain(eventData);
        System.out.println(voidResult2);
    }

    private static String body = "{\"cleanFields\":[],\"completeDataProcess\":false,\"completeDataWrite\":false,\"dataReceiveType\":2,\"dataVersion\":*************,\"dataVersionList\":[*************],\"debugRecordIfDetailCauseMaterSync\":false,\"detailData\":{},\"detailObjectDatasMap\":{},\"detailWriteResults\":[],\"doProcess\":false,\"doWrite\":false,\"errCode\":0,\"errMsg\":\"成功\",\"finish\":false,\"queryCrmObject2DestNode\":false,\"reverseWrite2Crm\":false,\"sourceContextUserId\":\"1000\",\"sourceData\":{\"tenant_id\":\"88521\",\"modifiedTime\":1718263470586,\"object_describe_api_name\":\"SalesOrderObj\",\"_id\":\"65bc9dd83bc73c0001791d23\",\"current_level\":\"0\",\"discount\":\"100\",\"order_time\":1681660800000,\"receivable_amount\":\"479964.00\",\"logistics_status\":\"1\",\"order_status\":\"7\",\"extend_obj_data_id\":\"663cf5101758b200012f51a6\",\"order_amount\":\"479964.00\",\"owner_department_id\":\"1015\",\"owner_department\":\"研发部\",\"searchAfterId\":[\"*************\",\"65bc9dd83bc73c0001791d23\"],\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1715270928457,\"submit_time\":1715270927727,\"resource\":\"0\",\"payment_amount\":\"0.00\",\"version\":\"3\",\"created_by\":[\"-10000\"],\"data_own_department\":[\"1015\"],\"name\":\"********-011400\",\"invoice_status\":\"3\",\"remark\":\"修改备注触发同步\",\"invoice_amount\":\"0.00\",\"is_deleted\":false,\"returned_goods_amount\":\"0.00\",\"refund_amount\":\"0.00\",\"product_amount\":\"479964.00\",\"owner\":[\"1000\"],\"last_modified_time\":*************,\"life_status\":\"normal\",\"last_modified_by\":[\"1000\"],\"record_type\":\"default__c\",\"account_id\":\"64d4af9c9825f30001e59992\",\"account_id__relation_ids\":\"64d4af9c9825f30001e59992\",\"no_invoice_amount\":\"479964.00\",\"dynamic_amount\":\"0.00\"},\"sourceEventType\":1,\"sourceTenantType\":1,\"stop\":false,\"success\":true,\"syncLogId\":\"J-E.88521.0.SalesOrderProductObj.293iLj5uW9G.0\"}";
}