package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/23
 */
@Ignore
public class WarehouseSpecialBusinessImplTest extends BaseTest {

    @Autowired
    private WarehouseSpecialBusinessImpl warehouseSpecialBusiness;

    @Autowired
    private StockBusinessImpl stockBusiness;

    @Test
    public void testGetFlexFields() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://*************/K3Cloud/",
                "5ec229fad54306", "ces1", "666666");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam,"780777150699143168");
//        List<String> fields;
//        fields = warehouseSpecialBusiness.getFlexValueFields("TCK00010", apiClient);
//        fields = warehouseSpecialBusiness.getFlexValueFields("TCK0009", apiClient);
//        for (int i = 0; i < 10; i++) {
//            fields = warehouseSpecialBusiness.getFlexValueFields("TCK00010", apiClient);
//            System.out.println(fields);
//        }
        List<String> list = stockBusiness.getAuxPropertyFieldList(apiClient);
        System.out.println(list);

    }
    @Test
    public void getStockData() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://*************/K3Cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(1585484657000L);
        timeFilterArg.setEndTime(1680092657046L);
        timeFilterArg.setTenantId("81243");
        timeFilterArg.setObjAPIName("BD_STOCK");
        timeFilterArg.setOffset(100);
        timeFilterArg.setLimit(100);


        Map<String, List<String>> stringListMap = warehouseSpecialBusiness.queryModifiedWarehouseLocs(timeFilterArg, apiClient);
        System.out.println("WarehouseSpecialBusinessImplTest.getStockData");

    }



    @Test
    public void getStockDataTemp() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://*************/K3Cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(1585484657000L);
        timeFilterArg.setEndTime(1680092657046L);
        timeFilterArg.setTenantId("81243");
        timeFilterArg.setObjAPIName("BD_STOCK");
        timeFilterArg.setOffset(100);
        timeFilterArg.setLimit(100);


        Map<String, List<String>> stringListMap = warehouseSpecialBusiness.queryModifiedWarehouseLocs(timeFilterArg, apiClient);
        System.out.println("WarehouseSpecialBusinessImplTest.getStockData");

    }
}