package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpTenantConfiguration;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.TenantConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Ignore
@Slf4j
public class ErpDataServiceImplTest extends BaseTest {

    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private TenantConfigurationService tenantConfigurationService;

    @Test
    public void getErpDataTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId("84801");
        erpIdArg.setObjAPIName("BD_MATERIAL");
        erpIdArg.setDataId("CH1476");
        IdFieldKey idFieldKey=new IdFieldKey();
        idFieldKey.setIdFieldKey("Number");
        idFieldKey.setNumFieldKey("Number");
        idFieldKey.setFakeIdFieldKey("Number");
        Result<SyncDataContextEvent> erpObjDataById = erpDataService.getErpObjDataById(erpIdArg, "780777150699143168", idFieldKey);
        log.info("erpObjDataByIdresult:{}", JacksonUtil.toJson(erpObjDataById));
    }


    @Test
    public void testConfiguration(){
        String apiName="BD_SAL_PriceList";
        Result<ErpTenantConfiguration> erpTenantConfigurationResult = tenantConfigurationService.queryConfig("811243", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.toString());
        long aggregationTime=2000l;
        if(ObjectUtils.isNotEmpty(erpTenantConfigurationResult.getData())){
            Map map = JSONObject.parseObject(erpTenantConfigurationResult.getData().getConfiguration(), Map.class);
            Object dispatchTimeObj = map.get(apiName);
            aggregationTime=ObjectUtils.isNotEmpty(dispatchTimeObj)?Long.valueOf(dispatchTimeObj.toString()):2000l;
        }
    }

    @Test
    public void testErpDataCreateService(){
        ObjectData objectData=new ObjectData();
        objectData.putApiName("accountObj");
        SaveArg saveArg=new SaveArg();
//        saveArg.setNeedReturnFields(Lists.newArrayList("*********"));
        List<String> needReturnFields = saveArg.getNeedReturnFields();
        saveArg.setIsAutoSubmitAndAudit(Boolean.FALSE);
        Boolean isEntryBatchFill = saveArg.getIsEntryBatchFill();


        String dataJson="{\"dataId\":\"6433b915c3dbba0001bd5a95\",\"destData\":{\"object_describe_api_name\":\"BD_Customer\",\"tenant_id\":\"84801\",\"FName\":\"测试数据客户*********\",\"_id\":\"6433b91ed7e15400012475f2\",\"sync_data_id\":\"6433b91ebdef13109c38dace\"},\"destDataId\":\"6433b91ed7e15400012475f2\",\"destDetailSyncDataIdAndDestDataMap\":{\"6433b91ebdef13109c38dacf\":{\"object_describe_api_name\":\"BD_Customer.BD_CUSTCONTACT\",\"tenant_id\":\"84801\",\"fake_master_detail\":\"6433b91ed7e15400012475f2\",\"FIsDefaultPayer\":true,\"_id\":\"6433b91ed7e15400012475f4\",\"sync_data_id\":\"6433b91ebdef13109c38dacf\"}},\"destEventType\":1,\"destObjectApiName\":\"BD_Customer\",\"destTenantId\":\"84801\",\"destTenantType\":2,\"mainObjApiName\":\"AccountObj\",\"objectApiName\":\"AccountObj\",\"sourceTenantId\":\"84801\",\"syncDataId\":\"6433b91ebdef13109c38dace\",\"syncDataMap\":{\"6433b91ebdef13109c38dacf\":{\"destObjectApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"sourceDataId\":\"6433b916c3dbba0001bd5b2d\",\"sourceObjectApiName\":\"AccountAddrObj\",\"syncDataId\":\"6433b91ebdef13109c38dacf\"},\"6433b91ebdef13109c38dace\":{\"destDataId\":\"6433b91ed7e15400012475f2\",\"destObjectApiName\":\"BD_Customer.BillHead\",\"sourceDataId\":\"6433b915c3dbba0001bd5a95\",\"sourceObjectApiName\":\"AccountObj\",\"syncDataId\":\"6433b91ebdef13109c38dace\"}},\"syncPloyDetailId\":\"75d7b7606177479794a9de7b2c253557\",\"syncPloyDetailSnapshotId\":\"54e6fb138ea3407381489775050e6da9\",\"tenantId\":\"84801\",\"version\":*************}";
        SyncDataContextEvent doWriteMqData = JSONObject.parseObject(dataJson, SyncDataContextEvent.class);

        Result<ErpIdResult> erpObjData = erpDataService.createErpObjData(doWriteMqData, "780777150699143168");
        log.info("erpObjData");
    }
}
