package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDepartmentOrPersonnelService;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 17:48 2020/12/19
 * @Desc:
 */
@Ignore
public class SyncDepartmentOrPersonnelServiceImplTest extends BaseTest {
    @Autowired
    private SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;

    @Test
    public void afterWriteDepartmentOrPersonnel2Crm() {
//        DoWriteResultData message=new DoWriteResultData();
//        message.setDoWriteMqData(new DoWriteMqData());
//        message.setCompleteDataWriteArg(new CompleteDataWriteArg());
//        message.getCompleteDataWriteArg().setWriteResult(new CompleteDataWriteArg.WriteResult());
//
//        message.getDoWriteMqData().setDestTenantId("79675");
//        message.getDoWriteMqData().setDestObjectApiName(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName());
//        message.getDoWriteMqData().setSyncDataId("c8bf48063a6f46d9bef10de470a180da");
//        message.getDoWriteMqData().setDestDataId("759da6fafb96487e98ce07108a5cd441");
//        message.getDoWriteMqData().setDestEventType(1);
//        syncDepartmentOrPersonnelService.afterWriteDepartmentOrPersonnel2Crm(message);
//        System.out.println("");
        SyncDataContextEvent message=new SyncDataContextEvent();
        message.setWriteResult(new SyncDataContextEvent.WriteResult());

        message.setDestTenantId("79675");
        message.setDestObjectApiName(ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName());
        message.setSyncDataId("c8bf48063a6f46d9bef10de470a180da");
        message.setDestDataId("5ea792c7a5083da35e180846");
        message.setDestEventType(1);
        syncDepartmentOrPersonnelService.afterWriteDepartmentOrPersonnel2Crm(message);
        System.out.println("");
    }
}