package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CopyService;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 16:29 2020/12/23
 * @Desc:
 */
@Ignore
public class TenantConfigurationServiceImplTest extends BaseTest {
    @Autowired
    private CopyService tenantConfigurationService;

    @Test
    public void copyTenantPloyAndPloyDetail() {
        CopyTenantConfigurationArg.CopyPloyAndDetailArg arg=new CopyTenantConfigurationArg.CopyPloyAndDetailArg();
        arg.setSourceTenantId("81138");
        arg.setSourceDataCenterId("620483680381042688");
        arg.setTargetTenantId("81961");
        arg.setDestDataCenterId("663704757762129920");
        arg.setDirection(1);
//        arg.setObjApiNames(Lists.newArrayList("AccountObj"));
        Result<List<String>> listResult = tenantConfigurationService.copyTenantPloyAndPloyDetail(arg,null);
        System.out.println("");
    }

    @Test
    public void copyTenantErpObj() {
        CopyTenantConfigurationArg.CopyErpObjArg arg=new CopyTenantConfigurationArg.CopyErpObjArg();
        arg.setSourceTenantId("81772");
        arg.setSourceDataCenterId("642530472589131776");
        arg.setTargetTenantId("79675");
        arg.setDestDataCenterId("663935893406875648");
        arg.setObjApiNames(Lists.newArrayList("PRD_MO"));
        Result<List<String>> listResult = tenantConfigurationService.copyTenantErpObj(arg,null);
        System.out.println(listResult);
    }

    @Test
    public void copyTenantConnectInfo() {
        CopyTenantConfigurationArg.CopyConnectArg arg=new CopyTenantConfigurationArg.CopyConnectArg();
        arg.setSourceTenantId("81138");
        arg.setSourceDataCenterId("620483680381042688");
        arg.setTargetTenantId("81961");
        Result<List<String>> listResult = tenantConfigurationService.copyTenantConnectInfo(arg.getSourceTenantId(),arg.getSourceDataCenterId(),arg.getTargetTenantId(),null);
        System.out.println("");
    }
}