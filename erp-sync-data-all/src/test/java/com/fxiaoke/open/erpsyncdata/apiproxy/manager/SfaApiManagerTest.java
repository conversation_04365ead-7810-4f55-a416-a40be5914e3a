package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.standard.StdRestApiHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ProductCategory;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/9
 */
@Ignore
@Slf4j
public class SfaApiManagerTest extends BaseTest {
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private StdRestApiHandler standardDataManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    @Test
    public void listProductCategory() {
        ProductCategory.ListResult result = sfaApiManager.listProductCategory("81772");
        System.out.println(JacksonUtil.toJson(result));
    }

    @Test
    public void testList(){
        TimeFilterArg timeFilterArg=new TimeFilterArg();
        timeFilterArg.setTenantId("81243");
        timeFilterArg.setObjAPIName("erpAccountObj");
        timeFilterArg.setOperationType(2);
        timeFilterArg.setStartTime(1678433406374l);
        timeFilterArg.setEndTime(1678440608435L);
        timeFilterArg.setSnapshotId("5e03bcb2ecc84e40bbfef255215ad251");
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.getByIdAndTenantId("81243", "63f82854a8bd974420925e83");

        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<StandardListData> standardListDataResult = standardDataManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);

    }
    @Test
    public void deleteProductCategory() throws InterruptedException {
        ProductCategory.ListResult result = sfaApiManager.listProductCategory("81772");
        System.out.println(result);
        for (ObjectData objectData : result.getData().getResult()) {
//            ProductCategory.Vo vo = ProductCategory.Vo.fromObjData(objectData);
//            ProductCategory.BaseResult baseResult = sfaApiManager.deleteProductCategory("81772", vo);
//            log.info("delete arg:{},result:{}",objectData,baseResult);
            Thread.sleep(1000);
        }
    }

    @Test
    public void testDesc(){
        HeaderObj headerObj=new HeaderObj(81961,1000);

        Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(headerObj, "object_7rK4W__c1");
        String message=describe.getMessage();

        boolean active = describe.getData().getDescribe().isActive();
        log.info("object_sohfz__c");
    }

    public void checkDeleteProductDescUpdated(String tenantId,String code) {
        try{
            HeaderObj headerMap = new HeaderObj(Integer.valueOf(tenantId), -10000);
            for (int i = 0; i < 10; i++) {
                Result<ControllerGetDescribeResult> productObj = objectDescribeService.getDescribe(headerMap, "ProductObj");
                List<Map<String, Object>> options = productObj.getData().getDescribe().getFields().get("category").getOptions();
                boolean hasDelete = options.stream().noneMatch(v -> code.equals(v.get("value")));
                log.debug("has deleted:{},options:{}",hasDelete,options);
                if (hasDelete){
                    return;
                }
                //休眠100毫秒等待下次查询是否已更新描述,第一次100第二次200
                Thread.sleep(100*i);
            }
        }catch (Exception e){
            log.error("check error,",e);
        }
        throw new ErpSyncDataException("更新产品描述超时，请稍后重试！",null,null);
    }

    @Test
    public void testSpu(){
        String value = sfaApiManager.querySpuOpenStatus("84801", true);
        log.info("value");

    }
}