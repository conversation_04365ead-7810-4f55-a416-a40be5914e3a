package com.fxiaoke.open.erpsyncdata;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.qixin.api.model.message.content.TextInfo;
import com.facishare.uc.api.exeception.UserCenterException;
import com.facishare.uc.api.model.enterprise.arg.*;
import com.facishare.uc.api.model.enterprise.result.*;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.common.interceptor.TenantShardingTableInterceptor;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendSuperAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AlertArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.NoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import io.vavr.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.function.Consumer;

/**
 * 由于在common里面引入了不应该引入的service，这个Test基类已无法使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/spring-db-test.xml"})
@Slf4j
public abstract class BaseDbTest {
    @BeforeClass
    public static void before() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data");
    }

    /**
     * 调用该方法可以打印专表sql
     * @param tenantId
     */
    protected void statSql(String tenantId){
        TenantShardingTableInterceptor.switchStatistics(tenantId, true);
        TenantShardingTableInterceptor.setStatConsumer(new StatConsumer());
    }


    private static class StatConsumer implements Consumer<Tuple3<String, String, Object>> {

        @Override
        public void accept(Tuple3<String, String, Object> tuple3) {
            String param = JSON.toJSONString(tuple3._3, SerializerFeature.WriteMapNullValue);
            String sql = tuple3._2;
            sql = StrUtil.removeAllLineBreaks(sql);
            String tenantId = tuple3._1;
            log.info("stat-{},{},{}", tenantId, sql, param);
        }
    }
}
