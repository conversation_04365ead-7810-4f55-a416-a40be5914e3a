package com.fxiaoke.open.erpsyncdata.apiproxy;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ZhiHuManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ZhiHuConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 方法描述：
 *
 * @author: zhangshf9284
 * @date: 2022/5/1116:36
 */
@Ignore
@Slf4j
public class ZhiHuPaintTest extends BaseTest {
    String json;
    ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
    Long syncTime;
    @Autowired
    private ZhiHuManager zhiHuManager;
    @Before
    public void setUp() throws Exception {
        ZhiHuConnectParam zhiHuConnectParam = new ZhiHuConnectParam();
        zhiHuConnectParam.setBaseUrl("https://xg.zhihu.com/api/v1/canvas/clue_data");
        zhiHuConnectParam.setUseFsHttpClient(true);
        zhiHuConnectParam.setUserId("530359");
        zhiHuConnectParam.setToken("75e50265093447ebb5121c6503f3e20c");
        json = new Gson().toJson(zhiHuConnectParam);
        LocalDateTime localDateTime = LocalDateTime.of(2021, 6, 20, 23, 10, 0);
        syncTime = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        erpConnectInfoEntity.setId("62284f4c2c96e6000157d0cb");
        erpConnectInfoEntity.setConnectParams(json);
    }


    @Test
    public void listErpObjDataByTime(){
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setObjAPIName("aafdfsdf");
        timeFilterArg.setTenantId("74164");
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.YXT_MARKETING_ZHIHU);
        erpConnectInfoEntity.setTenantId("74164");
        erpConnectInfoEntity.setConnectParams(json);
        Result<StandardListData> data = zhiHuManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);
        System.out.println(data);

    }
}
