package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpSyncTimeVO;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.PROBE_DATA_SET_REDIS_KEY;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/6/1
 */
@Ignore
public class ProbeErpDataServiceImplTest extends BaseTest {

    @Autowired
    private ProbeErpDataServiceImpl probeErpDataService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ProbeErpDataManager probeErpDataManager;

    @Test
    public void testExecutePloys() throws InterruptedException {
        Result<Void> voidResult = probeErpDataService.executeRollingErpHistoryDataJob("81138");
        System.out.println("");
        executePloys();
    }

    private void executePloys() throws InterruptedException {
        String tenantId = "81961";
        String objApiName = "BD_STOCK.BillHead";
        String queueRedisKey = String.format(PROBE_DATA_SET_REDIS_KEY, tenantId);
        Long sadd = redisDataSource.get(this.getClass().getSimpleName()).sadd(queueRedisKey, objApiName);
        System.out.println(sadd);
        Result<Void> voidResult = probeErpDataService.executePloys(tenantId, new ArrayList<>());
        System.out.println(voidResult);
        Thread.sleep(30000);
    }

    @Test
    public void testExecuteRollingErpDataFromMongoJob() throws InterruptedException {
        executeRollingErpDataFromMongoJob();
    }
    private void executeRollingErpDataFromMongoJob() throws InterruptedException {
        String tenantId = "83952";
        String objApiName = "push_main_1ftrccncg";
        ErpSyncTimeVO syncTimeVO=new ErpSyncTimeVO();
        syncTimeVO.setTenantId(tenantId);
        syncTimeVO.setObjectApiName(objApiName);
        syncTimeVO.setOperationType(2);
        syncTimeVO.setSnapshotId("cbe7b5b57700462299ecc771cec4b481");
        syncTimeVO.setLastQueryMongoTime(1665732455000L);
        syncTimeVO.setLastSyncTime(1665732455000L);
        syncTimeVO.setCurrentTime(1665993850000L);
        syncTimeVO.setId("824317154868756480");
        Result<Void> voidResult = probeErpDataService.executeRollingErpDataFromMongoJob(tenantId, Lists.newArrayList(syncTimeVO));
        System.out.println(voidResult);
    }
    @Test
    public void testExecutePloysDirect() throws InterruptedException {
        String tenantId = "82777";
        String objApiName = "BD_MATERIAL.BillHead";
        List<ErpSyncTimeVO> syncTimeVos = probeErpDataService.getSyncTimesByObjApiName(tenantId, objApiName);
        syncTimeVos.forEach(v->v.setLastSyncTime(1648449025000L));
        Result<Void> voidResult = probeErpDataService.executePloys(tenantId, syncTimeVos);
        System.out.println(voidResult);
        Thread.sleep(30000);
    }

    @Test
    public void sendMq() {
        SyncDataContextEvent erpObjDatumResult = new SyncDataContextEvent();
        String objStr = "{\"tenant_id\":\"81772\",\"owner\":[\"-10000\"],\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"_id\":\"101353\",\"last_modified_by\":[\"-10000\"]}";
        ObjectData objectData = JacksonUtil.fromJson(objStr,ObjectData.class);
        objectData.put("fake_master_detail","100654#XSDD000333");
        erpObjDatumResult.setSourceData(objectData);
        erpObjDatumResult.setSourceEventType(3);
        probeErpDataManager.asyncSendErpDataMq(Collections.singletonList(erpObjDatumResult),true);
    }
}