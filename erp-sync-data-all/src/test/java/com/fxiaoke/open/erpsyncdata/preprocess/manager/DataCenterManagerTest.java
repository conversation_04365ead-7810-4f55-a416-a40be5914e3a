package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/28
 */
@Ignore
public class DataCenterManagerTest extends BaseTest {
    @Autowired
    private DataCenterManager dataCenterManager;

    @Test
    public void getDataCenterBySyncDataId() {
        String str = dataCenterManager.getDataCenterBySyncDataId("81961", "f5caa65630a74f66acdcc46925791bcf");
        System.out.println(str);
    }
}