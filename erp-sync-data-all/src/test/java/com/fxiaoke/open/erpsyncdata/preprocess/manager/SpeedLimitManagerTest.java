package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 15:39 2021/7/30
 * @Desc:
 */
@Ignore
public class SpeedLimitManagerTest extends BaseTest {
    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private RedisDataSource redisDataSource;


    @Test
    public void count2ErpMsgQuantity() {
        String redisKey="aaaaa";
        Long expireSeconds=15000L;
        String luaScript = "local c=redis.call('pttl',KEYS[1]) if c>0 then redis.call('pexpire',KEYS[1],c+tonumber(ARGV[1])) else redis.call('set',KEYS[1],KEYS[1]) redis.call('pexpire',KEYS[1],ARGV[1]) end";
        Object obj = redisDataSource.get(this.getClass().getSimpleName()).eval(luaScript, 1, redisKey, String.valueOf(expireSeconds));
        String str=redisDataSource.get(this.getClass().getSimpleName()).get(redisKey);
        Long llll=redisDataSource.get(this.getClass().getSimpleName()).pttl(redisKey);
        String luaScript1 = "local c=redis.call('pttl',KEYS[1]) if c>0 then redis.call('pexpire',KEYS[1],c+tonumber(ARGV[1])) else redis.call('set',KEYS[1],KEYS[1]) redis.call('pexpire',KEYS[1],ARGV[1]) end";
        Object obj1 = redisDataSource.get(this.getClass().getSimpleName()).eval(luaScript, 1, redisKey, String.valueOf(expireSeconds));
        String str1=redisDataSource.get(this.getClass().getSimpleName()).get(redisKey);
        Long llll1=redisDataSource.get(this.getClass().getSimpleName()).pttl(redisKey);
        System.out.println("");
    }

    @Test
    public void getCount2CrmMsgQuantity() {
        String redisKey="aaaaa";
        Long aLong = speedLimitManager.incrAndExpireMilliSeconds(redisKey, 20L, 10000L, 30L);
        String str=redisDataSource.get(this.getClass().getSimpleName()).get(redisKey);
        Long llll=redisDataSource.get(this.getClass().getSimpleName()).pttl(redisKey);
        Long bLong = speedLimitManager.incrAndExpireMilliSeconds(redisKey, 25L, 10000L, 30L);
        String str1=redisDataSource.get(this.getClass().getSimpleName()).get(redisKey);
        Long llll1=redisDataSource.get(this.getClass().getSimpleName()).pttl(redisKey);
        System.out.println("");
    }

    @Test
    public void count2CrmMsgQuantity() {
    }
}