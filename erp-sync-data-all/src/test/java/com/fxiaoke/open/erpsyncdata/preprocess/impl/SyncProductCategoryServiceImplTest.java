//package com.fxiaoke.open.erpsyncdata.preprocess.impl;
//
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
//import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ProductCategoryUtils;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
//import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncProductCategoryService;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class SyncProductCategoryServiceImplTest extends BaseTest {
//    @Autowired
//    private SyncProductCategoryService syncProductCategoryService;
//    @Test
//    public void syncProductCategory() {
//        Result<Boolean> result = syncProductCategoryService.syncProductCategory("81138", "getDcId()");
//        System.out.println(result);
//    }
//
//    @Test
//    public void test() {
//        String connectStr = "{\n" +
//                "    \"baseUrl\":\"https://clear.test.ik3cloud.com/k3cloud\",\n" +
//                "    \"dbId\":\"20180803104914\",\n" +
//                "    \"dbName\":\"测试_深圳市巨鼎医疗股份有限公司\",\n" +
//                "    \"authType\":1,\n" +
//                "    \"userName\":\"纷享销客\",\n" +
//                "    \"password\":\"fxxk_123\",\n" +
//                "    \"lcid\":2052\n" +
//                "}";
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(connectStr,"");
//        ProductCategoryUtils.getMaterialGroupList(apiClient);
//    }
//}
