package com.fxiaoke.k3

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.CommonStockBusinessImpl
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.StockBusinessImpl
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Sets
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2022/11/21 16:02:45
 */
class CommonStockBusinessImplTest extends Specification {

    static {
        System.setProperty("process.profile", "fstest-vip");
        System.setProperty("process.profile.candidates", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data-vip");
//        System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,SSLv3")
    }

//    def "测试获取物料辅助属性"() {
//        when:
//        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//                "http://*************/k3cloud/",
//                "5ec229fad54306", "ces2", "8888888");
//        K3CloudApiClient k3CloudApiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "123134");
//
//        def newFields = CommonStockBusinessImpl.getMaterialAuxPropertyFieldListByMaterialNumber(k3CloudApiClient, "KN360-SP-pro")
//        def oldFields = getMaterialAuxPropertyFieldListByMaterialNumber(k3CloudApiClient, "KN360-SP-pro")
//
//        then:
//        Sets.newHashSet(newFields) == Sets.newHashSet(oldFields)
//    }


//    老代码
    public static List<String> getMaterialAuxPropertyFieldListByMaterialNumber(final K3CloudApiClient apiClient, final String materialNumber) {
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber(materialNumber);
        Result<ViewResult> viewResult = apiClient.view(K3CloudForm.BD_MATERIAL, viewArg);
        ErpSyncDataException.checkResult(viewResult);
        List<K3Model> modelList = viewResult.getData().getResult().getResult().getDetails("MaterialAuxPty");

        List<String> auxPropertyList = new ArrayList<>();
        for (K3Model k3Model : modelList) {
            Boolean isEnabled = (Boolean) k3Model.get("IsEnable1");
            if (isEnabled) {
                Map<String, Object> auxPropertyId = (Map<String, Object>) k3Model.get("AuxPropertyId");
                String id = auxPropertyId.get("Id").toString();
                String valueType = auxPropertyId.get("ValueType").toString();

                if ("2".equalsIgnoreCase(valueType)) {
                    auxPropertyList.add("FAuxPropId.FF" + id);
                } else {
                    auxPropertyList.add("FAuxPropId.FF" + id);
                    auxPropertyList.add("FAuxPropId.FF" + id + ".FNumber");
                    if ("1".equalsIgnoreCase(valueType)) {
                        auxPropertyList.add("FAuxPropId.FF" + id + ".FDataValue");
                    }
                }
            }
        }
        return auxPropertyList;
    }

//    public K3Model calculateCombineQTY(Integer pageRows, Integer maxWhile) {
//        String connectParam = "{\n" +
//                "                    \"baseUrl\": \"https://kactusbio.ik3cloud.com/k3cloud\",\n" +
//                "                    \"dbId\": \"1371564529644768256\",\n" +
//                "                    \"dbName\": \"恺佧生物科技(上海)有限公司\",\n" +
//                "                    \"authType\": 1,\n" +
//                "                    \"userName\": \"kingdee\",\n" +
//                "                    \"password\": \"kingdee@123\",\n" +
//                "                    \"lcid\": 2052,\n" +
//                "                    \"pushDataApiNames\": [],\n" +
//                "                    \"useFsHttpClient\": false\n" +
//                "                }";
//        K3CloudApiClient k3CloudApiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "123134");
//
//
//        def model = new K3Model()
//
//        def stockBusinessImpl = new StockBusinessImpl()
//        stockBusinessImpl.initField()
//        return stockBusinessImpl.calculateCombineQTY(k3CloudApiClient, "100", "LIL-HM4A2-100μg", "CK002", "503788", "030401", model, false, "be74b13f-4749-8141-11ed-2ab883d5704f", pageRows, maxWhile)
//    }
//
//    def "获取即时仓库物料数量"() {
//        when:
//        def model = calculateCombineQTY(1, 1000)
//
//        then:
//        Objects.nonNull(model.get("FLockQty"))
//        println model
//    }
//
//    def "获取物料数量过多导致报错"() {
//        when:
//        def model = calculateCombineQTY(1, 2)
//
//        then:
//        thrown(NullPointerException)
//    }
}
