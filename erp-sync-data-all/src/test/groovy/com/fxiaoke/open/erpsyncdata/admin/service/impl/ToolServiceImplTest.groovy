package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.ToolService
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ToolServiceImplTest extends BaseSpockTest {
    @Autowired
    private ToolService toolService

    @Test
    void initErpProcessedData() {
        def result = toolService.initErpProcessedData("81243",
                "63f82854a8bd974420925e83",
                Lists.newArrayList("2ec6a0fd71e64b92ace340e5e559258f"))
        println(result)
    }
}
