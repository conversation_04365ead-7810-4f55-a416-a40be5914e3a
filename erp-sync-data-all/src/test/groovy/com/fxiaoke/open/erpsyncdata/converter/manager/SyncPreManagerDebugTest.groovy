//package com.fxiaoke.open.erpsyncdata.converter.manager;
//
//import com.fxiaoke.crmrestapi.common.util.JsonUtil
//import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
//import com.fxiaoke.open.erpsyncdata.converter.helpers.AviatorHelper
//import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
//import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum
//import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataData
//import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataMappingData;
//import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2
//import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory
//import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
//import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService
//import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
//import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService
//import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService
//import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDataMappingService
//import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService
//import com.google.common.collect.Lists
//import lombok.AllArgsConstructor
//import lombok.Data;
//import org.apache.commons.lang.SerializationUtils
//import org.junit.Ignore
//import spock.lang.Specification;
//
//@Ignore
//public class SyncPreManagerDebugTest  extends Specification {
//    SyncPreManager syncPreManager;
//    SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData;
//    BatchSendEventDataArg.EventData eventData;
//    SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotDataCopy;
//    BatchSendEventDataArg.EventData eventDataCopy1;
//    BatchSendEventDataArg.EventData eventDataCopy2;
//    SyncDataMappingService syncDataMappingService;
//    OuterServiceFactory outerServiceFactory;
//    OuterService outerService;
//
//    def "diffTest" () {
//
//    }
//
//    def "hello,world" () {
//        given:
//        when:
//        System.out.println("hello");
//        then:0==0
//    }
//
//    def "setup" () {
//        outerService = Mock(OuterService) {
//            getObjectData(*_) >> {return new ObjectData();}
//        }
//            syncPreManager = new SyncPreManager(
//                    syncPloyDetailSnapshotService: Mock(SyncPloyDetailSnapshotService) {},
//                    syncPloyDetailSnapshotManager: Mock(SyncPloyDetailSnapshotManager) {},
//                    syncDataMappingService: Mock(SyncDataMappingService) {
//                        boolean existMapping = false;
//                        existByTwoWay(*_) >> { return Result2.newSuccess(existMapping); }
//                        getSyncDataMapping(*_) >> {
//                            if (existMapping) {
//                                SyncDataMappingData syncDataMappingData = new SyncDataMappingData();
//                                syncDataMappingData.setIsCreated(true);
//                                return Result2.newSuccess(syncDataMappingData);
//                            } else {
//                                return Result2.newSuccess(null);
//                            }
//                        }
//                    },
//                    outerServiceFactory: Mock(OuterServiceFactory) {
//                        get(*_) >> {return outerService;}
//                    },
//                    overrideOuterService: Mock(OverrideOuterService) {
//                        listDetailDatasByIdAndFilter(*_) >> {return Result2.newSuccess(Lists.newArrayList())}
//                    },
//                    eventTriggerService: Mock(EventTriggerService) {},
//                    syncDataManager: Mock(SyncDataManager) {
//                        saveErpTempLog(_) >> {}
//                    },
//                    syncPloyDetailManager: Mock(SyncPloyDetailManager) {},
//                    aviatorHelper: Mock(AviatorHelper) {
//                        execute(*_) >> {return true;}
//                    },
//                    syncLogManager: Mock(SyncLogManager){
//                        ObjectData oldSourceData;//用这个模拟上次的快照数据
//                        batchGet(_) >> {
//                                SyncDataData syncDataData = new SyncDataData();
//                                syncDataData.setSourceData(oldSourceData);
//                                List<SyncDataData> result = new ArrayList<>();
//                                result.add(syncDataData);
//                                return result;
//                        }
//                    },
//                    erpTempDataDao: Mock(ErpTempDataDao) {},
//                    notificationService: Mock(NotificationService) {},
//                    configCenterConfig: Mock(ConfigCenterConfig) {});
//
//            String syncploysnapshotJson = "";
//            String eventDataJson = "";
//            syncPloyDetailSnapshotData = JsonUtil.fromJson(syncploysnapshotJson, SyncPloyDetailSnapshotData2.class);
//            eventData = JsonUtil.fromJson(eventDataJson, BatchSendEventDataArg.EventData.class);
//
//            syncPloyDetailSnapshotDataCopy = null;
//            eventDataCopy1 = null;
//            eventDataCopy2 = null;
//
//            syncPloyDetailSnapshotDataCopy = BeanUtil.deepCopy(syncPloyDetailSnapshotData, SyncPloyDetailSnapshotData2.class);
//            eventDataCopy1 = BeanUtil.deepCopy(eventData, BatchSendEventDataArg.EventData.class);
//            if (!eventData.equals(eventDataCopy1)) {
//                System.out.println("eventDataCopy1 not copy right.");
//            }
//            eventDataCopy2 = (BatchSendEventDataArg.EventData) SerializationUtils.clone(eventData);
//            if (!eventData.equals(eventDataCopy2)) {
//                System.out.println(" eventDataCopy2 not copy right.");
//            }
//        }
//
//
//
//    @Data
//    @AllArgsConstructor
//    class MockSyncDataMappingService implements com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDataMappingService {
//        private boolean existMapping = false;
//        @Override
//        public Result2<Boolean> existByTwoWay(String tenantId, String sourceTenantId, String sourceApiName, String sourceDataId, String destTenantId, String destApiName) {
//            return Result2.newSuccess(existMapping);
//        }
//        @Override
//        public Result2<SyncDataMappingData> getSyncDataMapping(String tenantId, String sourceTenantId, String sourceObjectApiName, String sourceObjectId, String destTenantId, String destObjectApiName) {
//            if(existMapping) {
//                SyncDataMappingData syncDataMappingData = new SyncDataMappingData();
//                syncDataMappingData.setIsCreated(true);
//                return Result2.newSuccess(syncDataMappingData);
//            }else {
//                return Result2.newSuccess(null);
//            }
//        }
//        @Override
//        public Result2<Void> updateDataIdByMergeInfo(String tenantId, String objectApiName, List<String> oldMergeObjectIds, String newMergeObjectId) {return null;}
//        @Override
//        public Result2<List<SyncDataMappingData>> getDataByMasterId(String tenantId, String sourceTenantId, String sourceApiName, String destApiName, String masterId) {return null;}
//        @Override
//        public Result2<SyncDataMappingData> getSyncDataMappingByDest(String tenantId, String sourceTenantId, String sourceObjectApiName, String destObjectId, String destTenantId, String destObjectApiName) {return null;}
//
//        @Override
//        Result2<List<SyncDataMappingData>> getMappingByDestObjApiNameAndId(String tenantId, String destObjectApiName, String destObjectId) {
//            return null
//        }
//    }
//
//    @Data
//    @AllArgsConstructor
//    class MockSyncLogManager extends SyncLogManager {
//        @Override
//        public void saveErpTempLog(String tenantId, SyncLogTypeEnum type, Integer status, String logId, ErpTempData logObj) {}
//    }
//
//    @Data
//    @AllArgsConstructor
//    class MockSyncDataManager extends SyncDataManager {
//        private ObjectData oldSourceData;//用这个模拟上次的快照数据
//        public List<SyncDataData> batchGet(String tenantId, List<String> ids, boolean cachePri) {
//            SyncDataData syncDataData = new SyncDataData();
//            syncDataData.setSourceData(oldSourceData);
//            List<SyncDataData> result = new ArrayList<>();
//            result.add(syncDataData);
//            return result;
//        }
//    }
//}
