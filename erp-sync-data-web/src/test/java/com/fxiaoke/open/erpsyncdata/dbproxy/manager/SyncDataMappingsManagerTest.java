package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/7/13
 */
@Ignore
public class SyncDataMappingsManagerTest extends BaseTest {
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;


    @Test
    public void testGet2Way() {
        for (int i = 0; i < 10; i++) {
            boolean b = syncDataMappingsManager.exitMappingAndCreated("82370", "AccountObj_1f5hqpedr", "4dfsasfda", "AccountObj");
            boolean b1 = syncDataMappingsManager.exitMappingAndCreated("82370", "AccountObj_1f5hqpedr", "no999", "AccountObj");
            boolean b2 = syncDataMappingsManager.exitMappingAndCreated("82370", "AccountObj", "60e2cd59bd3f630001da71e5", "AccountObj_1f5hqpedr");
            System.out.printf("%s,%s,%s",b,b1,b2);
        }
    }
}