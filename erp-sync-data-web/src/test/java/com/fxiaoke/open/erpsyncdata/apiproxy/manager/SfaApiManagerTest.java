package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.BaseResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.EnterpriseRelationSupportResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.FindCustomFieldDescribeResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ProductCategory;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/9
 */
@Ignore
@Slf4j
public class SfaApiManagerTest extends BaseTest {
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Test
    public void isEnterpriseRelationSupport() {
        boolean support = sfaApiManager.isEnterpriseRelationSupport("82777", 1000);
        System.out.println(support);
    }

    @Test
    public void haveOutDataPrivilege() {
        Boolean result = sfaApiManager.haveOutDataPrivilege("82777", 1000,
                "SalesOrderObj","account_id");
        System.out.println(result);
    }

    @Test
    public void listProductCategory() {
        ProductCategory.ListResult result = sfaApiManager.listProductCategory("81772");
        System.out.println(JacksonUtil.toJson(result));
    }


    public void checkDeleteProductDescUpdated(String tenantId,String code) {
        try{
            HeaderObj headerMap = new HeaderObj(Integer.valueOf(tenantId), -10000);
            for (int i = 0; i < 10; i++) {
                Result<ControllerGetDescribeResult> productObj = objectDescribeService.getDescribe(headerMap, "ProductObj");
                List<Map<String, Object>> options = productObj.getData().getDescribe().getFields().get("category").getOptions();
                boolean hasDelete = options.stream().noneMatch(v -> code.equals(v.get("value")));
                log.debug("has deleted:{},options:{}",hasDelete,options);
                if (hasDelete){
                    return;
                }
                //休眠100毫秒等待下次查询是否已更新描述,第一次100第二次200
                Thread.sleep(100*i);
            }
        }catch (Exception e){
            log.error("check error,",e);
        }
        throw new ErpSyncDataException("更新产品描述超时，请稍后重试！",null,null);
    }
}