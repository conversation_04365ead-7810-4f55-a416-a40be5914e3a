package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.DataVerificationIdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryDataVerificationArg;
import com.fxiaoke.open.erpsyncdata.admin.result.DataVerificationResult;
import com.fxiaoke.open.erpsyncdata.admin.service.DataVerificationService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationIdStatus;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 20:11 2022/11/9
 * @Desc:
 */
@Ignore
public class DataVerificationIdStatusDaoTest extends BaseTest {
    @Autowired
    private DataVerificationIdStatusDao dataVerificationIdStatusDao;
    @Autowired
    private DataVerificationService dataVerificationService;

    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;

    private String tenantId = "89029";
    private String dcId = "780777150699143168";
    private String dataVerificationTaskId = "636dfecf762dcd5939f12c14";


    @Test
    public void queryDataVerificationIdStatus() {
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(tenantId).getById(tenantId, "a78b668ae39849b284f9e8e696d247cb");
        String str = JSONObject.toJSONString(entity);
        Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> map = Maps.newHashMap();
        map.put(IdSyncStatus.not_temp, Maps.newHashMap());
        map.put(IdSyncStatus.temp, Maps.newHashMap());
        DataVerificationIdStatus idStatus = new DataVerificationIdStatus();
        idStatus.setDataId("A");
        idStatus.setSyncLogId("sync_log_id");
        idStatus.setStatusReason("remark");
        idStatus.setStatus(IdSyncStatus.temp);
        map.get(IdSyncStatus.temp).put("A", idStatus);
        String lsssl = JSONObject.toJSONString(map);
        List<DataVerificationIdStatus> dataVerificationIdStatuses = dataVerificationIdStatusDao.queryDataVerificationIdStatus(tenantId, dataVerificationTaskId, null, null, null, null, null, 100);
        System.out.println("");
    }

    @Test
    public void createDataVerificationIdStatusList() {
        DataVerificationIdStatus idStatus = new DataVerificationIdStatus();
        idStatus.setId(new ObjectId());
        idStatus.setDataVerificationTaskId(dataVerificationTaskId);
        idStatus.setTenantId(tenantId);
        idStatus.setDataId("1332");
        dataVerificationIdStatusDao.batchInsert(tenantId, Lists.newArrayList(idStatus));
        System.out.println("");
    }

    @Test
    public void buildDataVerificationTask() {
    }

    @Test
    public void verifyDataId() {
        DataVerificationIdArg arg = new DataVerificationIdArg();
        arg.setDataVerificationTaskId("65faa925b062440001e6e975");
        arg.setStreamId("64a3c3ac199f9400013473a6");
        arg.setFileUrl("TN_a9953e0ccff84986ad8dcd8ede7add6b");
        Result<DataVerificationResult> result = dataVerificationService.verifyDataId(tenantId, 1001, dcId, arg,null);
        System.out.println("");
    }

    @Test
    public void queryDataVerificationTask() {
        QueryDataVerificationArg arg = new QueryDataVerificationArg();
        arg.setDataVerificationTaskId("6371e44e95f9d30c98fb66eb");
        Result<DataVerificationResult> result = dataVerificationService.queryDataVerificationTask(tenantId, 1001, dcId, arg);
        System.out.println("");
    }

    @Test
    public void batchReSync() {
        QueryDataVerificationArg arg = new QueryDataVerificationArg();
        arg.setDataVerificationTaskId("6371e44e95f9d30c98fb66eb");
        arg.setStreamId("a78b668ae39849b284f9e8e696d247cb");
        Result<String> result = dataVerificationService.batchReSync(tenantId, dcId, 1000, arg,null);
        System.out.println("");
    }
}