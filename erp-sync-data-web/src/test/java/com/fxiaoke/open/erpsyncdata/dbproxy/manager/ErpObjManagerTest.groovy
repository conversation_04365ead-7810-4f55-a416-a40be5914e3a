package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.BaseDbTest
import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/5/19
 */
@Ignore
class ErpObjManagerTest extends BaseDbTest {
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private SyncDataFixDao syncDataFixDao;

    @Test
    public void testGetRelation() {
        syncDataFixDao.setTenantId("81243").insertCache(new SyncDataEntity())
        println(erpObjManager.getRelation("81243","BD_STOCK.BillHead"))
    }
}
