package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.Impl.NotificationServiceImpl;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.NoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/1
 */
@Ignore
public class NotificationServiceImplTest extends BaseTest {
    @Autowired
    private NotificationServiceImpl notificationService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    public void sendErpSyncDataAppNotice() {
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setTenantId("81138");
        arg.setReceivers(ImmutableList.of(1000, 1001));
        arg.setMsg("[AccountObj]解析成功【2】条，新增数据【0】条，删除数据【0】条，更新数据【1】条\n处理失败【1】条：\n  行号【2】：[AccountObj]插入数据失败:createOrGet sourcedMappingsData failed\n解析异常【0】条：\n");
        Result<Void> result = notificationService.sendErpSyncDataAppNotice(arg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
        System.out.println(result);
    }
    public String generateId() {
        return IdUtil.generateId();
    }
    @Test
    public void testt() {
        ParallelUtils.ParallelTask backgroundTask = ParallelUtils.createErpRollingDataTask();
        int limit=300;
        for(int i=1;i<=200;i++){
            int finalI = i;
            backgroundTask.submit(()->this.dojob(limit*(finalI -1),limit* finalI));
        }
        backgroundTask.run();
        boolean b=true;
        while (b){

        }
        System.out.println("");
    }

    public void dojob(int start,int end) {
        String tenantId="82370",sourceObjectApiName="ProductObj_1fms0402o",sourceDataId="erpDataId20220317-2",sourceDataName="erpDataName0317-1",
                destObjectApiName="ProductObj";
        for(int i=start;i<end;i++){
            try{
                List<SyncDataEntity> syncDataEntityList= Lists.newArrayList();
                List<SyncDataMappingsEntity> syncDataMappingsEntityList= Lists.newArrayList();
                for(int j=0;j<1000;j++){
                    ObjectData sourceData = new ObjectData();
                    ObjectData destData = new ObjectData();

                    sourceData.putApiName(sourceObjectApiName);
                    sourceData.putId(sourceDataId+1000*i+j);
                    sourceData.put("name", sourceDataName+1000*i+j);
                    sourceData.putTenantId(tenantId);
                    sourceData.put("version", 1);

                    destData.putId(IdUtil.generateId());
                    destData.put("name", sourceDataName+1000*i+j);
                    destData.putTenantId(tenantId);
                    destData.putApiName(destObjectApiName);
                    String syncDataId=new ObjectId().toString();
                    String syncDataMappingId=IdUtil.generateId();
                    SyncDataEntity syncData = getSyncData(tenantId, syncDataId, syncDataMappingId, 1, TenantType.ERP, sourceData, destData, 6,
                            "2bae9acebede4a639319754ce8e01692", destData.getId(), TenantType.CRM, destObjectApiName,
                            tenantId, 1, "自动导入", null);
                    SyncDataMappingsEntity syncDataMapping = getSyncDataMapping(syncDataId, 6, destObjectApiName, tenantId, destData.getId(), destData.getName(),
                            sourceData, true, null);
                    syncDataEntityList.add(syncData);
                    syncDataMappingsEntityList.add(syncDataMapping);
                }
//                adminSyncDataDao.setTenantId(tenantId).batchInsert(tenantId,syncDataEntityList);
                syncDataMappingsDao.setTenantId(tenantId).batchInsert(tenantId,syncDataMappingsEntityList);
                syncDataEntityList.clear();
                syncDataMappingsEntityList.clear();
            }catch (Exception e){

            }

        }
    }


    public SyncDataMappingsEntity getSyncDataMapping(String syncDataId,
                                                         int status,
                                                         String destObjectApiName,
                                                         String destTenantId,
                                                         String destDataId,
                                                         String destDataName,
                                                         ObjectData sourceData,
                                                         Boolean mappingIsCreated,
                                                         String masterDataId) {
        String dataMappingEntityId = IdUtil.generateId();
        SyncDataMappingsEntity dataMappingData = new SyncDataMappingsEntity();
        dataMappingData.setId(dataMappingEntityId);
        dataMappingData.setTenantId(sourceData.getTenantId());
        dataMappingData.setSourceDataId(sourceData.getId());
        dataMappingData.setSourceDataName(sourceData.getName());
        dataMappingData.setSourceTenantId(sourceData.getTenantId());
        dataMappingData.setSourceObjectApiName(sourceData.getApiName());
        dataMappingData.setLastSourceDataVserion(sourceData.getVersion());
        dataMappingData.setLastSyncStatus(status);
        dataMappingData.setLastSyncDataId(syncDataId);
        dataMappingData.setDestObjectApiName(destObjectApiName);
        dataMappingData.setDestTenantId(destTenantId);
        dataMappingData.setDestDataId(destDataId);
        dataMappingData.setDestDataName(destDataName);
        dataMappingData.setIsCreated(mappingIsCreated);
        //增加保存主对象源数据ID的字段
        dataMappingData.setMasterDataId(masterDataId);
        dataMappingData.setIsDeleted(false);
        dataMappingData.setCreateTime(System.currentTimeMillis());
        return dataMappingData;
    }
    public SyncDataEntity getSyncData(String tenantId, String syncDataId, String syncDataMappingId, Integer sourceEventType, Integer sourceTenantType, ObjectData sourceData, ObjectData destData, Integer status,
                                      String syncPloyDetailSnapshotId, String destDataId, Integer destEventType, String destObjectApiName, String destTenantId, Integer destTenantType, String remark, String errorCode) {
        SyncDataEntity dataEntity = new SyncDataEntity();
        dataEntity.setId(syncDataId);
        dataEntity.setTenantId(tenantId);
        dataEntity.setSourceTenantType(sourceTenantType);
        dataEntity.setDestTenantType(destTenantType);
        dataEntity.setSourceDataId(sourceData.getId());
        dataEntity.setSourceEventType(sourceEventType);
        dataEntity.setSourceTenantId(sourceData.getTenantId());
        dataEntity.setSourceObjectApiName(sourceData.getApiName());
        dataEntity.setSourceData(sourceData);
        dataEntity.setDestData(destData);
        dataEntity.setDestTenantId(destTenantId);
        dataEntity.setDestObjectApiName(destObjectApiName);
        dataEntity.setDestDataId(destDataId);
        dataEntity.setDestEventType(destEventType);
        dataEntity.setStatus(status);
        dataEntity.setRemark(remark);
        dataEntity.setErrorCode(errorCode);
        dataEntity.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotId);
        dataEntity.setCreateTime(System.currentTimeMillis());
        dataEntity.setIsDeleted(false);
        dataEntity.setOperatorId(sourceData.getLastModifiedBy());
        return dataEntity;
    }

    @Test
    public void sendNoticeByConfigTest2() throws InterruptedException {
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setTenantId("84801");
        arg.setMsgTitle("集成平台告警通知");
        arg.setMsg("消息测试....."+System.currentTimeMillis());

        notificationService.sendNoticeByConfig(arg,
                Lists.newArrayList(1008,1000,1001,1004,1007),
                Lists.newArrayList("00000000000000000000000000000006"),
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);

    }


    @Test
    public void sendNoticeByConfigTest() throws InterruptedException {
        ExecutorService executorService= Executors.newFixedThreadPool(100);
        int count=10;
        while (true){
            if (count>0){
                List<SendTextNoticeArg>lists=Lists.newArrayList();
                for (int i = 0; i < 100; i++) {
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setTenantId("84801");
                    arg.setMsgTitle("集成平台告警通知");
                    arg.setMsg("消息测试....."+System.currentTimeMillis());
                    lists.add(arg);
                }
                for (SendTextNoticeArg sendTextNoticeArg : lists) {
                    executorService.execute(()->notificationService.sendNoticeByConfig(sendTextNoticeArg,
                            Lists.newArrayList(1008,1000,1001,1004,1007),
                            Lists.newArrayList("00000000000000000000000000000006"),
                            AlarmRuleType.OTHER,
                            AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                            AlarmType.OTHER,
                            AlarmLevel.GENERAL));
                }
                lists=Lists.newArrayList();
                Thread.sleep(2000);
                count--;
            }


        }
    }

    @Test
    public void testDataPgError(){
        Integer count = syncDataMappingsDao.setTenantId("89029").count("89029");

    }


    @Test
    public void testService(){
        //Result<List<IntegrationViewResult.SyncFailResult>> listResult = integrationStreamService.querySyncFailCount("89029", Lists.newArrayList("64a3c3ac199f9400013473a9"));

    }

    @Test
    public void testPg(){
        SyncDataMappingsEntity syncDataMappingsEntity=new SyncDataMappingsEntity();
        syncDataMappingsEntity.setId(IdUtil.generateId());
        syncDataMappingsEntity.setSourceObjectApiName("OBJECT");
        syncDataMappingsEntity.setDestObjectApiName("OBJECT");
        syncDataMappingsEntity.setSourceDataId("64a3c3ac199f9400013473a9");
        syncDataMappingsEntity.setDestDataId("64a3c3ac199f9400013473a9");
        syncDataMappingsDao.setTenantId("89029").insert(syncDataMappingsEntity);
        syncDataMappingsDao.setTenantId("89029").insert(syncDataMappingsEntity);
    }
}