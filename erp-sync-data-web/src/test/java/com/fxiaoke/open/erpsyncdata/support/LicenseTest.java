package com.fxiaoke.open.erpsyncdata.support;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.*;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.fxiaoke.open.erpsyncdata.admin.utils.SyncQuotaHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/license-client.xml"})
@Slf4j
public class LicenseTest{
    @Autowired
    private LicenseClient licenseClient;
    LicenseContext context;

    @BeforeClass
    public static void beforeClass() throws Exception {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data");
    }

    @Before
    public void setUp() throws Exception {
        context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId("74860");
        context.setUserId("-10000");
    }

    @Test
    public void judgeModule() {
        QueryProductArg arg = new QueryProductArg();
        JudgeModuleArg judgeModuleArg = new JudgeModuleArg();
        judgeModuleArg.setContext(context);
        judgeModuleArg.setModuleCodes(Lists.newArrayList("wechat_standard_edition","feishu_data_sync_app","jdy_app","jdy_edition"));
        Result result = licenseClient.judgeModule(judgeModuleArg);
        log.info("judge module license,arg:{}.result:{}", judgeModuleArg, result);
        if (result.getErrCode() == PaasMessage.SUCCESS.getCode()) {
            Set<ErpChannelEnum> collect = ((JudgeModulePojo) result.getResult()).getModuleFlags().stream()
                    .filter(ModuleFlag::isFlag)
                    .map(v -> ErpChannelEnum.CHANNEL_CODE_MAP.get(v.getModuleCode())).collect(Collectors.toSet());
            log.info("channel:{}",collect);
        }
    }


    @Test
    public void getLimitTest() {
        getLimit("data_sync_count_pkg_app","data_sync_count_pkg_limit");
        getLimit("data_sync_count_app","data_sync_count_limit");
    }

    private void getLimit(String moduleCode,String para) {
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(context);
        arg.setModuleCode(moduleCode);
        arg.setParaKeys(Sets.newHashSet(para));
        ParaInfoResult result = licenseClient.queryModulePara(arg);
        log.info("queryModulePara,arg:{}.result:{}", arg, result);
        if (result.getErrCode() == PaasMessage.SUCCESS.getCode()) {
            List<ModuleParaPojo> pojos = result.getResult();
            if (CollUtil.isEmpty(pojos)){
                log.info("pojos empty");
                return;
            }
            ModuleParaPojo moduleParaPojo = pojos.get(0);
            log.info("pojo:{}",moduleParaPojo);
        }
    }


    @Test
    public void queryModule() {
        final QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(context);
        final ModuleInfoResult result =  licenseClient.queryModule(arg);
        System.out.println("result:" + result);
    }

    @Test
    public void judgeModule2() {
        JudgeModuleArg moduleArg = new JudgeModuleArg();
        moduleArg.setContext(context);
        moduleArg.setModuleCodes(Lists.newArrayList("data_sync_count_pkg_app"));
        Result<JudgeModulePojo> result = licenseClient.judgeModule(moduleArg);
        System.out.println("result:" + result);
    }

    @Test
    public void batchGetLimitTest() {
        batchGetLimit(SyncQuotaHelper.LAZY_HOLDER.pkgModuleParaMap);
    }

    private void batchGetLimit(Map<String, Set<String>> moduleParaMap) {
        BatchQueryModuleParaArg arg = new BatchQueryModuleParaArg();
        arg.setContext(context);
        arg.setModuleCodeParaKeyMap(moduleParaMap);
        Result result = licenseClient.batchQueryModulePara(arg);
        log.info("queryModulePara,arg:{}.result:{}", arg, JSON.toJSONString(result));
    }

    @Test
    public void queryProductVersion() {
        context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId("71658");
        context.setUserId("-10000");

        QueryProductArg queryProductArg = new QueryProductArg();
        queryProductArg.setLicenseContext(context);
        LicenseVersionResult result = licenseClient.queryProductVersion(queryProductArg);
        System.out.println(result);
    }
}
