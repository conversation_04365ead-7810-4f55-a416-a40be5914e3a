package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FileUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryInventoryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.util.SpecUtils;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/26
 */
@Ignore
@Slf4j
public class SyncSkuSpuServiceImplTest extends BaseTest {
    @Autowired
    private SyncSkuSpuServiceImpl syncSkuSpuService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    public void testSYnc() throws IOException {
        String str = FileUtils.readString("D:\\project\\fs-erp-sync-data\\erp-sync-data-all\\src\\test\\resources\\testfile\\DoWriteMsg.json");
        SyncDataContextEvent doWriteResultData = JacksonUtil.fromJson(str, new TypeReference<SyncDataContextEvent>() {
        });
        syncSkuSpuService.handleSkuSpu2Crm(doWriteResultData);
    }

    @Test
    public  void testJson(){
        String message="{\"rowcount\":1,\"success\":true,\"message\":\"success\",\"data\":[{\"FID\":\"001dd8c7-1cd7-80da-11ea-0136af836c91\",\"FSTOCKORGID\":1,\"FSTOCKORGNUMBER\":\"000\",\"FSTOCKORGNAME\":\"纷享销客\",\"FKEEPERTYPEID\":\"BD_KeeperOrg\",\"FKEEPERTYPENAME\":null,\"FKEEPERID\":1,\"FKEEPERNUMBER\":\"000\",\"FKEEPERNAME\":\"纷享销客\",\"FOWNERTYPEID\":\"BD_OwnerOrg\",\"FOWNERTYPENAME\":null,\"FOWNERID\":1,\"FOWNERNUMBER\":\"000\",\"FOWNERNAME\":\"纷享销客\",\"FSTOCKID\":658621,\"FSTOCKNUMBER\":\"CK0001\",\"FSTOCKNAME\":\"测试仓库1\",\"FSTOCKLOCID\":0,\"FSTOCKLOC\":null,\"FAUXPROPID\":0,\"FAUXPROP\":null,\"FSTOCKSTATUSID\":10000,\"FSTOCKSTATUSNUMBER\":\"KCZT01_SYS\",\"FSTOCKSTATUSNAME\":\"可用\",\"FLOT\":681727,\"FLOTNUMBER\":\"PC001\",\"FBOMID\":0,\"FBOMNUMBER\":\"\",\"FMTONO\":\"\",\"FPROJECTNO\":\"\",\"FPRODUCEDATE\":\"0001-01-01 00:00:00\",\"FEXPIRYDATE\":\"0001-01-01 00:00:00\",\"FBASEUNITID\":10101,\"FBASEUNITNUMBER\":\"Pcs\",\"FBASEUNITNAME\":\"Pcs\",\"FBASEQTY\":1050.0000000000,\"FBASELOCKQTY\":50.0000000000,\"FSECQTY\":0.0,\"FSECLOCKQTY\":0.0,\"FSTOCKUNITID\":10101,\"FSTOCKUNITNUMBER\":\"Pcs\",\"FSTOCKUNITNAME\":\"Pcs\",\"FMATERIALID\":681726,\"FMASTERID\":681726,\"FMATERIALNUMBER\":\"CH4416\",\"FMATERIALNAME\":\"批次测试001\",\"FQTY\":1050.0000000000,\"FLOCKQTY\":50.0000000000,\"FSECUNITID\":0,\"FSECUNITNUMBER\":null,\"FSECUNITNAME\":null,\"FOBJECTTYPEID\":\"STK_Inventory\",\"FBASEAVBQTY\":1000.0000000000,\"FAVBQTY\":1000.0000000000,\"FSECAVBQTY\":0.0,\"FUPDATETIME\":\"2021-06-09 18:06:18\"}]}";
        QueryInventoryResult.CombineInventoryResult combineInventoryResult = JacksonUtil.fromJson(message, QueryInventoryResult.CombineInventoryResult.class);
        QueryInventoryResult.CombineInventoryResult result = JSONObject.parseObject(message, new com.alibaba.fastjson.TypeReference<QueryInventoryResult.CombineInventoryResult>() {
        });
        String resss = JacksonUtil.toJson(result);
        QueryInventoryResult.CombineInventoryResult combineInventoryResult1 = JacksonUtil.fromJson(JacksonUtil.toJson(result), QueryInventoryResult.CombineInventoryResult.class);
        System.out.println(combineInventoryResult);
    }



    @Test
    public void test(){
        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);
//        Result<ObjectDataQueryListByIdsResult> queryResult = objectDataService
//                .queryListByIds(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), Collections.singletonList("6041d63a0584b5000160dd46"));
//        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
//        searchTemplateQuery.addFilter("product_id", Lists.newArrayList("6041d63a0584b5000160dd14"), "EQ");
//        Result<QueryBySearchTemplateResult> result = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), searchTemplateQuery);
//        ControllerListArg controllerListArg=new ControllerListArg();
//        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
//        SearchQuery searchQuery=new SearchQuery();
//        searchQuery.setOffset(0);
//        searchQuery.setLimit(100);
//        Filter filter=new Filter();
//        filter.setFieldName("product_id");
//        filter.setOperator("EQ");
//        filter.setFieldValues(Lists.newArrayList("6041d63a0584b5000160dd14"));
//        searchQuery.setFilters(Lists.newArrayList(filter));
//        controllerListArg.setSearchQuery(searchQuery);
//        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(),controllerListArg );
//        ObjectData fsObjectData=new ObjectData();
//        fsObjectData.setTenantId(81243);
//        fsObjectData.put("name","2222");
//        fsObjectData.put("object_describe_api_name",ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
//        fsObjectData.put("_id","6041d63a0584b5000160dd14");
//        fsObjectData.put("multi_unit_data",list.getData().getDataList());
//        ActionEditArg actionEditArg = new ActionEditArg();
//        actionEditArg.setObjectData(fsObjectData);
//        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), actionEditArg);
//        ObjectData addObjectData=new ObjectData();
//        addObjectData.put("_id", IdUtil.generateId());
//        addObjectData.setTenantId(81243);
//        addObjectData.put("object_describe_api_name", ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
//        addObjectData.put("product_id","6041d63a0584b5000160dd14");
//        addObjectData.put("is_base",false);
//        addObjectData.put("conversion_ratio","11");
//        addObjectData.put("unit_id","5");
//        ActionAddArg addArg = new ActionAddArg();
//        addArg.setObjectData(addObjectData);
//        Result<ActionAddResult> add = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), addArg);
//        ObjectData addProductData=new ObjectData();
//        addProductData.put("_id", IdUtil.generateId());
//        addProductData.setTenantId(81243);
//        addProductData.put("object_describe_api_name", ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
//        addProductData.put("owner", Lists.newArrayList("1000"));
//        addProductData.put("created_by",Lists.newArrayList("1000"));
//        addProductData.put("batch_sn","1");
//        addProductData.put("product_status","1");
//        addProductData.put("category","742629");
//        addProductData.put("product_code","34123412");
//        addProductData.put("is_multiple_unit",true);
//        addProductData.put("record_type","default__c");
//        addProductData.put("unit","60472a4ed215ff000163abc6");
//        addProductData.put("name","7890678");
//
//        Map<String,Object> baseUnit= Maps.newHashMap();
//        baseUnit.put("is_base",true);
//        baseUnit.put("is_enable",true);
//        baseUnit.put("is_pricing",true);
//        baseUnit.put("price","1");
//        baseUnit.put("conversion_ratio","1");
//        baseUnit.put("unit_id","60472a4ed215ff000163abc6");
//        addProductData.put("multi_unit_data", Lists.newArrayList(baseUnit));
//        ActionAddArg addArg = new ActionAddArg();
//        addArg.setObjectData(addProductData);
//        Result<ActionAddResult> add = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), addArg);
        ObjectData fsObjectData=new ObjectData();
        fsObjectData.setTenantId(81961);
        fsObjectData.put("object_describe_api_name", ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        fsObjectData.put("_id","604b39761c08f0000176184f");
        fsObjectData.put("product_id","");
        fsObjectData.put("spu_id","604b19ca024ac9000151cf63");
        ActionEditArg actionEditArg = new ActionEditArg();
        actionEditArg.setObjectData(fsObjectData);
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), false,false, null, null, actionEditArg);
        System.out.println("");
    }

    @Test
    public void testSPU(){
        com.fxiaoke.open.erpsyncdata.common.data.ObjectData spuObjectDataData=new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
        spuObjectDataData.putOwner(Lists.newArrayList("1001"));
        spuObjectDataData.putTenantId("81772");
        spuObjectDataData.putApiName(ObjectApiNameEnum.FS_SPU.getObjApiName());
//        spuObjectDataData.put("batch_sn","1");
//        spuObjectDataData.put("category","31");
//        spuObjectDataData.put("name","测试名称产品1");
//        spuObjectDataData.put("is_spec",false);
        spuObjectDataData.putId("60913665446242d381b522a2d6fbb5fb");
//        spuObjectDataData.put("record_type","default__c");
//        spuObjectDataData.put("standard_price","1");
//        spuObjectDataData.put("unit","3");

        Result<String> result = syncSkuSpuService.updateSpuObj(81772, spuObjectDataData);
        System.out.println("");
    }
    @Test
    public void testSpuMultiUnit(){
        List<ObjectData> multiUnitList = getMultiUnitList("81961", "604b28ec1c08f0000175fa8f", "605055cf1c08f000017928fb", "");
        List<ObjectData> multiUnitList1 = getMultiUnitList("81961", "604b28ec1c08f0000175fa8f", "", "605055cf1366bc0001402d19");

        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);

        ObjectData addObjectData=new ObjectData();
        addObjectData.put("_id", IdUtil.generateId());
        addObjectData.setTenantId(81961);
        addObjectData.put("object_describe_api_name", ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        addObjectData.put("product_id","604ec945bd93170001c130bc");
        addObjectData.put("spu_id","604ec945bd93170001c130bb");
        addObjectData.put("is_base",false);
        addObjectData.put("conversion_ratio","11");
        addObjectData.put("unit_id","6");
        ActionAddArg addArg = new ActionAddArg();
        addArg.setObjectData(addObjectData);
        Result<ActionAddResult> add = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), false,false, null, null, addArg);

        ControllerListArg controllerListArg1=new ControllerListArg();
        controllerListArg1.setObjectDescribeApiName(ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
        SearchQuery searchQuery1=new SearchQuery();
        searchQuery1.setOffset(0);
        searchQuery1.setLimit(100);
        Filter filter1=new Filter();
        filter1.setFieldName("spu_id");
        filter1.setOperator("EQ");
        filter1.setFieldValues(Lists.newArrayList("604ec945bd93170001c130bb"));
        searchQuery1.setFilters(Lists.newArrayList(filter1));
        controllerListArg1.setSearchQuery(searchQuery1);
        Result<Page<ObjectData>> list1 = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),controllerListArg1 );

        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        Filter filter=new Filter();
        filter.setFieldName("spu_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList("604ec945bd93170001c130bb"));
        searchQuery.setFilters(Lists.newArrayList(filter));
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(),controllerListArg );

        ControllerListArg controllerListArg2=new ControllerListArg();
        controllerListArg2.setObjectDescribeApiName(ObjectApiNameEnum.FS_SPU.getObjApiName());
        SearchQuery searchQuery2=new SearchQuery();
        searchQuery2.setOffset(0);
        searchQuery2.setLimit(100);
        Filter filter2=new Filter();
        filter2.setFieldName("_id");
        filter2.setOperator("EQ");
        filter2.setFieldValues(Lists.newArrayList("604ec945bd93170001c130bb"));
        searchQuery2.setFilters(Lists.newArrayList(filter2));
        controllerListArg2.setSearchQuery(searchQuery2);
        Result<Page<ObjectData>> list2 = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(),controllerListArg );
        ObjectData fsObjectData=list2.getData().getDataList().get(0);
        fsObjectData.put("sku",list1.getData().getDataList());
        fsObjectData.put("multi_unit_data", list.getData().getDataList());
        ActionEditArg actionEditArg = new ActionEditArg();
        actionEditArg.setObjectData(fsObjectData);
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(), false,false, null, null, actionEditArg);

        System.out.println("");
    }

    private List<ObjectData> getMultiUnitList(String tenantId, String unitId, String productId, String spuId) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        searchQuery.setFilters(Lists.newArrayList());
        if (StringUtils.isNotBlank(productId)) {
            Filter productFilter = new Filter();
            productFilter.setFieldName("product_id");
            productFilter.setOperator("EQ");
            productFilter.setFieldValues(Lists.newArrayList(productId));
            searchQuery.getFilters().add(productFilter);
        }
        if (StringUtils.isNotBlank(spuId)) {
            Filter spuFilter = new Filter();
            spuFilter.setFieldName("spu_id");
            spuFilter.setOperator("EQ");
            spuFilter.setFieldValues(Lists.newArrayList(spuId));
            searchQuery.getFilters().add(spuFilter);
        }
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), controllerListArg);
        if (list != null && list.getData() != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(list.getData().getDataList())) {
            for(ObjectData objectData:list.getData().getDataList()){
                if(unitId.equals(objectData.get("unit_id"))){
                    return Lists.newArrayList(objectData);
                }
            }
            return list.getData().getDataList();
        }
        return Lists.newArrayList();
    }

    @Test
    public void createSpecification() {

        ActionAddArg actionAddArg = new ActionAddArg();
        ObjectData objectData = new ObjectData();
        objectData.put("object_describe_api_name",ObjectApiNameEnum.FS_SPECIFICATIONOBJ.getObjApiName());
        objectData.put("record_type","default__c");
        objectData.put("status","1");
        objectData.put("name","spec3");
        objectData.put("owner",Lists.newArrayList("1000"));
        actionAddArg.setObjectData(objectData);

        Map<String,List<ObjectData>> detailsMap = new HashMap<>();
        List<ObjectData> childList = new ArrayList<>();
        for(int i=1;i<2;i++) {
            ObjectData child = new ObjectData();
            child.put("object_describe_api_name",ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName());
            child.put("record_type","default__c");
            child.put("status","1");
            child.put("name","sp"+i);
            childList.add(child);
        }

        detailsMap.put(ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName(),childList);
        actionAddArg.setDetails(detailsMap);

        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);

        Result<ActionAddResult> result = metadataActionService.add(headerObj,
                ObjectApiNameEnum.FS_SPECIFICATIONOBJ.getObjApiName(),false,false,
                null, null, actionAddArg);

        System.out.println(result.getCode()+";"+result.getMessage());

        System.out.println(result);
    }

    @Test
    public void querySpec() {
        ObjectData objectData = SpecUtils.querySpec("81961",
                "颜色",
                metadataControllerService,i18NStringManager);
        System.out.println(objectData);
    }

    @Test
    public void querySpecValue() {
        Filter filter=new Filter();
        filter.setFieldName("name");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList("黄色"));

        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        searchQuery.setFilters(Lists.newArrayList(filter));

        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName());
        controllerListArg.setSearchQuery(searchQuery);

        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);
        Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj,
                ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName(),
                controllerListArg);
        if(listResult.isSuccess() && listResult.getData().getDataList().size()>0) {
            ObjectData objectData = listResult.getData().getDataList().get(0);
        }
    }

    @Test
    public void createSpecificationValue() {
        ActionAddArg actionAddArg = new ActionAddArg();
        ObjectData objectData = new ObjectData();
        objectData.put("object_describe_api_name",ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName());
        objectData.put("record_type","default__c");
        objectData.put("status","1");
        objectData.put("name","青色");
        objectData.put("specification_id","6164f3581734770001724187");
        actionAddArg.setObjectData(objectData);

        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);
        Result<ActionAddResult> result = metadataActionService.add(headerObj,
                ObjectApiNameEnum.FS_SPECIFICATIONVALUEOBJ.getObjApiName(),false,false,
                null, null, actionAddArg);

        String json = JSONObject.toJSONString(result);

        System.out.println(json);
    }

    @Test
    public void querySpecRelatedList() {
        List<ObjectData> list = SpecUtils.querySpecRelatedList("81961","6164f3581734770001724187",metadataControllerService,i18NStringManager);
        System.out.println(list);
    }

    @Test
    public void querySpuRelatedList() {
//        List<ObjectData> list = SpecUtils.querySpuRelatedList("81961","6166790efbff6e000123127a",metadataControllerService);
//        System.out.println(list);

//        List<ObjectData> dataList = getMultiUnitList("81961","6166790efbff6e000123127a");
//        String json = JSONObject.toJSONString(dataList);
//        System.out.println(json);
        String json = "{\n" +
                "    \"tenant_id\": \"81961\",\n" +
                "    \"batch_sn\": \"1\",\n" +
                "    \"is_multiple_unit\": true,\n" +
                "    \"multi_unit_data\": [\n" +
                "        {\n" +
                "            \"tenant_id\": \"81961\",\n" +
                "            \"is_base\": true,\n" +
                "            \"is_deleted\": false,\n" +
                "            \"is_pricing\": true,\n" +
                "            \"is_enable\": true,\n" +
                "            \"price\": 1.0,\n" +
                "            \"conversion_ratio\": \"1.0000\",\n" +
                "            \"is_editable\": true,\n" +
                "            \"id\": \"616ce30361d446000158e514\",\n" +
                "            \"_id\": \"616ce30361d446000158e514\",\n" +
                "            \"spu_id\": \"616ce30261d446000158e505\",\n" +
                "            \"unit_id\": \"604b28ec1c08f0000175fa8f\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"is_deleted\": false,\n" +
                "    \"object_describe_api_name\": \"SPUObj\",\n" +
                "    \"owner_department_id\": \"999998\",\n" +
                "    \"owner_department\": \"待分配\",\n" +
                "    \"sku\": [\n" +
                "        {\n" +
                "            \"tenant_id\": \"81961\",\n" +
                "            \"owner\": [\n" +
                "                \"1000\"\n" +
                "            ],\n" +
                "            \"status_flag\": 1,\n" +
                "            \"batch_sn\": \"1\",\n" +
                "            \"product_status\": \"1\",\n" +
                "            \"product_code\": \"CH523z\",\n" +
                "            \"record_type\": \"default__c\",\n" +
                "            \"is_multiple_unit\": true,\n" +
                "            \"unit\": \"604b28ec1c08f0000175fa8f\",\n" +
                "            \"price\": \"1\",\n" +
                "            \"object_describe_api_name\": \"ProductObj\",\n" +
                "            \"name\": \"CH523z#不带规格的物料2\",\n" +
                "            \"_id\": \"616ce25b19bf4d00019e5d1f\",\n" +
                "            \"category\": \"19\",\n" +
                "            \"spu_id\": \"616ce30261d446000158e505\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"owner\": [\n" +
                "        \"1000\"\n" +
                "    ],\n" +
                "    \"standard_price\": \"1\",\n" +
                "    \"lock_status\": \"0\",\n" +
                "    \"package\": \"CRM\",\n" +
                "    \"last_modified_time\": 1634525955119,\n" +
                "    \"create_time\": 1634525955119,\n" +
                "    \"life_status\": \"normal\",\n" +
                "    \"last_modified_by\": [\n" +
                "        \"-10000\"\n" +
                "    ],\n" +
                "    \"is_spec\": false,\n" +
                "    \"created_by\": [\n" +
                "        \"-10000\"\n" +
                "    ],\n" +
                "    \"record_type\": \"default__c\",\n" +
                "    \"relevant_team\": [\n" +
                "        {\n" +
                "            \"teamMemberEmployee\": [\n" +
                "                \"1000\"\n" +
                "            ],\n" +
                "            \"teamMemberType\": \"0\",\n" +
                "            \"teamMemberRole\": \"1\",\n" +
                "            \"teamMemberPermissionType\": \"2\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"unit\": \"604b28ec1c08f0000175fa8f\",\n" +
                "    \"data_own_department\": [\n" +
                "        \"999998\"\n" +
                "    ],\n" +
                "    \"name\": \"CH523z#不带规格的物料2\",\n" +
                "    \"_id\": \"616ce30261d446000158e505\",\n" +
                "    \"category\": \"19\"\n" +
                "}";

        ObjectData objectData = JSONObject.parseObject(json,ObjectData.class);
        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);

        updateCrmObjectData(headerObj,ObjectApiNameEnum.FS_SPU.getObjApiName(), objectData,null);
    }

    public Result<ActionEditResult> updateCrmObjectData(HeaderObj headerObj, String objectApiName, com.fxiaoke.crmrestapi.common.data.ObjectData fsObjectData, DoWriteMqData doWriteMqData) {
        Result<ActionEditResult> result = null;
        try {
            ActionEditArg actionEditArg = new ActionEditArg();
            actionEditArg.setObjectData(fsObjectData);
//            if (userMasterDetailUpdateApiNames.contains(objectApiName)) {
//                actionEditArg = fillDetailDatas(headerObj, actionEditArg);
//            }
            log.info("SyncSkuSpuServiceImpl.updateCrmObjectData,actionEditArg={}",JSONObject.toJSONString(actionEditArg));
            result = metadataActionService.edit(headerObj, objectApiName, false,false, null, null, actionEditArg);
        } catch (CrmBusinessException e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage(e.getMessage());
            log.warn(e.getMessage(), e);
        } catch (Exception e) {
            result = new Result<>();
            result.setCode(5000000);
            result.setMessage("未知异常，请提供traceId和接口参数，找该对象相关团队查询");
            log.warn(e.getMessage(), e);
        }
        return result;
    }

    @Test
    public void editData() {
        HeaderObj headerObj = new HeaderObj(82777,-10000);
        String json = "{\"details\":{\"SalesOrderProductObj\":[{\"tenant_id\":\"82777\",\"owner\":[\"1006\"],\"quantity\":1.0,\"subtotal\":10.0,\"object_describe_api_name\":\"SalesOrderProductObj\",\"product_id\":\"624173caab860e00012b6a90\",\"sales_price\":10.0,\"discount\":0.0,\"product_price\":8.85,\"_id\":\"6278dac93e47a00001e0c86f\",\"order_id\":\"6278dac93e47a00001e0c86e\"}]},\"fillOutOwner\":false,\"objectData\":{\"tenant_id\":\"82777\",\"owner\":[\"1006\"],\"account_id\":\"622f35159489770001bdeed9\",\"object_describe_api_name\":\"SalesOrderObj\",\"remark\":\"333\",\"order_time\":*************,\"_id\":\"6278dac93e47a00001e0c86e\",\"record_type\":\"default__c\"}}";
        ActionEditArg actionEditArg = JSONObject.parseObject(json,ActionEditArg.class);
        log.info("SyncSkuSpuServiceImpl.updateCrmObjectData,actionEditArg={}",JSONObject.toJSONString(actionEditArg));
        Result<ActionEditResult> result = metadataActionService.edit(headerObj, "SalesOrderObj",false,false, null, null, actionEditArg);
        System.out.println(result);
    }

    private List<com.fxiaoke.crmrestapi.common.data.ObjectData> getMultiUnitList(String tenantId, String productId) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        Filter filter = new Filter();
        filter.setFieldName("spu_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList(productId));
        searchQuery.setFilters(Lists.newArrayList(filter));
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<com.fxiaoke.crmrestapi.common.data.ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), controllerListArg);
        if (list != null && list.getData() != null && CollectionUtils.isNotEmpty(list.getData().getDataList())) {
            return list.getData().getDataList();
        }
        return Lists.newArrayList();
    }

}