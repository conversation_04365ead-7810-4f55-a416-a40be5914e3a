package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.GetByIdBreakManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpSyncTimeVO;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.PROBE_DATA_SET_REDIS_KEY;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/6/1
 */
@Ignore
public class ProbeErpDataServiceImplTest extends BaseTest {

    @Autowired
    private ProbeErpDataServiceImpl probeErpDataService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ProbeErpDataManager probeErpDataManager;
    @Autowired
    private GetByIdBreakManager getByIdBreakManager;

    @Test
    public void testExecutePloys() throws InterruptedException {
        executePloys();
    }

    private void executePloys() throws InterruptedException {
        String tenantId = "81961";
        String objApiName = "BD_STOCK.BillHead";
        String queueRedisKey = String.format(PROBE_DATA_SET_REDIS_KEY, tenantId);
        Long sadd = redisDataSource.get(this.getClass().getSimpleName()).sadd(queueRedisKey, objApiName);
        System.out.println(sadd);
        Result<Void> voidResult = probeErpDataService.executePloys(tenantId, new ArrayList<>());
        System.out.println(voidResult);
        Thread.sleep(30000);
    }

    @Test
    public void testExecuteRollingErpDataFromMongoJob() throws InterruptedException {
        executeRollingErpDataFromMongoJob();
    }
    private void executeRollingErpDataFromMongoJob() throws InterruptedException {
        String tenantId = "81772";
        String objApiName = "BD_STOCK.BillHead";
        ErpSyncTimeVO syncTimeVO=new ErpSyncTimeVO();
        syncTimeVO.setTenantId(tenantId);
        syncTimeVO.setObjectApiName(objApiName);
        syncTimeVO.setOperationType(1);
        syncTimeVO.setSnapshotId("51f84a5bb74249e0ba3ac1aa82e24f49");
        Result<Void> voidResult = probeErpDataService.executeRollingErpDataFromMongoJob(tenantId, Lists.newArrayList(syncTimeVO));
        System.out.println(voidResult);
    }
    @Test
    public void testExecutePloysDirect() throws InterruptedException {
        String tenantId = "81772";
        String objApiName = "SAL_SaleOrder.BillHead";
        List<ErpSyncTimeVO> syncTimeVos = probeErpDataService.getSyncTimesByObjApiName(tenantId, objApiName);
        syncTimeVos.forEach(v->v.setLastSyncTime(1624956621083L));
        Result<Void> voidResult = probeErpDataService.executePloys(tenantId, syncTimeVos);
        System.out.println(voidResult);
        Thread.sleep(30000);
    }

    @Test
    public void sendMq() {
        SyncDataContextEvent erpObjDatumResult = new SyncDataContextEvent();
        String objStr = "{\"tenant_id\":\"81772\",\"owner\":[\"-10000\"],\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"_id\":\"101353\",\"last_modified_by\":[\"-10000\"]}";
        ObjectData objectData = JacksonUtil.fromJson(objStr,ObjectData.class);
        objectData.put("fake_master_detail","100654#XSDD000333");
        erpObjDatumResult.setSourceData(objectData);
        erpObjDatumResult.setSourceEventType(3);
        probeErpDataManager.asyncSendErpDataMq(Collections.singletonList(erpObjDatumResult),true);
    }

    @Test
    public void testSync(){
        getByIdBreakManager.isBreak("84801","780777150699143168", ErpChannelEnum.ERP_K3CLOUD,"BD_SAL_PriceList");
         String KEY_GET_BY_ID_BREAK = "key_get_by_id_break";
        String key = KEY_GET_BY_ID_BREAK + "_" + "84801" + "_" + "780777150699143168" + "_" + "BD_SAL_PriceList";
        String jsonValue = redisDataSource.get(this.getClass().getSimpleName()).get(key);
        GetByIdBreakManager.FailedData failedData=new GetByIdBreakManager.FailedData();
        failedData.setTimestamp(System.currentTimeMillis());
        failedData.setFailedCount(1000);
        String set = redisDataSource.get(this.getClass().getSimpleName()).set(key, JSONObject.toJSONString(failedData));
        GetByIdBreakManager.FailedData bd_sal_priceList = getFailedData("84801", "780777150699143168", "BD_SAL_PriceList");
    }

    public GetByIdBreakManager.FailedData getFailedData(String tenantId, String dataCenterId, String objectApiName) {
        String key = "key_get_by_id_break" + "_" + tenantId + "_" + dataCenterId + "_" + objectApiName;
        String jsonValue = redisDataSource.get(this.getClass().getSimpleName()).get(key);
        GetByIdBreakManager.FailedData breakData = null;
        if (StringUtils.isNotEmpty(jsonValue)) {
            breakData = com.alibaba.fastjson.JSONObject.parseObject(jsonValue, GetByIdBreakManager.FailedData.class);
        }
        return breakData;
    }
}