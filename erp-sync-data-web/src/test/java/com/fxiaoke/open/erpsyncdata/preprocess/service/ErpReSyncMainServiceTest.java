package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 14:41 2021/11/12
 * @Desc:
 */
@Ignore
public class ErpReSyncMainServiceTest extends BaseTest {
    @Autowired
    private ErpReSyncDataService erpReSyncDataService;

    @Test
    public void reSyncDataExecuteSingleTenantTasks() {
        Map<String,Long> objectObjectHashMap = Maps.newHashMap();
        objectObjectHashMap.put("data_sync_count_limit",5000000L);
        SyncQuota syncQuota = SyncQuota.create(Sets.newHashSet("standard_data_sync_app", "k3_data_sync_app"), objectObjectHashMap);
        Result<Void> result=erpReSyncDataService.reSyncDataExecuteSingleTenantTasks("81243");
        System.out.println("");
    }
}