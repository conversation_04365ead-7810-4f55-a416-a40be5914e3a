package com.fxiaoke.open.erpsyncdata.preprocess.job
//package com.fxiaoke.open.erpsyncdata.preprocess.job
//
//
//import com.fxiaoke.open.erpsyncdata.BaseTest
//import org.junit.Test
//
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2020/8/21
// */
//class SyncDataLockTest extends BaseTest {
//
//    @Test
//    void lockTest() {
//        given:
//        def t1 = new MyThread("线程1", 2000, "81772")
//        def t2 = new MyThread("线程2", 50, "81772")
//        new Thread(t1).start()
//        new Thread(t2).start()
//        Thread.sleep(10000)
//        expect:
//        true
//    }
//
//    @Test
//    void lockHoldTest(){
//        SyncDataLock.start("81772")
//        def lock = SyncDataLock.getLock(5)
//        println(lock)
//        Thread.sleep(3000000)
//        def release = SyncDataLock.releaseLock()
//        println(release)
//    }
//
//    @Test
//    void getAllChildrenTest() {
//        given:
//        for (int i = 0; i < 10; i++) {
//            def t = new funcTh("线程" + i, "path" + i)
//            new Thread(t).start()
//        }
//        Thread.sleep(50)
//        def childs = SyncDataLock.getAllChildNode()
//        Thread.sleep(2000)
//        println(childs)
//        expect:
//        true
//    }
//
//    class funcTh implements Runnable {
//        String name
//        String path
//
//        funcTh(String name, String path) {
//            this.name = name
//            this.path = path
//        }
//
//        @Override
//        void run() {
//            SyncDataLock.start(path)
//            boolean success = SyncDataLock.getLock(1)
//            println(name + success)
//            Thread.sleep(1000)
//            boolean re = SyncDataLock.releaseLock()
//            println(name + "释放锁" + re)
//        }
//    }
//
//    class MyThread implements Runnable {
//        String name
//        Long sleep
//        String path
//
//        MyThread(String name, Long sleep, String path) {
//            this.name = name
//            this.sleep = sleep
//            this.path = path
//        }
//
//        @Override
//        void run() {
//            SyncDataLock.start(path)
//            boolean success = SyncDataLock.getLock(1)
//            println(name + success)
//            Thread.sleep(sleep)
//            boolean success2 = SyncDataLock.getLock(1)
//            println(name + success2)
//            boolean re = SyncDataLock.releaseLock()
//            println(name + "释放锁" + re)
//        }
//    }
//}
