package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FileUtils;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
public class GroovyDriverUtil {

    public static void groovyClassLoaderTest() throws IOException, IllegalAccessException, InstantiationException {
        String text = FileUtils.readString("D:\\project\\fs-erp-sync-data\\erp-sync-data-all\\src\\test\\resources\\testfile\\GroovyFileTest.groovy");
        System.out.println(text);
        GroovyClassLoader loader = new GroovyClassLoader();
        Class fileCreator = loader.parseClass(text);
        GroovyObject object = (GroovyObject) fileCreator.newInstance();
        for (int i = 0; i < 10; i++) {
            Long start = System.currentTimeMillis();
            Map token = (Map) object.invokeMethod("getToken", null);
            System.out.println(token);
            System.out.println("use： " + (System.currentTimeMillis() - start));
        }
    }


    public static void main(String[] args) throws IllegalAccessException, IOException, InstantiationException {
        groovyClassLoaderTest();
    }
}
