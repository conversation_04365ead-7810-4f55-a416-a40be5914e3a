package com.fxiaoke.open.erpsyncdata

import groovy.util.logging.Slf4j
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/2/17
 */

@Slf4j
abstract class BaseSpockTest extends Specification {
    static  {
        System.setProperty("process.name", "fs-erp-sync-data-web")
        setFsTestEnv()
    }

    static void setFsTestEnv(){
        log.info("set fstest env")
        System.setProperty("process.profile", "fstest")
    }


    //用于模拟非线下环境
    static void setFirstshareEnv(){
        log.info("set firstshare env")
        System.setProperty("process.profile", "firstshare")
    }
}
