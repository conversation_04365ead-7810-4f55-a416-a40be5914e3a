package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/8/18
 */
@Ignore
public class PloyBreakManagerTest extends BaseTest {
    @Autowired
    private PloyBreakManager ployBreakManager;
    @Autowired
    private SyncDataFixDao syncDataFixDao;

    @Test
    public void handelPollingErpResult() {
//        TimeFilterArg arg = new TimeFilterArg();
//        arg.setTenantId("83952");
//        arg.setStartTime(0L);
//        arg.setEndTime(1660809905000L);
//        arg.setObjAPIName("LargeBatchObj_1g8vh6rsm");
//        arg.setSnapshotId("3451dd88ac3041e8be59883a767101d7");
//        arg.setOperationType(2);
//        ployBreakManager.handelPollingErpResult(arg,true);
//        for (int i = 0; i < 9; i++) {
//            ployBreakManager.handelPollingErpResult(arg,false);
//        }
//        ployBreakManager.handelPollingErpResult(arg,true);
//        for (int i = 0; i < 10; i++) {
//            ployBreakManager.handelPollingErpResult(arg,false);
//        }
    }

    @Test
    public void handelPollingTempFailed() {
        TimeFilterArg arg = new TimeFilterArg();
        arg.setTenantId("83952");
        arg.setStartTime(0L);
        arg.setEndTime(1660809905000L);
        arg.setObjAPIName("LargeBatchObj_1g8vh6rsm");
        arg.setSnapshotId("3451dd88ac3041e8be59883a767101d7");
        ployBreakManager.handelPollingTempFailed(arg, Result.newError("测试轮询temp异常"));
    }

    @Test
    public void incrFailedSyncDataNum() {
        testIncrFailed();
    }

    private void testIncrFailed() {
        SyncDataEntity syncData = new SyncDataEntity();
        String testId = "testId";
        syncData.setId(testId);
        syncData.setTenantId("83952");
        syncData.setSourceObjectApiName("LargeBatchObj_1g8vh6rsm");
        syncData.setSyncPloyDetailSnapshotId("3451dd88ac3041e8be59883a767101d7");
        syncDataFixDao.insertCache(syncData);
        syncData.setSourceDataId(testId);
        for (int i = 0; i < 300; i++) {
            //相同id
            ployBreakManager.incrFailedSyncDataNum("83952", testId);
        }
        for (int i = 0; i < 301; i++) {
            //不同id
            syncData.setSourceDataId(testId+i);
            ployBreakManager.incrFailedSyncDataNum("83952", testId);
        }
        for (int i = 0; i < 2001; i++) {
            //不同id
            syncData.setSourceDataId(testId+i);
            ployBreakManager.incrFailedSyncDataNum("83952", testId);
        }
    }

    @Test
    public void getBreakFailedSyncDataNum() {
    }
}