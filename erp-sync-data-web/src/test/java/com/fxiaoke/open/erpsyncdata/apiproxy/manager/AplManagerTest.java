package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.facishare.function.biz.api.service.FunctionService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

/**
 * <AUTHOR> (^_−)☆
 */
@Ignore
@Slf4j
public class AplManagerTest extends BaseTest {

    @Autowired
    private FunctionService functionService;

    @Test
    public void testExecute() {
        String s = functionService.executeFuncMethod("83952", "PubLibfRSMW__c", "testNullMap", new ArrayList<>());
        log.info("result:{}", s);
    }
}
