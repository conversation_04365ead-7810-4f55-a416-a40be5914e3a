package com.fxiaoke.open.erpsyncdata.apiproxy.u8;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.U8DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p></p>
 * @dateTime 2020/9/4 15:31
 * <AUTHOR>
 * @version 1.0
 */
@Ignore
@Slf4j
public class CrmToU8Test extends BaseTest {

  @Autowired
  private U8DataManager u8DataManager;

  @Test
  public void crmToU8SyncTest() {

    while (true){

    }

  }

  @Test
  public void createErpObjData(){
    String str1="{\"destData\": {\"tenant_id\": \"79675\",\"code\": \"********-000431\",\"mobile\":\"***********\",\"ccusexch_name\":\"港币 \",\"sort_code\":\"102\",\"bcusoverseas\":\"1\",\"bcusdomestic\":\"1\",\"abbrname\":\"lxs测试客户000052\",\"object_describe_api_name\":\"customer\",\"contact\":\"AAA\",\"name\":\"lxs测试客户000052\",\"_id\":\"5fa8ffc2b3cf6b00018d7102\",\"fax\":\"2323235\"},\"destDataId\":\"5fa8ffc2b3cf6b00018d7102\",\"destDetailSyncDataIdAndDestDataMap\":{\"0234805797d84c8d84f59261541ea5ba\":{\"tenant_id\":\"79675\",\"owner\":[],\"caccountnum\":\"**************\",\"object_describe_api_name\":\"banks\",\"bdefault\":\"0\",\"caccountname\":\"用友软件股份有限公司\",\"_id\":\"5fa8ffc2b3cf6b00018d7105\",\"ccuscode\":\"5fa8ffc2b3cf6b00018d7102\",\"cbranch\":\"招商银行双榆树支行\",\"created_by\":[],\"cbank\":\"02\"},\"f18b3bef58bb42a1a6229b45601c316b\":{\"tenant_id\":\"79675\",\"owner\":[],\"object_describe_api_name\":\"addresses\",\"bdefault\":\"1\",\"_id\":\"5fa8ffc2b3cf6b00018d7103\",\"ccuscode\":\"5fa8ffc2b3cf6b00018d7102\",\"created_by\":[],\"caddcode\":\"Addr.2020-11-09_000310\"},\"be946ebbcce64ff9bac4f4d29080e9e8\":{\"tenant_id\":\"79675\",\"owner\":[],\"privilege_type\":\"3\",\"privilege_id\":\"03\",\"object_describe_api_name\":\"auths\",\"_id\":\"5fa8ffc2b3cf6b00018d7104\",\"ccuscode\":\"5fa8ffc2b3cf6b00018d7102\",\"created_by\":[]},\"f38d8fa3dc0c4562987bc45dcfee91d6\":{\"tenant_id\":\"79675\",\"owner\":[],\"object_describe_api_name\":\"invoicecustomers\",\"bdefault\":\"1\",\"_id\":\"5fa8ffc3b3cf6b00018d7106\",\"ccuscode\":\"5fa8ffc2b3cf6b00018d7102\",\"cinvoicecompany\":\"********-000431\",\"created_by\":[]}},\"destEventType\":1,\"destObjectApiName\":\"customer\",\"destTenantId\":\"79675\",\"destTenantType\":2,\"sourceTenantId\":\"79675\",\"syncDataId\":\"59ce3be43e4e4844b9ef0f3138cf1cdf\",\"syncPloyDetailSnapshotId\":\"f718a525efd146f5987a3b4fd1351052\"}";

    String str2=" {\n" + "        \"channel\": \"ERP_U8\",\n" + "        \"connectParams\": {\n"
      + "            \"baseUrl\": \"http://api.yonyouup.com\",\n" + "            \"fromAccount\": \"fineartcrm\",\n"
      + "            \"toAccount\": \"fineart\",\n" + "            \"appKey\": \"opa9b9b43b2cfc57768\",\n"
      + "            \"appSecret\": \"b628f335d6444dccb0f020777fffb80a\",\n" + "            \"ds_sequence\": 1\n"
      + "        },\n" + "        \"createTime\": *************,\n" + "        \"enterpriseName\": \"zsl测试企业19\",\n"
      + "        \"id\": \"603396635238203392\",\n" + "        \"tenantId\": \"79675\",\n"
      + "        \"updateTime\": *************\n" + "    }";
    SyncDataContextEvent doWriteMqData = JSON.parseObject(str1, SyncDataContextEvent.class);
    ErpConnectInfoEntity erpConnectInfoEntities = JSON.parseObject(str2, ErpConnectInfoEntity.class);
    StandardData standardData = FormatConvertUtil.crm2StdErp(doWriteMqData);
    Result<ErpIdResult> erpObjData = u8DataManager.createErpObjData(standardData, erpConnectInfoEntities);
  }

  @Test
  public void updateErpObjData(){
    String str1="{\n" + "    \"destData\":{\n" + "        \"tenant_id\":\"79675\",\n"
      + "        \"contactId\":\"lxs0000001#测试联系人3\",\n" + "        \"object_describe_api_name\":\"customercontacts\",\n"
      + "        \"mobile\":\"13234243234\",\n" + "        \"_id\":\"lxs0000001\"\n" + "    },\n"
      + "    \"destDataId\":\"lxs0000001\",\n" + "    \"destDetailSyncDataIdAndDestDataMap\":{\n" + "\n" + "    },\n"
      + "    \"destEventType\":2,\n" + "    \"destObjectApiName\":\"customercontacts\",\n"
      + "    \"destTenantId\":\"79675\",\n" + "    \"destTenantType\":2,\n" + "    \"sourceTenantId\":\"79675\",\n"
      + "    \"stop\":false,\n" + "    \"syncDataId\":\"ef4c274dbf3e4e19b1ebf668b355dce1\",\n"
      + "    \"syncPloyDetailSnapshotId\":\"e7bb96bd9b77457d993ea649a61b0fd7\"\n" + "}";

    String str2=" {\n" + "        \"channel\": \"ERP_U8\",\n" + "        \"connectParams\": {\n"
      + "            \"baseUrl\": \"http://api.yonyouup.com\",\n" + "            \"fromAccount\": \"fineartcrm\",\n"
      + "            \"toAccount\": \"fineart\",\n" + "            \"appKey\": \"opa9b9b43b2cfc57768\",\n"
      + "            \"appSecret\": \"b628f335d6444dccb0f020777fffb80a\",\n" + "            \"ds_sequence\": 1\n"
      + "        },\n" + "        \"createTime\": *************,\n" + "        \"enterpriseName\": \"zsl测试企业19\",\n"
      + "        \"id\": \"603396635238203392\",\n" + "        \"tenantId\": \"79675\",\n"
      + "        \"updateTime\": *************\n" + "    }";
    SyncDataContextEvent doWriteMqData = JSON.parseObject(str1, SyncDataContextEvent.class);
    ErpConnectInfoEntity erpConnectInfoEntities = JSON.parseObject(str2, ErpConnectInfoEntity.class);
    StandardData standardData = FormatConvertUtil.crm2StdErp(doWriteMqData);
    Result<ErpIdResult> resultResult = u8DataManager.updateErpObjData(standardData, erpConnectInfoEntities);
  }

  @Test
  public void getErpObjData(){
    String str1="{\"dataId\":\"************\",\"includeDetail\":true,\"objAPIName\":\"inventory\",\"tenantId\":\"79675\"}";

    String connectParam="{\n" + "        \"baseUrl\": \"http://api.yonyouup.com\",\n"
      + "        \"fromAccount\": \"fineartcrm\",\n" + "        \"toAccount\": \"fineart\",\n"
      + "        \"appKey\": \"opa9b9b43b2cfc57768\",\n"
      + "        \"appSecret\": \"b628f335d6444dccb0f020777fffb80a\",\n" + "        \"ds_sequence\": 1\n" + "    }";
    ErpIdArg timeFilterArg = JSON.parseObject(str1, ErpIdArg.class);
      ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
      erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_U8);
      erpConnectInfoEntity.setTenantId("81138");
      erpConnectInfoEntity.setConnectParams(connectParam);
    Result standardListDataResult = u8DataManager.getErpObjData(timeFilterArg, erpConnectInfoEntity);


  }

  @Test
  public void listErpObjDataByTime(){
    String str1="{\"endTime\":*************,\"includeDetail\":true,\"limit\":1,\"objAPIName\":\"inventory\",\"offset\":0,\"operationType\":1,\"snapshotId\":\"bdb275d745cf4212bfbfba4d51da2727\",\"startTime\":0,\"tenantId\":\"79675\"}";

    String connectParam="{\n" + "        \"baseUrl\": \"http://api.yonyouup.com\",\n"
      + "        \"fromAccount\": \"fineartcrm\",\n" + "        \"toAccount\": \"fineart\",\n"
      + "        \"appKey\": \"opa9b9b43b2cfc57768\",\n"
      + "        \"appSecret\": \"b628f335d6444dccb0f020777fffb80a\",\n" + "        \"ds_sequence\": 1\n" + "    }";
    TimeFilterArg timeFilterArg = JSON.parseObject(str1, TimeFilterArg.class);
      ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
      erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_U8);
      erpConnectInfoEntity.setTenantId("81138");
      erpConnectInfoEntity.setConnectParams(connectParam);
    Result standardListDataResult = u8DataManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);


  }

}
