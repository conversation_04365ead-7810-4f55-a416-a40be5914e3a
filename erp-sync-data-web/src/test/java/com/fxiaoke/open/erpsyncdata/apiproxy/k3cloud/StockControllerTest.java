package com.fxiaoke.open.erpsyncdata.apiproxy.k3cloud;

import com.alibaba.fastjson.JSONArray;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.K3CloudStockArg;
import com.fxiaoke.open.erpsyncdata.admin.result.K3CloudStockResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.out.K3CStockController;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class StockControllerTest extends BaseTest {
    @Autowired
    private K3CStockController stockController;

    @Test
    public void getK3CloudStockData() {
        K3CloudStockArg arg = new K3CloudStockArg();
        arg.setTenantId("82777");
        K3CloudStockArg.StockArg stockArg = new K3CloudStockArg.StockArg();
        stockArg.setWarehouseNumber("CK006");
        //stockArg.setProductName("CH469z#aaaaa");
        stockArg.setProductNumber("CH469z");
        //stockArg.setBatchNumber("CH800z/qwrwr24/1");

        String json = "[\n" +
                "        {\n" +
                "            \"warehouseNumber\": \"CK068\",\n" +
                "            \"productName\": \"CH4373#测试物料1112\",\n" +
                "            \"productNumber\": \"CH4373\",\n" +
                "            \"batchNumber\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"warehouseNumber\": \"CK068\",\n" +
                "            \"productName\": \"CH464z#测试物料0831\",\n" +
                "            \"productNumber\": \"CH464z\",\n" +
                "            \"batchNumber\": null\n" +
                "        }\n" +
                "    ]";


        List<K3CloudStockArg.StockArg> stockArgList = JSONArray.parseArray(json, K3CloudStockArg.StockArg.class);
        //stockArgList.add(stockArg);

        arg.setStockArgList(stockArgList);

        String token = "K3C_STOCK_QUERY"+System.currentTimeMillis();

        Result<List<K3CloudStockResult>> result = stockController.getStockData(token, arg);
        System.out.println(result);
    }
}
