package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ObjToMapUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:42 2020/12/7
 * @Desc:
 */
@Ignore
public class SpecialWayDataServiceImplTest extends BaseTest {

    @Autowired
    private SpecialWayDataService specialWayDataService;

    @Test
    public void getErpObjDataByIdWithDB() {
    }

    @Test
    public void listErpObjDataByTimeWithDB() {
    }

    @Test
    public void getErpObjDataByIdWithGroovy() {
    }

    @Test
    public void listErpObjDataByTimeWithGroovy() {
        TimeFilterArg timeFilterArg=new TimeFilterArg();
        timeFilterArg.setOperationType(1);
        timeFilterArg.setObjAPIName("testApiName");
        timeFilterArg.setTenantId("79675");
        ErpObjGroovyEntity erpObjGroovyEntity=new ErpObjGroovyEntity();
        List<StandardData> list=Lists.newArrayList();
        StandardListData standardListData=new StandardListData();
        for(int i=0;i<1;i++){
            standardListData.setTotalNum(i+1);
            StandardData standardData=new StandardData();
            standardData.setObjAPIName(timeFilterArg.getObjAPIName());
            ObjectData objectData=new ObjectData();
            standardData.setMasterFieldVal(objectData);
            list.add(standardData);
        }
        standardListData.setDataList(list);
        String groovy="import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;\n" +
                "import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;\n" +
                "import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;\n" +
                "import com.google.common.collect.Lists;\n" +
                "import com.google.common.collect.Maps;\n" +
                "\n" +
                "    public Object executeGroovyScript() {\n" +
                "        String tenantId = arg.getTenantId();//得到企业id等等\n" +
                "        List<StandardData> list=Lists.newArrayList();\n" +
                "        StandardListData standardListData=new StandardListData();\n" +
                "        for(int i=0;i<2;i++){\n" +
                "            standardListData.setTotalNum(i+1);\n" +
                "            StandardData standardData=new StandardData();\n" +
                "            standardData.setObjAPIName(arg.getObjAPIName());\n" +
                "            ObjectData objectData=new ObjectData();\n" +
                "            standardData.setMasterFieldVal(objectData);\n" +
                "            list.add(standardData);\n" +
                "        }\n" +
                "        standardListData.setDataList(list);" +
                " return standardListData;\n" +
                "    }\n" +
                "return executeGroovyScript();";
        erpObjGroovyEntity.setGroovyScript(groovy);
        Result<StandardListData> standardListDataResult = specialWayDataService.listErpObjDataByTimeWithGroovy(timeFilterArg, erpObjGroovyEntity,"");
        System.out.println("");
    }

    @Test
    public void createErpObjDataWithGroovy() {
    }

    @Test
    public void createErpObjDetailDataWithGroovy() {
    }

    @Test
    public void updateErpObjDataWithGroovy() {
        String doWriteJson = "{\"destData\":{\"tenant_id\":\"82370\",\"order_date\":\"2021-07-06\",\"object_describe_api_name\":\"SalesOrderObj\",\"name\":\"20210706-000001\",\"remark\":\"111\",\"_id\":\"60e3fa23bd3f630001daeaf0\"},\"destDataId\":\"60e3fa23bd3f630001daeaf0\",\"destDetailSyncDataIdAndDestDataMap\":{\"6d30619e7a5140e2a20265412e54e101\":{\"tenant_id\":\"82370\",\"owner\":[],\"fake_master_detail\":\"60e3fa23bd3f630001daeaf0\",\"price\":\"10.00\",\"object_describe_api_name\":\"SalesOrderObj.detail\",\"count\":\"5.00\",\"detailId\":\"000000000001\",\"_id\":\"60e3fa23bd3f630001daeaf1\",\"product_name\":\"撒大声地\",\"created_by\":[]}},\"destEventType\":2,\"destObjectApiName\":\"SalesOrderObj\",\"destTenantId\":\"82370\",\"destTenantType\":2,\"sourceTenantId\":\"82370\",\"syncDataId\":\"6debe5bd08e34c24957d9570884fe24a\",\"syncPloyDetailSnapshotId\":\"c3a5938eb0d44a82a3298c32dfd4b07d\"}";
        String erpObjGroovyEntityJson = "{\"createTime\":1625553045570,\"dataCenterId\":\"d10e6153355a4124a3b949f172a6cab8\",\"funcApiName\":\"func_l9icp__c\",\"id\":\"689485345800683520\",\"objApiName\":\"SalesOrderObj\",\"tenantId\":\"82370\",\"updateTime\":1625553045570,\"url\":\"update\"}";
        String dataCenterId="d10e6153355a4124a3b949f172a6cab8";
        SyncDataContextEvent doWriteMqData = JSONObject.parseObject(doWriteJson, SyncDataContextEvent.class);
        ErpObjGroovyEntity erpObjGroovyEntity = JSONObject.parseObject(erpObjGroovyEntityJson,ErpObjGroovyEntity.class);

        StandardData standardData = FormatConvertUtil.crm2StdErp(doWriteMqData);

        Result<ErpIdResult> result = specialWayDataService.updateErpObjDataWithGroovy(doWriteMqData,standardData,erpObjGroovyEntity,dataCenterId);
        System.out.println(result);
    }

    @Test
    public void updateErpObjDetailDataWithGroovy() {
        String doWriteJson = "{\"destData\":{\"tenant_id\":\"82370\",\"order_date\":\"2021-07-06\",\"object_describe_api_name\":\"SalesOrderObj\",\"name\":\"20210706-000001\",\"remark\":\"111\",\"_id\":\"60e3fa23bd3f630001daeaf0\"},\"destDataId\":\"60e3fa23bd3f630001daeaf0\",\"destDetailSyncDataIdAndDestDataMap\":{\"6d30619e7a5140e2a20265412e54e101\":{\"tenant_id\":\"82370\",\"owner\":[],\"fake_master_detail\":\"60e3fa23bd3f630001daeaf0\",\"price\":\"10.00\",\"object_describe_api_name\":\"SalesOrderObj.detail\",\"count\":\"5.00\",\"detailId\":\"000000000001\",\"_id\":\"60e3fa23bd3f630001daeaf1\",\"product_name\":\"撒大声地\",\"created_by\":[]}},\"destEventType\":2,\"destObjectApiName\":\"SalesOrderObj\",\"destTenantId\":\"82370\",\"destTenantType\":2,\"sourceTenantId\":\"82370\",\"syncDataId\":\"6debe5bd08e34c24957d9570884fe24a\",\"syncPloyDetailSnapshotId\":\"c3a5938eb0d44a82a3298c32dfd4b07d\"}";
        String erpObjGroovyEntityJson = "{\"createTime\":1625553045570,\"dataCenterId\":\"d10e6153355a4124a3b949f172a6cab8\",\"funcApiName\":\"func_l9icp__c\",\"id\":\"689485345800683520\",\"objApiName\":\"SalesOrderObj\",\"tenantId\":\"82370\",\"updateTime\":1625553045570,\"url\":\"update\"}";
        SyncDataContextEvent doWriteMqData = JSONObject.parseObject(doWriteJson,SyncDataContextEvent.class);
        ErpObjGroovyEntity erpObjGroovyEntity = JSONObject.parseObject(erpObjGroovyEntityJson,ErpObjGroovyEntity.class);

        StandardDetailData standardDetailData = FormatConvertUtil.crm2StdDetailErp(doWriteMqData);
        Result<StandardDetailId> result = specialWayDataService.updateErpObjDetailDataWithGroovy(doWriteMqData,standardDetailData,erpObjGroovyEntity);
        System.out.println(result);
    }

    @Test
    public void executeCustomFunction() {
        StandardData standardData = new StandardData();
        standardData.setObjAPIName("AccountObj");
        ObjectData masterObj = new ObjectData();
        masterObj.put("name", "208");
        masterObj.put("age", "208");
        standardData.setMasterFieldVal(masterObj);
        standardData.setDetailFieldVals(Maps.newHashMap());

//        Result<String> result = specialWayDataService.executeCustomFunction("81138","","func_m3g7k__c",
//                ObjToMapUtil.objectToMap(standardData),null);

//        System.out.println(result);
    }
}