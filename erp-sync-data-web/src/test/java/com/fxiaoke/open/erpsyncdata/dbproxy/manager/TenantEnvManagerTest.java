package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/5/31
 */
@Ignore
public class TenantEnvManagerTest extends BaseTest {
    @Autowired
    private TenantEnvManager tenantEnvManager;



    boolean changeAndCheck(String tenantId, ErpSyncDataBackStageEnvironmentEnum newEnv) {
        tenantEnvManager.changeTenantEnv(tenantId, newEnv);
        ThreadUtil.safeSleep(130000);
        ErpSyncDataBackStageEnvironmentEnum env = tenantEnvManager.getTenantAllModelEnv(tenantId);
        return env == newEnv;
    }

    @Test()
    public void testChange() {
        ErpSyncDataBackStageEnvironmentEnum tenantAllModelEnv = tenantEnvManager.getTenantAllModelEnv("84307");
        Assert.assertEquals(tenantAllModelEnv,ErpSyncDataBackStageEnvironmentEnum.NORMAL);
        Assert.assertTrue(changeAndCheck("84307",ErpSyncDataBackStageEnvironmentEnum.VIP));
        Assert.assertTrue(changeAndCheck("84307",ErpSyncDataBackStageEnvironmentEnum.GRAY));
        Assert.assertTrue(changeAndCheck("84307",ErpSyncDataBackStageEnvironmentEnum.NORMAL));
    }
}