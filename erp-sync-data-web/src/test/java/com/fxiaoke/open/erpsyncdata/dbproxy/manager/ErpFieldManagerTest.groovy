package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/12/7
 */
@Ignore
class ErpFieldManagerTest extends BaseTest {
    @Autowired
    private ErpFieldManager erpFieldManager

    @Test
    public void testGet() {
        for (i in 0..<10) {
            def result = erpFieldManager.findByObjApiNameAndType("81138","BD_STOCK.BillHead", [ErpFieldTypeEnum.id])
            println("find result:"+result)
        }
    }
}
