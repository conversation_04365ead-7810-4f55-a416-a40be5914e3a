package com.fxiaoke.open.erpsyncdata.preprocess.impl

import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg
import com.fxiaoke.otherrestapi.function.data.HeaderObj
import com.fxiaoke.otherrestapi.function.result.FunctionResult
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/11
 */
@Ignore
class ErpCRMOuterServiceImplTest extends BaseTest {
    @Autowired
    private ErpOuterServiceImpl erpOuterService

    @Autowired
    private CustomFunctionService customFunctionService

    @Test
    void getDetailTest() {
        def result = erpOuterService.listDetailDatasById(
                "79675",2,"erpSaleOrderProduct",null,"0001")
        println(result)
        assert result.success
    }

    @Test
    void getMasterApiNameTest() {
        def result = erpOuterService.getMasterObjectApiName("79675",2,"erpSaleOrderProduct")
        println(result)
        assert result.success
    }

    @Test
    void testFunctionService(){
        HeaderObj headerObj = new HeaderObj(81138, 1001)
        FunctionServiceFindArg findArg = new FunctionServiceFindArg()
        findArg.setApiName("func_m3g7k__c")
        findArg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME)
        Result<FunctionResult<FunctionServiceFindResult>> result = customFunctionService.find(headerObj,findArg)
        println(result)
    }

    @Test
    public void testGetObjName() {
        erpOuterService.getObjectNameByApiName("81772","BD_Customer.BillHead")
    }
}
