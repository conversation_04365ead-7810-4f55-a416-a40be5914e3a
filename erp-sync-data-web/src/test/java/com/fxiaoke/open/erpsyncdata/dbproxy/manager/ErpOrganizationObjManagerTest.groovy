package com.fxiaoke.open.erpsyncdata.dbproxy.manager


import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpOrganizationObj
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.StopWatch

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
@Ignore
class ErpOrganizationObjManagerTest extends BaseTest {
    @Autowired
    private ErpOrganizationObjManager erpOrganizationObjManager

    @Test
    void testQueryAllErpOrganizationObj() {
        StopWatch stopWatch = new StopWatch("test Cache");
        List<ErpOrganizationObj> objs
        for (int i = 0; i < 10; i++) {
            stopWatch.start(String.format("%s time", i));
            objs = erpOrganizationObjManager.queryDcErpOrganizationObj("81772","642530472589131776")
            stopWatch.stop();
        }
        println(objs)
        System.out.println(stopWatch.prettyPrint());
    }
}
