package com.fxiaoke.open.erpsyncdata.apiproxy.u8;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.U8EaiDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.BaseResult;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8EaiConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

@Ignore
@Slf4j
public class U8EaiTest  extends BaseTest {

    @Autowired
    private U8EaiDataManager eaiDataManager;
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Test
    public void postUrl() {
        Map<String,String> header = new HashMap<>();
        header.put("traceId","100101207");

        String url = "http://122.224.21.50:8091/adapt/proxy/api/queryMasterBatch";
        String body = "{\n" +
                "    \"strategy\": \"DB\",\n" +
                "    \"objApiName\": \"inventory\",\n" +
                "    \"sender\": null,\n" +
                "    \"receiver\": \"u8\",\n" +
                "    \"requestEaiBody\": null,\n" +
                "    \"masterDataIdField\": \"code\",\n" +
                "    \"masterDataSql\": \"select  i.cinvcode as code,  i.cinvname as name, cInvDefine3,\\tfGrossW,bPropertyCheck, (i.cinvname+'-'+i.cinvcode) as alias_of_name ,  i.cinvaddcode as invaddcode,  i.cinvstd as specs, \\t\\t\\t\\t\\ti.cInvMnemCode as cInvMnemCode, \\t\\t\\t\\t\\ti.ccurrencyName as currencyName,  i.cinvccode as sort_code,  ic.cinvcname as sort_name,  i.cComUnitCode as main_measure,  u.cComUnitName as ccomunitname,  i.iGroupType as unitgroup_type,  i.cGroupCode as unitgroup_code,  i.cPUComunitCode as puunit_code,  upu.cComUnitName as puunit_name,  upu.iChangRate as puunit_ichangrate,  i.cSAComunitCode as saunit_code,  usa.cComUnitName as saunit_name,  usa.iChangRate as saunit_ichangrate,  i.cSTComunitCode as stunit_code,  ust.cComUnitName as stunit_name,  ust.iChangRate as stunit_ichangrate, \\t\\t\\t\\t\\ti.iInvSPrice as ref_cost,  i.iInvSCost as ref_sale_price,  i.iInvLSCost as bottom_sale_price,  i.dsdate as start_date,  i.dedate as end_date, \\t\\t  i.cDefWareHouse as defwarehouse, \\t\\t  w.cwhname as defwarehousename, \\t\\t  i.iSupplyType  as iSupplyType,  \\t\\t  s.iDrawType as drawtype,  \\t\\t  s.bsuitretail as  bsuitretail,  i.cinvdefine1 as self_define1,  i.cinvdefine2 as self_define2,  i.cinvdefine3 as self_define3,  i.cinvdefine4 as self_define4,  i.cinvdefine5 as self_define5,  i.cinvdefine6 as self_define6,  i.cinvdefine7 as self_define7,  i.cinvdefine8 as self_define8,  i.cinvdefine9 as self_define9,  i.cinvdefine10 as self_define10,  i.cinvdefine11 as self_define11,  i.cinvdefine12 as self_define12,  i.cinvdefine13 as self_define13,  i.cinvdefine14 as self_define14,  i.cinvdefine15 as self_define15,  i.cinvdefine16 as self_define16, \\t\\t\\t\\t\\ti.iImpTaxRate as iimptaxrate, \\t\\t\\t\\t\\ti.iTaxRate as tax_rate,  i.dModifyDate as ModifyDate,  i.bbarcode,  i.cbarcode as barcode, \\t\\t\\t\\t\\ti.fRetailPrice as fRetailPrice, \\t\\t\\t\\t\\ti.bSale as sale_flag, \\t\\t\\t\\t\\ti.bexpsale as bexpsale,  convert(money, i.pubufts) as timestamp       from inventory i with(nolock) inner join inventoryclass ic with(nolock) on i.cinvccode = ic.cinvccode  Left join Inventory_Sub s with(nolock) on i.cinvccode = s.cInvSubCode \\t\\t   inner join ComputationUnit u with(nolock) on i.ccomunitcode = u.ccomunitcode \\t\\t  left outer join warehouse w with(nolock) on i.cDefWareHouse = w.cwhcode  left outer join ComputationUnit upu with(nolock) on i.cPUComunitCode = upu.ccomunitcode  left outer join ComputationUnit usa with(nolock) on i.cSAComunitCode = usa.ccomunitcode  left outer join ComputationUnit ust with(nolock) on i.cSTComunitCode = ust.ccomunitcode \\t\\t\\t\\t\\twhere 1=1 and (i.dModifyDate>'2022-12-05 10:23:53' and i.dModifyDate<='2022-12-07 11:54:06' )\",\n" +
                "    \"detailDataSql\": {\n" +
                "        \"inventoryEntry\": [\n" +
                "            \"SELECT\\tpartid as iid,\\tinvcode,\\tfree1,\\tfree2,\\tfree3,\\tfree4,\\tfree5,\\tfree6,\\tfree7,\\tfree8,\\tfree9,\\tfree10 FROM\\tbas_part WITH ( nolock ) WHERE\\t1 =1\\tand invcode='#pid'\"\n" +
                "        ]\n" +
                "    },\n" +
                "    \"masterIdQuerySql\": null,\n" +
                "    \"detailIdQuerySql\": null,\n" +
                "    \"excuteMasterSqlList\": null,\n" +
                "    \"excuteDetailSqlList\": null,\n" +
                "    \"queryDataFromCache\": false,\n" +
                "    \"offset\": 0,\n" +
                "    \"limit\": 100,\n" +
                "    \"startTime\": 1670207033954,\n" +
                "    \"endTime\": 1670385246423,\n" +
                "    \"useCombine\": false\n" +
                "}";
//        body = "{\n" +
//                "    \"strategy\": \"DB\",\n" +
//                "    \"objApiName\": \"currentstock\",\n" +
//                "    \"sender\": null,\n" +
//                "    \"receiver\": \"u8\",\n" +
//                "    \"requestEaiBody\": null,\n" +
//                "    \"masterDataIdField\": \"id\",\n" +
//                "    \"masterDataSql\": \"SELECT CS.AutoId as id       ,CS.cExpirationdate as expirationdate       ,E1.enumname as expiratdatecalcu       ,W.cWhCode as whcode       ,W.cWhName as whname       ,I.cInvCode as invcode       ,I.cInvAddCode as invaddcode       ,I.cInvName as invname       ,I.cInvStd as invstd       ,I.cInvCCode  as invccode       ,IC.cInvCName as invcname       ,CU_M.cComUnitName AS invmunit       ,CASE WHEN I.iGroupType = 0 THEN NULL  WHEN I.iGrouptype = 2 THEN CU_A.cComUnitName  WHEN I.iGrouptype = 1 THEN CU_G.cComUnitName END  AS invaunit       ,convert(nvarchar(38),convert(decimal(38,2),CASE WHEN I.iGroupType = 0 THEN NULL      WHEN I.iGroupType = 2 THEN (CASE WHEN CS.iQuantity = 0.0 OR CS.iNum = 0.0 THEN NULL ELSE CS.iQuantity/CS.iNum END)      WHEN I.iGroupType = 1 THEN CU_G.iChangRate END)) AS exchrate       ,cs.cfree1 as free1, cs.cfree2 as free2, cs.cfree3 as free3, cs.cfree4 as free4, cs.cfree5 as free5, cs.cfree6 as free6,CS.cFree7 as free7, cs.cfree8 as free8,CS.cFree9 as free9, cs.cfree10 as free10       ,i.cInvDefine1 as invdefine1, i.cinvdefine2 as invdefine2, i.cinvdefine3 as invdefine3, i.cinvdefine4 as invdefine4, i.cinvdefine5 as invdefine5, i.cinvdefine6 as invdefine6, i.cinvdefine7 as invdefine7, i.cinvdefine8 as invdefine8, i.cinvdefine9 as invdefine9, i.cinvdefine10 as invdefine10, i.cinvdefine11 as invdefine11, i.cinvdefine12 as invdefine12, i.cinvdefine13 as invdefine13, i.cinvdefine14 as invdefine14, i.cinvdefine15 as invdefine15, i.cinvdefine16 as invdefine16       ,cs.cBatch as batch       ,cs.EnumName As sotypename       ,cs.csocode as socode       ,cs.cdemandmemo as demandmemo       ,convert(nvarchar,cs.isoseq) as rowno       ,cs.cvmivencode as vmivencode       ,v1.cvenabbname as cvmivenname       ,isnull(E.enumname,N'') as massunitname       ,CS.dvdate as vdate       ,CS.dmdate as mdate       ,convert(varchar(20),CS.iMassDate) as massdate       ,(iQuantity) AS qty       ,( CASE WHEN iGroupType = 0 THEN 0 WHEN iGroupType = 2 THEN ISNULL(iNum,0) WHEN iGroupType = 1 THEN iQuantity/ CU_G.iChangRate END) AS num       ,CASE WHEN CS.bStopFlag = 1 OR CS.bGspStop = 1 THEN iQuantity ELSE IsNull(fStopQuantity,0) END AS stopqty       ,CASE WHEN CS.bStopFlag = 1 OR CS.bGspStop = 1 THEN (CASE WHEN iGroupType = 0 THEN 0 WHEN iGroupType = 2 THEN ISNULL(iNum,0) WHEN iGroupType = 1 THEN iQuantity/ CU_G.iChangRate END)       ELSE (CASE WHEN iGroupType = 0 THEN 0 WHEN iGroupType = 2 THEN ISNULL(fStopNum,0) WHEN iGroupType = 1 THEN fStopQuantity/ CU_G.iChangRate END) END AS stopnum       ,(fInQuantity) AS inqty       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fInNum,0) WHEN iGroupType = 1 THEN fInQuantity/ CU_G.iChangRate END) AS innum       ,(fTransInQuantity) AS transinqty       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fTransInNum,0) WHEN iGroupType = 1 THEN fTransInQuantity/ CU_G.iChangRate END) AS transinnum       ,(ISNULL(fInQuantity,0) + ISNULL(fTransInQuantity,0)) AS inqtysum       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fInNum,0) + ISNULL(fTransInNum,0) WHEN iGroupType = 1 THEN (ISNULL(fInQuantity,0) + ISNULL(fTransInQuantity,0))/ CU_G.iChangRate END) AS innumsum       ,(fOutQuantity) AS outqty       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fOutNum,0) WHEN iGroupType = 1 THEN fOutQuantity/ CU_G.iChangRate END) AS outnum       ,CS.cBatchProperty1 as batchproperty1       ,CS.cBatchProperty2 as batchproperty2       ,CS.cBatchProperty3 as batchproperty3       ,CS.cBatchProperty4 as batchproperty4       ,CS.cBatchProperty5 as batchproperty5       ,CS.cBatchProperty6 as batchproperty6       ,CS.cBatchProperty7 as batchproperty7       ,CS.cBatchProperty8 as batchproperty8       ,CS.cBatchProperty9 as batchproperty9       ,CS.cBatchProperty10 as batchproperty10       ,(fTransOutQuantity) AS transoutqty       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fTransOutNum,0) WHEN iGroupType = 1 THEN fTransOutQuantity/ CU_G.iChangRate END) AS transoutnum       ,(ISNULL(fOutQuantity,0) + ISNULL(fTransOutQuantity,0)) AS outqtysum       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fOutNum,0) + ISNULL(fTransOutNum,0) WHEN iGroupType = 1 THEN (ISNULL(fOutQuantity,0) + ISNULL(fTransOutQuantity,0))/ CU_G.iChangRate END) AS outnumsum       ,(fDisableQuantity) AS disableqty       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(fDisableNum,0) WHEN iGroupType = 1 THEN fDisableQuantity/ CU_G.iChangRate END) AS disablenum       ,(ipeqty) AS peqty       ,(CASE WHEN iGroupType = 0 THEN NULL WHEN iGroupType=2 THEN ISNULL(ipenum,0) WHEN iGroupType = 1 THEN ipeqty/ CU_G.iChangRate END) AS penum       ,(CASE WHEN bInvBatch=1 THEN  CASE WHEN bStopFlag =1 OR bGSPStop= 1 THEN 0 ELSE ISNULL(iQuantity,0)- IsNull(fStopQuantity,0) END  + ISNULL(fInQuantity,0) - ISNULL(fOutQuantity,0) ELSE  CASE WHEN bStopFlag =1 OR bGSPStop= 1 THEN 0 ELSE ISNULL(iQuantity,0)- IsNull(fStopQuantity,0) END  + ISNULL(fInQuantity,0) - ISNULL(fOutQuantity,0) END) AS availqty       ,dLastCheckDate as lastcheckdate       ,(CASE WHEN iGroupType = 0 THEN 0  WHEN iGroupType = 2 THEN  CASE WHEN bInvBatch=1 THEN  CASE WHEN bStopFlag =1 OR bGSPStop= 1 THEN 0 ELSE ISNULL(iNum,0)- IsNull(fStopNum,0) END  + ISNULL(fInNum,0) - ISNULL(fOutNum,0) ELSE  CASE WHEN bStopFlag =1 OR bGSPStop= 1 THEN 0 ELSE ISNULL(iNum,0)- IsNull(fStopNum,0) END  + ISNULL(fInNum,0) - ISNULL(fOutNum,0) END WHEN iGroupType = 1 THEN  (CASE WHEN bInvBatch=1 THEN  CASE WHEN bStopFlag =1 OR bGSPStop= 1 THEN 0 ELSE ISNULL(iQuantity,0)- IsNull(fStopQuantity,0) END  + ISNULL(fInQuantity,0) - ISNULL(fOutQuantity,0) ELSE  CASE WHEN bStopFlag =1 OR bGSPStop= 1 THEN 0 ELSE ISNULL(iQuantity,0)- IsNull(fStopQuantity,0) END  + ISNULL(fInQuantity,0) - ISNULL(fOutQuantity,0) END)/CU_G.iChangRate ELSE NULL END) AS availnum       ,CS.bstopflag as stopflag       FROM v_ST_currentstockForReport  CS inner join dbo.Inventory I ON I.cInvCode = CS.cInvCode       left join dbo.InventoryClass IC ON IC.cInvCCode = I.cInvCCode LEFT OUTER JOIN dbo.ComputationUnit CU_G ON       I.cSTComUnitCode =CU_G.cComUnitCode       LEFT OUTER JOIN dbo.ComputationUnit CU_A ON I.cAssComUnitCode = CU_A.cComunitCode       LEFT OUTER JOIN dbo.ComputationUnit CU_M ON I.cComUnitCode = CU_M.cComunitCode       LEFT OUTER JOIN dbo.Warehouse W ON CS.cWhCode = W.cWhCode       left join vendor v1 on v1.cvencode = cs.cvmivencode       left join v_aa_enum E1 on E1.enumcode = ISNULL(cs.iExpiratDateCalcu,0) and E1.enumtype=N'SCM.ExpiratDateCalcu'       LEFT OUTER JOIN dbo.v_aa_enum E with (nolock) on E.enumcode=convert(nchar,CS.cMassUnit) and E.enumType=N'ST.MassUnit' where 1=1\",\n" +
//                "    \"detailDataSql\": {},\n" +
//                "    \"masterIdQuerySql\": null,\n" +
//                "    \"detailIdQuerySql\": null,\n" +
//                "    \"excuteMasterSqlList\": null,\n" +
//                "    \"excuteDetailSqlList\": null,\n" +
//                "    \"queryDataFromCache\": true,\n" +
//                "    \"offset\": 0,\n" +
//                "    \"limit\": 1,\n" +
//                "    \"startTime\": 1670233088020,\n" +
//                "    \"endTime\": 1670319488020,\n" +
//                "    \"useCombine\": false\n" +
//                "}";

        try {
            String result = proxyHttpClient.postUrl(url, body, header,new TypeReference<String>() {
            });
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void queryTestLogin(){
        Map<String,String> header=new HashMap<String,String>();
        header.put("Content-Type","application/x-www-form-urlencoded");

//        proxyHttpClient.postUrl("/login","");
    }

    @Test
    public void queryTest(){

        TimeFilterArg timeFilterArg=new TimeFilterArg();
        timeFilterArg.setObjAPIName("currentstock");
        timeFilterArg.setTenantId("82814");
        timeFilterArg.setOffset(0);
        timeFilterArg.setLimit(20);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR,-7);
        timeFilterArg.setStartTime(0L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setOperationType(EventTypeEnum.ADD.getType());

        ErpConnectInfoEntity connectInfoEntity=new ErpConnectInfoEntity();
        connectInfoEntity.setTenantId("1000");
        connectInfoEntity.setChannel(ErpChannelEnum.ERP_U8_EAI);
        connectInfoEntity.setDataCenterName("测试EAI");
        connectInfoEntity.setId("734091863128670208");

        U8EaiConnectParam u8EaiConnectParam=new U8EaiConnectParam();
        u8EaiConnectParam.setBaseUrl("http://localhost:8080/adapt/proxy/api");
        u8EaiConnectParam.setSender("001");
        u8EaiConnectParam.setReciver("u8");
        connectInfoEntity.setConnectParams(JacksonUtil.toJson(u8EaiConnectParam));

        Result<StandardListData> standardListDataResult =
          eaiDataManager.listErpObjDataByTime(timeFilterArg, connectInfoEntity);
        log.info(JacksonUtil.toJson(standardListDataResult));
    }

    @Test
    public void queryTest2(){
        String timeFilterStr="{\"endTime\":1646668800000,\"includeDetail\":true,\"limit\":100,\"objAPIName\":\"warehouse\",\"offset\":0,\"operationType\":2,\"startTime\":1644249600000,\"tenantId\":\"82814\"}";
        TimeFilterArg timeFilterArg=JacksonUtil.fromJson(timeFilterStr,TimeFilterArg.class);

        String connectStr="{\"channel\":\"ERP_U8_EAI\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://**********:8080/adapt/proxy/api\\\",\\\"sender\\\":\\\"999\\\",\\\"reciver\\\":\\\"u8\\\",\\\"authType\\\":1,\\\"userName\\\":\\\"admian\\\",\\\"password\\\":\\\"123456\\\"}\",\"createTime\":1638846823681,\"dataCenterName\":\"ERP_U8_EAI\",\"enterpriseName\":\"Liuxsh001\",\"id\":\"734091863128670208\",\"tenantId\":\"82814\",\"updateTime\":1645018993760}";
        ErpConnectInfoEntity connectInfoEntity=JacksonUtil.fromJson(connectStr,ErpConnectInfoEntity.class);

        Result<StandardListData> standardListDataResult =
          eaiDataManager.listErpObjDataByTime(timeFilterArg, connectInfoEntity);
        log.info(JacksonUtil.toJson(standardListDataResult));
    }
    @Test
    public void queryTest3(){

        String timeFilterStr="{\n" + "    \"endTime\": 1641571199000,\n" + "    \"includeDetail\": true,\n"
          + "    \"limit\": 100,\n" + "    \"objAPIName\": \"inventory\",\n" + "    \"offset\": 0,\n"
          + "    \"operationType\": 2,\n" + "    \"startTime\": 1607011200000,\n" + "    \"tenantId\": \"82814\"\n"
          + "}";
        TimeFilterArg timeFilterArg=JacksonUtil.fromJson(timeFilterStr,TimeFilterArg.class);

        String connectStr="{\n" + "    \"channel\": \"ERP_U8_EAI\",\n"
          + "    \"connectParams\": \"{\\\"baseUrl\\\": \\\"http://**********:8080/dev/adapt/proxy/api\\\",\\\"sender\\\": \\\"5bd164c806c6c8\\\",\\\"reciver\\\": \\\"u8\\\",\\\"authType\\\": 1,\\\"userName\\\": \\\"admin\\\",\\\"password\\\": \\\"test123456\\\"}\",\n"
          + "    \"createTime\": 1638846823681,\n" + "    \"dataCenterName\": \"ERP_U8_EAI\",\n"
          + "    \"enterpriseName\": \"Liuxsh001\",\n" + "    \"id\": \"734091863128670208\",\n"
          + "    \"tenantId\": \"82814\",\n" + "    \"updateTime\": 1638846823681\n" + "}";
        ErpConnectInfoEntity connectInfoEntity=JacksonUtil.fromJson(connectStr,ErpConnectInfoEntity.class);

        Result<StandardListData> standardListDataResult =
          eaiDataManager.listErpObjDataByTime(timeFilterArg, connectInfoEntity);
        log.info(JacksonUtil.toJson(standardListDataResult));
    }

    @Test
    public void getTest(){

        ErpIdArg erpIdArg=new ErpIdArg();
        erpIdArg.setObjAPIName("saleorder");
        erpIdArg.setTenantId("12344");
        erpIdArg.setDataId("1000000001");

        ErpConnectInfoEntity connectInfoEntity=new ErpConnectInfoEntity();
        connectInfoEntity.setTenantId("1000");
        connectInfoEntity.setChannel(ErpChannelEnum.ERP_U8_EAI);
        connectInfoEntity.setDataCenterName("测试EAI");
        connectInfoEntity.setId("111111111111");

        U8EaiConnectParam u8EaiConnectParam=new U8EaiConnectParam();
        u8EaiConnectParam.setBaseUrl("http://localhost:8080/dev/adapt/proxy/api");
        u8EaiConnectParam.setSender("001");
        u8EaiConnectParam.setReciver("u8");
        connectInfoEntity.setConnectParams(JacksonUtil.toJson(u8EaiConnectParam));

        Result<StandardData> erpObjData = eaiDataManager.getErpObjData(erpIdArg, connectInfoEntity);
        log.info(JacksonUtil.toJson(erpObjData));
    }

    @Test
    public void createTest(){

     /*   StandardData standardData =new StandardData();
        standardData.setObjAPIName("saleorder");
        ObjectData masterFieldVal=new ObjectData();
        masterFieldVal.put("typecode","01");
        masterFieldVal.put("date","2021-05-28 00:00:00");
        masterFieldVal.put("code","0000000001");
        masterFieldVal.put("custcode","00000001");
        masterFieldVal.put("deptcode","100");
        masterFieldVal.put("personcode","001");
        masterFieldVal.put("currency","人民币");
        masterFieldVal.put("currencyrate","1");
        masterFieldVal.put("taxrate","13");
        masterFieldVal.put("maker","test1");
        masterFieldVal.put("businesstype","普通销售");
        masterFieldVal.put("disflag","0");
        masterFieldVal.put("cusname","测试客户00001");
        masterFieldVal.put("dpredatebt","2021-05-28 00:00:00");
        masterFieldVal.put("dpremodatebt","2021-05-28 00:00:00");
        masterFieldVal.put("bmustbook","0");
        standardData.setMasterFieldVal(masterFieldVal);

        Map<String, List<ObjectData>> details= new HashMap<>();
        standardData.setDetailFieldVals(details);
        List<ObjectData>entrys=new ArrayList<>();
        details.put("saleorderEntry",entrys);
        ObjectData detailObj=new ObjectData();
        entrys.add(detailObj);
        detailObj.put("id","1000000001");
        detailObj.put("inventorycode","00000001");
        detailObj.put("preparedate","2021-05-28 00:00:00");
        detailObj.put("quantity","10");
        detailObj.put("num","2");
        detailObj.put("quotedprice","0");
        detailObj.put("unitprice","0");
        detailObj.put("taxunitprice","0");
        detailObj.put("money","0");
        detailObj.put("tax","0");
        detailObj.put("sum","0");
        detailObj.put("assistantunit","000002");
        detailObj.put("discount","0");
        detailObj.put("natunitprice","0");
        detailObj.put("natmoney","0");
        detailObj.put("nattax","0");
        detailObj.put("natsum","0");
        detailObj.put("natdiscount","0");
        detailObj.put("mid","1000000001");
        detailObj.put("discountrate","100");
        detailObj.put("discountrate2","100");
        detailObj.put("taxrate","13");
        detailObj.put("irowno","1");
        detailObj.put("unitrate","5");
        detailObj.put("unitcode","000002");
        detailObj.put("dpredate","2021-05-28 00:00:00");
        detailObj.put("dpremodate","2021-05-28 00:00:00");
        detailObj.put("bsaleprice","1");
        detailObj.put("bgift","0");
        detailObj.put("fcusminpricgetOrCreateSyncDataMappingByTwoWaye","0");

        ErpConnectInfoEntity connectInfoEntity=new ErpConnectInfoEntity();
        connectInfoEntity.setTenantId("82814");
        connectInfoEntity.setChannel(ErpChannelEnum.ERP_U8_EAI);
        connectInfoEntity.setDataCenterName("测试EAI");
        connectInfoEntity.setId("732989845689466880");

        U8EaiConnectParam u8EaiConnectParam=new U8EaiConnectParam();
        u8EaiConnectParam.setBaseUrl("http://localhost:8080/dev/adapt/proxy/api");
        u8EaiConnectParam.setSender("001");
        u8EaiConnectParam.setReciver("u8");
        connectInfoEntity.setConnectParams(JacksonUtil.toJson(u8EaiConnectParam));*/

        String str="{\"detailFieldVals\":{},\"masterFieldVal\":{\"name\":\"测试产品00005\",\"retailprice\":\"233.00\",\"code\":\"00000005\",\"sort_code\":\"0101\",\"object_describe_api_name\":\"inventory\",\"tenant_id\":\"82814\",\"_id\":\"61c9b1c656ce9c00019db39c\"},\"objAPIName\":\"inventory\"}";
        String str2="{\"channel\":\"ERP_U8_EAI\",\"connectParams\":\"{\\\"baseUrl\\\": \\\"http://**********:8080/dev/adapt/proxy/api\\\",\\\"sender\\\": \\\"001\\\",\\\"reciver\\\": \\\"u8\\\",\\\"authType\\\": 1,\\\"userName\\\": \\\"admin\\\",\\\"password\\\": \\\"test123456\\\"}\",\"createTime\":1638846823681,\"dataCenterName\":\"ERP_U8_EAI\",\"enterpriseName\":\"Liuxsh001\",\"id\":\"734091863128670208\",\"tenantId\":\"82814\",\"updateTime\":1638846823681}";
        StandardData standardData=JacksonUtil.fromJson(str,StandardData.class);
        ErpConnectInfoEntity connectInfoEntity=JacksonUtil.fromJson(str2,ErpConnectInfoEntity.class);
        Result<ErpIdResult> erpObjData = eaiDataManager.createErpObjData(standardData, connectInfoEntity);
        log.info(JacksonUtil.toJson(erpObjData));
    }

}
