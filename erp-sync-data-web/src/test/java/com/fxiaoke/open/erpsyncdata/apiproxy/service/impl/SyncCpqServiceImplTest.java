package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 15:22 2020/12/28
 * @Desc:
 */
@Ignore
public class SyncCpqServiceImplTest extends BaseTest {

    @Autowired
    private SyncCpqService syncCpqService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataActionService metadataActionService;

    @Test
    public void listErpProductByTime() {
        HeaderObj headerObj = new HeaderObj(82777, CrmConstants.SYSTEM_USER);
        Result<ObjectDataGetByIdResult> result = objectDataService.getById(headerObj, "SalesOrderObj", "623062462777220001419973", false);
        System.out.println(result);
    }

    @Test
    public void handleSapCpq() {
    }

    @Test
    public void getErpProductById() {
    }

    @Test
    public void test() {
        Integer tenantId=79675;
        HeaderObj headerObj = new HeaderObj(tenantId, CrmConstants.SYSTEM_USER);
        Result<ObjectDataGetByIdResult> byId = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(), "5fe42f8c2dfc710001f17feb", false, false, false, false);
        ActionEditArg actionEditArg = new ActionEditArg();
        byId.getData().getObjectData().put("bom_path", byId.getData().getObjectData().get("parent_bom_id")+"."+byId.getData().getObjectData().get("_id"));
        actionEditArg.setObjectData(byId.getData().getObjectData());
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(),false,false, null, null, actionEditArg);
        Result<ObjectDataGetByIdResult> byId1 = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(), "5fe442112dfc710001f19ac7", false, false, false, false);

        Result<ObjectDataGetByIdResult> byId2 = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(), "5fd0b57a8e54fc000184d3f1", false, false, false, false);
        System.out.println("");
    }
}