package com.fxiaoke.PureTest;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.BaseObjResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.Submit;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/3
 */
public class SdkTest {

    private static final Configuration CONFIGURATION = Configuration.defaultConfiguration()
            .setOptions(Option.SUPPRESS_EXCEPTIONS,Option.DEFAULT_PATH_LEAF_TO_NULL,Option.ALWAYS_RETURN_LIST)
            .jsonProvider(new JacksonJsonProvider());

    private static void testSubmit(K3CloudApiClient apiClient){
        Submit.BaseArg arg = new Submit.BaseArg();
        arg.setIds("718698");
        Result<Submit.Result> submitResult = apiClient.submit("BD_Customer", arg);
        System.out.println(JacksonUtil.toJson(submitResult));
        Result<Submit.Result> auditResult = apiClient.audit("BD_Customer", arg);
        System.out.println(JacksonUtil.toJson(auditResult));
    }

    private static void testLotDup(K3CloudApiClient apiClient){
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BATCH_OBJ);
        queryArg.setFieldKeys("FNumber,FMaterialId.FNumber,FBizType");
        queryArg.appendEqualFilter("FCreateOrgId.FNumber","1");
        queryArg.setOrderString("FNumber ASC");
        List<K3Model> listResult = apiClient.queryAll(queryArg).getData();
        long allC = listResult.size();
        long count = listResult.stream().distinct().count();
        System.out.println(allC+","+count);
    }

    private static void testStockDup(K3CloudApiClient apiClient){
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.STK_Inventory);
        queryArg.setFieldKeys("FStockId.FNumber,FMaterialId.FNumber,FLot.FNumber,FStocklocId");
        queryArg.appendEqualFilter("FStockOrgId.FNumber","1");
        List<K3Model> listResult = apiClient.queryAll(queryArg).getData();
        long allC = listResult.size();
        long count = listResult.stream().distinct().count();
        System.out.println(allC+","+count);
        Set<K3Model> collect = new HashSet<>();
        for (K3Model k3Model : listResult) {
            if (collect.contains(k3Model)){
                System.out.println(k3Model);
                return;
            }
            collect.add(k3Model);
        }
        System.out.println("no dup");
    }

    public static void testReLogin() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/K3Cloud/",
                "5ec229fad54306", "ces1", "8888888");
        K3CloudApiClient k3CloudApiClient = new K3CloudApiClient("http://172.31.100.60/K3Cloud/");
        k3CloudApiClient.setConnectParam(connectParam);
        Result<List<BaseObjResult>> listResult = k3CloudApiClient.queryDataCenterList();
        System.out.println(listResult);
        QueryBusinessInfoArg stk_inventory = new QueryBusinessInfoArg("STK_Inventory");
        Result<QueryBusinessInfoResult> queryBusinessInfoResultResult = k3CloudApiClient.queryBusinessInfo(stk_inventory);
        System.out.println(queryBusinessInfoResultResult);
    }

    public static void main(String[] args) {
        testReLogin();
    }

}
