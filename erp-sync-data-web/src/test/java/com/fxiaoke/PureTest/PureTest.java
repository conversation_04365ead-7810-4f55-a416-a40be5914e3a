package com.fxiaoke.PureTest;

import com.fxiaoke.open.erpsyncdata.apiproxy.utils.TimeUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.FeatureCalculation;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.Md5Util;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/24
 */
@Slf4j
public class PureTest {

    @Data
    private static class Demo{
        Map<String,String> map ;
        String name;
    }

    public static void main(String[] args) {
        Map<String,String> fields  = new HashMap<>();
        for (int i = 0; i < 100; i++) {
            fields.put("field"+i,UUID.randomUUID().toString());
        }
        Map<String,String>  fieldsCopy = new HashMap<>();
        fieldsCopy = BeanUtil.deepCopy(fields,Map.class);
        System.out.println(fields);
        System.out.println(fieldsCopy);
        StopWatch stopWatch = new StopWatch("test equals");
        stopWatch.start("md5");
        String s1 = Md5Util.md5(JacksonUtil.toJson(fields));
        String s2 = Md5Util.md5(JacksonUtil.toJson(fieldsCopy));
        System.out.println(s1.equals(s2));
        stopWatch.stop();
        stopWatch.start("direct equal");
        Map<String, String> finalFieldsCopy = fieldsCopy;
        boolean b = fields.entrySet().stream().allMatch(k -> JacksonUtil.toJson(finalFieldsCopy.get(k.getKey())).equals(JacksonUtil.toJson(k.getValue())));
        System.out.println(b);
        stopWatch.stop();

        fields.clear();
        fieldsCopy.clear();
        for (int i = 0; i < 100; i++) {
            fields.put("field"+i,UUID.randomUUID().toString());
        }
        fieldsCopy = BeanUtil.deepCopy(fields,Map.class);
        System.out.println(fields);
        System.out.println(fieldsCopy);
        stopWatch.start("md52");
        s1 = Md5Util.md5(JacksonUtil.toJson(fields));
        s2 = Md5Util.md5(JacksonUtil.toJson(fieldsCopy));
        System.out.println(s1.equals(s2));
        stopWatch.stop();
        stopWatch.start("direct equal2");
        Map<String, String> finalFieldsCopy1 = fieldsCopy;
        boolean b2 = fields.entrySet().stream().allMatch(k -> JacksonUtil.toJson(finalFieldsCopy1.get(k.getKey())).equals(JacksonUtil.toJson(k.getValue())));
        System.out.println(b2);
        stopWatch.stop();
        System.out.println(stopWatch.prettyPrint());
    }
}
