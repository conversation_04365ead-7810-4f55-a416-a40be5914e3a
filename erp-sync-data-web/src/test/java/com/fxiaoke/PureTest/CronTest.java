package com.fxiaoke.PureTest;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.CronPattern;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.CronPatternBuilder;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.Part;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/9/9
 */
public class CronTest {
    private static final Map<String, CronPattern> cronPatternMap = new ConcurrentHashMap<>();

    public static void main(String[] args) {
        String cronExpression = "0 0 * * 7";
        DateTime dateTime = new DateTime("2022-11-27 00:00:30");
        //当cron表达式不为空时，都使用这个
        CronPattern cronPattern = cronPatternMap.computeIfAbsent(cronExpression, CronPattern::of);
        System.out.println(cronPattern.match(dateTime.getTime(), false));

        CronPatternBuilder patternBuilder = CronPatternBuilder.of();
        patternBuilder.setValues(Part.MINUTE,0);
        patternBuilder.setValues(Part.HOUR,0);
        patternBuilder.setValues(Part.DAY_OF_WEEK,7);
        String cron = patternBuilder.build();
        cronPattern = cronPatternMap.computeIfAbsent(cron, CronPattern::of);
        System.out.println(cronPattern.match(dateTime.getTime(), false));
    }
}
