package com.fxiaoke.i18n;

import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/13 14:51:58
 */
public class AddIngoreI18nNote {

    String filePath = "/Users/<USER>/Downloads/iLangScan/fs-erp-sync-data-git.txt"; // 替换为实际文件路径
    String directoryPath = "/Users/<USER>/workspaces/java/fs-erp-sync-data-git"; // 替换为源代码目录路径
    Map<File, List<Integer>> classLineMap = new HashMap<>();

    @Test
    public void name() {
        readAndFindFiles(filePath, directoryPath);

        classLineMap.forEach((name, lines) -> {
            try {
                String content = FileUtils.readFileToString(name, "UTF-8");
                for (Integer line : lines) {
                    String lineContent = content.split("\n")[line - 1];
                    if (lineContent.contains("ingoreI18n")) {
                        continue;
                    }
                    String newLineContent = lineContent + "   // ignoreI18n";
                    content = content.replace(lineContent, newLineContent);
                }
                FileUtils.writeStringToFile(name, content, "UTF-8");
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("文件：" + name.getName() + "处理失败");
            }
        });
    }

    private void readAndFindFiles(String filePath, String directoryPath) {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            Path sourceDirectory = Paths.get(directoryPath); // 项目的源文件目录

            String line;
            Map<String, List<Integer>> nameLineMap = new HashMap<>();
            while ((line = reader.readLine()) != null) {
                final String java = StringUtils.substringBefore(line, ":");
                final List<Integer> lines = nameLineMap.computeIfAbsent(java, k -> new ArrayList<>());
                lines.add(Integer.valueOf(StringUtils.substringBetween(line, ":", " ")));
            }
            findSourceFiles(sourceDirectory, nameLineMap);
        } catch (IOException e) {
            System.err.println("Error reading file: " + filePath);
            e.printStackTrace();
        }
    }

    private void findSourceFiles(Path sourceDirectory, Map<String, List<Integer>> nameLineMap) {
        classLineMap = nameLineMap.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> findSourceFile(sourceDirectory, entry.getKey()),
                        Map.Entry::getValue
                ));

        classLineMap.remove(null);
    }

    @SneakyThrows
    private File findSourceFile(Path sourceDirectory, String className) {
        String name = className.replace('.', '/') + ".java";
        try (Stream<Path> stream = Files.walk(sourceDirectory)) {
            return stream.filter(path -> !path.toFile().isDirectory() &&
                            !path.toString().contains("/target/") &&
                            !path.toString().contains("/test/") &&
                            path.toString().endsWith(".java")
                    ).filter(path -> path.toString().contains(name))
                    .findFirst()
                    .map(Path::toFile)
                    .orElseGet(() -> {
                        System.out.println("File not found: " + name);
                        return null;
                    });
        }
    }
}
