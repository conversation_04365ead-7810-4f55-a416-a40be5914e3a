package com.fxiaoke.open.erpsyncdata.web.controller.setUp

import com.alibaba.excel.annotation.ExcelProperty
import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.admin.model.ExportDistrictMapping
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingsResult
import com.fxiaoke.open.erpsyncdata.admin.result.IntegrationViewResult
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.reflect.FieldUtils
import spock.lang.Specification
import spock.lang.Unroll

import java.util.stream.Collectors

/**
 * <AUTHOR> 
 * @date 2023/5/4 20:23:40
 */
class ExcelFileCepControllerTest extends Specification {


    def "getExcelSheetArgs"() {
        when:
        def s = "[{\"broken\":false,\"destDc\":{\"dcChannel\":\"CRM\",\"dcId\":\"780777210996457472\",\"dcName\":\"纷享销客\",\"tenantId\":\"84801\"},\"destObjectApiName\":\"AccountObj\",\"destObjectName\":\"客户\",\"destTenantDatas\":[{\"tenantId\":\"84801\",\"tenantName\":\"(84801)测试企业112_杨贤杰\"}],\"destTenantType\":1,\"detailObjectMappings\":[{\"destObjectApiName\":\"AccountAddrObj\",\"destObjectName\":\"客户地址\",\"fieldMappings\":[{\"destApiName\":\"account_id\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceTargetApiName\":\"BD_Customer.BillHead\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"account_id\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"fake_master_detail\",\"sourceTargetApiName\":\"BD_Customer.BillHead\",\"sourceType\":\"master_detail\",\"value\":\"\"},{\"destApiName\":\"address\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FADDRESS1\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"sourceObjectName\":\"客户-地址信息\"}],\"id\":\"c44791297f9b4b5d9aedca5b4a462842\",\"integrationStreamName\":\"未命名集成流\",\"sourceDc\":{\"dcChannel\":\"ERP_K3CLOUD\",\"dcId\":\"780777150699143168\",\"dcName\":\"金蝶K3Cloud\",\"tenantId\":\"84801\"},\"sourceObjectApiName\":\"BD_Customer.BillHead\",\"sourceObjectName\":\"客户\",\"sourceTenantDatas\":[{\"tenantId\":\"84801\",\"tenantName\":\"(84801)测试企业112_杨贤杰\"}],\"sourceTenantType\":2,\"status\":1,\"statusName\":\"启用\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 4-22 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"22:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"04:40\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"broken\":false,\"destDc\":{\"dcChannel\":\"ERP_K3CLOUD\",\"dcId\":\"780777150699143168\",\"dcName\":\"金蝶K3Cloud\",\"tenantId\":\"84801\"},\"destObjectApiName\":\"BD_SAL_PriceList.BillHead\",\"destObjectName\":\"销售价目表\",\"destTenantDatas\":[{\"tenantId\":\"84801\",\"tenantName\":\"(84801)测试企业112_杨贤杰\"}],\"destTenantType\":2,\"detailObjectMappings\":[{\"destObjectApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\"destObjectName\":\"销售价目表-价格明细\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destTargetApiName\":\"BD_SAL_PriceList.BillHead\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"pricebook_id\",\"sourceTargetApiName\":\"PriceBookObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FPrice\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"stand_price\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"price\",\"sourceQuoteFieldType\":\"currency\",\"sourceQuoteRealField\":\"product_id\",\"sourceType\":\"quote\",\"value\":\"\"},{\"destApiName\":\"FUnitID.FNumber\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"unit\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceType\":\"quote\",\"value\":\"\"}],\"sourceObjectApiName\":\"PriceBookProductObj\",\"sourceObjectName\":\"价目表明细\"},{\"destObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"destObjectName\":\"销售价目表-适用客户\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destTargetApiName\":\"BD_SAL_PriceList.BillHead\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"price_book_id\",\"sourceTargetApiName\":\"PriceBookObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"DetailId\",\"destType\":\"id\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"_id\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"PriceBookAccountObj\",\"sourceObjectName\":\"价目表适用客户\"}],\"id\":\"44d79da1ac91474095a5faa845cf89ad\",\"integrationStreamName\":\"销售价目表 CRM往ERP\",\"sourceDc\":{\"dcChannel\":\"CRM\",\"dcId\":\"780777210996457472\",\"dcName\":\"纷享销客\",\"tenantId\":\"84801\"},\"sourceObjectApiName\":\"PriceBookObj\",\"sourceObjectName\":\"价目表\",\"sourceTenantDatas\":[{\"tenantId\":\"84801\",\"tenantName\":\"(84801)测试企业112_杨贤杰\"}],\"sourceTenantType\":1,\"status\":1,\"statusName\":\"启用\",\"syncRules\":{\"events\":[1,2,3,5,8],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]"
        def s1 = "{\"destTenantIds\":[\"84801\"],\"detailObjectMappings\":[{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectName\":\"销售订单-订单明细\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destType\":\"master_detail\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_id\",\"sourceName\":\"订单\",\"sourceTargetApiName\":\"SalesOrderObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FUnitID.FNumber\",\"destName\":\"销售单位(编码)\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"Pcs\",\"valueType\":1},{\"destApiName\":\"FIsFree\",\"destName\":\"是否赠品\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"FStockUnitID.FNumber\",\"destName\":\"库存单位(编码)\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"Pcs\",\"valueType\":1},{\"destApiName\":\"FMaterialId.FNumber\",\"destName\":\"物料编码(编码)\",\"destTargetApiName\":\"BD_MATERIAL.BillHead\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"product_id\",\"sourceName\":\"产品名称\",\"sourceTargetApiName\":\"ProductObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FQty\",\"destName\":\"销售数量\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"quantity\",\"sourceName\":\"数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"FTaxPrice\",\"destName\":\"含税单价\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"sales_price\",\"sourceName\":\"销售单价\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"FEntryNote\",\"destName\":\"备注\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"FAllAmount\",\"destName\":\"价税合计\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"subtotal\",\"sourceName\":\"小计\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"FRowType\",\"destName\":\"产品类型\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"Son\",\"sourceOption\":\"option1\"},{\"destOption\":\"Standard\",\"sourceOption\":\"eBwwD61uD\"},{\"destOption\":\"Parent\",\"sourceOption\":\"czUmdez5L\"},{\"destOption\":\"Server\",\"sourceOption\":\"6aUT25Pi7\"}],\"sourceApiName\":\"field_hd5K2__c\",\"sourceName\":\"产品类型\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"prod_pkg_key\",\"destName\":\"产品组合虚拟key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"prod_pkg_key\",\"sourceName\":\"产品组合虚拟key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"root_prod_pkg_key\",\"destName\":\"产品组合虚拟root key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"root_prod_pkg_key\",\"sourceName\":\"产品组合虚拟root key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"parent_prod_pkg_key\",\"destName\":\"产品组合虚拟parent key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parent_prod_pkg_key\",\"sourceName\":\"产品组合虚拟parent key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FUnitID.FNumber\",\"destName\":\"销售单位(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"Pcs\",\"sourceOption\":\"1\"}],\"sourceApiName\":\"unit\",\"sourceName\":\"单位\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceType\":\"quote\",\"value\":\"\",\"valueType\":1}],\"sourceObjectApiName\":\"SalesOrderProductObj\",\"sourceObjectName\":\"订单产品\"},{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderPlan\",\"destObjectName\":\"销售订单-收款计划\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destType\":\"master_detail\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_080EO__c\",\"sourceName\":\"销售订单\",\"sourceTargetApiName\":\"SalesOrderObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FRecAdvanceAmount\",\"destName\":\"应收金额\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_PkTsr__c\",\"sourceName\":\"计划回款金额\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"FRecAdvanceRate\",\"destName\":\"应收比例(%)\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_9V81j__c\",\"sourceName\":\"应收比例\",\"sourceType\":\"percentile\",\"value\":\"\"},{\"destApiName\":\"FMustDate\",\"destName\":\"到期日\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_7s10D__c\",\"sourceName\":\"计划回款日期\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"object_36FOK__c\",\"sourceObjectName\":\"收款计划\"}],\"masterObjectMappings\":{\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectName\":\"销售订单\",\"fieldMappings\":[{\"destApiName\":\"FSaleOrgId.FNumber\",\"destName\":\"销售组织(编码)\",\"destTargetApiName\":\"ORG_Organizations.BillHead\",\"destType\":\"object_reference\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"000\",\"valueType\":1},{\"destApiName\":\"FBillTypeID.FNumber\",\"destName\":\"单据类型(编码)\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"XSDD01_SYS\",\"valueType\":1},{\"destApiName\":\"FSaleOrderFinance.FExchangeRate\",\"destName\":\"汇率\",\"destType\":\"number\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"FChangeReason\",\"destName\":\"变更原因\",\"destType\":\"text\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"编辑了订单\",\"valueType\":1},{\"destApiName\":\"FSalerId.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"00665\",\"valueType\":1},{\"destApiName\":\"FCustId.FNumber\",\"destName\":\"客户(编码)\",\"destTargetApiName\":\"BD_Customer.BillHead\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FDate\",\"destName\":\"日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_time\",\"sourceName\":\"下单日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"FSalerId.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceName\":\"负责人\",\"sourceType\":\"employee\",\"value\":\"owner\"},{\"destApiName\":\"FNote\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"FSaleOrderFinance.FBillAllAmount\",\"destName\":\"价税合计\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_amount\",\"sourceName\":\"销售订单金额(元)\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"FSaleDeptId.FNumber\",\"destName\":\"销售部门(编码)\",\"destType\":\"department\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"data_own_department\",\"sourceName\":\"归属部门\",\"sourceType\":\"department\",\"value\":\"\"},{\"destApiName\":\"FSalePhaseID.FNumber\",\"destName\":\"销售阶段(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_3qdNO__c\",\"sourceName\":\"单选\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"sourceObjectApiName\":\"SalesOrderObj\",\"sourceObjectName\":\"销售订单\"},\"sourceTenantIds\":[\"84801\"]}"
        def array = JSON.parseArray(s, IntegrationViewResult.class)

        def controller = new ExcelFileCepController(
                adminSyncPloyDetailService: Mock(AdminSyncPloyDetailService) {
                    getFieldMappingsById(*_) >> Result.newSuccess(JSON.parseObject(s1, FieldMappingsResult.class))
                },
                integrationStreamService: Mock(IntegrationStreamService),
                i18NStringManager: Mock(I18NStringManager) {
                    get(_, _, _) >> { I18NStringEnum enumValue, String key, String defaultValue ->
                        enumValue.getI18nValue()
                    }
                }
        )

        def args = controller.getExcelSheetArgs("84801", "123", array,"zh-CN")

        def manager = new DBFileManager()
        def stream = manager.createExcelStream('84801', 'zh-CN', "测试.xlsx", args)

        then:
        FileUtils.writeByteArrayToFile(new File("测试.xlsx"), stream.toByteArray())
    }

    @Unroll
    def "测试重名"() {
        when:
        Map<String, Integer> map = [:]
        def collect = names.stream().map {
            def result = new IntegrationViewResult()
            result.setIntegrationStreamName(it)
            return result
        }.collect(Collectors.toList())
        def controller = new ExcelFileCepController(
                i18NStringManager: Mock(I18NStringManager) {
                    get(_, _, _) >> { I18NStringEnum enumValue, String key, String defaultValue ->
                        enumValue.getI18nValue()
                    }
                    getByEi(*_) >> ""
                }
        )
        for (final def c in collect) {
            controller.resetName("84801", map, c)
        }
        def result = collect.stream().map { it.getIntegrationStreamName() }.collect(Collectors.toList())

        then:
        result == data

        where:
        names                                                  | data
        ["", "", ""]                                           | ["", "(1)", "(2)"]
        ["", "(1)", ""]                                        | ["", "(1)", "(1)(1)"]
        ["", "", "", "(1)", "(2)"]                             | ["", "(1)", "(2)", "(1)(1)", "(2)(1)"]
        ["", "", "(1)", "(1)(1)", "(1)(1)(1)", "(1)(1)(1)(1)"] | ["", "(1)", "(1)(1)", "(1)(1)(1)", "(1)(1)(1)(1)", "(1)(1)(1)(1)(1)"]
    }

    def "获取excel header"() {
        expect:
        final List<String> heads = Arrays.stream(FieldUtils.getAllFields(ExportDistrictMapping.DataFlowDiagram.class))
                .map({field -> field.getAnnotation(ExcelProperty.class)})
                .filter({ Objects.nonNull(it)})
                .sorted(Comparator.comparingInt({it.index()}))
                .map({annotation ->
                    final String[] value = annotation.value();
                    final String key = value[0];
                    return key;
                }).collect(Collectors.toList())
        println heads
    }
}
