package com.fxiaoke.open.erpsyncdata.web.interceptor

import com.alibaba.fastjson2.JSON
import com.facishare.asm.api.auth.AuthXC
import com.facishare.asm.api.enums.ValidateStatus
import com.facishare.asm.api.model.CookieToAuth
import com.facishare.asm.api.service.ActiveSessionAuthorizeService
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import groovy.util.logging.Slf4j
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import spock.lang.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import javax.servlet.http.Cookie

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Ignore
class UserInterceptorsTest extends Specification {

    private ActiveSessionAuthorizeService activeSessionAuthorizeService
    private TenantEnvManager tenantEnvManager
    private UserRoleManager userRoleManager
    private UserInterceptors userInterceptors

    void setup() {
        //预定义一些依赖项的输出
        activeSessionAuthorizeService = Stub(ActiveSessionAuthorizeService)
        tenantEnvManager = Stub(TenantEnvManager)
        userRoleManager = Stub(UserRoleManager)

        //假cookie分割成ea,ei和userId,.分割
        activeSessionAuthorizeService.cookieToAuthXC(_ as CookieToAuth.Argument) >> { CookieToAuth.Argument arg ->
            def mockCookie = arg.getCookie()
            if (mockCookie) {
                def authXC = new AuthXC()
                def split = mockCookie.split("\\.")
                authXC.setEnterpriseAccount(split[0])
                authXC.setEnterpriseId(Integer.parseInt(split[1]))
                authXC.setEmployeeId(Integer.parseInt(split[2]))
                return CookieToAuth.Result.newInstance(authXC)
            }
            return CookieToAuth.Result.newInstance(false, ValidateStatus.SESSION_TIMEOUT)
        }

        userRoleManager.checkAdminAuth("83952", "1000") >> true
        userRoleManager.checkAdminAuth(_, _) >> false

        tenantEnvManager.checkTenantWebEnvironment("83952") >> true
        tenantEnvManager.checkTenantWebEnvironment(_) >> false

        userInterceptors = new UserInterceptors(
                activeSessionAuthorizeService: activeSessionAuthorizeService,
                tenantEnvManager: tenantEnvManager,
                userRoleManager: userRoleManager
        )
    }

    @Unroll
    def "test superAdmin with cookie"() {
        given: "a request and response"
        def request = new MockHttpServletRequest()
        request.requestURI = "/erp/syncdata/superadmin/opstool"
        def response
        boolean result

        when: "superAdmin with error cookie fstest"
        response = new MockHttpServletResponse()
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then: "通过"
        checkResultAndUser(result, "83952", 83952, 1000)

        when: "superAdmin with error cookie firstshare"//模拟线上环境
        response = new MockHttpServletResponse()
        setFirstshareEnv()
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then: "不通过"
        !result
        checkResponseResult(response, ResultCodeEnum.NO_USER)

        when: "superAdmin with right cookie fstest"
        response = new MockHttpServletResponse()
        setFsTestEnv()
        request.setCookies(new Cookie("FSAuthXC", "83952.83952.1000"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then: "通过"
        checkResultAndUser(result, "83952", 83952, 1000)
    }

    @Unroll
    def "test superAdmin with token "() {
        given: "a request and response"
        def request = new MockHttpServletRequest()
        def response = new MockHttpServletResponse()
        request.requestURI = "/erp/syncdata/superadmin/opstool"
        boolean result
        //模拟线上环境
        setFirstshareEnv()

        when: "superAdmin with right token header"
        response = new MockHttpServletResponse()
        def vo = new UserVo()
        vo.setEnterpriseAccount("83952")
//        vo.setEnterpriseId(83952)
        vo.setEmployeeId(1000)
        def token = SuperAdminTokenUtil.generateToken(vo)
        request.setCookies(Cookie.newInstance(SuperAdminTokenUtil.erpDssToken, token))
        result = userInterceptors.preHandle(request, response, userInterceptors)

        then: "通过"
        checkResultAndUser(result, "83952", null, 1000)


        when: "superAdmin with error token header "
        response = new MockHttpServletResponse()
        token = token + "1"
        request.removeHeader(SuperAdminTokenUtil.erpDssToken)
        request.setCookies(Cookie.newInstance(SuperAdminTokenUtil.erpDssToken, token))
        result = userInterceptors.preHandle(request, response, userInterceptors)

        then: "不通过"
        !result
        checkResponseResult(response, ResultCodeEnum.NO_USER)
    }


    @Unroll
    def "test fsUser"() {
        given: "a request and response"
        def request = new MockHttpServletRequest()
        def response = new MockHttpServletResponse()
        request.requestURI = "/erp/syncdata/fstool"
        boolean result


        when: " with null user fstest"
        response = new MockHttpServletResponse()
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        //默认值
        checkResultAndUser(result, "83952", 83952, 1000)

        when: " with error user fstest"
        response = new MockHttpServletResponse()
        //需要ei为1
        request.setCookies(new Cookie("FSAuthXC", "83951.83951.1000"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        //真实值
        checkResultAndUser(result, "83951", 83951, 1000)

        when: " with error user 线上"
        response = new MockHttpServletResponse()
        setFirstshareEnv()
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        !result
        checkResponseResult(response, ResultCodeEnum.NO_USER)


        when: " with right user 线上"
        response = new MockHttpServletResponse()
        request.setCookies(new Cookie("FSAuthXC", "fs.1.1000"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        checkResultAndUser(result, "fs", 1, 1000)
    }


    @Unroll
    def "test noUser #uri"(String uri, boolean expect) {
        def request = new MockHttpServletRequest()
        def response = new MockHttpServletResponse()
        request.requestURI = uri
        boolean result = userInterceptors.preHandle(request, response, userInterceptors)
        expect:
        result == expect

        where:
        uri                          | expect
        "/erp/syncdata/setup"        | false
        "/erp/syncdata/noAuth/check" | true
    }


    @Unroll
    def "test tenantUser "() {
        given: "a request and response"
        def request = new MockHttpServletRequest()
        def response = new MockHttpServletResponse()
        request.requestURI = "/erp/syncdata/setup"
        boolean result

        when: " with no user"
        response = new MockHttpServletResponse()
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        !result
        checkResponseResult(response, ResultCodeEnum.NO_USER)


        when: " with error env fstest"
        response = new MockHttpServletResponse()
        request.setCookies(new Cookie("FSAuthXC", "83952.83952.1000"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        checkResultAndUser(result, "83952", 83952, 1000)


        when: " with error env firstshare"
        setFirstshareEnv()
        response = new MockHttpServletResponse()
        request.setCookies(new Cookie("FSAuthXC", "81122.81122.1000"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        !result
        checkResponseResult(response, ResultCodeEnum.ERROR_ENVIRONMENT)

        when: " with error user"
        response = new MockHttpServletResponse()
        request.setCookies(new Cookie("FSAuthXC", "83952.83952.-2"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        !result
        checkResponseResult(response, ResultCodeEnum.NO_USER)


        when: " with noAdmin"
        response = new MockHttpServletResponse()
        request.setCookies(new Cookie("FSAuthXC", "83952.83952.1003"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        !result
        checkResponseResult(response, ResultCodeEnum.NOT_HAVE_DATA_AUTH)


        when: " with admin"
        response = new MockHttpServletResponse()
        request.setCookies(new Cookie("FSAuthXC", "83952.83952.1000"))
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        checkResultAndUser(result, "83952", 83952, 1000)
    }

    @Unroll
    def "test cep "() {
        given: "a request and response"
        def request = new MockHttpServletRequest()
        def response = new MockHttpServletResponse()
        request.requestURI = "/cep/setup"
        boolean result

        when: "right header"
        response = new MockHttpServletResponse()
        request.addHeader("X-fs-Enterprise-Account", "83952")
        request.addHeader("X-fs-Enterprise-Id", 83952)
        request.addHeader("X-fs-Employee-Id", 1000)
        result = userInterceptors.preHandle(request, response, userInterceptors)
        then:
        checkResultAndUser(result, "83952", 83952, 1000)
    }

    private boolean checkResultAndUser(boolean result, String ea, Integer ei, Integer userId) {
        def userVo = UserContextHolder.getUserVo()
        UserContextHolder.remove()
        log.info("check result,expect:$ea,$ei,$userId,actually:$userVo)")
        if (result) {
            if (userVo != null) {
                def valid = userVo.getEnterpriseAccount() == ea && userVo.getEnterpriseId() == ei && userVo.getEmployeeId() == userId
                return valid
            }
        }
        return false
    }

    private boolean checkResponseResult(MockHttpServletResponse response, ResultCodeEnum resultCodeEnum) {
        if (response.getStatus() == 200) {
            def content = response.getContentAsString()
            if (JSON.isValidObject(content)) {
                def result = JSON.parseObject(content, Result.class)
                if (result != null && result.getErrCode() == resultCodeEnum.getErrCode()) {
                    return true
                }
            }
        }
        return false
    }
}
