package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import cn.hutool.core.date.TimeInterval
import cn.hutool.core.thread.ThreadUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData
import spock.lang.Specification

import java.lang.reflect.Field
import java.lang.reflect.Modifier

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2022-10-24
 */
class SyncPloyDetailManagerTest extends Specification {
    private SyncPloyDetailManager syncPloyDetailManager

    void setup() {
        AdminSyncPloyDetailDao dao = Stub()
        dao.listObjMappingByTenantId("83952")>>{
            //单对象
            SyncPloyDetailEntity entity1 = new SyncPloyDetailEntity(
                    sourceObjectApiName: "srcObj1",
                    destObjectApiName: "destObj1"
            )
            //单对象,明细空列表
            SyncPloyDetailEntity entity3 = new SyncPloyDetailEntity(
                    sourceObjectApiName: "srcObj1",
                    destObjectApiName: "destObj1",
                    detailObjectMappings: new DetailObjectMappingsData()
            )
            //主从对象
            SyncPloyDetailEntity entity2 = new SyncPloyDetailEntity(
                    sourceObjectApiName: "srcObj2",
                    destObjectApiName: "destObj2",
                    detailObjectMappings: new DetailObjectMappingsData()
            )
            entity2.detailObjectMappings.add(new DetailObjectMappingsData.DetailObjectMappingData(
                    sourceObjectApiName: "srcDetailObj2",
                    destObjectApiName: "destDetailObj2"
            ))
            //模拟数据库访问耗时
            ThreadUtil.sleep(500)
            return [entity1,entity2,entity3]
        }
        syncPloyDetailManager = new SyncPloyDetailManager(
                adminSyncPloyDetailDao: dao,
        )
        def field = SyncPloyDetailManager.class.getDeclaredField("timeOutSecond")
        field.setAccessible(true)

        // 修改字段值
        Field modifiersField = Field.class.getDeclaredField("modifiers")
        modifiersField.setAccessible(true)
        modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL)

        field.set(syncPloyDetailManager, 10L)
    }

    def "testCheckExist"() {
        when:
        //存在集成流，未走缓存
        TimeInterval ti = new TimeInterval()
        def res = syncPloyDetailManager.checkStreamExist("83952", "srcObj1", "destObj1")
        def cost = ti.intervalRestart()
        then:
        verifyAll {
            res
            cost > 500L
        }

        when:
        //不存在集成流，走缓存
        res = syncPloyDetailManager.checkStreamExist("83952", "srcObj1", "destObj2")
        cost = ti.intervalRestart()
        then:
        verifyAll {
            !res
            cost < 500L
        }


        when:
        //明细映射不存在集成流，走缓存
        res = syncPloyDetailManager.checkStreamExist("83952", "srcDetailObj1", "destDetailObj2")
        cost = ti.intervalRestart()
        then:
        verifyAll {
            !res
            cost < 500L
        }

        when:
        //明细映射存在集成流，走缓存
        res = syncPloyDetailManager.checkStreamExist("83952", "srcDetailObj2", "destDetailObj2")
        cost = ti.intervalRestart()
        then:
        verifyAll {
            res
            cost < 500L
        }

        when:
        //休眠后缓存失效
        ThreadUtil.safeSleep(10000)
        ti.restart()
        res = syncPloyDetailManager.checkStreamExist("83952", "srcObj2", "destObj2")
        cost = ti.intervalRestart()
        then:
        verifyAll {
            res
            cost > 500L
        }
    }


}
