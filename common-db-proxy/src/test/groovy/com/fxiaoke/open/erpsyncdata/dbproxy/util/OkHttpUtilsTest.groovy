package com.fxiaoke.open.erpsyncdata.dbproxy.util

import cn.hutool.core.codec.Base64
import cn.hutool.http.HttpUtil
import cn.hutool.http.server.SimpleServer
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpRequest2
import spock.lang.Ignore
import spock.lang.Specification

/**
 *  此测试依赖 http-spring-support 版本大于 5.1.0-SNAPSHOT
 *  需要修改父Pom为fxiaoke-parent-pom-rc
 * <AUTHOR> (^_−)☆
 */
@Ignore
class OkHttpUtilsTest extends Specification {

    private static SimpleServer realServer
    private static SimpleServer proxyServer2
    static {
        System.setProperty("config.mode", "localNoUpdate")
        //修改配置
        //启动2个http代理服务器来测试代理情况
        realServer = HttpUtil.createServer(31111)
                .addAction("/", { request, response ->
                    String targetUrl = request.getURI().toString();
                    def body = request.getBody()
                    println("proxy1 receive request,url:${targetUrl},body:${body}")
                    response.write("real");
                })
                .addAction("/r", { request, response ->
                    String targetUrl = request.getURI().toString();
                    def body = request.getBody()
                    println("proxy1 receive request,url:${targetUrl},body:${body}")
                    response.write(body);
                })
                .start()
        proxyServer2 = HttpUtil.createServer(31112)
                .addAction("/", { request, response ->
                    String targetUrl = request.getURI().toString();
                    def body = request.getBody()
                    println("proxy2 receive request,url:${targetUrl},body:${body}")
                    response.write("proxy2");
                })
                .start()
        def factoryBean = new HttpSupportFactoryBean()
        factoryBean.setConfigName("test-all")
        factoryBean.setSectionNames("http-support")
        factoryBean.init()
        def httpSupport = factoryBean.getObject()
        new OkHttpUtils().setOkHttpSupport(httpSupport)
    }

    void setup() {
    }

    def "test Json"() {
        def request = new HttpRequest2()
        request.setUrl("http://localhost:31111/")
        request.setMethod("GET")
        request.setBody("{\"name\":\"xiejiay\"}")
        def json = JacksonUtil.toJson(request)
        println(json)
        HttpRequest2 request2 = JacksonUtil.fromJson(json, HttpRequest2.class)
        expect:
        request2.getBody() == "{\"name\":\"xiejiay\"}"
        request2.getUrl() == "http://localhost:31111/"
    }

    def "testExecuteGet"() {
        def request = new HttpRequest2()
        request.setUrl("http://test.cn?t=1")
        request.setMethod("GET")
        def execute = OkHttpUtils.execute(request)

        println(execute)
        expect:
        execute.getBody() == 'proxy2'
    }


    def "testExecuteGetReal"() {
        def request = new HttpRequest2()
        request.setUrl("http://localhost:31111?t=1")
        request.setMethod("GET")
        def execute = OkHttpUtils.execute(request)

        println(execute)
        expect:
        execute.getBody() == 'real'
    }


    def "testExecutePost"() {
        def request = new HttpRequest2()
        request.setUrl("http://test.cn?t=1")
        request.setMethod("POST")
        request.setBody('{"f1":1}')
        def execute = OkHttpUtils.execute(request)

        println(execute)
        expect:
        execute.getBody() == 'proxy2'
    }


    def "testNoBase64"() {
        def request = new HttpRequest2()
        request.setUrl("http://localhost:31111/r")
        request.setMethod("POST")
        request.setBody('{"f1":1}')
        def execute = OkHttpUtils.execute(request)
        println(execute)
        expect:
        execute.getBody() == '{"f1":1}'
    }


    def "testRequestBodyNeedBase64Decode"() {
        def request = new HttpRequest2()
        request.setUrl("http://localhost:31111/r")
        request.setMethod("POST")
        request.setBody(Base64.encode('{"f1":1}'))
        request.setRequestBodyNeedBase64Decode(true)
        def execute = OkHttpUtils.execute(request)
        println(execute)
        expect:
        execute.getBody() == '{"f1":1}'
    }


    def "testBase64"() {
        def request = new HttpRequest2()
        request.setUrl("http://localhost:31111/r")
        request.setMethod("POST")
        request.setBody(Base64.encode('{"f1":1}'))
        request.setRequestBodyNeedBase64Decode(true)
        request.setResponseBodyNeedBase64Encode(true)
        def execute = OkHttpUtils.execute(request)
        println(execute)
        expect:
        execute.getBody() == Base64.encode('{"f1":1}')
    }
}
