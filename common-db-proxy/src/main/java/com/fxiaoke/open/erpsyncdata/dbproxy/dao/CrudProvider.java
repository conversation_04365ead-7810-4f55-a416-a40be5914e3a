package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

/**
 * Created by fengyh on 2020/8/25.
 */

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.github.mybatis.util.EntityUtil;
import com.github.mybatis.util.PersistMeta;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.util.CollectionUtils;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Crud模板
 */
@Slf4j
public class CrudProvider {
    public static final String CLASS_KEY = "clazz";
    public static final String PARA_KEY = "para";
    public static final String PAGE_KEY = "page";
    public static final String COLUMN_KEY = "column";
    public static final String WHERE_KEY = "where";
    public static final String ORDER_KEY = "order";
    public static final String GROUP_KEY = "groupBy";
    //使用不可见字符
    public static final char FIELD_LEFT = '\'';
    //使用不可见字符
    public static final char FIELD_RIGHT = '\'';

    /**
     * 查询所有记录
     *
     * @param clazz
     * @return
     */
    public String findAll(final Class<?> clazz) {
        PersistMeta meta = EntityUtil.getMeta(clazz);
        String sql =  new SQL().SELECT("*").FROM(EntityUtil.getTableName(clazz)).toString();
        return sql;
    }



    /**
     * 查询（根据whereEntity条件）
     *
     * @param parameter 请求参数
     * @return
     */
    @SuppressWarnings("Duplicates")
    public String findByEntity(final Map<String, Object> parameter) {
        Object obj = parameter.get(PARA_KEY);
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        StringBuilder where = new StringBuilder();
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();
            if (isNull(field, obj)) {
                continue;
            }
            where.append(kv.getKey()).append("=#{").append(PARA_KEY).append('.').append(field.getName()).append("} AND ");
        }
        int index = where.lastIndexOf(" AND");
        if (index > 0) {
            where.setLength(index);
        }

        String result = new SQL().SELECT("*").FROM(getTableName(meta, obj)).WHERE(where.toString()).toString();
        return result;
    }

    /**
     * 查询（根据whereEntity条件）
     *
     * @param obj
     * @return
     * @see com.github.mybatis.provider.CrudProvider#findByEntity(Map)
     */
    @Deprecated
    @SuppressWarnings("Duplicates")
    public String findBySelective(final Object obj) {
        Class<?> clazz = obj.getClass();
        if(obj instanceof HashMap) {
            HashMap map = (HashMap)obj;
            clazz = map.get("clazz").getClass();
        }
        PersistMeta meta = EntityUtil.getMeta(clazz);
        StringBuilder where = new StringBuilder();
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();
            if (isNull(field, obj)) {
                continue;
            }

            where.append(kv.getKey()).append("=#{").append(field.getName()).append("} AND ");
        }

        int index = where.lastIndexOf(" AND");
        if (index > 0) {
            where.setLength(index);
        }
        String sql = new SQL().SELECT("*").FROM(getTableName(meta, obj)).WHERE(where.toString()).toString();
        return sql;
    }

    /**
     * 统计所有记录行数
     *
     * @param clazz
     * @return
     */
    public String countAll(final Class<?> clazz) {
        return new SQL().SELECT("count(0)").FROM(EntityUtil.getTableName(clazz)).toString();
    }

    /**
     * 删除所有记录
     *
     * @param clazz
     * @return
     */
    public String deleteAll(final Class<?> clazz) {
        return new SQL().DELETE_FROM(EntityUtil.getTableName(clazz)).toString();
    }

    /**
     * 删除旧表，并新增一个同名的新表，比deleteAll要快，而且能回收innodb的表存储空间
     *
     * @param clazz
     * @return
     */
    public String truncate(final Class<?> clazz) {
        return "TRUNCATE TABLE " + FIELD_LEFT + EntityUtil.getTableName(clazz) + FIELD_RIGHT;
    }

    /**
     * 根据主键查找记录
     *
     * @param parameter
     * @return
     */
    public String findById(final Map<String, Object> parameter) {
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        return new SQL().SELECT("*").FROM(meta.getTableName()).WHERE(meta.getIdColumnName() + "=#{" + PARA_KEY + '}').toString();
    }

    /**
     * 根据联合主键查找记录
     *
     * @param parameter
     * @return
     */
    public String findByMultiId(final Map<String, Object> parameter) {
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        return new SQL().SELECT("*").FROM(meta.getTableName()).WHERE(buildWheres(meta).toArray(new String[meta.getPkColumns().size()])).toString();
    }

    /**
     * 查询所有记录(分页)
     *
     * @param map
     * @return
     */
    public String findByPage(final Map<String, Object> map) {
        Class<?> clazz = (Class<?>) map.get(CLASS_KEY);
        String names = map.containsKey(COLUMN_KEY) ? map.get(COLUMN_KEY).toString() : "*";
        SQL sql = new SQL().SELECT(names).FROM(EntityUtil.getTableName(clazz));
        if (map.containsKey(WHERE_KEY)) {
            Object obj = map.get(WHERE_KEY);
            if (obj != null && !Strings.isNullOrEmpty(obj.toString())) {
                sql.WHERE((String) obj);
            }
        }
        if (map.containsKey(ORDER_KEY)) {
            Object obj = map.get(ORDER_KEY);
            if (obj != null && !Strings.isNullOrEmpty(obj.toString())) {
                sql.ORDER_BY((String) map.get(ORDER_KEY));
            }
        }
        if (map.containsKey(GROUP_KEY)) {
            Object obj = map.get(GROUP_KEY);
            if (obj != null && !Strings.isNullOrEmpty(obj.toString())) {
                sql.GROUP_BY((String) map.get(GROUP_KEY));
            }
        }
        return sql.toString();
    }

    /**
     * 按照id查找并删除某个对象
     *
     * @param obj
     * @return
     */
    public String delete(final Object obj) {
        final PersistMeta meta = EntityUtil.getMeta(obj.getClass());
        return new SQL().DELETE_FROM(getTableName(meta, obj)).WHERE(buildWheres(meta).toArray(new String[meta.getPkColumns().size()])).toString();
    }

    /**
     * 根据主键删除记录
     *
     * @return
     */
    public String deleteById(final Map<String, Object> parameter) {
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        return new SQL().DELETE_FROM(meta.getTableName()).WHERE(meta.getIdColumnName() + "=#{" + PARA_KEY + '}').toString();
    }

    /**
     * 根据主键删除记录
     *
     * @return
     */

    private String getWhereKeyStr(PersistMeta meta, Class annotationClass) {
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();

            if(null != field.getAnnotation(annotationClass)) {
                return kv.getKey();
            }
        }
        return null;
    }

    public String deleteByEiAndId(final Map<String, Object> parameter) {
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);

        String whereStr = getWhereKeyStr(meta, Id.class);
        whereStr += "="+ FIELD_LEFT + parameter.get("arg1") + FIELD_RIGHT;  //arg0是 ei,  arg1是id
        if(StringUtils.isEmpty((String)parameter.get("arg1"))) {
            //id不能为空。
            return null;
        }
        whereStr += " and " ;
        whereStr +=getWhereKeyStr(meta, TenantID.class) ;
        whereStr += "=" + FIELD_LEFT +  parameter.get("arg0") + FIELD_RIGHT;
        return new SQL().DELETE_FROM(meta.getTableName()).WHERE(whereStr).toString();
    }


    /**
     * 根据主键集合删除记录
     *
     * @return
     */
    public String deleteByIds(final Map<String, Object> parameter) throws SQLException {
        List<?> ids = (List<?>) parameter.get(PARA_KEY);
        if (null == ids || ids.isEmpty()) {
            throw new SQLException(PARA_KEY + " is null or empty");
        }
        Class<?> clazz = (Class<?>) parameter.get(CLASS_KEY);
        PersistMeta meta = EntityUtil.getMeta(clazz);
        String where;
        if (ids.iterator().next() instanceof Number) {
            where = meta.getIdColumnName() + " in (" + Joiner.on(",").join(ids) + ')';
        } else {
            where = meta.getIdColumnName() + " in ('" + Joiner.on("','").join(ids
                    .stream()
                    .map(id -> StringEscapeUtils.escapeSql(String.valueOf(id)))
                    .collect(Collectors.toList())) + "')";
        }
        return new SQL().DELETE_FROM(meta.getTableName()).WHERE(where).toString();
    }


    /**
     * 更新操作
     *
     * 根据id更新其它不为空的字段
     *
     * @param obj
     * @return String
     */
    public String update(Object obj) {
        return updateWithNull(obj, true);
    }


    private String updateWithNull(Object obj, boolean skipNull) {
        Class<?> clazz = obj.getClass();
        PersistMeta meta = EntityUtil.getMeta(clazz);
        if (CollectionUtils.isEmpty(meta.getPkColumns())) {
            return new SQL().toString();
        }
        StringBuilder setting = new StringBuilder(32);
        int i = 0;
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();
            if (meta.getPkColumns().containsKey(kv.getKey())) {
                continue;
            }

            if (skipNull && isNull(field, obj)) {
                continue;
            }

            if (i++ != 0) {
                setting.append(',');
            }

            setting.append(kv.getKey()).append("=#{").append(field.getName()).append('}');
        }
        return new SQL()
                .UPDATE(getTableName(meta, obj))
                .SET(setting.toString())
                .WHERE(buildWheres(meta).toArray(new String[meta.getPkColumns().size()]))
                .toString();
    }

    /**
     * 新增操作
     *
     * @param obj
     * @return String
     */

    public String insert(Object obj) {
        PersistMeta meta = EntityUtil.getMeta(obj.getClass());
        StringBuilder names = new StringBuilder(), values = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, Field> kv : meta.getColumns().entrySet()) {
            Field field = kv.getValue();
            if (isNull(field, obj)) {
                continue;
            }

            if (i++ != 0) {
                names.append(',');
                values.append(',');
            }

            names.append(kv.getKey());
            values.append("#{").append(field.getName()).append('}');
        }

        String sql = new SQL().INSERT_INTO(getTableName(meta, obj)).VALUES(names.toString(), values.toString()).toString();
        return sql;
    }

    protected String getTableName(PersistMeta meta, Object obj) {
        if (meta.getPostfix() != null) {
            try {
                return meta.getTableName() + '_' + meta.getPostfix().invoke(obj);
            } catch (Exception e) {
                log.error("cannot invoke postfix: {}", meta.getPostfix(), e);
            }
        }
        return meta.getTableName();
    }



    /**
     * 列名判空处理
     *
     * @param field
     * @return boolean
     */
    protected boolean isNull(Field field, Object obj) {
        try {
            return field.get(obj) == null;
        } catch (IllegalAccessException e) {
            return true;
        }
    }

    /**
     * 构建where条件
     *
     * @param meta
     * @return
     */
    private List<String> buildWheres(PersistMeta meta) {
        List<String> wheres = Lists.newArrayList();
        meta.getPkColumns().forEach((columnName, field) -> wheres.add(columnName + "=#{" + field.getName() + "}"));
        return wheres;
    }
}
