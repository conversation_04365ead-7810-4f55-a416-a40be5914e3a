package com.fxiaoke.open.erpsyncdata.dbproxy.Impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EmailNotificationService;
import com.fxiaoke.otherrestapi.email.arg.BaseEmailArg;
import com.fxiaoke.otherrestapi.email.arg.QuerySystemEmailListArg;
import com.fxiaoke.otherrestapi.email.arg.SendEmailArg;
import com.fxiaoke.otherrestapi.email.data.SendEmailData;
import com.fxiaoke.otherrestapi.email.result.BaseEmailResult;
import com.fxiaoke.otherrestapi.email.result.QuerySystemEmailListResult;
import com.fxiaoke.otherrestapi.email.result.SendEmailResult;
import com.fxiaoke.otherrestapi.email.service.EmailService;
import com.fxiaoke.paasauthrestapi.arg.RoleUserArg;
import com.fxiaoke.paasauthrestapi.result.RoleUserResult;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmailNotificationServiceImpl implements EmailNotificationService {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private PaasAuthService paasAuthService;

    private Result<Object> sendEmailNotice(SendEmailArg arg) {
        SendEmailResult result = emailService.sendEmail(arg);
        return new Result(result.getErrorCode() + "", result.getErrorMessage(), result.getData());
    }

    @Override
    public Result<Object> sendEmailNotice(String tenantId, List<Integer> userIdList, List<String> roleIdList, String subject, String content) {
        if (userIdList == null) {
            userIdList = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            com.fxiaoke.paasauthrestapi.common.data.HeaderObj headerObj = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(Integer.valueOf(tenantId));
            for (String roleCode : roleIdList) {
                //角色
                try {
                    com.fxiaoke.paasauthrestapi.common.result.Result<RoleUserResult> roleUserRes = paasAuthService.roleUser(headerObj, new RoleUserArg("CRM", Integer.valueOf(tenantId), -10000, roleCode));
                    if (roleUserRes.getErrCode() != 0) {
                        log.warn("find role user failed,{},{}", roleCode, roleUserRes);
                    } else {
                        roleUserRes.getResult().getUsers().stream().mapToInt(Integer::parseInt).forEach(userIdList::add);
                    }
                } catch (Exception e) {
                    log.error("find role user error", e);
                }
            }
        }
        List<String> userIds = userIdList.stream().map(i -> i.toString()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        SendEmailArg arg = new SendEmailArg();
        SendEmailData arg1 = new SendEmailData();
        arg.setArg1(arg1);
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        arg1.setEa(ea);
        arg1.setUserIdList(userIds);
        QuerySystemEmailListArg querySystemEmailListArg = new QuerySystemEmailListArg();
        querySystemEmailListArg.setArg1(new BaseEmailArg());
        querySystemEmailListArg.getArg1().setFsEa(ea);
        BaseEmailResult<QuerySystemEmailListResult> systemEmailList = emailService.querySystemEmailList(querySystemEmailListArg);
        if (systemEmailList != null && systemEmailList.getData() != null && CollectionUtils.isNotEmpty(systemEmailList.getData().getSystemEmailList())) {
            List<QuerySystemEmailListResult.SystemEmail> systemEmails = systemEmailList.getData().getSystemEmailList().stream()
                    .filter(email -> email.getStatus() != null && email.getStatus().equals(5) && StringUtils.isNotBlank(email.getSystemEmailName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(systemEmails)) {
                arg1.setSender(systemEmails.get(0).getSystemEmailName());
            }
        }
        if (StringUtils.isBlank(arg1.getSender())) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(Integer.valueOf(tenantId));
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);
        batchGetEmployeeDtoArg.setEmployeeIds(userIdList);
        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        if (batchGetEmployeeDtoResult != null && batchGetEmployeeDtoResult.getEmployeeDtos() != null) {
            arg1.setToList(batchGetEmployeeDtoResult.getEmployeeDtos().stream().filter(e -> StringUtils.isNotBlank(e.getEmail())).map(e -> String.valueOf(e.getEmail())).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(arg1.getToList())) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        arg1.setSubject(subject);
        arg1.setContent(content);
        return this.sendEmailNotice(arg);
    }


}
