package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:25 2021/2/25
 * @Desc:
 */
@Repository
public interface ErpTenantConfigurationDaoAccess extends ErpBaseDao<ErpTenantConfigurationEntity>, ITenant<ErpTenantConfigurationDao> {

    /**
     * 获取企业配置，暂时直接在这里做缓存
     * 如果有设置接口展示实时数据需求，不可调用该接口。
     *
     * @param tenantId
     * @param dataCenterId
     * @param channel      可以传空
     * @param type
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 3)
    ErpTenantConfigurationEntity findOne(@Param("tenantId") String tenantId,
                                         @Param("dataCenterId") String dataCenterId,
                                         @Nullable @Param("channel") String channel,
                                         @Param("type") String type);

    ErpTenantConfigurationEntity findOneNoCache(@Param("tenantId") String tenantId,
                                                @Param("dataCenterId") String dataCenterId,
                                                @Nullable @Param("channel") String channel,
                                                @Param("type") String type);

    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    ErpTenantConfigurationEntity findByIdCache(@Param("id") String id);

    int updateConfig(@Param("id") String id,
                     @Param("updatedConfiguration") String updatedConfiguration, @Param("updatedUpdateTime") Long updatedUpdateTime);

    int deleteByTenantId(String tenantId);

    int deleteByTenantIdAndId(@Param("tenantId") String tenantId, @Param("id") String Id);

    List<ErpTenantConfigurationEntity> findOnePage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    int deleteByDataCenterId(@Param("tenantId") String tenantId, @Param("dataCenterId") String dataCenterId);

    int batchDeleteByDataCenterId(@Param("tenantId") String tenantId, @Param("dataCenterIds") List<String> dataCenterIds);
}