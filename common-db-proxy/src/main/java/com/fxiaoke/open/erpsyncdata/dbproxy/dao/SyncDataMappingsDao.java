package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.ibatis.TenantBaseMapper;
import com.fxiaoke.open.erpsyncdata.common.interceptor.TenantShardingTableInterceptor;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MappingCreatedData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncDataMappingsFailedCountData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingCountVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UpdateMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.model.TableRowCountDto;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface SyncDataMappingsDao extends TenantBaseMapper<SyncDataMappingsEntity>, ITenant<SyncDataMappingsDao> {

    SyncDataMappingsEntity getByUninKey(@Param("tenantId") String tenantId,
                                        @Param("sourceObjectApiName") String sourceObjectApiName,
                                        @Param("sourceDataId") String sourceDataId,
                                        @Param("destObjectApiName") String destObjectApiName);

    /**
     * 和{@link SyncDataMappingsDao#getByUninKeyReverse}
     * 两个方法只是方法和传参的名称有区分。sql是一样的。
     */
    SyncDataMappingsEntity getByUninKeyByDestId(@Param("tenantId") String tenantId,
                                                @Param("sourceObjectApiName") String sourceObjectApiName,
                                                @Param("destDataId") String destDataId,
                                                @Param("destObjectApiName") String destObjectApiName);
    /**
     * 和{@link SyncDataMappingsDao#getByUninKeyByDestId}
     * 两个方法只是方法和传参的名称有区分。sql是一样的。
     */
    SyncDataMappingsEntity getByUninKeyReverse(@Param("tenantId") String tenantId,
                                               @Param("sourceObjectApiName") String sourceObjectApiName,
                                               @Param("sourceDataId") String sourceDataId,
                                               @Param("destObjectApiName") String destObjectApiName);


    int updateById(@Param("tenantId") String tenantId,
                   @Param("id") String id,
                   @Param("lastSyncDataId") String lastSyncDataId,
                   @Param("lastSyncStatus") Integer lastSyncStatus,
                   @Param("lastSourceDataVserion") Long lastSourceDataVserion,
                   @Param("updateTime") Long updateTime);

    /**
     * 原，syncData传列表，改为单条
     */
    int updateBySyncDataId(@Param("tenantId") String tenantId,
                           @Param("syncDataId") String syncDataId,
                           @Param("syncStatus") Integer syncStatus,
                           @Param("destDataName") String destDataName,
                           @Param("remark") String remark,
                           @Param("updateTime") Long updateTime);

    /**
     * @param tenantId
     * @param updateDestName
     * @param args
     * @return
     */
    int bulkUpdateBySyncDataId(@Param("tenantId") String tenantId,
                               @Param("updateDestName") boolean updateDestName,
                               @Param("args") List<UpdateMapping.BySyncDataIdArg> args);

    /**
     * 更新mapping为创建成功，以及destDataId
     *
     * @param tenantId
     * @param updateDestIdAndName 更新目标名称
     * @param args                更新参数
     * @return
     */
    int bulkUpdateDestBySourceArgs(@Param("tenantId") String tenantId,
                                   @Param("updateDestIdAndName") boolean updateDestIdAndName,
                                   @Param("args") List<UpdateMapping.BySourceArg> args);

    int batchUpdateMasterDataIdBySyncDataIdList(@Param("tenantId") String tenantId,
                                                @Param("syncDataIds") List<String> syncDataIds,
                                                @Param("masterDataId") String masterDataId);


    int batchUpdateMasterDataId(@Param("tenantId") String tenantId, @Param("ids") List<String> ids,@Param("masterDataId") String  masterDataId);

    int batchUpdateStatusByIds(@Param("tenantId") String tenantId, @Param("ids") List<String> ids, @Param("remark") String remark, @Param("status") Integer syncStatus);

    int updateCreatedById(@Param("tenantId") String tenantId,
                          @Param("id") String id,
                          @Param("isCreated") Boolean isCreated);

    int updateByUniKey(@Param("tenantId") String tenantId,
                       @Param("sourceObjectApiName") String sourceObjectApiName,
                       @Param("destObjectApiName") String destObjectApiName,
                       @Param("sourceDataId") String sourceDataId,
                       @Param("destDataId") String destDataId,
                       @Param("destDataName") String destDataName,
                       @Param("lastSyncStatus") Integer lastSyncStatus,
                       @Param("isCreated") Boolean isCreated);


    int mergeDestDataId(@Param("tenantId") String tenantId,
                        @Param("ids") List<String> ids,
                        @Param("sourceObjectApiName") String sourceObjectApiName,
                        @Param("destObjectApiName") String destObjectApiName,
                        @Param("oldDestDataId") String oldDestDataId,
                        @Param("newDestDataId") String newDestDataId,
                        @Param("updateTime") Long updateTime);

    int mergeSourceDataId(@Param("tenantId") String tenantId,
                          @Param("ids") List<String> ids,
                          @Param("sourceObjectApiName") String sourceObjectApiName,
                          @Param("destObjectApiName") String destObjectApiName,
                          @Param("oldSourceDataId") String oldSourceDataId,
                          @Param("newSourceDataId") String newSourceDataId,
                          @Param("updateTime") Long updateTime);


    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    List<SyncDataMappingsEntity> listByDestInfo(@Param("tenantId") String tenantId,
                                                @Param("destObjectApiName") String destObjectApiName,
                                                @Param("destDataIds") List<String> destDataIds);

    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    List<SyncDataMappingsEntity> listBySourceInfo(@Param("tenantId") String tenantId,
                                                  @Param("sourceObjectApiName") String destObjectApiName,
                                                  @Param("sourceDataIds") List<String> sourceDataIds);

    int updateLastSyncStatusById(@Param("tenantId") String tenantId,
                                 @Param("id") String id,
                                 @Param("lastSyncStatus") Integer lastSyncStatus,
                                 @Param("remark") String remark,
                                 @Param("updateTime") Long updateTime);

    List<SyncDataMappingsEntity> findDetailByMasterId(@Param("tenantId") String tenantId,
                                              @Param("sourceDetailApiName") String sourceDetailApiName,
                                              @Param("destDetailApiName") String destDetailApiName,
                                              @Param("maserDataId") String masterId);

    /**
     * 仅返回srcId isCreated
     */
    List<MappingCreatedData> listCreatedBySource(@Param("tenantId") String tenantId,
                                                 @Param("sourceObjectApiName") String sourceObjectApiName,
                                                 @Param("destObjectApiName") String destObjectApiName,
                                                 @Param("sourceDataIdCollection") Collection<String> sourceDataIdCollection);

    List<SyncDataMappingsEntity> listByIds(@Param("tenantId") String tenantId,
                                           @Param("idCollection") Collection<String> idCollection);

    /**
     * @param tenantId 必须给,用于动态生成表名 {@link TenantShardingTableInterceptor#getTenantIdByParameter(Object)}
     */
    void truncateTable(@Param("tenantId") String tenantId);

    TableRowCountDto getCountFromPgClass(@Param("tenantId") String tenantId);

    TableRowCountDto getCountFromPgStatTable(@Param("tenantId") String tenantId);

    long countByTenantId(@Param("tenantId") String tenantId);

    int updateSourceDataIdById(@Param("tenantId") String tenantId,
                               @Param("id") String id,
                               @Param("sourceDataId") String sourceDataId);

    int updateDestDataIdById(@Param("tenantId") String tenantId,
                             @Param("id") String id,
                             @Param("destDataId") String destDataId);

    int updateMappingById(@Param("tenantId") String tenantId,
                          @Param("id") String id,
                          @Param("sourceDataId") String sourceDataId,
                          @Param("destDataId") String destDataId,
                          @Param("sourceDataName") String sourceDataName,
                          @Param("destDataName") String destDataName,
                          @Param("masterDataId") String masterDataId,
                          @Param("lastSyncStatus") Integer lastSyncStatus,
                          @Param("isCreated") Boolean isCreated,
                          @Param("updateTime") Long updateTime,
                          @Param("remark") String remark);

    // 用于分页查询
    List<SyncDataMappingsEntity> pageTenantIdByMaxId(@Param("tenantId") String tenantId,
                                                     @Param("maxId") String maxId,
                                                     @Param("pageSize") int pageSize);

    int deleteByTenantIdAndId(@Param("tenantId") String tenantId,
                              @Param("id") String id);


    int deleteByTenantIdAndIds(@Param("tenantId") String tenantId,
                               @Param("ids") List<String> ids);


    SyncDataMappingsEntity getById(@Param("tenantId") String tenantId, @Param("id") String id);


    SyncDataMappingsEntity getBySourceData(@Param("tenantId") String tenantId,
                                           @Param("sourceObjApiName") String sourceObjApiName,
                                           @Param("destObjApiName") String destObjApiName,
                                           @Param("sourceDataId") String sourceDataId);

    SyncDataMappingsEntity getByDestData(@Param("tenantId") String tenantId,
                                         @Param("sourceObjectApiName") String sourceObjectApiName,
                                         @Param("destObjectApiName") String destObjectApiName,
                                         @Param("destDataId") String destDataId);

    SyncDataMappingsEntity getByDestData2(@Param("tenantId") String tenantId,
                                          @Param("destObjectApiName") String destObjectApiName,
                                          @Param("destDataId") String destDataId);


    int countByObjectApiNames(@Param("tenantId") String tenantId,
                              @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings,
                              @Param("status") Integer status,
                              @Param("sourceDataId") String sourceDataId,
                              @Param("sourceDataName") String sourceDataName,
                              @Param("destDataId") String destDataId,
                              @Param("destDataName") String destDataName,
                              @Param("remark") String remark,
                              @Param("startTime") Long startTime,
                              @Param("endTime") Long endTime);

    int countByObjectApiNamesLimit1000(@Param("tenantId") String tenantId,
                                       @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings,
                                       @Param("status") Integer status,
                                       @Param("sourceDataId") String sourceDataId,
                                       @Param("sourceDataName") String sourceDataName,
                                       @Param("destDataId") String destDataId,
                                       @Param("destDataName") String destDataName,
                                       @Param("remark") String remark,
                                       @Param("startTime") Long startTime,
                                       @Param("endTime") Long endTime);

    int countByObjectApiNames2Limit1000(@Param("tenantId") String tenantId,
                                        @Param("objectApiNameMapping") SyncObjectAndTenantMappingData objectApiNameMapping,
                                        @Param("status") Integer status,
                                        @Param("sourceDataId") String sourceDataId,
                                        @Param("sourceDataName") String sourceDataName,
                                        @Param("destDataId") String destDataId,
                                        @Param("destDataId") String destDataName,
                                        @Param("remark") String remark,
                                        @Param("startTime") Long startTime);

    /**
     * @param status {@link com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum}
     */
    Long countByObjectApiNamesOnlyLimit1000(@Param("tenantId") String tenantId,
                                  @Param("sourceObjApiName") String sourceObjApiName,
                                  @Param("destObjectApiName") String destObjApiName,
                                  @Param("status") Integer status);

    /**
     * @param status {@link com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum}
     */
    List<MappingCountVo> countGroup(@Param("tenantId") String tenantId);

    /**
     * @param tenantId
     * @param objectApiNameMapping
     * @param status               对应的last_sync_status：1成功->6;2失败->1,3,5;3同步中->2,4,7
     * @param
     * @param limit
     * @param maxTime
     * @param maxId
     * @return
     */
    List<SyncDataMappingsEntity> listByObjectApiNames2(@Param("tenantId") String tenantId,
                                                       @Param("objectApiNameMapping") SyncObjectAndTenantMappingData objectApiNameMapping,
                                                       @Param("status") Integer status,
                                                       @Param("sourceDataId") String sourceDataId,
                                                       @Param("sourceDataName") String sourceDataName,
                                                       @Param("destDataId") String destDataId,
                                                       @Param("destDataName") String destDataName,
                                                       @Param("remark") String remark,
                                                       @Param("limit") Integer limit,
                                                       @Param("maxTime") Long maxTime,
                                                       @Param("maxId") String maxId,
                                                       @Param("startTime") Long startTime);

    int countByDetailObjectApiNames(@Param("tenantId") String tenantId,
                                    @Param("masterDataId") String masterDataId,
                                    @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings,
                                    @Param("status") Integer status,
                                    @Param("sourceDataId") String sourceDataId, @Param("sourceDataName") String sourceDataName,
                                    @Param("destDataId") String destDataId, @Param("destDataName") String destDataName,
                                    @Param("remark") String remark,
                                    @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<SyncDataMappingsFailedCountData> countSyncFailed(@Param("tenantId") String tenantId,
                                                          @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings);

    /**
     * 返回错误行数
     * 这个定时任务跑的，count有隐患。而且为什么可以count所有再减offset
     * @param tenantId
     * @param objectApiNameMapping
     * @return
     */
    int countBySyncFailedAndLimit(@Param("tenantId") String tenantId,
                                  @Param("objectApiNameMapping") SyncObjectAndTenantMappingData objectApiNameMapping,
                                  @Param("offset") Integer offset,
                                  @Param("limit") Integer limit);


    List<SyncDataMappingsEntity> listByObjectApiNames(@Param("tenantId") String tenantId,
                                                      @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings,
                                                      @Param("status") Integer status,
                                                      @Param("sourceDataId") String sourceDataId,
                                                      @Param("sourceDataName") String sourceDataName,
                                                      @Param("destDataId") String destDataId,
                                                      @Param("destDataName") String destDataName,
                                                      @Param("remark") String remark,
                                                      @Param("offset") Integer offset,
                                                      @Param("limit") Integer limit,
                                                      @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<SyncDataMappingsEntity> listByMasterDataId(@Param("tenantId") String tenantId,
                                                    @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings,
                                                    @Param("masterDataId") String masterDataId);

    List<SyncDataMappingsEntity> listCreatedDetailMappingByMasterDataId(@Param("tenantId") String tenantId,
                                                                        @Param("objectApiNameMapping") SyncObjectAndTenantMappingData objectApiNameMapping,
                                                                        @Param("masterDataId") String masterDataId);

    List<SyncDataMappingsEntity> listByDetailObjectApiNames(@Param("tenantId") String tenantId,
                                                            @Param("masterDataId") String masterDataId,
                                                            @Param("objectApiNameMappings") List<SyncObjectAndTenantMappingData> objectApiNameMappings,
                                                            @Param("status") Integer status,
                                                            @Param("sourceDataId") String sourceDataId,
                                                            @Param("sourceDataName") String sourceDataName,
                                                            @Param("destDataId") String destDataId,
                                                            @Param("destDataName") String destDataName,
                                                            @Param("remark") String remark,
                                                            @Param("offset") Integer offset,
                                                            @Param("limit") Integer limit,
                                                            @Param("startTime") Long startTime,
                                                            @Param("endTime") Long endTime);


    int batchUpdateByIds(@Param("tenantId") String tenantId,
                         @Param("ids") List<String> syncDataIds,
                         @Param("syncStatus") Integer syncStatus,
                         @Param("destDataName") String destDataName,
                         @Param("remark") String remark,
                         @Param("updateTime") Long updateTime);


    List<SyncDataMappingsEntity> listBySourceAndDestObjectApiName(
            @Param("tenantId") String tenantId,
            @Param("sourceObjectApiName") String sourceObjectApiName,
            @Param("destObjectApiName") String destObjectApiName);

    /**
     * 扫描超时的中间表mapping
     *
     * @param tenantId
     * @param
     * @param startTime
     * @param limit
     * @param offset
     * @param endTime
     * @return
     */
    List<SyncDataMappingsEntity> listByStatusTimeOutMappings(
            @Param("tenantId") String tenantId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("sourceObjectApiName") String sourceApiName,
            @Param("destObjectApiName") String destObjectApiName,
            @Param("limit") Integer limit,
            @Param("offset") Integer offset
    );

    List<SyncDataMappingsEntity> listByStatusFailMappings(
            @Param("tenantId") String tenantId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("sourceObjectApiName") String sourceApiName,
            @Param("destObjectApiName") String destObjectApiName,
            @Param("limit") Integer limit,
            @Param("offset") Integer offset
    );

    int updateDestDataId(@Param("tenantId") String tenantId, @Param("id") String id, @Param("destDataId") String destDataId, @Param("setIsCreatedTrue") boolean setIsCreatedTrue, @Param("updateTime") Long updateTime);

    int updateDataMapping2Success(@Param("tenantId") String tenantId,
                                  @Param("id") String id,
                                  @Param("destDataId") String destDataId,
                                  @Param("updateTime") Long updateTime,
                                  @Param("masterDataId") String masterDataId);

    int updateDataMappingByParams(@Param("tenantId") String tenantId,
                                  @Param("id") String id,
                                  @Param("destDataId") String destDataId,
                                  @Param("updateTime") Long updateTime,
                                  @Param("masterDataId") String masterDataId,
                                  @Param("sourceDataName") String sourceDataName,
                                  @Param("destDataName") String destDataName,
                                  @Param("remark") String remark);

    int updateSourceDataId(@Param("tenantId") String tenantId, @Param("id") String id, @Param("sourceDataId") String sourceDataId, @Param("updateTime") Long updateTime);

    int updateLastStatusById(@Param("tenantId") String tenantId, @Param("id") String id, @Param("status") Integer status);

    List<SyncDataMappingsEntity> queryBySourceDataIdList(@Param("tenantId") String tenantId,
                                                         @Param("sourceObjectApiName") String sourceObjectApiName,
                                                         @Param("sourceDataIds") List<String> sourceDataIds,
                                                         @Param("destObjectApiName") String destObjectApiName);


    List<SyncDataMappingsEntity> queryByDestDataIdList(@Param("tenantId") String tenantId,
                                                       @Param("sourceObjectApiName") String sourceObjectApiName,
                                                       @Param("destDataIds") List<String> destDataIds,
                                                       @Param("destObjectApiName") String destObjectApiName);

    int deleteSyncDataMappings(@Param("tenantId") String tenantId,
                               @Param("sourceObjApiName") String sourceObjApiName,
                               @Param("destObjApiName") String destObjApiName);

    int countIsCreatedMapping(@Param("tenantId") String tenantId,
                              @Param("sourceObjectApiName") String sourceObjectApiName,
                              @Param("destObjectApiName") String destObjectApiName,
                              @Param("isCreated") Boolean isCreated);


    /**
     * 根据destDataId删除数据
     */
    int deleteMappingsByDestId(@Param("tenantId") String tenantId,
                               @Param("dataId") String dataId,
                               @Param("sourceObjectApiName") String sourceObjectApiName,
                               @Param("destObjectApiName") String destObjectApiName);

    /**
     * 根据sourceDataId删除数据
     */
    int deleteMappingsBySourceId(@Param("tenantId") String tenantId,
                                 @Param("dataId") String dataId,
                                 @Param("sourceObjectApiName") String sourceObjectApiName,
                                 @Param("destObjectApiName") String destObjectApiName);


    /**
     * 仅返回srcId isCreated
     */
    List<SyncDataMappingsEntity> listCreatedBySourceDataIds(@Param("tenantId") String tenantId,
                                                            @Param("sourceObjApiName") String sourceObjApiName,
                                                            @Param("destObjApiName") String destObjApiName,
                                                            @Param("sourceDataIds") Collection<String> sourceDataIds);

    /**
     * 仅返回destId isCreated
     */
    List<SyncDataMappingsEntity> listCreatedByDestDataIds(@Param("tenantId") String tenantId,
                                                          @Param("sourceObjApiName") String sourceObjApiName,
                                                          @Param("destObjApiName") String destObjApiName,
                                                          @Param("destDataIds") List<String> destDataIds);

    List<SyncDataMappingsEntity> listOrderById(@Param("tenantId") String tenantId,
                                               @Param("id") String id,
                                               @Param("limit") Integer limit);

    Integer count(@Param("tenantId") String tenantId);

    Set<String> listSuccessBySrcIds(@Param("tenantId") String tenantId,
                                    @Param("sourceObjectApiName") String sourceObjectApiName,
                                    @Param("destObjectApiName") String destObjectApiName,
                                    @Param("sourceDataIdCollection") Collection<String> sourceDataIdCollection);

    /**
     * 统计上次熔断后的异常数据
     */
    int countSyncFailedAfterTime(@Param("tenantId") String tenantId,
                                 @Param("objectApiNameMapping") SyncObjectAndTenantMappingData objectApiNameMapping,
                                 @Param("minTime") Long minTime);

    List<SyncDataMappingsEntity> listByObjApiName(@Param("tenantId") String tenantId,
                                                  @Param("objApiName") String objApiName,
                                                  @Param("limit") Integer limit,
                                                  @Param("offset") Integer offset);
    List<String> queryDeletedData(@Param("tenantId")String tenantId,@Param("minId")String minId);

    int deleteByIds(@Param("tenantId") String tenantId,@Param("ids") Collection<String> ids);

    List<SyncDataMappingsEntity> getMappingByDestObjApiNameAndId(@Param("tenantId") String tenantId, @Param("destObjectApiName") String destObjectApiName, @Param("destObjectId") String destObjectId);

    int countByTimestamp(@Param("tenantId") String tenantId,
                         @Param("sourceObjectApiName") String sourceObjectApiName,
                         @Param("destObjectApiName") String destObjectApiName,
                         @Param("status") Integer status,
                         @Param("startCreateTime") Long startCreateTime,
                         @Param("endCreateTime") Long endCreateTime,
                         @Param("startUpdateTime") Long startUpdateTime,
                         @Param("endUpdateTime") Long endUpdateTime
    );

    int countByIdsTimestamp(@Param("tenantId") String tenantId,
                            @Param("sourceObjectApiName") String sourceObjectApiName,
                            @Param("destObjectApiName") String destObjectApiName,
                            @Param("status") Integer status,
                            @Param("startCreateTime") Long startCreateTime,
                            @Param("startUpdateTime") Long startUpdateTime,
                            @Param("sourceDataIds") List<String> sourceDataIds
    );

    List<SyncDataMappingsEntity> listOrderByUpdateTime(@Param("tenantId") String tenantId, @Param("lastUpdateTime") Long lastUpdateTime, @Param("limit") int limit);

    List<SyncDataMappingsEntity> listOrderByEqUpdateTime(@Param("tenantId") String tenantId, @Param("lastUpdateTime") Long lastUpdateTime);

    int deleteMappingsByMasterId(@Param("tenantId") String tenantId,
                                 @Param("masterDataId") String masterDataId,
                                 @Param("sourceObjectApiName") String sourceObjectApiName,
                                 @Param("destObjectApiName") String destObjectApiName);

    List<SyncDataMappingsEntity> getLastSyncData(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiName") String sourceObjectApiName,
                                           @Param(value = "destObjectApiName") String destObjectApiName, @Param(value = "offset") Integer offset,
                                           @Param(value = "limit") Integer limit);

    SyncDataMappingsEntity getBySourceDataName(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiName") String sourceObjectApiName,
                                               @Param(value = "destObjectApiName") String destObjectApiName, @Param(value = "sourceDataName") String sourceDataName);

    List<SyncDataMappingsEntity> listFailedMappingEqTime(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiName") String sourceObjectApiName, @Param(value = "destObjectApiName") String destObjectApiName, @Param(value = "updateTime") long updateTime, @Param(value = "maxId") String maxId);

    List<SyncDataMappingsEntity> listFailedMappingLtTime(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiName") String sourceObjectApiName, @Param(value = "destObjectApiName") String destObjectApiName, @Param(value = "updateTime") long updateTime);
}
