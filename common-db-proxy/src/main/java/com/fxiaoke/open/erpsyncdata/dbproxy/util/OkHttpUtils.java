package com.fxiaoke.open.erpsyncdata.dbproxy.util;


import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpRequest2;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse2;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponseMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.Request.Builder;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
@Slf4j
@Service
public class OkHttpUtils {

    private static OkHttpSupport okHttpSupport;

    @Autowired
    public void setOkHttpSupport(OkHttpSupport okHttpSupport) {
        OkHttpUtils.okHttpSupport = okHttpSupport;
    }

    private static final SyncCallback syncCallback = new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
            if (Objects.nonNull(response)) {
                HttpResponse httpResponse = new HttpResponse(response);
                return httpResponse;
            } else {
                log.error("the response is null!");
                return null;
            }
        }
    };


    private static final SyncCallback syncCallback2 = new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
            if (Objects.nonNull(response)) {
                HttpResponse2 httpResponse = new HttpResponse2(response,false);
                return httpResponse;
            } else {
                log.warn("syncCallback2 the response is null!");
                return null;
            }
        }
    };


    private static final SyncCallback syncCallbackBase64Encode = new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
            if (Objects.nonNull(response)) {
                HttpResponse2 httpResponse = new HttpResponse2(response,true);
                return httpResponse;
            } else {
                log.warn("syncCallbackBase64Encode the response is null!");
                return null;
            }
        }
    };

    public static HttpResponse post(String url, Map<String, String> headerMap, String body) {
        Request request = buildPostRequest(url, headerMap, body);
        return (HttpResponse)okHttpSupport.syncExecute(request, syncCallback);
    }

    public static HttpResponse postNonblocking(String url, Map<String, String> headerMap, String body) throws IOException {
        Request request = buildPostRequest(url, headerMap, body);
        return (HttpResponse) okHttpSupport.syncNonblockingExecute(request, syncCallback);
    }

    /**
     * 以request builder为参数执行，返回string的response。。简单实现先。
     */
    public static HttpResponse2 execute(HttpRequest2 httpRequest2) {
        Builder builder = new Builder().url(httpRequest2.getUrl());
        Map<String, List<String>> headers = httpRequest2.getHeaders();
        if (headers != null) {
            headers.forEach((k, vs) -> {
                if (vs != null) {
                    vs.forEach(v -> builder.addHeader(k, v));
                }
            });
        }
        @Nullable RequestBody requestBody = httpRequest2.getBody() == null ? null : RequestBody.create(httpRequest2.getContentBytes(), MediaType.parse(httpRequest2.getContentType()));
        builder.method(httpRequest2.getMethod(),
                requestBody);
        Request request = builder.build();
        if (httpRequest2.isResponseBodyNeedBase64Encode()) {
            return (HttpResponse2) okHttpSupport.syncExecute(request, syncCallbackBase64Encode);
        } else {
            return (HttpResponse2) okHttpSupport.syncExecute(request, syncCallback2);
        }
    }

    public static HttpResponseMessage sendHttpPost(String url, Map<String, String> headerMap, String body) {
        HttpResponse httpResponse = post(url, headerMap, body);
        HttpResponseMessage response = new HttpResponseMessage();
        response.setHttpcode(String.valueOf(httpResponse.getCode()));
        response.setMessage(httpResponse.getMessage());
        response.setContent(httpResponse.getBody());
        return response;
    }

    private static Request buildPostRequest(String url, Map<String, String> headerMap, String body) {
        Builder requestBuilder = new Builder();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body);
        headerMap.forEach(requestBuilder::addHeader);
        return requestBuilder.url(url).post(requestBody).build();
    }

}
