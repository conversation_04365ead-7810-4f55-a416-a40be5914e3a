package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.template.QuickConfig;
import com.drew.lang.annotations.NotNull;
import com.fxiaoke.open.erpsyncdata.common.constant.SpuSkuConstant;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RecycleType;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DeleteWithLogicBySourceData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingCountGroupVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.MappingCountVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.data.GetOrCreateByTwoWayData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RecycleBinDao;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncDataMappingManager {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private RecycleBinDao recycleBinDao;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 计数缓存
     */
    private Cache<String,MappingCountGroupVo> countCache;

    @PostConstruct
    public void init(){
        countCache = cacheManager.getOrCreateCache(
                QuickConfig.newBuilder("mappingCount:")
                        .syncLocal(true)
                        .expire(Duration.ofHours(2))
                        .localExpire(Duration.ofMinutes(1))
                        .cacheType(CacheType.BOTH)
                        .build()
        );
    }

    @NotNull
    public long countByStreamIdLimit1000(String tenantId, String streamId, @Nullable Integer syncStatus) {
        SyncPloyDetailEntity entry = syncPloyDetailManager.getEntryById(tenantId, streamId);
        long count = countByObjLimit1000(tenantId, entry.getSourceObjectApiName(), entry.getDestObjectApiName(), syncStatus);
        if (count >= 1000) {
            return 1000;
        }
        if (CollUtil.isNotEmpty(entry.getDetailObjectMappings())) {
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : entry.getDetailObjectMappings()) {
                //明细查询
                long detailCount = countByObjLimit1000(tenantId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName(), syncStatus);
                count += detailCount;
                if (count >= 1000) {
                    return 1000;
                }
            }
        }
        return count;
    }

    @NotNull
    public long countByStreamId(String tenantId, String streamId, @Nullable Integer syncStatus) throws ErpSyncDataException {
        SyncPloyDetailEntity entry = syncPloyDetailManager.getEntryById(tenantId, streamId);
        List<SyncObjectAndTenantMappingData> objectApiNameMappingData = new ArrayList<>();
        objectApiNameMappingData.add(SyncObjectAndTenantMappingData.newInstance(tenantId, entry.getSourceObjectApiName(), entry.getDestObjectApiName()));
        if (CollUtil.isNotEmpty(entry.getDetailObjectMappings())) {
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : entry.getDetailObjectMappings()) {
                //明细查询 只能走缓存，避免重复查。
                objectApiNameMappingData.add(SyncObjectAndTenantMappingData.newInstance(tenantId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName()));
            }
        }
        long count = syncDataMappingsDao.countByObjectApiNames(tenantId, objectApiNameMappingData, syncStatus, null, null, null, null, null, null, null);
        return count;
    }



    @NotNull
    public long countByObjLimit1000(String tenantId, String sourceObjApiName, String destObjApiName, @Nullable Integer syncStatus) {
        return syncDataMappingsDao.countByObjectApiNamesOnlyLimit1000(tenantId, sourceObjApiName, destObjApiName, syncStatus);
    }

    @Nullable
    public MappingCountGroupVo countAllGroupVo(String tenantId, boolean onlyCache, boolean forceRefresh) throws ErpSyncDataException {
        MappingCountGroupVo mappingCountGroupVo = countCache.get(tenantId);
        if (onlyCache) {
            return mappingCountGroupVo;
        }
        if (mappingCountGroupVo != null) {
            if (!forceRefresh || mappingCountGroupVo.getEndTime() < System.currentTimeMillis() - Duration.ofMinutes(5).toMillis()) {
                //缓存在5分钟内强制走缓存
                return mappingCountGroupVo;
            }
        }
        //计算
        String lockKey = CommonConstant.REDIS_LOCK_COUNT_MAPPING + tenantId;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            //加锁成功开始计算
            try {
                //直接查出该企业所有的分组count
                Long beginTime = System.currentTimeMillis();
                List<MappingCountVo> countVos = syncDataMappingsDao.countGroup(tenantId);
                MappingCountGroupVo groupVo = MappingCountGroupVo.builder()
                        .mappingCountVos(countVos)
                        .beginTime(beginTime)
                        .endTime(System.currentTimeMillis())
                        .build();
                //实时计算成功，保存缓存
                countCache.put(tenantId, groupVo);
                return mappingCountGroupVo;
            } catch (Exception e) {
                //计算超时，异常直接抛出，不缓存
                throw ErpSyncDataException.wrap(e);
            } finally {
                lock.unlock();
            }
        }
        throw new ErpSyncDataException(ResultCodeEnum.COUNT_TASK_IN_PROGRESS);
    }

    /**
     * 物理删除，放入回收站（不管是否成功）
     * @param tenantId
     * @param syncDataMappingsEntity
     */
    public void recycle(String tenantId,SyncDataMappingsEntity syncDataMappingsEntity){
        recycleBinDao.insertIgnore(tenantId, RecycleType.SYNC_DATA_MAPPING,syncDataMappingsEntity);
        this.deleteByTenantIdAndId(tenantId, syncDataMappingsEntity.getId());
    }


    /**
     * 物理删除，放入回收站,成功后再删除删除syncData
     * @param tenantId
     */
    public void recycleBatch(String tenantId, List<String> ids){
        List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(tenantId).listByIds(tenantId, ids);
        recycleBatchMapping(tenantId, syncDataMappingsEntities);
    }

    public void recycleBatchMapping(String tenantId, List<SyncDataMappingsEntity> syncDataMappingsEntities) {
        if (CollUtil.isEmpty(syncDataMappingsEntities)){
            return;
        }
        //如果异常将不会删除
        recycleBinDao.batchInsert(tenantId, RecycleType.SYNC_DATA_MAPPING, syncDataMappingsEntities);
        final List<String> collect = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
        this.deleteByTenantIdAndIds(tenantId, collect);
        List<DeleteWithLogicBySourceData> deleteWithLogicBySourceDatas = syncDataMappingsEntities.stream()
                .map(val -> new DeleteWithLogicBySourceData(val.getSourceTenantId(), val.getSourceObjectApiName(), val.getSourceDataId(), val.getDestTenantId(), val.getDestObjectApiName()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteWithLogicBySourceDatas)) {
            syncDataFixDao.deleteWithLogicBySourceDatas(tenantId, deleteWithLogicBySourceDatas);
        }
    }

    /**
     * 是否存在映射，一定已创建
     * 当结果为true时缓存，缓存两分钟
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES, postCondition = "#result",localLimit = 10000)
    public boolean existByTwoWay(String tenantId, String sourceObjectApiName, String sourceDataId, String destObjectApiName) {
        //此处调用的方法，如果left不为空，right就不查询了一定为空
        Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = getMappingFirstBySource(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
        if (mapping2Way.getLeft() != null) {
            return mapping2Way.getLeft().getIsCreated();
        }
        if (mapping2Way.getRight() != null) {
            return mapping2Way.getRight().getIsCreated();
        }
        return false;
    }

    /**
     * 获取双向的mapping
     *
     * @return pair 正向mapping，反向mapping,都可能为null
     */
    public Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> getMapping2Way(String tenantId, String sourceTenantId, String sourceObjectApiName, String sourceDataId, String destTenantId, String destObjectApiName) {
        SyncDataMappingsEntity sourceMapping = syncDataMappingsDao.setTenantId(tenantId).getByUninKey(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
        SyncDataMappingsEntity destMapping = null;
        if (syncPloyDetailManager.checkStreamExist(tenantId, destObjectApiName, sourceObjectApiName)) {
            //存在反向集成流时，检查反向的映射
            destMapping = syncDataMappingsDao.setTenantId(tenantId).getByUninKeyReverse(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
        }
        return Pair.of(sourceMapping, destMapping);
    }

    /**
     * 获取双向的mapping
     *
     * @return pair 正向mapping，反向mapping,都可能为null,如果正向mapping不为空，不查反向mapping
     */
    public Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> getMappingFirstBySource(String tenantId, String sourceObjectApiName, String sourceDataId, String destObjectApiName) {
        SyncDataMappingsEntity sourceMapping = syncDataMappingsDao.setTenantId(tenantId).getByUninKey(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
        SyncDataMappingsEntity destMapping = null;
        if (sourceMapping == null) {
            if (syncPloyDetailManager.checkStreamExist(tenantId, destObjectApiName, sourceObjectApiName)) {
                //sourceMapping为空，并且存在反向集成流时，检查反向的映射
                destMapping = syncDataMappingsDao.setTenantId(tenantId).getByUninKeyReverse(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
            }
        }
        return Pair.of(sourceMapping, destMapping);
    }

    private void processSpuAndProduct(ObjectData detailData, SyncDataMappingsEntity dataMappingData) {
        if (detailData.getApiName().equals(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ)) {
            dataMappingData.setSourceObjectApiName(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ);
            dataMappingData.setSourceDataName(detailData.getId());
        } else if (detailData.getApiName().equals(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ)) {
            dataMappingData.setSourceObjectApiName(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ);
            dataMappingData.setSourceDataName(detailData.getId());
        }
    }

    public GetOrCreateByTwoWayData getOrCreateSyncDataMappingByTwoWay(String tenantId, String syncDataId, int status, String destObjectApiName, String destTenantId, ObjectData sourceData, boolean isCreate, String  sourceMasterId) {
        GetOrCreateByTwoWayData createResult = new GetOrCreateByTwoWayData();
        String sourceTenantId = sourceData.getTenantId();
        String sourceObjectApiName = sourceData.getApiName();
        String sourceDataId = sourceData.getId();
        Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = getMapping2Way(tenantId, sourceTenantId, sourceObjectApiName, sourceDataId, destTenantId, destObjectApiName);
        SyncDataMappingsEntity sourceMapping = mapping2Way.getLeft();
        SyncDataMappingsEntity destMapping = mapping2Way.getRight();
        if (sourceMapping != null) {
            createResult.setCreate(false);
            createResult.setSourcedData(sourceMapping);
            createResult.setDestData(destMapping);
            /**
             * 如果源映射不为空，目标映射也不为空，那么如果目标映射中目标数据被成功创建的话，则将源映射的数据是否被成功创建也改为true
             */
            if (destMapping != null && !sourceMapping.getIsCreated() && destMapping.getIsCreated()) {
                syncDataMappingsDao.setTenantId(tenantId).updateCreatedById(tenantId, sourceMapping.getId(),true);
                sourceMapping.setIsCreated(true);
            }
            return createResult;
        }
        if (!isCreate) {
            createResult.setCreate(false);
            createResult.setDestData(destMapping);
            return createResult;
        }
        String destDataId = null;
        boolean mappingIsCreated = false;
        if (destMapping != null) {
            destDataId = destMapping.getSourceDataId();
            mappingIsCreated = true;
        } else {
            destDataId = idGenerator.get(tenantId,sourceDataId);
        }
        createResult = createSyncDataMapping(tenantId, syncDataId, status, destObjectApiName, destTenantId, destDataId, null, sourceData, mappingIsCreated,sourceMasterId);
        if (destMapping != null) {
            createResult.setDestData(destMapping);
            createResult.setCreate(false);
        }
        return createResult;
    }

    public GetOrCreateByTwoWayData createSyncDataMapping(String tenantId, String syncDataId, int status, String destObjectApiName, String destTenantId, String destDataId, String destDataName, ObjectData sourceData,
                                                         Boolean mappingIsCreated,String sourceMasterId) {
        String dataMappingEntityId = idGenerator.get(tenantId,sourceData.getId());
        SyncDataMappingsEntity dataMappingData = new SyncDataMappingsEntity();
        dataMappingData.setId(dataMappingEntityId);
        dataMappingData.setSourceDataId(sourceData.getId());
        dataMappingData.setSourceDataName(sourceData.getName());
        dataMappingData.setTenantId(tenantId);
        dataMappingData.setSourceTenantId(sourceData.getTenantId());
        dataMappingData.setSourceObjectApiName(sourceData.getApiName());
        dataMappingData.setLastSourceDataVserion(sourceData.getVersion());
        dataMappingData.setLastSyncStatus(status);
        dataMappingData.setLastSyncDataId(syncDataId);
        dataMappingData.setDestObjectApiName(destObjectApiName);
        dataMappingData.setDestTenantId(destTenantId);
        dataMappingData.setDestDataId(destDataId);
        dataMappingData.setDestDataName(destDataName);
        dataMappingData.setIsCreated(mappingIsCreated);
        dataMappingData.setIsDeleted(false);
        dataMappingData.setCreateTime(System.currentTimeMillis());
        //从对象创建的时候，就指定主对象id.主对象不存在的情况下，在前面的事件已经被拦截了
        dataMappingData.setMasterDataId(sourceMasterId);
        processSpuAndProduct(sourceData, dataMappingData);
        int result = syncDataMappingsDao.setTenantId(tenantId).insertIgnore(dataMappingData);
        if (result == 1) {
            GetOrCreateByTwoWayData createResult = new GetOrCreateByTwoWayData();
            createResult.setCreate(true);
            createResult.setSourcedData(dataMappingData);
            return createResult;
        }
        GetOrCreateByTwoWayData createResult = new GetOrCreateByTwoWayData();
        createResult.setCreate(false);
        SyncDataMappingsEntity oldEntity = syncDataMappingsDao.setTenantId(tenantId).getByUninKey(tenantId, sourceData.getApiName(), sourceData.getId(), destObjectApiName);
        createResult.setSourcedData(oldEntity);
        return createResult;
    }


    /**
     * 根据时间和id排序来翻页
     * 仅用于列表页
     * 从对象页面需要再另外写
     *
     * @param tenantId                 企业id
     * @param objectApiNameMappingData 仅允许查询单个对象
     * @param maxId                    为null时查询下一段时间，不为null时，查询=maxTime的数据
     * @param needReturnTotalNum       是否返回总数，最大1000
     * @return
     */
    public Page<SyncDataMappingsEntity> listMappingsAfterTimeAndId(String tenantId,
                                                                    SyncObjectAndTenantMappingData objectApiNameMappingData,
                                                                    Integer limit,
                                                                    Long maxTime,
                                                                    String maxId,
                                                                    Integer status,
                                                                    String sourceDataId,
                                                                    String destDataId,
                                                                    String sourceDataName,
                                                                    String destDataName,
                                                                    String remark,
                                                                    Long startTime,
                                                                    boolean needReturnTotalNum) {
        Page<SyncDataMappingsEntity> page = new Page<>();
        List<SyncDataMappingsEntity> entryList;
        //只精确查询id
        Boolean isOnlySearchId = tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.ONLY_SEARCH_ID_TENANTS);
        if (isOnlySearchId && !StringUtils.isAllEmpty(sourceDataId, destDataId)) {
            //只精确搜索id
            String searchId = ObjectUtils.isEmpty(sourceDataId) ? destDataId : sourceDataId;
            entryList = searchById(tenantId, searchId, Collections.singletonList(objectApiNameMappingData));
            page.setTotalNum(entryList.size());
        } else {
            StopWatch sw = new StopWatch();
            try {
                sw.start("listByObjectApiNames2");
                entryList = syncDataMappingsDao.setTenantId(tenantId).listByObjectApiNames2(tenantId,
                        objectApiNameMappingData,
                        status,
                        sourceDataId,
                        sourceDataName,
                        destDataId,
                        destDataName,
                        remark,
                        limit,
                        maxTime,
                        maxId,
                        startTime);
            } catch (Exception e) {
                log.warn("listByObjectApiNames2 timeout");
                throw new ErpSyncDataException(I18NStringEnum.s120,tenantId);
            } finally {
                sw.stop();
                log.info("listByObjectApiNames2 cost:{}", sw.getTotalTimeMillis());
            }
            if (needReturnTotalNum) {
                //统计总数量,只查询1000以内
                int totalCount = syncDataMappingsDao.setTenantId(tenantId).countByObjectApiNames2Limit1000(tenantId, objectApiNameMappingData, status,
                        sourceDataId, sourceDataName, destDataId, destDataName, remark, startTime);
                page.setTotalNum(totalCount);
            }
        }
        page.setData(entryList);
        page.setHasNext(limit.equals(entryList.size()));
        return page;
    }

    public List<SyncDataMappingsEntity> searchById(String tenantId, String searchId, List<SyncObjectAndTenantMappingData> objectApiNameMappingDatas) {
        List<SyncDataMappingsEntity> entryList;
        entryList = new ArrayList<>();
        SyncDataMappingsEntity byId = syncDataMappingsDao.setTenantId(tenantId).getById(tenantId, searchId);
        if (byId != null) {
            entryList.add(byId);
        }
        for (SyncObjectAndTenantMappingData objectApiNameMappingData : objectApiNameMappingDatas) {
            SyncDataMappingsEntity bySourceData = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, objectApiNameMappingData.getSourceObjectApiName(), objectApiNameMappingData.getDestObjectApiName(), searchId);
            if (bySourceData != null) {
                entryList.add(bySourceData);
            }
            SyncDataMappingsEntity byDestData = syncDataMappingsDao.setTenantId(tenantId).getByDestData(tenantId, objectApiNameMappingData.getSourceObjectApiName(), objectApiNameMappingData.getDestObjectApiName(), searchId);
            if (byDestData != null) {
                entryList.add(byDestData);
            }
        }
        return entryList;
    }


    public Integer updateLastSyncStatusById(String tenantId, String id, Integer status, String remark) {
        return syncDataMappingsDao.setTenantId(tenantId).updateLastSyncStatusById(tenantId, id, status, remark, System.currentTimeMillis());
    }

    public int updateSourceDataIdById(String tenantId,
                               String id,
                               String sourceDataId) {
        return syncDataMappingsDao.updateSourceDataIdById(tenantId, id, sourceDataId);
    }

    public int updateDestDataIdById(String tenantId,
                             String id,
                             String destDataId) {
        return syncDataMappingsDao.updateDestDataIdById(tenantId, id, destDataId);
    }

    public int deleteByTenantIdAndId(String tenantId,String id){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteByTenantIdAndId(tenantId,id);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteByTenantIdAndId(tenantId,id);
    }

    public int deleteByTenantIdAndIds(String tenantId, List<String> ids){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteByTenantIdAndIds(tenantId,ids);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteByTenantIdAndIds(tenantId,ids);
    }

    public int deleteSyncDataMappings(String tenantId,String sourceObjApiName,String destObjApiName){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteSyncDataMappings(tenantId,sourceObjApiName,destObjApiName);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteSyncDataMappings(tenantId,sourceObjApiName,destObjApiName);
    }
    public int deleteMappingsByDestId(String tenantId,String dataId,String sourceObjectApiName,String destObjectApiName){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteMappingsByDestId(tenantId,dataId,sourceObjectApiName,destObjectApiName);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteMappingsByDestId(tenantId,dataId,sourceObjectApiName,destObjectApiName);
    }

    public int deleteMappingsBySourceId(String tenantId,String dataId,String sourceObjectApiName,String destObjectApiName){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteMappingsBySourceId(tenantId,dataId,sourceObjectApiName,destObjectApiName);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteMappingsBySourceId(tenantId,dataId,sourceObjectApiName,destObjectApiName);
    }
    public int deleteMappingsByMasterId(String tenantId,String masterDataId,String sourceObjectApiName,String destObjectApiName){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteMappingsByMasterId(tenantId,masterDataId,sourceObjectApiName,destObjectApiName);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteMappingsByMasterId(tenantId,masterDataId,sourceObjectApiName,destObjectApiName);
    }
    public int deleteByIds(String tenantId,Collection<String> ids){
        String destRoute=getTenantDestRoute(tenantId);
        if(StringUtils.isNotBlank(destRoute)){
            try {
                int delete=syncDataMappingsDao.setTenantId(destRoute).deleteByIds(tenantId,ids);
                log.info("operate dest db tenantId={} destRoute={} delete={}",tenantId,destRoute,delete);
            }catch (Exception e){
                log.info("operate dest db tenantId={} destRoute={} e={}",tenantId,destRoute,e);
            }
        }
        return syncDataMappingsDao.setTenantId(tenantId).deleteByIds(tenantId,ids);
    }
    private String getTenantDestRoute(String tenantId){
        try {
            Map<String,Object> result=tenantConfigurationManager.needBidirectionalWriting(tenantId);
            if(result!=null){
                if(result.containsKey("destRoute")){
                    return result.get("destRoute").toString();
                }
            }
        }catch (Exception e){
           log.info("needBidirectionalWriting e={}",e);
        }
        return null;
    }

    public List<SyncDataMappingsEntity> getLastSyncData(String tenantId, String sourceObjectApiName, String destObjectApiName, Integer offset, Integer limit) {
        return syncDataMappingsDao.setTenantId(tenantId).getLastSyncData(tenantId,sourceObjectApiName,destObjectApiName,offset,limit);
    }
}
