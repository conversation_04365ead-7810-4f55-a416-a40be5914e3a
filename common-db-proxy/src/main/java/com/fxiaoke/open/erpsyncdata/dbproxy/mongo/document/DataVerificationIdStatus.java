package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdCheckStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 18:00 2022/11/9
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class DataVerificationIdStatus implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * 核对任务id
     */
    private String dataVerificationTaskId;
    /**
     * 数据id
     */
    private String dataId;

    /**
     * 日志id
     */
    private String syncLogId;

    /**
     * 数据同步状态
     */
    private IdSyncStatus status;

    /**
     * 数据核对状态
     */
    private IdCheckStatus checkStatus;

    /**
     * 数据状态原因
     */
    private String statusReason;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}
