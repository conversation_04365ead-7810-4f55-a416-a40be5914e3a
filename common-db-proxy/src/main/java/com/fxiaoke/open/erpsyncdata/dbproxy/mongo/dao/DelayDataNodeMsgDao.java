package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.MongoClientSettings;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mongodb.client.model.Filters.and;

/**
 * <AUTHOR>
 * @Date: 19:01 2023/1/12
 * @Desc:
 */
@Slf4j
@Component
@DependsOn("erpSyncDataLogMongoStore")
public class DelayDataNodeMsgDao {
    private static final String f_id = "_id";
    private static final String f_nodeTimes = "nodeTimes";
    private static final String f_tenantId = "tenantId";
    private static final String f_isTop60 = "isTop60";
    private static final String f_uniqueKey = "uniqueKey";
    private static final String f_noVersionUniqueKey = "noVersionUniqueKey";
    private static final String f_objApiName = "objApiName";
    private static final String f_dataId = "dataId";
    private static final String f_version = "version";
    private static final String f_streamId = "streamId";
    private static final String f_nodeTypes = "nodeTypes";
    private static final String f_nodeNames = "nodeNames";
    private static final String f_time = "time";
    private static final String f_nodeRemarks = "nodeRemarks";
    private static final String f_traceId = "traceId";

    private static final String f_createTime = "createTime";
    private static final String f_updateTime = "updateTime";
    private String DATABASE;
    private DatastoreExt store;
    private final static String collectionName = "delay_data_node_msg";

    DelayDataNodeMsgDao() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
    }


    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(DataNodeMsgDoc.class)
                        .automatic(true).build()));
    }

    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    public void setStore(DatastoreExt store) {
        this.store = store;
    }


    public void deleteByNoVersionUniqueKey(List<String> noVersionUniqueKeyList) {
        MongoCollection<DataNodeMsgDoc> mongoCollection = this.getOrCreateDelayDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_noVersionUniqueKey, noVersionUniqueKeyList));
        mongoCollection.deleteMany(Filters.and(filters));
    }

    public void deleteByIds(List<ObjectId> idList) {
        if(CollectionUtils.isEmpty(idList)){
            return;
        }
        MongoCollection<DataNodeMsgDoc> mongoCollection = this.getOrCreateDelayDataNodeMsgCollection();
        List<Bson> filters = Lists.newArrayList();
        filters.add(Filters.in(f_id, idList));

        mongoCollection.deleteMany(Filters.and(filters));
    }


    public void batchUpsertDataNodeMsgDoc(List<DataNodeMsgDoc> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        int size = dataList.size();
        StopWatch sw = new StopWatch("batchUpsert-" + collectionName + '[' + size + ']');
        List<WriteModel<DataNodeMsgDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (int i = 0; i < dataList.size(); i++) {
            DataNodeMsgDoc dataNodeMsgDoc = dataList.get(i);
            UpdateOneModel<DataNodeMsgDoc> updateOneModel = new UpdateOneModel<>(this.updateBy(dataNodeMsgDoc), this.upsert(dataNodeMsgDoc), updateOption);
            requests.add(updateOneModel);
        }

        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);
        sw.start("bulkWrite-" + collectionName + '[' + size + ']');
        MongoCollection<DataNodeMsgDoc> collection = this.getOrCreateDelayDataNodeMsgCollection();
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
            sw.stop();
            long cost = sw.getTotalTimeMillis();
            if (cost > 5000) {
                log.warn("bulkWrite collection: {}, cost: {}ms, items: {}", collectionName, cost, size);
                log.warn(sw.prettyPrint());
            }
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
    }

    private Bson updateBy(DataNodeMsgDoc message) {
        Document result = new Document();
        result.put(f_noVersionUniqueKey, message.getNoVersionUniqueKey());
        return result;
    }

    private Bson upsert(DataNodeMsgDoc message) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();
        Document addToSet = new Document();

        //更新
        updateDoc.append(f_updateTime, new Date());
        if(StringUtils.isNotBlank(message.getTraceId())){
            updateDoc.append(f_traceId, message.getTraceId());
        }

        //插入
        setOnInsertDoc
                .append(f_tenantId, Objects.requireNonNull(message.getTenantId(), "tenantId cannot be null"))
                .append(f_isTop60, message.getIsTop60())
                .append(f_objApiName, Objects.requireNonNull(message.getObjApiName(), "objApiName cannot be null"))
                .append(f_dataId, Objects.requireNonNull(message.getDataId(), "dataId cannot be null"))
                .append(f_version, Objects.requireNonNull(message.getVersion(), "version cannot be null"))
                .append(f_streamId, Objects.requireNonNull(message.getStreamId(), "streamId cannot be null"))
                .append(f_uniqueKey, Objects.requireNonNull(message.getUniqueKey(), "uniqueKey cannot be null"))
                .append(f_noVersionUniqueKey, Objects.requireNonNull(message.getNoVersionUniqueKey(), "noVersionUniqueKey cannot be null"))
                .append(f_time, message.getTime())
                .append(f_createTime, new Date());

        //添加到集合
        if (CollectionUtils.isNotEmpty(message.getNodeTypes())) {
            addToSet.put(f_nodeTypes, new Document("$each", message.getNodeTypes()));
        }
        if (CollectionUtils.isNotEmpty(message.getNodeTypes())) {
            addToSet.put(f_nodeNames, new Document("$each", message.getNodeNames()));
        }
        if (CollectionUtils.isNotEmpty(message.getNodeTypes())) {
            addToSet.put(f_nodeRemarks, new Document("$each", message.getNodeRemarks()));
        }
        if (CollectionUtils.isNotEmpty(message.getNodeTimes())) {
            addToSet.put(f_nodeTimes, new Document("$each", message.getNodeTimes()));
        }

        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        if (!addToSet.isEmpty()) {
            doc.put("$addToSet", addToSet);
        }
        return doc;
    }

    public List<DataNodeMsgDoc> listDataNodeMsg(Boolean isTop60, String tenantId, String objApiName, String dataId,Integer limit,Integer offset) {
        MongoCollection<DataNodeMsgDoc> collectionList = this.getOrCreateDelayDataNodeMsgCollection();
        List<Bson> filters = new ArrayList<>();
        if(StringUtils.isNotBlank(tenantId)){
            filters.add(Filters.eq(f_tenantId, tenantId));
        }
        if(isTop60!=null){
            filters.add(Filters.eq(f_isTop60, isTop60));
        }
        if(StringUtils.isNotBlank(objApiName)){
            filters.add(Filters.eq(f_objApiName, objApiName));
        }
        if(StringUtils.isNotBlank(dataId)){
            filters.add(Filters.eq(f_dataId, dataId));
        }
        Bson sort = Sorts.descending( f_id);
        if(CollectionUtils.isNotEmpty(filters)){
            Bson filter = and(filters);
            FindIterable<DataNodeMsgDoc> dataNodeMsgDocs = collectionList.find(filter).sort(sort).skip(offset).limit(limit);
            return Lists.newArrayList(dataNodeMsgDocs);
        }else{
            FindIterable<DataNodeMsgDoc> dataNodeMsgDocs = collectionList.find().sort(sort).skip(offset).limit(limit);
            return Lists.newArrayList(dataNodeMsgDocs);
        }
    }

    public MongoCollection<DataNodeMsgDoc> getOrCreateDelayDataNodeMsgCollection() {
        MongoCollection<DataNodeMsgDoc> collectionList = store.getMongo().getDatabase(DATABASE).withCodecRegistry(DelayDataNodeMsgDao.SingleCodecHolder.codecRegistry).getCollection(collectionName, DataNodeMsgDoc.class);
        List<String> exists = Lists.newArrayList();
        collectionList.listIndexes().iterator().forEachRemaining(doc -> {
            String name = doc.getString("name");
            if (!"_id_".equals(name)) {
                exists.add(name);
            }
        });
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending(f_createTime);
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(5L, TimeUnit.DAYS).background(true)));
        }
        key = "index_noVersionUniqueKey";
        if (!exists.remove(key)) {
            Bson index_noVersionUniqueKey = Indexes.compoundIndex(Indexes.ascending(f_noVersionUniqueKey));
            toBeCreate.add(new IndexModel(index_noVersionUniqueKey, new IndexOptions().name(key).background(true)));
        }
        key = "index_dataId";
        if (!exists.remove(key)) {
            Bson index_dataId = Indexes.compoundIndex(Indexes.ascending(f_dataId));
            toBeCreate.add(new IndexModel(index_dataId, new IndexOptions().name(key).background(true)));
        }
        key = "index_obj";
        if (!exists.remove(key)) {
            Bson index_obj = Indexes.compoundIndex(Indexes.ascending(f_objApiName));
            toBeCreate.add(new IndexModel(index_obj, new IndexOptions().name(key).background(true)));
        }
        key = "index_top60";
        if (!exists.remove(key)) {
            Bson index_top60 = Indexes.compoundIndex(Indexes.ascending(f_isTop60));
            toBeCreate.add(new IndexModel(index_top60, new IndexOptions().name(key).background(true)));
        }
        key = "index_ei_obj_dataId";
        if (!exists.remove(key)) {
            Bson index_ei_obj_dataId = Indexes.compoundIndex(Indexes.ascending(f_tenantId), Indexes.ascending(f_objApiName), Indexes.ascending(f_dataId));
            toBeCreate.add(new IndexModel(index_ei_obj_dataId, new IndexOptions().name(key).background(true)));
        }
        if (!toBeCreate.isEmpty()) {
            List<String> created = collectionList.createIndexes(toBeCreate);
            log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        }
        return collectionList;
    }

}
