package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDBProxyConfigEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ErpDbProxyConfigDao extends ErpBaseDao<ErpDBProxyConfigEntity>,ITenant<ErpDbProxyConfigDao> {

    ErpDBProxyConfigEntity getDBProxyConfigByTenantAndObjApiName(@Param("tenantId") String tenantId, @Param("objApiName") String objApiName, @Param("datacenterId") String datacenterId);

    List<ErpDBProxyConfigEntity> queryDBProxyConfigByTenantAndParentObjApiName(@Param("tenantId") String tenantId, @Param("parentObjApiName") String parentObjApiName, @Param("datacenterId") String datacenterId);

    int deleteByTenantId(@Param("tenantId")String tenantId);

}
