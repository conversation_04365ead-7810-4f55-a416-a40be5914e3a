package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TokenVersionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushIdentifyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class PushIdentifyManager {
    @Autowired
    private ErpPushIdentifyDao erpPushIdentifyDao;


    private static final String keyAddStr = "!&(*)Ash&^d%";


    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 2, postCondition = "#result")
    public boolean checkIdentify(String tenantId, String token) {
        if (tenantId == null || token == null) {
            return false;
        }
        String token1 = getToken(tenantId);
        return Objects.equals(token1, token);
    }

    public String getToken(String tenantId) {
        if (tenantId == null) {
            return null;
        }
        ErpPushIdentifyEntity entity = erpPushIdentifyDao.getByTenantId(tenantId);
        if (entity == null) {
            return null;
        }
        return entity.getToken();
    }


    public String getOrCreateToken(String tenantId) {
        ErpPushIdentifyEntity entity = erpPushIdentifyDao.getByTenantId(tenantId);
        if (entity == null) {
            return generatorTokenKey(tenantId, TokenVersionEnum.MD5.getValue());
        } else {
            return entity.getToken();
        }
    }


    public String generatorTokenKey(String tenantId, String version) {
        String token;
        if (TokenVersionEnum.MD5.getValue().equals(version)) {
            token = generatorKeyByMd5(tenantId, version);
            log.info("tenantId:[{}] generator token success version:[{}],token:[{}]", tenantId, version, token);
        } else {
            throw new IllegalArgumentException(I18NStringEnum.s729.getText());
        }
        saveToken(tenantId, version, token);
        return token;
    }

    private void saveToken(String tenantId, String version, String token) {
        ErpPushIdentifyEntity pushIdentifyEntity = erpPushIdentifyDao.getByTenantId(tenantId);
        if (pushIdentifyEntity == null) {
            pushIdentifyEntity = new ErpPushIdentifyEntity();
            pushIdentifyEntity.setId(IdGenerator.get());
            pushIdentifyEntity.setTenantId(tenantId);
            pushIdentifyEntity.setVersion(version);
            pushIdentifyEntity.setToken(token);
            pushIdentifyEntity.setCreateTime(new Date().getTime());
            pushIdentifyEntity.setUpdateTime(new Date().getTime());
            erpPushIdentifyDao.insert(pushIdentifyEntity);
        } else {
            pushIdentifyEntity.setToken(token);
            pushIdentifyEntity.setUpdateTime(new Date().getTime());
            erpPushIdentifyDao.updateById(pushIdentifyEntity);
        }
    }

    private String generatorKeyByMd5(String tenantId, String version) {
        String sourceStr = tenantId + keyAddStr + version + System.currentTimeMillis();
        String key = DigestUtils.md5DigestAsHex(sourceStr.getBytes());
        return key;
    }
}
