package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/1/10 15:56
 * @Version 1.0
 */
@Service
@Slf4j
public class ErpFieldMappingManager {

    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    public List<ErpFieldDataMappingEntity> listByErpIds(String tenantId, String dcId, ErpFieldTypeEnum dataType, List<Object> erpDataIds){
        List erpString=erpDataIds.stream().map(item ->item.toString()).collect(Collectors.toList());
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities = erpFieldDataMappingDao.listByErpIds(tenantId, dcId, dataType, erpString);
        return erpFieldDataMappingEntities;

    }
}
