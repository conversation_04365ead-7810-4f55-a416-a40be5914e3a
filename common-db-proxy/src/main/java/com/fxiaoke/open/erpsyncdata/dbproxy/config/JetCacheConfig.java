package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import com.alicp.jetcache.CacheBuilder;
import com.alicp.jetcache.anno.CacheConsts;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.GlobalCacheConfig;
import com.alicp.jetcache.anno.support.JetCacheBaseBeans;
import com.alicp.jetcache.embedded.EmbeddedCacheBuilder;
import com.alicp.jetcache.embedded.LinkedHashMapCacheBuilder;
import com.alicp.jetcache.support.FastjsonKeyConvertor;
import com.alicp.jetcache.support.Kryo5ValueDecoder;
import com.alicp.jetcache.support.Kryo5ValueEncoder;
import com.fxiaoke.open.erpsyncdata.common.jetcache.CustomCacheMonitor;
import com.fxiaoke.open.erpsyncdata.common.jetcache.RedissonCacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@EnableMethodCache(basePackages = {"com.fxiaoke.open.erpsyncdata"}, order = 0)
@Import(JetCacheBaseBeans.class) //need since jetcache 2.7+
public class JetCacheConfig {

    private static final String remoteCacheRoom = "erpdssJetCacheNotifyRoom";

    @Autowired
    private RedissonClient redissonClient;

    @Autowired(required = false)
    private List<CustomCacheMonitor> customCacheMonitors;

    @Bean
    public GlobalCacheConfig config() {
        Map<String, CacheBuilder> localBuilders = new HashMap<>();
        EmbeddedCacheBuilder<LinkedHashMapCacheBuilder.LinkedHashMapCacheBuilderImpl> localBuilder = LinkedHashMapCacheBuilder
                .createLinkedHashMapCacheBuilder()
                .keyConvertor(FastjsonKeyConvertor.INSTANCE);
        localBuilders.put(CacheConsts.DEFAULT_AREA, localBuilder);

        Map<String, CacheBuilder> remoteBuilders = new HashMap<>();
        RedissonCacheBuilder.RedissonDataCacheBuilderImpl redissonDataCacheBuilder = RedissonCacheBuilder.createBuilder()
                .redissonClient(redissonClient)
                //没作用，遍寻源码，发现压根没调用订阅方法。也懒得改造了，自己写一个简单的二级缓存。
                .broadcastChannel(remoteCacheRoom)
                //默认十分钟超时
                .expireAfterWrite(10L, TimeUnit.MINUTES)
                .keyPrefix("erpSyncData:cache:")
                //这个性能好
                .valueEncoder(Kryo5ValueEncoder.INSTANCE)
                .valueDecoder(Kryo5ValueDecoder.INSTANCE)
                .keyConvertor(FastjsonKeyConvertor.INSTANCE);
        remoteBuilders.put(CacheConsts.DEFAULT_AREA, redissonDataCacheBuilder);

        if (customCacheMonitors != null && !customCacheMonitors.isEmpty()) {
            //注入自定义监视器！
            for (CustomCacheMonitor customCacheMonitor : customCacheMonitors) {
                localBuilder.addMonitor(customCacheMonitor);
                redissonDataCacheBuilder.addMonitor(customCacheMonitor);
            }
        }

        GlobalCacheConfig globalCacheConfig = new GlobalCacheConfig();
        globalCacheConfig.setLocalCacheBuilders(localBuilders);
        globalCacheConfig.setRemoteCacheBuilders(remoteBuilders);
        globalCacheConfig.setStatIntervalMinutes(15);
        return globalCacheConfig;
    }
}