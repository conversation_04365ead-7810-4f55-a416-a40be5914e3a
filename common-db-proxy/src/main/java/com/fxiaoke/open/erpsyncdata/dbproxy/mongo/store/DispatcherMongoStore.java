package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.fxiaoke.dispatcher.common.Constants;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoSocketReadTimeoutException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.BsonDocument;
import org.bson.BsonString;
import org.bson.Document;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @Date: 15:47 2021/12/14
 * @Desc:
 */

@Slf4j
public class DispatcherMongoStore {

    private DatastoreExt store;
    private String dbName;
    private static TimedCache<String, Set<String>> collectionNames = CacheUtil.newTimedCache(5 * 60 * 1000L);
    private int maxWaitSeconds = 60;

    @PostConstruct
    public void init() {
        initTenantCollectionNamesCache();
    }

    public DispatcherMongoStore(DatastoreExt store) {
        this.store = store;
        dbName = store.getDB().getName();
    }

    public Map<String, Long> getAllStoreCollectionSize() {
        Set<String> collectionNames = store.getDB().getCollectionNames();
        Map<String, Long> collectionSize = Maps.newHashMap();
        collectionNames.forEach(collectionName -> {
            collectionSize.put(collectionName, store.getDB().getCollection(collectionName).count());
        });
        return collectionSize;
    }

    public Long getStoreCollectionSize(String tenantId) {
        Long collectionSize = 0L;
        Set<String> collectionNames = getTenantCollectionNames(tenantId);
        BasicDBObject query = new BasicDBObject();
        addTenantFilter(query, tenantId);
        query.put("status", new BasicDBObject("$ne", Constants.STATUS_ERROR));
        for (String name : collectionNames) {
            long count = store.getDB().getCollection(name).count(query);
            collectionSize += count;

        }
        return collectionSize;
    }

    private Set<String> getTenantCollectionNames(String tenantId) {
        String collectionName = getCollectionName(tenantId);
        Set<String> collectionNameList = this.collectionNames.get(collectionName, false);
        if (collectionNameList == null) {
            initTenantCollectionNamesCache();
            collectionNameList = this.collectionNames.get(collectionName, false);
        }
        if (collectionNameList == null) {
            return Sets.newHashSet();
        }
        return collectionNameList;
    }

    private Set<String> initTenantCollectionNamesCache() {
        Map<String, Set<String>> map = Maps.newHashMap();
        Set<String> collectionNameList = store.getDB().getCollectionNames();
        for (String name : collectionNameList) {
            if (!name.startsWith("buf_")) {
                continue;
            }
            if (name.startsWith("buf_merge_")) {
                map.put(name, Sets.newHashSet(name));
            }
            String[] s = name.split("_");
            if (s.length > 1) {
                String ei = s[1];
                String collectionName = getCollectionName(ei);
                if (map.containsKey(collectionName)) {
                    map.get(collectionName).add(name);
                } else {
                    map.put(collectionName, Sets.newHashSet(name));
                }
            }
        }
        for (String collectionName : map.keySet()) {
            this.collectionNames.put(collectionName, map.get(collectionName));
        }

        initMergeCollectionTenantIndex();
        return collectionNameList;
    }

    public Set<String> getAllCollectionNameList() {
        Set<String> all = collectionNames.get("all", false);
        if (all == null) {
            Set<String> collectionNameList = store.getDB().getCollectionNames();
            collectionNames.put("all", collectionNameList);
            return collectionNameList;
        }
        return all;
    }

    public Long getStoreCollectionCountLimit(String tenantId) {
        Long collectionSize = 0L;
        String collectionName = getCollectionName(tenantId);
        Set<String> collectionNames = store.getDB().getCollectionNames();
        final BsonDocument filter = new BsonDocument();
        addTenantFilter(filter, tenantId);
        for (String name : collectionNames) {
            if (name.startsWith(collectionName)) {
                CountOptions options = new CountOptions().limit(10000).maxTime(10, TimeUnit.SECONDS);
                //有可能有特别对象单独collection
                long count = store.getMongo().getDatabase(dbName).getCollection(name).countDocuments(filter, options);
                collectionSize += count;
            }
        }
        return collectionSize;
    }


    private Long getTenantBufCollectionCount(Set<String> collectionNames) {
        Long num = 0L;
        if (org.springframework.util.CollectionUtils.isEmpty(collectionNames)) {
            return num;
        }
        EstimatedDocumentCountOptions options = new EstimatedDocumentCountOptions().maxTime(maxWaitSeconds, TimeUnit.SECONDS);
        for (String topic : collectionNames) {
            if (!topic.startsWith("buf_")) {
                continue;
            }
            Long count = store.getMongo().getDatabase(dbName).getCollection(topic).estimatedDocumentCount(options);
            if (count != null) {
                num = num + count;
            }
        }
        return num;
    }

    public Long getStoreCollectionCount(String tenantId) {
        Set<String> tenantCollectionNames = getTenantCollectionNames(tenantId);
        if (CollectionUtils.isEmpty(tenantCollectionNames)) {
            return 0L;
        }

        if (!ConfigCenter.isAllowMergeDispatcherCollection(tenantId)) {
            // 不是合并表的,使用EstimatedDocumentCountOptions速度更快,资源消耗更少
            return getTenantBufCollectionCount(tenantCollectionNames);
        }

        Long num = 0L;
        CountOptions options = new CountOptions().maxTime(maxWaitSeconds, TimeUnit.SECONDS);
        final BsonDocument filter = new BsonDocument();
        addTenantFilter(filter, tenantId);

        for (String topic : tenantCollectionNames) {
            if (!topic.startsWith("buf_")) {
                continue;
            }
            Long count = store.getMongo().getDatabase(dbName).getCollection(topic).countDocuments(filter, options);
            if (count != null) {
                num = num + count;
            }
        }
        return num;
    }

    private void addTenantFilter(BasicDBObject query, String tenantId) {
        if (ConfigCenter.isAllowMergeDispatcherCollection(tenantId)) {
            query.put("ei", new BasicDBObject("$eq", tenantId));
        }
    }

    private void addTenantFilter(BsonDocument filter, String tenantId) {
        if (ConfigCenter.isAllowMergeDispatcherCollection(tenantId)) {
            filter.put("ei", new BsonDocument("$eq", new BsonString(tenantId)));
        }
    }

    private String getCollectionName(String tenantId) {//buf_开头的只能是分发框架使用的集合，不能有其他
        if (ConfigCenter.isAllowMergeDispatcherCollection(tenantId)) {
            return getMergeDispatcherCollectionName(tenantId);
        }
        return "buf_" + tenantId;
    }

    public static String getMergeDispatcherCollectionName(String tenantId) {
        final int index = Integer.parseInt(tenantId) % ConfigCenter.mergeDispatcherCollectionCount;
        return "buf_merge_" + index;
    }


    /**
     * 使用线程池来处理索引创建,防止超时等问题
     * 只有一个线程处理,防止并发创建索引
     */
    private static ThreadPoolExecutor createMongoIndexExecutor = DynamicExecutors.newThreadPool(0, 1, 10 * 1000, new ThreadFactoryBuilder().setNameFormat("create-dispatch-index-%d").build());

    private static String indexName = "ei_hash";

    private static IndexModel tenantIndex = new IndexModel(Indexes.compoundIndex(Indexes.hashed("ei")), new IndexOptions().name(indexName).background(true));

    private static Set<String> existsIndexesCollection = Sets.newHashSet();

    private void initMergeCollectionTenantIndex() {
        createMongoIndexExecutor.submit(() -> {
            final Set<String> collectionNameList = collectionNames.get("buf_merge");
            collectionNameList.removeAll(existsIndexesCollection);
            for (String collectionName : collectionNameList) {
                final MongoCollection<Document> coll = store.getMongo().getDatabase(dbName).getCollection(collectionName);
                if (checkIndexExists(coll, indexName)) {
                    continue;
                }

                try {
                    coll.createIndexes(Lists.newArrayList(tenantIndex));
                } catch (MongoSocketReadTimeoutException e) {
//                    等待index创建完成
                    for (int i = 0; i < 100 && !checkIndexExists(coll, indexName); i++) {
                        try {
                            Thread.sleep(10_000L);
                        } catch (Throwable ex) {
                        }
                    }
                } catch (Exception e) {
                    log.error("create index exception", e);
                }
            }
            existsIndexesCollection.addAll(collectionNameList);
        });
    }

    private static boolean checkIndexExists(MongoCollection<Document> coll, String indexName) {
        return StreamSupport.stream(coll.listIndexes().spliterator(), false)
                .anyMatch(document -> document.getString("name").equals(indexName));
    }
}
