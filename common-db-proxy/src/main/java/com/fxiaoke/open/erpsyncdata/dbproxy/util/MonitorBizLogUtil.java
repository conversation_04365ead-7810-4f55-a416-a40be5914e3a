package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import ch.qos.logback.classic.Level;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.DataScreenSyncLog;
import com.fxiaoke.log.ErpSyncMonitorLog;
import com.fxiaoke.log.dto.DataScreenSyncLogDTO;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MonitorLogModule;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.pb.Pojo2Protobuf;
import lombok.extern.slf4j.Slf4j;

/**
 * 监控biz日志上报工具
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/2/1
 */
@Slf4j
public class MonitorBizLogUtil {

    static {
        //warn级别
        LogUtil.changeLevel(log.getName(), Level.WARN);
    }

    public static ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder(MonitorLogModule module, String action, String tenantId) {
        return builder(module, action, tenantId, "0");
    }

    public static ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder(MonitorLogModule module, String action, String tenantId, String status) {
        return builder(module, action, tenantId, status, null);
    }

    /**
     * 提取公共方法如果还有公共部分方便修改
     *
     * @param module   模块，不可为空，用于区分什么业务
     * @param action   操作，业务下区分，可为空
     * @param tenantId 企业id，无可以填0
     * @param status   状态，成功使用0，其他均是失败。
     * @return
     */
    public static ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder(MonitorLogModule module, String action, String tenantId, String status, String eventId) {
        if (module == null) {
            throw new ErpSyncDataException("module can not be null",null,null);
        }
        ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder = ErpSyncMonitorLogDTO.builder()
                .appName(ProcessInfo2.appName)
                .serverIp(ProcessInfo2.serverIp)
                .profile(ProcessInfo2.profile)
                .traceId(TraceUtil.get())
                .module(module.name())
                .eventId(eventId)
                .action(action)
                .tenantId(tenantId)
                .status(status);
        return builder;
    }

    public static void send(ErpSyncMonitorLogDTO dto) {
        try {
            BizLogClient.send("biz-log-erpsyncmonitor", Pojo2Protobuf.toMessage(dto, ErpSyncMonitorLog.class).toByteArray());
            log.info("send biz log,{}", dto);
        } catch (Exception e) {
            log.warn("send biz log error", e);
        }
    }

    public static void sendDataScreen(DataScreenSyncLogDTO dto) {
        try {
            BizLogClient.send("biz-log-erpdatascreen", Pojo2Protobuf.toMessage(dto, DataScreenSyncLog.class).toByteArray());
        } catch (Exception e) {
            log.warn("send biz log error", e);
        }
    }

    public static long safeLong(Object o) {
        try {
            if (o == null) {
                return 0L;
            }
            if (o instanceof Long) {
                return (long) o;
            }
            if (o instanceof Number) {
                return ((Number) o).longValue();
            }
            Long.parseLong(o.toString());
        } catch (Exception e) {
            log.warn("transfer long error", e);
        }
        return 0L;
    }
}
