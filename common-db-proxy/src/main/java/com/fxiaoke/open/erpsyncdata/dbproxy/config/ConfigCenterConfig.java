package com.fxiaoke.open.erpsyncdata.dbproxy.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3CreateConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.TriggerFlowConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.release.GrayRule;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Configuration
@Slf4j
public class ConfigCenterConfig {
    /**
     * 灰度到历史环境的企业，分号分隔
     */
    public static final String GRAY_TENANTS = "GRAY_TENANTS";

    /**
     * 获取erp对象数据时的每页数量
     */
    public static final String ERP_LIST_PAGE_SIZE = "ERP_LIST_PAGE_SIZE";
    /**
     * 轮询特殊分页参数，key:中间对象apiname 或者tenantId_apiname,value：每页拉取多少数据
     */
    public static final String SPECIAL_LIST_SIZE = "SPECIAL_LIST_SIZE";
    /**
     * 在一个统计维度内，条数限制(默认)2crm
     */
    public static final String DEFAULT_LIMIT_PER_COUNT_SECOND_2CRM = "DEFAULT_LIMIT_PER_COUNT_SECOND_2CRM";
    /**
     * 特殊企业的条数限制2crm
     */
    public static final String TENANT_LIMIT_PER_COUNT_SECOND_2CRM = "TENANT_LIMIT_PER_COUNT_SECOND_2CRM";
    /**
     * 在一个统计维度内，条数限制(默认)2erp
     */
    public static final String DEFAULT_LIMIT_PER_COUNT_SECOND_2ERP = "DEFAULT_LIMIT_PER_COUNT_SECOND_2ERP";
    /**
     * 特殊企业的条数限制2erp
     */
    public static final String TENANT_LIMIT_PER_COUNT_SECOND_2ERP = "TENANT_LIMIT_PER_COUNT_SECOND_2ERP";
    /**
     * getById熔断过期时间，单位秒
     */
    private static final String KEY_GET_BY_ID_BREAK_EXPIRE_TIME = "KEY_GET_BY_ID_BREAK_EXPIRE_TIME";
    /**
     * getById熔断失败次数阀值
     */
    private static final String KEY_GET_BY_ID_BREAK_COUNT = "KEY_GET_BY_ID_BREAK_COUNT";
    /**
     * k3cloud渠道，CRM没开CPQ，ERP开启了BOM，新建对象报明细不一致，专用查询，适用于所有对象
     */
    private static final String KEY_K3CLOUD_DETAIL_NOT_MATCH_SPECIAL_QUERY = "KEY_K3CLOUD_DETAIL_NOT_MATCH_SPECIAL_QUERY";
    /**
     * 更新SAP商品+产品的企业EI
     */
    private static final String KEY_SAP_UPDATE_SPU_AND_SKU_EIS = "KEY_SAP_UPDATE_SPU_AND_SKU_EIS";
    /**
     * 库存查询字段列表,逗号分隔
     */
    public static final String STOCK_FIELDS = "STOCK_FIELDS";
    /**
     * 可销控制查询字段列表,逗号分隔
     */
    public static final String SAL_SC_CustMat_FIELDS = "SAL_SC_CustMat_FIELDS";
    /**
     * 批号字段
     */
    public static final String BATCH_FIELDS = "BATCH_FIELDS";
    /**
     * 特殊限制明细数量的对象,key:明细的对象apiName，value:数量,
     * 如果在配置文件配置这个，一定一定记得把默认的给加上去
     */
    public static final String SPECIAL_LIMIT_DETAIL_SIZE = "SPECIAL_LIMIT_DETAIL_SIZE";
    /**
     * 明细长度默认值
     */
    public static final String DEFAULT_LIMIT_DETAIL_SIZE = "DEFAULT_LIMIT_DETAIL_SIZE";
    /**
     * K3仓位字段
     */
    public static final String STOCKLOC_FIELDS_SET = "STOCKLOC_FIELDS_SET";
    /**
     * 需要填充明细数据的CRM对象，ERP往CRM方向，全企业
     * 如：发货单
     */
    public static final String NEED_FILL_UP_CRM_OBJ_SET = "NEED_FILL_UP_CRM_OBJ_SET";
    /**
     * 不发送明细数据的目标CRM对象，ERP往CRM方向，全企业
     * 如：发货单
     */
    public static final String NO_SEND_DETAIL_EVENT_CRM_OBJ_SET = "NO_SEND_DETAIL_EVENT_CRM_OBJ_SET";
    /**
     * K3没有审核状态的单据列表
     */
    public static final String NO_APPROVE_FORM_SET = "NO_APPROVE_FORM_SET";
    /**
     * K3允许同步禁用数据的企业对象列表
     */
    public static final String K3_ALLOW_QUERY_FORBIDDATA = "K3_ALLOW_QUERY_FORBIDDATA";
    /**
     * k3组织字段，key：单据formid，value：组织字段
     */
    public static final String K3_DEFAULT_ORG_FIELD = "K3_DEFAULT_ORG_FIELD";

    /**
     * 延迟waiting数据分发的默认时间
     */
    public static final Long WAITING_DELAY_DISPATCHER_TIME = 10 * 60 * 1000L;

    /**
     * 使用直接订单变更，也就是直接更新K3C侧的订单，忽略K3C侧订单变更的配置
     */
    public static final String K3C_USE_DIRECT_ORDER_CHANGE = "K3C_USE_DIRECT_ORDER_CHANGE";

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @PostConstruct
    public void init() {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.ERP_LIST_PAGE_SIZE.name());
        if (configuration == null) {
            tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.ERP_LIST_PAGE_SIZE.name(), "100");
        }

        configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.SPECIAL_LIST_SIZE.name());
        if (configuration == null) {
            tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.SPECIAL_LIST_SIZE.name(), "{\"BD_STOCK.BillHead\":1}");
        }

        configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.DEFAULT_LIMIT_PER_COUNT_SECOND_2CRM.name());
        if (configuration == null) {
            tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.DEFAULT_LIMIT_PER_COUNT_SECOND_2CRM.name(), "180");
        }

        configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.DEFAULT_LIMIT_PER_COUNT_SECOND_2ERP.name());
        if (configuration == null) {
            tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.DEFAULT_LIMIT_PER_COUNT_SECOND_2ERP.name(), "3000");
        }

        configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.KEY_GET_BY_ID_BREAK_EXPIRE_TIME.name());
        if (configuration == null) {
            tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.KEY_GET_BY_ID_BREAK_EXPIRE_TIME.name(), (10 * 60) + "");
        }

        configuration = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.KEY_GET_BY_ID_BREAK_COUNT.name());
        if (configuration == null) {
            tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.KEY_GET_BY_ID_BREAK_COUNT.name(), "100");
        }
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public LinkedHashMap<String, String> getSpecialEnvMap() {
        ErpTenantConfigurationEntity config = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.SPECIAL_ENV_MAP.name());

        if (config != null && StringUtils.isNotEmpty(config.getConfiguration())) {
            LinkedHashMap<String, String> envMap = JacksonUtil.fromJson(config.getConfiguration(),
                    new TypeReference<LinkedHashMap<String, String>>() {
                    });
            return envMap;
        }
        return new LinkedHashMap<>();
    }

    public Integer getErpListPageSize() {
        return getIntConfig(ERP_LIST_PAGE_SIZE, 100);
    }

    public Map<String, Integer> getSpecialListSize() {
        return getMapConfig(SPECIAL_LIST_SIZE, new HashMap<>());
    }


    public int getErpListPageSize(String tenantId, String apiName) {
        String tenantKey = tenantId + "_" + apiName;
        Map<String, Integer> SPECIAL_LIST_SIZE = getSpecialListSize();
        if (SPECIAL_LIST_SIZE.containsKey(tenantKey)) {
            return SPECIAL_LIST_SIZE.get(tenantKey);
        }
        if (SPECIAL_LIST_SIZE.containsKey(apiName)) {
            return SPECIAL_LIST_SIZE.get(apiName);
        }
        return getErpListPageSize();
    }

    public Integer getDefaultLimitPerCountSecond2CRM() {
        return getIntConfig(DEFAULT_LIMIT_PER_COUNT_SECOND_2CRM, 150);
    }

    public Long getGetByIdBreakExpireTime() {
        return Long.valueOf(getIntConfig(KEY_GET_BY_ID_BREAK_EXPIRE_TIME, 10 * 60));
    }

    public Long getGetByIdBreakExpireTime(String tenantId, String dataCenterId, String channel) {
        return Long.valueOf(getIntConfig(tenantId, dataCenterId, channel, KEY_GET_BY_ID_BREAK_EXPIRE_TIME, 0));
    }

    public Long getGetByIdBreakCount() {
        return Long.valueOf(getIntConfig(KEY_GET_BY_ID_BREAK_COUNT, 100));
    }

    public Long getGetByIdBreakCount(String tenantId, String dataCenterId, String channel) {
        return Long.valueOf(getIntConfig(tenantId, dataCenterId, channel, KEY_GET_BY_ID_BREAK_COUNT, 0));
    }

    public Map<String, Integer> getTENANT_LIMIT_PER_COUNT_SECOND_2CRM() {
        return getMapConfig(TENANT_LIMIT_PER_COUNT_SECOND_2CRM, new HashMap<>());
    }

    public Integer getDEFAULT_LIMIT_PER_COUNT_SECOND_2ERP() {
        return getIntConfig(DEFAULT_LIMIT_PER_COUNT_SECOND_2ERP, 100);
    }

    public Map<String, Integer> getTENANT_LIMIT_PER_COUNT_SECOND_2ERP() {
        return getMapConfig(TENANT_LIMIT_PER_COUNT_SECOND_2ERP, new HashMap<>());
    }

    public Double getQueryTempCoefficient() {
        return tenantConfigurationManager.queryGlobalDoubleConfig(TenantConfigurationTypeEnum.DEFAULT_LIMIT_QUERY_TEMP_COEFFICIENT.name(), 1.1);
    }

    public String getK3CloudDetailNotMatchSpecialQuery(String tenantId, String dataCenterId, String channel, String erpObjApiName) {
        return getStringConfig(tenantId, dataCenterId, channel, KEY_K3CLOUD_DETAIL_NOT_MATCH_SPECIAL_QUERY + "_" + erpObjApiName, "");
    }

    public String getSAP_UPDATE_SPU_AND_SKU_EIS() {
        return getStringConfig(KEY_SAP_UPDATE_SPU_AND_SKU_EIS, "");
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public List<String> getSTOCK_FIELDS() {
        String value = getStringConfig(STOCK_FIELDS, "FID,FStockOrgId,FStockOrgId.FNumber,FStockId.FNumber,FStockLocID,FMaterialId.FNumber,FBaseQty,FLot,FLotProduceDate,FKFPeriod,FStockName,FBaseAVBQty,FLot.FNumber,FMaterialName,Fmodel,FStockUnitId.FName,FQty,FStockStatusId.FName,FStockOrgId.FName,FLockQty,FBaseProperty,FProduceDate,FExpiryDate,FOwnerTypeId");
        List<String> list = Splitter.on(",").splitToList(value);
        return list;
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public List<String> getSAL_SC_CustMat_FIELDS() {
        String value = getStringConfig(SAL_SC_CustMat_FIELDS, "FSALEORGID.FNumber,FMaterialId.FNumber,FCustomerId.FNumber,FCustomerId.FName,FCustomerId,FID");
        List<String> list = Splitter.on(",").splitToList(value);
        return list;
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public List<String> getBATCH_FIELDS() {
        String value = getStringConfig(BATCH_FIELDS, "FLOTID,FName,FNumber,FMaterialID,FHProduceDate,FHExpiryDate,FInStockDate");
        List<String> list = Splitter.on(",").splitToList(value);
        return list;
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Map<String, Integer> getSPECIAL_LIMIT_DETAIL_SIZE() {
        String specialLimitDetailSize = "{\"BD_SAL_PriceList.SAL_PRICELISTENTRY\":10000}";
        Map<String, Integer> defaultMap = JacksonUtil.fromJson(specialLimitDetailSize, Map.class);
        return getMapConfig(SPECIAL_LIMIT_DETAIL_SIZE, defaultMap);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 120)
    public Integer getDEFAULT_LIMIT_DETAIL_SIZE() {
        return getIntConfig(DEFAULT_LIMIT_DETAIL_SIZE, 1000);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Set<String> getSTOCKLOC_FIELDS_SET() {
        Set<String> defaultSet = ImmutableSet.copyOf(
                Splitter.on(";").split("FDestStockLocId;FSrcStockLocId;FStockLocId;FStockLocInId"));
        return getSetConfig(STOCKLOC_FIELDS_SET, defaultSet);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Set<String> getUpdateCrmMasterDetailTogetherObjList(final String tenantId) {
        final Map<String, Set<String>> needFillUpCrmObjSetByTenantId = this.getNeedFillUpCrmObjSetByTenantId();
        return needFillUpCrmObjSetByTenantId.containsKey(tenantId) ?
                needFillUpCrmObjSetByTenantId.get(tenantId) :
                needFillUpCrmObjSetByTenantId.get("*");
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    private Map<String, Set<String>> getNeedFillUpCrmObjSetByTenantId() {
        final Set<String> needFillUpCrmObjSet = getUpdateCrmMasterDetailTogetherObjList();

        final Map<String, String> mapConfig2 = getMapConfig2(TenantConfigurationTypeEnum.NEED_FILL_UP_CRM_OBJ_SET_BY_TENANT_ID.name(),
                ImmutableMap.of(
                        "707988", "DeliveryNoteObj",
                        "727890", "DeliveryNoteObj",
                        "748862", "DeliveryNoteObj",
                        "767835", "DeliveryNoteObj"
                ));
        final Map<String, Set<String>> map = mapConfig2.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> Stream.of(needFillUpCrmObjSet,
                                        Splitter.on(",").splitToList(entry.getValue()))
                                .flatMap(Collection::stream)
                                .collect(Collectors.toSet())
                ));

        map.put("*", new HashSet<>(needFillUpCrmObjSet));
        return map;
    }

    private Set<String> getUpdateCrmMasterDetailTogetherObjList() {
        Set<String> defaultSet = ImmutableSet.copyOf(Splitter.on(";").split("SalesOrderObj"));
        final HashSet<String> setConfig = Sets.newHashSet(getSetConfig(NEED_FILL_UP_CRM_OBJ_SET, defaultSet));

        // 使用业务自己的配置,确定哪些对象需要主从一起更新
        setConfig.addAll(ConfigCenter.sfaFillUpObjects);

        return setConfig;
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Set<String> getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET() {
        return getSetConfig(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET, new HashSet<>());
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Set<String> getNO_APPROVE_FORM_SET() {
        Set<String> defaultSet = ImmutableSet.copyOf(
                Splitter.on(";").split("BD_SerialMainFile;BD_RecCondition;SAL_CustMatMapping;BD_STOCK;ORG_Organizations;k34018f86ad9b426485c3bec791bb05a1;BD_Supplier;BAS_PreBaseDataOne;BD_BatchMainFile;BD_CommonContact;CRM_RepairOrder;BD_Rate;HXN_FXXKKXKZ;BD_Empinfo;BD_UNIT;k57cfeb7944ff4edab5c3b3b216c2b3c3;BD_Currency;PAEZ_CPX;BD_NEWSTAFF;k1376e13bae0c4a92a6498083bcbe3a70;BD_SETTLETYPE;k98d41c7ba1e14cbaaf17e27dea17f184;BD_OPERATOR;BD_Expense;ke1e7f98c9ee64a06b9289c73bfc5d4e5;k94b2434aae5a411486eae7619b006bbd;BD_Department;BBC_LEIBIE;BBC_XILIE;BBC_MENKUANG;BBC_COLOR;BBC_XDCZ;BBC_HUAZHI;BBC_SUOTI;BBC_SUOXIN;BBC_LASHOU;BBC_JIAOLIAN;BBC_MYML;BBC_KAIXIANG;BBC_BIANSUO;BBC_MSHD;BBC_KUANS;BBC_XSBJD;SAL_AvailableQuery;STK_Inventory;BBC_MYJD"));
        return getSetConfig(NO_APPROVE_FORM_SET, defaultSet);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Set<String> getK3_ALLOW_QUERY_FORBIDDATA() {
        Set<String> defaultSet = ImmutableSet.copyOf(
                Splitter.on(";").split("714520:BD_MATERIAL;727375:BD_MATERIAL;724263:BD_MATERIAL;713386:BD_MATERIAL"));
        return getSetConfig(K3_ALLOW_QUERY_FORBIDDATA, defaultSet);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 300)
    public Map<String, String> getK3_DEFAULT_ORG_FIELD() {
        String k3DefaultOrgField = "{ \"SAL_SaleOrder\": \"FSaleOrgId.FNumber\", \"BD_Customer\":\"FCreateOrgId.FNumber\", \"BD_MATERIAL\":\"FUseOrgId.FNumber\" }";
        Map<String, String> defaultMap = JacksonUtil.fromJson(k3DefaultOrgField, Map.class);
        return getMapConfig2(K3_DEFAULT_ORG_FIELD, defaultMap);
    }

    public Set<String> getSetConfig(String type, Set<String> defaultValue) {
        return getSetConfig("0", "0", "ALL", type, defaultValue);
    }

    public Set<String> getSetConfig(String tenantId, String dataCenterId, String channel, String type, Set<String> defaultValue) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        Set<String> set = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            set = ImmutableSet.copyOf(Splitter.on(";").split(configuration.getConfiguration()));
        }
        if (set == null)
            set = new HashSet<>();
        return set;
    }

    public String getStringConfig(String type, String defaultValue) {
        return getStringConfig("0", "0", "ALL", type, defaultValue);
    }

    public String getStringConfig(String tenantId, String dataCenterId, String channel, String type, String defaultValue) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        String value = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            if (StringUtils.isNotEmpty(configuration.getConfiguration())) {
                value = configuration.getConfiguration();
            }
        }
        return value;
    }

    public boolean getBooleanConfig(TenantConfigurationTypeEnum type, String defaultValue) {
        return getBooleanConfig("0", "0", "ALL", type.name(), defaultValue);
    }

    public boolean getBooleanConfig(String tenantId, String dataCenterId, String channel, String type, String defaultValue) {
        String config = getStringConfig(tenantId, dataCenterId, channel, type, defaultValue);
        boolean bConfig = false;
        try {
            bConfig = Boolean.valueOf(config);
        } catch (Exception e) {

        }
        return bConfig;
    }

    public Integer getIntConfig(String type, Integer defaultValue) {
        return getIntConfig("0", "0", "ALL", type, defaultValue);
    }

    public Integer getIntConfig(String tenantId, String dataCenterId, String channel, String type, Integer defaultValue) {
        return getConfig(tenantId, dataCenterId, channel, type, defaultValue, Integer::valueOf);
    }

    private Long getLongConfig(final String tenantId, final String dataCenterId, final String channel, final String type, final Long defaultValue) {
        return getConfig(tenantId, dataCenterId, channel, type, defaultValue, Long::valueOf);
    }

    private <T> T getConfig(final String type, final T defaultValue, Function<String, T> convert) {
        return getConfig("0", "0", "ALL", type, defaultValue, convert);
    }

    private <T> T getConfig(final String tenantId, final String dataCenterId, final String channel, final String type, final T defaultValue, Function<String, T> convert) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        return getConfig(configuration, defaultValue, convert);
    }

    private static <T> T getConfig(ErpTenantConfigurationEntity configuration, T defaultValue, Function<String, T> convert) {
        T value = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                final String config = configuration.getConfiguration();
                value = convert.apply(config);
            } catch (Exception e) {
                log.warn("getConfig error,tenantId:{} dataCenterId:{} channel:{} type:{}", configuration.getTenantId(), configuration.getDataCenterId(), configuration.getChannel(), configuration.getType(), e);
            }
        }
        return value;
    }

    public Long getPublicLongConfig(String tenantId, String dataCenterId, String channel, String type, Long defaultValue) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        Long value = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                value = Long.valueOf(configuration.getConfiguration());
            } catch (Exception e) {
                value = defaultValue;
            }
        }
        return value;
    }

    public Map<String, Integer> getMapConfig(String type, Map<String, Integer> defaultValue) {
        return getIntMapConfig("0", "0", "ALL", type, defaultValue);
    }

    public Map<String, Integer> getIntMapConfig(String tenantId, String dataCenterId, String channel, String type, Map<String, Integer> defaultValue) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        Map<String, Integer> map = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                map = JacksonUtil.fromJson(configuration.getConfiguration(), Map.class);
            } catch (Exception e) {
            }
        }
        if (map == null)
            map = new HashMap<>();
        return map;
    }

    public Map<String, String> getMapConfig2(String type, Map<String, String> defaultValue) {
        return getMapConfig("0", "0", "ALL", type, defaultValue);
    }

    public Map<String, String> getMapConfig(String tenantId, String dataCenterId, String channel, String type, Map<String, String> defaultValue) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        Map<String, String> map = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                map = JacksonUtil.fromJson(configuration.getConfiguration(), Map.class);
            } catch (Exception e) {
            }
        }
        if (map == null)
            map = new HashMap<>();
        return map;
    }

    public <T> Map<String, T> getObjectMapConfig(String tenantId, String dataCenterId, String channel, String type, Class<T> clazz, Map<String, T> defaultValue) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, type);
        Map<String, T> map = defaultValue;
        if (configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                final JSONObject jsonObject = JSON.parseObject(configuration.getConfiguration());
                map = jsonObject.keySet().stream().collect(Collectors.toMap(key -> key, key -> jsonObject.getObject(key, clazz)));
            } catch (Exception e) {
            }
        }
        if (map == null)
            map = new HashMap<>();
        return map;
    }

    public Map<String, List<String>> getBatchWrite2CrmTenantAndObjApiName() {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne("*", "*", "*", TenantConfigurationTypeEnum.BATCH_WRITE_TO_CRM_TENANT_OBJECT_API_NAME.name());
        if (configuration != null && configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                Map<String, List<String>> map = JacksonUtil.fromJson(configuration.getConfiguration(), Map.class);
                return map;
            } catch (Exception e) {
            }
        }
        return Maps.newHashMap();
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES)
    public List<String> getBatchWrite2CrmByTenant(String tenantId) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne("*", "*", "*", TenantConfigurationTypeEnum.BATCH_WRITE_TO_CRM_TENANT_OBJECT_API_NAME.name());
        if (configuration != null && configuration != null && StringUtils.isNotEmpty(configuration.getConfiguration())) {
            try {
                Map<String, List<String>> map = JacksonUtil.fromJson(configuration.getConfiguration(), Map.class);
                List<String> result = map.get(tenantId);
                return result != null ? result : Lists.newArrayList();
            } catch (Exception e) {
            }
        }
        return Lists.newArrayList();
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 6, timeUnit = TimeUnit.HOURS)
    public Set<String> getErpServerStatusCheckTenants() {
        return getSetConfig(TenantConfigurationTypeEnum.ERP_SERVER_CHECK_TEANANTS.name(), Collections.emptySet());
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 30, timeUnit = TimeUnit.MINUTES)
    public Integer getErpServerStatusErrorLimit(String tenantId) {
        return getIntConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.ERP_SERVER_STATUS_ERROR_LIMIT.name(), 1000);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 30, timeUnit = TimeUnit.MINUTES)
    public Integer getErpServerCheckInterval(String tenantId) {
        return getIntConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.ERP_SERVER_CHECK_INTERVAL.name(), 30);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public Map<String, String> getBOM_INSTANCE_DETAIL_EXTRA_FIELD_MAPPINGS(String tenantId) {
        return getMapConfig(tenantId, "0", "ERP_K3CLOUD", TenantConfigurationTypeEnum.BOM_INSTANCE_DETAIL_EXTRA_FIELD_MAPPINGS.name(), new HashMap<>());
    }

    public boolean grayXorderDelete(final String tenantId) {
        return getXorderDeleteGrayRule().isAllow(tenantId);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public GrayRule getXorderDeleteGrayRule() {
        return new GrayRule(getStringConfig(TenantConfigurationTypeEnum.XORDER_DELETE_GRAY_RULE.name(), "white:739065"));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public GrayRule getGrayRule(TenantConfigurationTypeEnum type) {
        return new GrayRule(getStringConfig(type.name(), "white:739065"));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public GrayRule getGrayRuleByMap(TenantConfigurationTypeEnum type, String key) {
        final Map<String, String> config = getMapConfig2(type.name(), Collections.emptyMap());
        if (MapUtils.isEmpty(config) || !config.containsKey(key)) {
            return new GrayRule("white:");
        }

        return new GrayRule(config.get(key));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public boolean ignoreDoubleWriteException() {
        return getBooleanConfig(TenantConfigurationTypeEnum.IGNORE_DOUBLE_WRITE_EXCEPTION, "true");
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public boolean getUseDirectOrderChange(String tenantId) {
        return getBooleanConfig(tenantId, "0", "ERP_K3CLOUD",
                TenantConfigurationTypeEnum.K3C_USE_DIRECT_ORDER_CHANGE.name(), "false");
    }

    public boolean isMonitorTenant(final String tenantId) {
        return getMonitorTenants().isAllow(tenantId);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 1, timeUnit = TimeUnit.MINUTES)
    private GrayRule getMonitorTenants() {
        return new GrayRule(getStringConfig(TenantConfigurationTypeEnum.MONITOR_TENANTS.name(), "white:683639"));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 1, timeUnit = TimeUnit.MINUTES)
    public Map<String, Integer> getAllSaveSyncLogLimit(final String type) {
        // 后面有做put操作,需要防止并发导致的问题
        // erp_temp_738217调用量达2000+,sync_log_727784达1800+,保险起见设置1W
        return new ConcurrentHashMap<>(getMapConfig(type, ImmutableMap.of("default", ConfigCenter.getDefaultSaveSyncLogLimit(type), "ALL", ConfigCenter.getAllSaveSyncLogLimit(type))));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 1, timeUnit = TimeUnit.MINUTES)
    public boolean isDropWhenOverSaveLogLimitPloy() {
        return getBooleanConfig(TenantConfigurationTypeEnum.DROP_WHEN_OVER_SAVE_LOG_LIMIT_PLOY, "false");
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public boolean isStopTransfer() {
        return getBooleanConfig(TenantConfigurationTypeEnum.STOP_TRANSFER, "false");
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public Integer getNotifyDuringFunctionAddFieldsInterval() {
        return getIntConfig(TenantConfigurationTypeEnum.NOTIFY_DURING_FUNCTION_ADD_FIELDS_INTERVAL.name(), 24 * 3600);
    }

    public boolean isNoTimeFilterObj(final String tenantId, final String objAPIName) {
        final Map<String, Set<String>> allNoTimeFilterObjectApiName = this.getAllNoTimeFilterObjectApiName();
        return allNoTimeFilterObjectApiName.get("*").contains(objAPIName) || SetUtils.emptyIfNull(allNoTimeFilterObjectApiName.get(tenantId)).contains(objAPIName);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public K3CreateConfig getK3CreateConfig(final String tenantId, final String dataCenterId, String objAPIName) {
        return getK3CreateConfig(tenantId, dataCenterId).getOrDefault(objAPIName, K3CreateConfig.DefaultCreateConfig);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public Map<String, K3CreateConfig> getK3CreateConfig(final String tenantId, final String dataCenterId) {
        return getObjectMapConfig(tenantId, dataCenterId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.K3C_CREATE_CONFIG.name(), K3CreateConfig.class, new HashMap<>());
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public Map<String, Set<String>> getAllNoTimeFilterObjectApiName() {
        final Map<String, String> salMaterialgroup = getMapConfig2(TenantConfigurationTypeEnum.NO_TIME_FILTER_OBJECT_API_NAME.name(), ImmutableMap.of("*", "SAL_MATERIALGROUP"));
        return salMaterialgroup.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> Sets.newHashSet(Splitter.on(",").splitToList(entry.getValue()))));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public List<String> getIntegrationStreamNodeWhiteList(String tenantId) {
        return Lists.newArrayList(getSetConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.INTEGRATION_STREAM_NODE_WHITE_LIST.name(), Collections.emptySet()));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 5, timeUnit = TimeUnit.MINUTES)
    public Map<String, List<Integer>> getSendNotifyToOtherTenantConfig(String tenantId) {
        return getConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.SEND_NOTIFY_TO_OTHER_TENANT_CONFIG.name(), Collections.emptyMap(), config -> {
            try {
                return JacksonUtil.fromJson(config, new TypeReference<Map<String, List<Integer>>>() {
                });
            } catch (Exception e) {
                return Collections.emptyMap();
            }
        });
    }

    public Long getSyncLogExpireDay(String tenantId) {
        return getLongConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.SYNC_LOG_EXPIRE_TIME_TENANT_CONFIG.name(), 90L);
    }

    public void setPloyDetailLastSyncTime(String tenantId, String ployDetailId, Long lastSyncTime) {
        tenantConfigurationManager.updateConfig(tenantId, ployDetailId, "ALL", TenantConfigurationTypeEnum.PLOY_DETAIL_LAST_SYNC_TIME.name(), String.valueOf(lastSyncTime));
    }

    public Long getPloyDetailLastSyncTime(String tenantId, String ployDetailId) {
        return getLongConfig(tenantId, ployDetailId, "ALL", TenantConfigurationTypeEnum.PLOY_DETAIL_LAST_SYNC_TIME.name(), null);
    }

    public Long getPloyDetailLastSyncTimeAndDel(String tenantId, String ployDetailId) {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne(tenantId, ployDetailId, "ALL", TenantConfigurationTypeEnum.PLOY_DETAIL_LAST_SYNC_TIME.name());
        final Long syncTime = getConfig(configuration, null, Long::valueOf);

        if (Objects.nonNull(syncTime)) {
            tenantConfigurationManager.deleteByTenantIdWithId(tenantId, configuration.getId());
        }
        return syncTime;
    }

    public void setMinPloyDetailLastSyncTimeIfExist(String tenantId, String id, Long lastModifyTime) {
        final Long syncTime = getLongConfig(tenantId, id, "ALL", TenantConfigurationTypeEnum.PLOY_DETAIL_LAST_SYNC_TIME.name(), null);
        if (Objects.nonNull(syncTime)) {
            final long min = Math.min(syncTime, lastModifyTime);
            if (!Objects.equals(min, syncTime)) {
                setPloyDetailLastSyncTime(tenantId, id, min);
            }
        }
    }

    public int getCronBeginMinute(String tenantId, String sourceObjectApiName) {
        final Integer beginMinute = getConfig(TenantConfigurationTypeEnum.CRON_BEGIN_MINUTE.name(), null, config ->
                Optional.ofNullable(config)
                        .map(JSON::parseObject)
                        .map(jsonObject -> Optional.ofNullable(
                                        jsonObject.getInteger(String.join("_", tenantId, sourceObjectApiName))
                                ).orElseGet(() -> jsonObject.getInteger(tenantId))
                        ).orElse(null));
        return Objects.isNull(beginMinute) ? getDefaultBeginMinute(tenantId, sourceObjectApiName) : beginMinute;
    }

    public static int getDefaultBeginMinute(String tenantId, String sourceObjectApiName) {
        return Math.abs(tenantId.hashCode() + sourceObjectApiName.hashCode()) % 5;
    }

    public Map<String, Integer> getCountPloySyncData() {
        return getIntMapConfig("0", "0", "ALL", TenantConfigurationTypeEnum.COUNT_PLOY_SYNC_DATA.name(), new HashMap<>());
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 30)
    public Set<String> readTempSecondaryPreference() {
        return getSetConfig(TenantConfigurationTypeEnum.READ_TEMP_SECONDARY_PREFERENCE.name(), Sets.newHashSet());
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 30)
    public Set<String> getK3ForbidOperationApiName() {
        return getSetConfig(TenantConfigurationTypeEnum.K3_FORBID_OPERATION_API_NAME.name(), Sets.newHashSet("BD_MATERIAL", "BD_Customer", "BD_CommonContact", "BAS_PreBaseDataTwo"));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public boolean isManagedEnterprise(String tenantId, boolean defaultValue) {
        return getConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.MANAGED_ENTERPRISE.name(), defaultValue, StringUtils::isNotBlank);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 10)
    public Map<String, List<String>> getAllEmployeeAutoBindFields(String tenantId) {
        final List<ErpTenantConfigurationEntity> all = tenantConfigurationManager.findAll(tenantId);
        return all.stream()
                .filter(entity -> Objects.equals(entity.getType(), TenantConfigurationTypeEnum.CRM_EMPLOYEE_AUTO_BIND_FIELD_CONFIG.name()))
                .collect(Collectors.toMap(ErpTenantConfigurationEntity::getDataCenterId, entity -> Splitter.on(";").splitToList(entity.getConfiguration())));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 1)
    public TriggerFlowConfig getTriggerFlowConfig(String tenantId, String crmObjApiName) {
        final Map<String, TriggerFlowConfig> all = getObjectMapConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.TRIGGER_FLOW_CONFIG.name(), TriggerFlowConfig.class, new HashMap<>());
        return all.getOrDefault(crmObjApiName, TriggerFlowConfig.defaultConfig);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public Integer getReSyncErpDataThreadNum(String tenantId) {
        return getIntConfig(tenantId, "0", "ALL", TenantConfigurationTypeEnum.RE_SYNC_ERP_DATA_THREAD_NUM.name(), 1);
    }
}
