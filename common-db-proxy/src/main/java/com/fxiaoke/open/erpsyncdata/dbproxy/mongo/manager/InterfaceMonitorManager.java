package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Semaphore;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/15
 */
@Component
@Slf4j
public class InterfaceMonitorManager {
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private SyncLogManager syncLogManager;
    private static final ThreadLocal<InterfaceMonitorData> WAITING_DATA = new ThreadLocal<>();

    /**
     * 可用于保存erp接口数据接口监控
     */
    public void saveErpInterfaceMonitor(String tenantId, String dcId, String objApiName, String type, Object arg, String result, Integer status, Long callTime, Long returnTime, String remark, String traceId, Long costTime, TimeFilterArg timeFilterArg) {
        try {
            if (!ConfigCenter.INTERFACE_MONITOR_WRITE_MONGO) {
                return;
            }
            if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(tenantId)) {
                return;
            }
            InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitorData(tenantId, dcId, objApiName, type, JacksonUtil.toJson(arg), result, status,
                    callTime, returnTime, remark, traceId, costTime);
            interfaceMonitorData.setTimeFilterArg(timeFilterArg);
            interfaceMonitorData.setLogId(LogIdUtil.get());
            if (ConfigCenter.ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS.contains(tenantId)) {
                //等待后续判断之后再处理
                WAITING_DATA.set(interfaceMonitorData);
                return;
            }
            saveInterfaceLogAndSyncLog(interfaceMonitorData);
        } catch (Exception e) {
            log.error("save erp interface log error,", e);
        }
    }

    @SneakyThrows
    public void saveInterfaceLogAndSyncLog(InterfaceMonitorData interfaceMonitorData) {
        String tenantId = interfaceMonitorData.getTenantId();
        Integer status = interfaceMonitorData.getStatus();
        String type = interfaceMonitorData.getType();

        List<String> objectIds = chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(tenantId, Lists.newArrayList(interfaceMonitorData));
        //接口日志只录入objectId/有可能受到限速。objectIds为空
        Integer syncStatus = status == 1 ? SyncLogStatusEnum.SYNC_SUCCESS.getStatus() : SyncLogStatusEnum.SYNC_FAIL.getStatus();
        if (CollectionUtils.isNotEmpty(objectIds)) {
            syncLogManager.saveLog(tenantId, SyncLogTypeEnum.convertInterfaceType(type), syncStatus, objectIds);
        }
    }

    public void saveAndRemoveWaitingData(boolean save) {
        try {
            InterfaceMonitorData interfaceMonitorData = WAITING_DATA.get();
            if (interfaceMonitorData != null) {
                if (save) {
                    saveInterfaceLogAndSyncLog(interfaceMonitorData);
                }
                WAITING_DATA.remove();
            }
        } catch (Exception e) {
            log.error("save erp interface log error saveOrRemoveWaitingData,", e);
        }


    }
}
