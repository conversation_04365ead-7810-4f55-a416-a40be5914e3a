package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;


import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.vo.LogId2CountVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


@Repository
public interface CHSyncLogDao {

    List<CHSyncLogEntity> listByLogIds(@Param(value = "tenantId") String tenantId, @Param(value = "logIds") List<String> logIds,
                                       @Param(value = "startLogTime") Date startLogTime,
                                       @Param(value = "endTLogTime") Date endLogTime) ;

    List<CHSyncLogEntity> listBetweenLogId(@Param(value = "tenantId") String tenantId, @Param(value = "gtLogId") String gtLogId,
                                           @Param(value = "ltLogId") String ltLogId, @Param(value = "limit") Integer limit,
                                           @Param(value = "startLogTime") Date startLogTime,
                                           @Param(value = "endTLogTime") Date endLogTime) ;

    CHSyncLogEntity getById(@Param(value = "tenantId") String tenantId, @Param(value = "id") String id,
                            @Param(value = "startLogTime") Date startLogTime,
                            @Param(value = "endTLogTime") Date endLogTime) ;


    List<CHSyncLogEntity>  pageByFilters(@Param(value = "tenantId") String tenantId, @Param(value = "realObjApiName") String realObjApiName,
                                         @Param(value = "streamId") String streamId, @Param(value = "interfaceMonitorType") List<String> interfaceMonitorType,
                                         @Param(value = "beginUpdateTime") Date beginUpdateTime, @Param(value = "endUpdateTime") Date endUpdateTime,
                                         @Param(value = "logIds") List<String> logIds, @Param(value = "status") String status,
                                         @Param(value = "erpTempDataDataId") String erpTempDataDataId, @Param(value = "erpTempDataDataNumber") String erpTempDataDataNumber,
                                         @Param(value = "erpTempDataTaskNum") String erpTempDataTaskNum,
                                         @Param(value = "offset") Integer offset, @Param(value = "limit") Integer limit);

    List<String> getAllLogId(@Param(value = "tenantId") String tenantId,@Param(value = "realObjApiName") String realObjApiName,
                             @Param(value = "streamId") String streamId, @Param(value = "interfaceMonitorType") List<String> interfaceMonitorType,
                             @Param(value = "beginUpdateTime") Date beginUpdateTime, @Param(value = "endUpdateTime") Date endUpdateTime,
                             @Param(value = "logIds") List<String> logIds, @Param(value = "status") String status,
                             @Param(value = "erpTempDataDataId") String erpTempDataDataId, @Param(value = "erpTempDataDataNumber") String erpTempDataDataNumber,
                             @Param(value = "erpTempDataTaskNum") String erpTempDataTaskNum,
                             @Param(value = "limit") Integer limit);

    List<LogId2CountVo> getCountByLogIds(@Param(value = "tenantId") String tenantId, @Param(value = "realObjApiName") String realObjApiName,
                                         @Param(value = "streamId") String streamId, @Param(value = "interfaceMonitorType") List<String> interfaceMonitorType,
                                         @Param(value = "beginUpdateTime") Date beginUpdateTime, @Param(value = "endUpdateTime") Date endUpdateTime,
                                         @Param(value = "logIds") List<String> logIds, @Param(value = "status") String status,
                                         @Param(value = "erpTempDataDataId") String erpTempDataDataId, @Param(value = "erpTempDataDataNumber") String erpTempDataDataNumber,
                                         @Param(value = "erpTempDataTaskNum") String erpTempDataTaskNum,
                                         @Param(value = "limit") Integer limit);

    Long countByFilters(@Param(value = "tenantId") String tenantId, @Param(value = "realObjApiName") String realObjApiName,
                        @Param(value = "streamId") String streamId, @Param(value = "interfaceMonitorType") List<String> interfaceMonitorType,
                        @Param(value = "beginUpdateTime") Date beginUpdateTime, @Param(value = "endUpdateTime") Date endUpdateTime,
                        @Param(value = "logIds") List<String> logIds, @Param(value = "status") String status,
                        @Param(value = "erpTempDataDataId") String erpTempDataDataId, @Param(value = "erpTempDataDataNumber") String erpTempDataDataNumber,
                        @Param(value = "erpTempDataTaskNum") String erpTempDataTaskNum,
                        @Param(value = "limit") Integer limit);

    Long countByTenantId(@Param(value = "tenantId") String tenantId) ;

    Integer batchInsert(@Param(value = "chSyncLogEntities") List<CHSyncLogEntity> chSyncLogEntities) ;
    Long findMinDate(@Param(value = "tenantId") String tenantId);

    List<CHSyncLogEntity> listBetween(@Param(value = "tenantId") String tenantId, @Param(value = "beginTime") Date beginTime, @Param(value = "endTime") Date endTime,
                       @Param(value = "lastId") String lastId,@Param(value = "limit") Integer limit);

    List<CHSyncLogEntity> listLogByType(@Param(value = "tenantId") String tenantId, @Param(value = "sourceObjectApiNames") List<String> sourceObjectApiNames,
                                        @Param(value = "sourceDataId") String sourceDataId, @Param(value = "sourceDataName") String sourceDataName,
                                        @Param(value = "logTypeList") List<String> logTypeList, @Param(value = "startLogTime") Date startLogTime,
                                        @Param(value = "endLogTime") Date endLogTime);
}