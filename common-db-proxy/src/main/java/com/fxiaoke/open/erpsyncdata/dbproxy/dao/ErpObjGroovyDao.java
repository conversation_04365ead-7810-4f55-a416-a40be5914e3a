package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 9:58 2020/10/22
 * @Desc:
 */
@Repository
@ManagedTenantReplace
public interface ErpObjGroovyDao extends ErpBaseDao<ErpObjGroovyEntity>, ITenant<ErpObjGroovyDao> {

    /**
     * 获取所有企业tenantId字段
     * @return
     */
    @SelectProvider(type = CrudProvider.class, method = "findAll")
    @Result(column="tenant_id", property="tenantId")
    List<String> listTenantId(final Class<?> clazz);

    /**
     * 获取groovy脚本
     *
     * @param tenantId
     * @param apiName
     * @param url
     * @return
     */
    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    ErpObjGroovyEntity getByTenantIdAndApiNameAndUrl(@Param("tenantId")String tenantId,
                                                     @Param("dcId")String dcId,
                                                     @Param("apiName")String apiName,
                                                     @Param("url")String url);

    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    List<ErpObjGroovyEntity> queryByObjectApiName(@Param("tenantId") String tenantId,
                                                  @Param("dcId") String dataCenterId,
                                                  @Param("objApiNameList") List<String> objApiNameList);


    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    List<ErpObjGroovyEntity> queryGroovyTenantId(@Param("tenantId") String tenantId);

    int deleteByTenantId(@Param("tenantId")String tenantId);
}