package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/4/23
 */
@Service
@Slf4j
public class JetCacheTestManager {

    @Cached(name = "jetCacheTestRemote.", key = "#key", expire = 10, timeUnit = TimeUnit.MINUTES)
    public Dict remote(String key) {
        log.info("nocache,{}", key);
        String now = DateUtil.now();
        return Dict.of("key", key, "value", "default", "time", now);
    }


    @CacheUpdate(name = "jetCacheTestRemote.", key = "#updateObj['key']", value = "#updateObj")
    public void remoteUpdate(Dict updateObj) {
        String now = DateUtil.now();
        updateObj.put("time", now);
        log.info("update cache,{}", updateObj);
    }


    @CacheInvalidate(name = "jetCacheTestRemote.", key = "#key")
    public void remoteInvalid(String key) {
        log.info("invalid cache,{}", key);
    }


    @Cached(name = "jetCacheTestRemote.", cacheType = CacheType.BOTH, key = "#key1+'.'+#key2", expire = 10, timeUnit = TimeUnit.MINUTES)
    public Dict both(String key1, String key2) {
        log.info("nocache,{}", key1);
        String now = DateUtil.now();
        return Dict.of("key", key1 + "." + key2, "value", "default", "time", now);
    }


    @CacheUpdate(name = "jetCacheTestRemote.", key = "#updateObj['key1']+'.'+#updateObj['key2']", value = "#updateObj")
    public void bothUpdate(Dict updateObj) {
        String now = DateUtil.now();
        updateObj.put("time", now);
        log.info("update cache,{}", updateObj);
    }


    @CacheInvalidate(name = "jetCacheTestRemote.", key = "#key1+'.'+#key2")
    public void bothInvalid(String key1, String key2) {
        log.info("invalid cache,{}", key1 + "." + key2);
    }
}
