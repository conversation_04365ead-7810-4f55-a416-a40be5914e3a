package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;
import org.mongodb.morphia.utils.IndexType;

/**
 * <AUTHOR>
 * @date 2024/3/13 11:45:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity(value = "manage_group_add_downstream_fail", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("task_id"), @Field(value = "create_time", type = IndexType.DESC)}, options = @IndexOptions(background = true)),
        @Index(fields = {@Field("create_time")}, options = @IndexOptions(background = true, expireAfterSeconds = 7 * 24 * 3600))
})
public class ManageGroupAddDownstreamFailEntity {

    @Id
    private ObjectId id;
    /**
     * @see ManageGroupAddTaskEntity#getId
     */
    @Property("task_id")
    private String taskId;
    @Property("downstream_id")
    private String downstreamId;
    @Property("downstream_name")
    private String downstreamName;
    @Embedded("check_result")
    private CheckResultEntity checkResult;
    @Property("create_time")
    private Long createTime;
}
