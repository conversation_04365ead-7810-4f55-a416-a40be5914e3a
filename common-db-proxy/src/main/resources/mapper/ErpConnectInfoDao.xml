<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        channel,
        data_center_name,
        enterprise_name,
        connect_params,
        create_time,
        update_time,
        number,
        status
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        <!--@mbg.generated-->
        <!--@Table erp_connect_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="data_center_name" jdbcType="VARCHAR" property="dataCenterName"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="connect_params" jdbcType="LONGVARCHAR" property="connectParams"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="number" property="number"/>
        <result column="status" property="status"/>
    </resultMap>
    <select id="listByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        order by id;
    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select *
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="listErpDcByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel !='CRM'
        order by create_time;
    </select>

    <select id="listErpDcIdByTenantId" resultType="java.lang.String">
        select id
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel !='CRM'
        order by create_time;
    </select>

    <select id="listErpDcByTenantIdWithDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and channel !='CRM'
        order by create_time;
    </select>

    <select id="getByIdAndTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="listTenantId" resultType="java.lang.String">
        select distinct (tenant_id)
        from erp_connect_info
        where "status" = 1;
    </select>

    <select id="getCurrentConnectInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        order by update_time desc
        limit 1;
    </select>

    <select id="getCRMConnectInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel=#{channel}
        order by update_time desc
        limit 1;
    </select>

    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where "status" = 1;
    </select>
    <select id="queryInfoByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and data_center_name=#{name,jdbcType=VARCHAR} limit 1;
    </select>
    <select id="queryInfoByNames"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and data_center_name in
        <foreach collection="names" item="name" open="(" close=")" separator=",">
            #{name}
        </foreach>
    </select>
    <delete id="deleteByTenantId">
        delete
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </delete>

    <!--auto generated by MybatisCodeHelper on 2022-09-05-->
    <select id="getByNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel=#{channel,jdbcType=VARCHAR}
        and "number"=#{number}
    </select>
    <select id="getOneDcByTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel=#{channel,jdbcType=VARCHAR}
        limit 1;
    </select>
    <select id="getFirstNumErpDcByTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel!='CRM'
        order by number
        limit 1;
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-07-24-->
    <update id="updateById">
        update erp_connect_info
        <set>
            <if test="updated.dataCenterName != null">
                data_center_name = #{updated.dataCenterName,jdbcType=VARCHAR},
            </if>
            <if test="updated.enterpriseName != null">
                enterprise_name = #{updated.enterpriseName,jdbcType=VARCHAR},
            </if>
            <if test="updated.connectParams != null">
                connect_params = #{updated.connectParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=BIGINT},
            </if>
        </set>
        where id=#{updated.id,jdbcType=VARCHAR}
        and tenant_id=#{updated.tenantId,jdbcType=VARCHAR}
    </update>
    <update id="logicDelete">
        update erp_connect_info
        set "status" = 2
        where id = #{id,jdbcType=VARCHAR}
          and tenant_id = #{tenantId,jdbcType=VARCHAR}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-12-->
    <select id="list100ByIdAfter" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        <where>
            channel in
            <foreach item="item" collection="@com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum@values()"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="minId != null">
                and id <![CDATA[>]]> #{minId,jdbcType=VARCHAR}
            </if>
        </where>
        AND "status" = 1
        order by id
        limit 100
    </select>
    <select id="getListDcByTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel=#{channel,jdbcType=VARCHAR}

    </select>
    <select id="listByTenantIdAndIdsExcludeCrm"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        select *
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND "status" = 1
        and channel!='CRM'
        <if test="ids!=null and ids.size()>0">
            and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="listErpDcExcludeOAByTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and channel not in ('CRM','OA')
        order by create_time;

    </select>

    <select id="maxNumByTenantIdAndConnectorId" resultType="java.lang.Integer">
        select number from erp_connect_info
        where tenant_id = #{tenantId}
        and number/100 = #{connectorId}
        order by number desc limit 1;
    </select>

<!--auto generated by MybatisCodeHelper on 2024-06-13-->
    <update id="updateNumber">
        update erp_connect_info
        set "number"=#{updatedNumber}
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id,jdbcType=VARCHAR}
    </update>

    <select id="listConnectInfoByEis" resultMap="BaseResultMap">
        select
        id,tenant_id,channel,create_time
        from erp_connect_info
        where "status" = 1
        and channel not in ('CRM','OA')
        <if test="eis!=null and eis.size()>0">
            and tenant_id in
            <foreach collection="eis" item="ei" index="index" open="(" close=")" separator=",">
                #{ei}
            </foreach>
        </if>
    </select>
</mapper>