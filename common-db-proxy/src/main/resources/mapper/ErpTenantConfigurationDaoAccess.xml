<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDaoAccess">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        data_center_id,
        channel,
        type,
        configuration,
        create_time,
        update_time
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity">
        <!--@mbg.generated-->
        <!--@Table erp_tenant_configuration-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="configuration" jdbcType="LONGVARCHAR" property="configuration"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>

    <select id="findOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_tenant_configuration
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterId != null and dataCenterId != ''">
            and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel,jdbcType=VARCHAR}
        </if>
        and type = #{type,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-12-09-->
    <update id="updateConfig">
        update erp_tenant_configuration
        set configuration=#{updatedConfiguration,jdbcType=LONGVARCHAR}, update_time=#{updatedUpdateTime,jdbcType=BIGINT}
        where id=#{id,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2021-12-13-->
    <select id="findByIdCache" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_tenant_configuration
        where id=#{id,jdbcType=VARCHAR}
    </select>

    <select id="findOneNoCache" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_tenant_configuration
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterId != null and dataCenterId != ''">
            and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
        </if>
        <if test="channel != null and channel != ''">
            and channel = #{channel,jdbcType=VARCHAR}
        </if>
        and type = #{type,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByTenantId">
        delete from erp_tenant_configuration
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByDataCenterId">
        delete from erp_tenant_configuration
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
          and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
    </delete>
    <select id="findOnePage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_tenant_configuration
        limit #{limit} offset #{offset}
    </select>

    <delete id="deleteByTenantIdAndId">
        delete from erp_tenant_configuration
        where tenant_id = #{tenantId}
          and id = #{id}
    </delete>
    <delete id="batchDeleteByDataCenterId">
        delete from erp_tenant_configuration
        where tenant_id = #{tenantId}
          and data_center_id in
        <foreach collection="dataCenterIds" item="item" index="index"  open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>