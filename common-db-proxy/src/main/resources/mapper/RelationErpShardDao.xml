<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao">
    <sql id="Base_Column_List">
        group_id,
        "name",
        tenant_id,
        template_id,
        dc_id,
        down_stream_id,
        "status"
    </sql>
    <sql id="Simple_Cols">
        tenant_id,
        template_id,
        dc_id,
        down_stream_id,
        "status"
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity">
        <!--@Table relation_erp_shard-->
        <result column="id" property="id"/>
        <result column="group_id" property="groupId"/>
        <result column="down_stream_id" property="downstreamId"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="RelationErpShardDto" type="com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto">
        <!--@Table relation_erp_shard-->
        <result column="group_id" property="groupId"/>
        <result column="name" property="name"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="template_id" property="templateId"/>
        <result column="dc_id" property="dcId"/>
        <result column="down_stream_id" property="downstreamId"/>
        <result column="status" property="status"/>
    </resultMap>

    <select id="queryFirstNormalByDownstreamId" resultMap="RelationErpShardDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM relation_erp_shard res
        JOIN relation_manage_group rmg
        ON res.group_id = rmg.id
        WHERE down_stream_id = #{tenantId,jdbcType=VARCHAR}
        AND status = 1
        limit 1
    </select>
    <select id="queryByDownstreamId" resultMap="RelationErpShardDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM relation_erp_shard res
        JOIN relation_manage_group rmg
        ON res.group_id = rmg.id
        WHERE down_stream_id = #{tenantId,jdbcType=VARCHAR}
        AND status = 1
    </select>

    <select id="queryByTemplateId" resultMap="RelationErpShardDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM relation_erp_shard res
        JOIN relation_manage_group rmg
        ON res.group_id = rmg.id
        WHERE template_id = #{tenantId,jdbcType=VARCHAR}
        AND status = 1
    </select>

    <select id="getAllDownstreamIdsByTemplateId" resultType="java.lang.String">
        SELECT DISTINCT down_stream_id
        FROM relation_erp_shard res
                 JOIN relation_manage_group rmg
                      ON res.group_id = rmg.id
        WHERE template_id = #{tenantId,jdbcType=VARCHAR}
          AND status = 1
    </select>
    <select id="getAllDownstreamIdsByTemplateIdAndDcId" resultType="java.lang.String">
        SELECT DISTINCT down_stream_id
        FROM relation_erp_shard res
                 JOIN relation_manage_group rmg
                      ON res.group_id = rmg.id
        WHERE template_id = #{tenantId,jdbcType=VARCHAR}
          AND dc_id = #{dcId,jdbcType=VARCHAR}
          AND status = 1
    </select>
    <select id="batchGetAllDownstreamIdsByTemplateIdAndDcId" resultType="java.lang.String">
        SELECT DISTINCT down_stream_id
        FROM relation_erp_shard res
        JOIN relation_manage_group rmg
        ON res.group_id = rmg.id
        WHERE template_id = #{templateId,jdbcType=VARCHAR}
        AND dc_id in
        <foreach item="item" index="index" collection="dcIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        AND status = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-03-05-->
    <select id="getSimple" resultMap="RelationErpShardDto">
        select
        <include refid="Simple_Cols"/>
        from relation_erp_shard res
        join relation_manage_group rmg
        on res.group_id = rmg.id
        where down_stream_id = #{downStreamId}
        and dc_id = #{dcId}
    </select>
    <select id="queryByGroupIdAndDownstreamId" resultMap="BaseResultMap">
        SELECT *
        FROM relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
          and down_stream_id = #{tenantId,jdbcType=VARCHAR}
    </select>
    <select id="queryByGroupIdAndDownstreamIds" resultMap="BaseResultMap">
        SELECT *
        FROM relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
        and down_stream_id in
        <foreach item="item" index="index" collection="downStreamIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="status != null and status.size() > 0">
            AND status in
            <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="pageByStatusAndTenantId" resultMap="BaseResultMap">
        SELECT *
        FROM relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
          AND status = #{status,jdbcType=INTEGER}
        order by update_time desc
        offset #{offset,jdbcType=INTEGER} limit #{limit,jdbcType=INTEGER}
    </select>
    <select id="countByStatusAndTenantId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
          AND status = #{status,jdbcType=INTEGER}
    </select>
    <select id="getAllDownstreamIdsByGroupId" resultType="java.lang.String">
        SELECT DISTINCT down_stream_id
        FROM relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
        <if test="status != null">
            AND status = #{status,jdbcType=INTEGER}
        </if>
    </select>
    <select id="getAllByGroupId" resultMap="BaseResultMap">
        SELECT *
        FROM relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
        <if test="status != null">
            AND status = #{status,jdbcType=INTEGER}
        </if>
        order by update_time desc
    </select>
    <select id="getAllDownstreamIdsByUpstreamId" resultType="java.lang.String">
        SELECT DISTINCT down_stream_id
        FROM relation_erp_shard res
                 JOIN relation_manage_group rmg
                      ON res.group_id = rmg.id
        WHERE tenant_id = #{tenantId,jdbcType=VARCHAR}
          AND status = 1
    </select>

    <delete id="deleteListByDownstreamIdAndGroupId">
        delete
        from relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
        and down_stream_id in
        <foreach item="item" index="index" collection="downStreamIds" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteDownstreamEnterprise">
        delete
        from relation_erp_shard
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
          and down_stream_id = #{downStreamId,jdbcType=VARCHAR}
    </delete>
</mapper>