<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncLogDao">
    <sql id="Base_Column_List">
        appName,
        traceId,
        serverIp,
        tenantId,
        createTime,
        updateTime,
        expireTime,
        logType,
        id,
        logId,
        "type",
        realObjApiName,
        sourceObjectApiName,
        streamId,
        "data",
        syncLogStatus,
        erpTempData,
        erpTempDataDataId,
        erpTempDataDataNumber,
        erpTempDataTaskNum
    </sql>

    <select id="listByLogIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="logIds!=null and logIds.size()>0">
            AND logId IN
            <foreach item="item" collection="logIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        order by createTime desc
        limit 1000
    </select>


    <select id="listBetweenLogId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="gtLogId!=null and gtLogId!=''">
            and logId > #{gtLogId}
        </if>
        <if test="ltLogId!=null and ltLogId!=''">
            and logId &lt; #{ltLogId}
        </if>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit #{limit}
    </select>

    <select id="getById" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        and id = #{id}
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 1
    </select>

    <select id="pageByFilters" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="realObjApiName!=null and realObjApiName!=''">
            and realObjApiName = #{realObjApiName}
        </if>
        <if test="streamId!=null and streamId!=''">
            and streamId = #{streamId}
        </if>
        <if test="interfaceMonitorType!=null and interfaceMonitorType.size()>0">
            and type in
            <foreach item="item" index="index" collection="interfaceMonitorType"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginUpdateTime!=null">
            and createTime > #{beginUpdateTime}
        </if>
        <if test="beginUpdateTime!=null">
            and createTime &lt; #{endUpdateTime}
        </if>
        <if test="logIds!=null and logIds.size()>0">
            and logId in
            <foreach item="item" index="index" collection="logIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and syncLogStatus = #{status}
        </if>
        <if test="erpTempDataDataId!=null">
            and erpTempDataDataId = #{erpTempDataDataId}
        </if>
        <if test="erpTempDataDataNumber!=null">
            and erpTempDataDataNumber = #{erpTempDataDataNumber}
        </if>
        <if test="erpTempDataTaskNum!=null">
            and has(erpTempDataTaskNum,#{erpTempDataTaskNum})
        </if>
        order by createTime desc
        limit #{limit} offset #{offset}

    </select>

    <select id="getAllLogId" resultType="java.lang.String">
        select logId
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        and realObjApiName = #{realObjApiName}
        <if test="streamId!=null">
            and streamId = #{streamId}
        </if>
        <if test="interfaceMonitorType!=null and interfaceMonitorType.size()>0">
            and type in
            <foreach item="item" index="index" collection="interfaceMonitorType"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginUpdateTime!=null">
            and createTime > #{beginUpdateTime}
        </if>
        <if test="beginUpdateTime!=null">
            and createTime &lt; #{endUpdateTime}
        </if>
        <if test="logIds!=null">
            and logId in
            <foreach item="item" index="index" collection="logIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and syncLogStatus = #{status}
        </if>
        <if test="erpTempDataDataId!=null">
            and erpTempDataDataId = #{erpTempDataDataId}
        </if>
        <if test="erpTempDataDataNumber!=null">
            and erpTempDataDataNumber = #{erpTempDataDataNumber}
        </if>
        <if test="erpTempDataTaskNum!=null">
            and has(erpTempDataTaskNum, #{erpTempDataTaskNum})
        </if>
        limit #{limit}

    </select>

    <select id="getCountByLogIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.vo.LogId2CountVo">
        select type ,count(*) as count
        from(select type,id
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="realObjApiName!=null and realObjApiName!=''">
            and realObjApiName = #{realObjApiName}
        </if>
        <if test="streamId!=null">
            and streamId = #{streamId}
        </if>
        <if test="interfaceMonitorType!=null and interfaceMonitorType.size()>0">
            and type in
            <foreach item="item" index="index" collection="interfaceMonitorType"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginUpdateTime!=null">
            and createTime > #{beginUpdateTime}
        </if>
        <if test="beginUpdateTime!=null">
            and createTime &lt; #{endUpdateTime}
        </if>
        <if test="logIds!=null and logIds.size()>0">
            and logId in
            <foreach item="item" index="index" collection="logIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and syncLogStatus = #{status}
        </if>
        <if test="erpTempDataDataId!=null">
            and erpTempDataDataId = #{erpTempDataDataId}
        </if>
        <if test="erpTempDataDataNumber!=null">
            and erpTempDataDataNumber = #{erpTempDataDataNumber}
        </if>
        <if test="erpTempDataTaskNum!=null">
            and has(erpTempDataTaskNum, #{erpTempDataTaskNum})
        </if>
        limit #{limit}
        ) as subquery
        group by type

    </select>

    <select id="countByFilters" resultType="java.lang.Long" timeout="10">
        select count(*)
        from(select id
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="realObjApiName!=null and realObjApiName!=''">
            and realObjApiName = #{realObjApiName}
        </if>
        <if test="streamId!=null">
            and streamId = #{streamId}
        </if>
        <if test="interfaceMonitorType!=null and interfaceMonitorType.size()>0">
            and type in
            <foreach item="item" index="index" collection="interfaceMonitorType"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginUpdateTime!=null">
            and createTime > #{beginUpdateTime}
        </if>
        <if test="beginUpdateTime!=null">
            and createTime &lt; #{endUpdateTime}
        </if>
        <if test="logIds!=null and logIds.size()>0">
            and logId in
            <foreach item="item" index="index" collection="logIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and syncLogStatus = #{status}
        </if>
        <if test="erpTempDataDataId!=null">
            and erpTempDataDataId = #{erpTempDataDataId}
        </if>
        <if test="erpTempDataDataNumber!=null">
            and erpTempDataDataNumber = #{erpTempDataDataNumber}
        </if>
        <if test="erpTempDataTaskNum!=null">
            and has(erpTempDataTaskNum, #{erpTempDataTaskNum})
        </if>
        limit #{limit}
        ) as subquery

    </select>

    <select id="listLogByType" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="sourceObjectApiNames!=null and sourceObjectApiNames.size()>0">
            and realObjApiName in
            <foreach item="item" index="index" collection="sourceObjectApiNames"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="sourceDataId!=null">
            and erpTempDataDataId = #{sourceDataId}
        </if>
        <if test="sourceDataName!=null">
            and erpTempDataDataNumber = #{sourceDataName}
        </if>
        <if test="logTypeList!=null and logTypeList.size()>0">
            and type in
            <foreach item="item" index="index" collection="logTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startLogTime!=null">
            and createTime > #{startLogTime}
        </if>
        <if test="endLogTime!=null">
            and createTime &lt; #{endLogTime}
        </if>
        limit 1000

    </select>

    <select id="countByTenantId" resultType="java.lang.Long" timeout="30">
        select count(id)
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
    </select>
    <select id="findMinDate" resultType="java.lang.Long">
        select createTime
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId}
          and logType = 'sync_log'
        order by createTime asc limit 1
    </select>
    <select id="listBetween" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='sync_log'
        <if test="startTime!=null">
            createTime>=#{startTime}
        </if>
        <if test="endTime!=null">
            createTime &lt; #{endTime}
        </if>
        <if test="lastId!=null">
            id > #{lastId}
        </if>
        order by id asc
        limit #{limit}
    </select>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO biz_log_erp_sync_log_dist
        (appName, traceId, tenantId, createTime, updateTime, serverIp, logType, id, dcId, objApiName, interfaceMonitorType,
         arg, result, interfaceMonitorStatus, callTime, returnTime, remark, costTime, expireTime, syncDataId, logId, timeFilterArg,
         timeFilterArgStartTime, timeFilterArgEndTime, timeFilterArgFilters, timeFilterArgOffset, timeFilterArgLimit, sourceTenantType,
         destTenantType, sourceEventType, sourceObjectApiName, sourceDataId, sourceData, sourceDetailSyncDataIds, destEventType, destObjectApiName,
         destDataId, destData, syncDataStatus, syncPloyDetailSnapshotId, operatorId, errorCode, isDeleted, needReturnDestObjectData, dataReceiveType,
         type, realObjApiName, streamId, data, syncLogStatus, erpTempData, erpTempDataDataId, erpTempDataDataNumber, erpTempDataTaskNum)
        VALUES
        <foreach collection="chSyncLogEntities" item="item" index="index" separator=",">
            (#{item.appName}, #{item.traceId}, #{item.tenantId}, #{item.createTime}, #{item.updateTime}, #{item.serverIp}, #{item.logType}, #{item.id}
            , #{item.dcId}, #{item.objApiName}, #{item.interfaceMonitorType}, #{item.arg}, #{item.result}, #{item.interfaceMonitorStatus}
            , #{item.callTime}, #{item.returnTime}, #{item.remark}, #{item.costTime}, #{item.expireTime}, #{item.syncDataId}, #{item.logId}
            , #{item.timeFilterArg}, #{item.timeFilterArgStartTime}, #{item.timeFilterArgEndTime}, #{item.timeFilterArgFilters}, #{item.timeFilterArgOffset}
            , #{item.timeFilterArgLimit}, #{item.sourceTenantType}, #{item.destTenantType}, #{item.sourceEventType}, #{item.sourceObjectApiName}
            , #{item.sourceDataId}, #{item.sourceData}, #{item.sourceDetailSyncDataIds}, #{item.destEventType}, #{item.destObjectApiName}, #{item.destDataId}
            , #{item.destData}, #{item.syncDataStatus}, #{item.syncPloyDetailSnapshotId}, #{item.operatorId}, #{item.errorCode}, #{item.isDeleted}
            , #{item.needReturnDestObjectData}, #{item.dataReceiveType}, #{item.type}, #{item.realObjApiName}, #{item.streamId}, #{item.data}
            , #{item.syncLogStatus}, #{item.erpTempData}, #{item.erpTempDataDataId}, #{item.erpTempDataDataNumber}, #{item.erpTempDataTaskNum})
        </foreach>
    </insert>

</mapper>