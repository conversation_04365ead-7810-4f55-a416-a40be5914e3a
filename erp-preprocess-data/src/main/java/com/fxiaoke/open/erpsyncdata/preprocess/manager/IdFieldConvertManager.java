package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.StockBusinessImpl;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CommonConstants;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/12
 */
@SuppressWarnings("UnstableApiUsage")
@Component
@Slf4j
public class IdFieldConvertManager {

    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private StockBusinessImpl stockBusiness;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;



    @LogLevel(LogLevelEnum.TRACE)
    public void convertField2Crm(String tenantId, SyncDataContextEvent standardData) {
        //替换id字段和masterDetail字段
        convertIdField2Crm(tenantId, standardData);
        //替换查找关联字段
        convertReferenceField2Crm(tenantId, standardData);

    }

    private void convertReferenceField2Crm(String tenantId, SyncDataContextEvent standardData) {
        ObjectData masterData = standardData.getSourceData();
        String objApiName = masterData.getApiName();
        List<ErpObjectFieldEntity> referenceFields = getReferenceFields(tenantId, objApiName);
        for (ErpObjectFieldEntity referenceField : referenceFields) {
            CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByReferenceField(tenantId,referenceField.getFieldExtendValue());
            if (compositeIdExtend.isComposite()) {
                String id = compositeIdExtend.composId(masterData);
                masterData.put(referenceField.getFieldApiName(), id);
            }
        }
        if(standardData.getDetailData()==null) return;
        standardData.getDetailData().forEach((detailApiName, detailObjDatas) -> {
            List<ErpObjectFieldEntity> dreferenceFields = getReferenceFields(tenantId, detailApiName);
            for (ErpObjectFieldEntity referenceField : dreferenceFields) {
                CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByReferenceField(tenantId,referenceField.getFieldExtendValue());
                if (compositeIdExtend.isComposite()) {
                    for (ObjectData detailObjData : detailObjDatas) {
                        String id = compositeIdExtend.composId(detailObjData);
                        detailObjData.put(referenceField.getFieldApiName(), id);
                    }
                }
            }
        });
    }

    private void convertIdField2Crm(String tenantId, SyncDataContextEvent standardData) {
        ObjectData masterData = standardData.getSourceData();
        ErpObjectFieldEntity idField = getIdField(tenantId, masterData.getApiName());
        String id = getObjectDataId(masterData, idField);
        masterData.putId(id);
        masterData.putTenantId(tenantId);
        masterData.putIfAbsent("last_modified_by", Lists.newArrayList("-10000"));
        if(masterData.get("name")==null){
            if(masterData.get("Number")!=null&&StringUtils.isNotBlank(masterData.get("Number").toString())){
                masterData.putIfAbsent("name", masterData.get("Number").toString());
            } else if(masterData.get("FNumber")!=null&&StringUtils.isNotBlank(masterData.get("FNumber").toString())){
                masterData.putIfAbsent("name", masterData.get("FNumber").toString());
            } else if(masterData.get("FBillNo")!=null&&StringUtils.isNotBlank(masterData.get("FBillNo").toString())){
                masterData.putIfAbsent("name", masterData.get("FBillNo").toString());
            } else{
                masterData.putIfAbsent("name", id);
            }
        }
        if(standardData.getDetailData()==null) return;
        standardData.getDetailData().forEach((k, v) -> {
            ErpObjectFieldEntity masterDetailField = erpFieldManager.findMasterDetailField(tenantId, k, masterData.getApiName());
            ErpObjectFieldEntity detailIdField = getIdField(tenantId, k);
            CompositeIdExtend detailCompositeIdExtend = CompositeIdExtend.getByIdField(detailIdField.getFieldExtendValue());
            for (ObjectData detailData : v) {
                String detailId;
                if (detailCompositeIdExtend.isComposite()) {
                    detailId =detailCompositeIdExtend.composId(detailData);
                    detailData.put(detailIdField.getFieldApiName(), detailId);
                } else {
                    detailId = String.valueOf(detailData.get(detailIdField.getFieldApiName()));
                }
                detailData.putId(detailId);
                detailData.putTenantId(tenantId);
                detailData.putIfAbsent("last_modified_by", Lists.newArrayList("-10000"));
                if(detailData.get("name")==null){
                    if(detailData.get("Number")!=null&&StringUtils.isNotBlank(detailData.get("Number").toString())){
                        detailData.putIfAbsent("name", detailData.get("Number").toString());
                    } else if(detailData.get("FNumber")!=null&&StringUtils.isNotBlank(detailData.get("FNumber").toString())){
                        detailData.putIfAbsent("name", detailData.get("FNumber").toString());
                    } else if(detailData.get("FBillNo")!=null&&StringUtils.isNotBlank(detailData.get("FBillNo").toString())){
                        detailData.putIfAbsent("name", detailData.get("FBillNo").toString());
                    } else{
                        detailData.putIfAbsent("name", detailId);
                    }
                }

                //把主对象的id赋值给从对象的主从关联字段
                if (Objects.nonNull(masterDetailField) && StringUtils.isNotBlank(masterDetailField.getFieldApiName())) {
                    detailData.put(masterDetailField.getFieldApiName(), id);
                }
            }
        });
    }

    public String getObjectDataId(ObjectData masterData, ErpObjectFieldEntity idField) {
        String id = String.valueOf(masterData.get(idField.getFieldApiName()));
        //新代码，在进入临时库的时候已经组合了id，但是兼容旧数据，如果获取不到，还是会组合一下
        if (StrUtil.isBlank(id)) {
            CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
            if (compositeIdExtend.isComposite()) {
                id = compositeIdExtend.composId(masterData);
                masterData.put(idField.getFieldApiName(), id);
            }
        }
        return id;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public void convertField2Erp(SyncDataContextEvent doWriteMqData,String dcId) {
        String tenantId = doWriteMqData.getDestTenantId();
        ObjectData mainData = doWriteMqData.getDestData();
        Collection<ObjectData> detailObjs = new ArrayList<>();
        Collection<ObjectData> updateDetailObjs = new ArrayList<>();
        doWriteMqData.getDestDetailSyncDataIdAndDestDataMap().forEach((k,v)->{
            detailObjs.add(v);
            //需要判断明细是否已经新建
            SyncDataMappingsEntity mappingBySyncDataId = syncDataMappingsManager.findMappingBySyncDataId(tenantId, k);
            if (mappingBySyncDataId.getIsCreated()){
                updateDetailObjs.add(v);
            }
        });
        Map<String, List<ObjectData>> detailApiUpdateObjs = updateDetailObjs.stream().collect(Collectors.groupingBy(ObjectData::getApiName));
        if (!doWriteMqData.getDestEventType().equals(EventTypeEnum.ADD.getType())) {
            //更新的数据才增加id
            convertIdField2Erp(tenantId, mainData, detailApiUpdateObjs);
        }
        Map<String, List<ObjectData>> detailApiObjs = detailObjs.stream().collect(Collectors.groupingBy(ObjectData::getApiName));
        convertReferenceField2Erp(tenantId,dcId, mainData, detailApiObjs);
    }

    private void convertReferenceField2Erp(String tenantId,String dcId, ObjectData mainData, Map<String, List<ObjectData>> detailApiObjs) {
        String objApiName = mainData.getApiName();
        List<ErpObjectFieldEntity> referenceFields = getReferenceFields(tenantId, objApiName);
        for (ErpObjectFieldEntity referenceField : referenceFields) {
            CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByReferenceField(tenantId,referenceField.getFieldExtendValue());
            if (compositeIdExtend.isComposite()) {
                String saveIdField = compositeIdExtend.getSaveIdField();
                if (StringUtils.isNotBlank(saveIdField)) {
                    String mainValue = mainData.getString(referenceField.getFieldApiName());
                    String saveId = compositeIdExtend.getSaveId(mainValue);
                    mainData.putIfAbsent(saveIdField, saveId);
                }
            }
        }
        detailApiObjs.forEach((apiName, objs) -> {
            List<ErpObjectFieldEntity> dreferenceFields = getReferenceFields(tenantId, apiName);
            for (ErpObjectFieldEntity referenceField : dreferenceFields) {
                CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByReferenceField(tenantId,referenceField.getFieldExtendValue());
                String saveIdField = compositeIdExtend.getSaveIdField();
                boolean isComposite = compositeIdExtend.isComposite() && StringUtils.isNotBlank(saveIdField);
                boolean isStockLoc = configCenterConfig.getSTOCKLOC_FIELDS_SET().contains(referenceField.getFieldApiName());
                if (isStockLoc) {
                    // 仓位字段需要特殊处理
                    dealStockLocField(tenantId,dcId, referenceField, objs);
                }
                if (isComposite) {
                    for (ObjectData detailData : objs) {
                        String detailValue = detailData.getString(referenceField.getFieldApiName());
                        String saveId = compositeIdExtend.getSaveId(detailValue);
                        detailData.putIfAbsent(saveIdField, saveId);
                    }
                }
            }
        });
    }

    private void dealStockLocField(String tenantId,String dcId, ErpObjectFieldEntity referenceField, List<ObjectData> objs) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId,dcId);
        K3CloudApiClient k3CloudApiClient = apiClientHolder.getK3ApiClient(tenantId, connectInfo.getConnectParams(),dcId);
        if (!connectInfo.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD)) {
            return;
        }
        for (ObjectData detailData : objs) {
            String key = referenceField.getFieldApiName();
            if (detailData.get(key) == null) {
                continue;
            }
            String oldValue = detailData.get(key).toString(); // 格式:仓库编码||仓位编码1.仓位编码2.仓位编码3
            String[] oldValueArgs = oldValue.split("\\|\\|");
            if (oldValueArgs.length < 2) {
                log.warn("Stock location is empty,data = {}", detailData);
                continue;
            }
            String warehouseNumber = oldValueArgs[0];
            List<String> stockLocList = Arrays.asList(oldValueArgs[1].split("\\."));
            List<String> flexValueFields = stockBusiness.getFlexValueFields(warehouseNumber, k3CloudApiClient);
            if (stockLocList.size() != flexValueFields.size()) {
                log.warn("stockLocList size not equal to flexValueFields size, stockLocList={}, flexValueFields={}", stockLocList, flexValueFields);
                continue;
            }
            Map<String, Map<String, String>> result = Maps.newHashMap();
            String headName = key + "__";
            for (int i = 0; i < stockLocList.size(); i++) {
                Map<String, String> map = Maps.newHashMap();
                map.put("FNumber", stockLocList.get(i));
                result.put(headName + flexValueFields.get(i), map);
            }
            detailData.put(key, result);
        }
    }


    private void convertIdField2Erp(String tenantId, ObjectData mainData, Map<String, List<ObjectData>> detailApiObjs) {
        String mainId = mainData.getId();
        String objApiName = mainData.getApiName();
        ErpObjectFieldEntity idField = getIdField(tenantId, objApiName);
        CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
        if (compositeIdExtend.isComposite()) {
            String saveIdField = compositeIdExtend.getSaveIdField();
            if (StringUtils.isNotBlank(saveIdField)) {
                String saveId = compositeIdExtend.getSaveId(mainId);
                mainData.putIfAbsent(saveIdField, saveId);
            }
        }
        //如果主id字段有值，则不会替换。
        mainData.putIfAbsent(idField.getFieldApiName(), mainId);
        detailApiObjs.forEach((apiName, objs) -> {
            ErpObjectFieldEntity detailIdField = getIdField(tenantId, apiName);
            CompositeIdExtend dcompositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
            String saveIdField = dcompositeIdExtend.getSaveIdField();
            boolean isComposite = dcompositeIdExtend.isComposite() && StringUtils.isNotBlank(saveIdField);
            for (ObjectData detailData : objs) {
                String detailId = detailData.getId();
                if (isComposite) {
                    String saveId = dcompositeIdExtend.getSaveId(detailId);
                    detailData.putIfAbsent(saveIdField, saveId);
                }
                detailData.putIfAbsent(detailIdField.getFieldApiName(), detailId);
            }
        });
    }

    public ErpObjectRelationshipEntity getRelation(String tenantId, String objApiName) {
        return erpObjManager.getRelation(tenantId, objApiName);
    }

    public String getRealObjApiName(String tenantId, String objApiName) {
        String erpRealObjApiName = getRelation(tenantId, objApiName).getErpRealObjectApiname();
        return erpRealObjApiName;
    }

    @LogLevel(LogLevelEnum.TRACE)
    public String getDataCenterId(String tenantId, String objApiName) {
        return dataCenterManager.getDataCenterByObjApiName(tenantId, objApiName);
    }

    /**
     * @param idResult
     * @param idFieldDtls
     */
    public void convertDetailApiName(ErpIdResult idResult, List<ErpObjExtendDto> idFieldDtls) {
        if (idResult == null) {
            return;
        }
        //key:真实apiName，value:splitAPiName
        Map<String, String> nameMap = new HashMap<>();
        for (ErpObjExtendDto idFieldDtl : idFieldDtls) {
            if (ErpObjSplitTypeEnum.DETAIL2LOOKUP_SPLIT.equals(idFieldDtl.getSplitType()) ||
                    ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT.equals(idFieldDtl.getSplitType())) {
                if (StringUtils.isNotBlank(idFieldDtl.parseDetailApiName())) {
                    nameMap.put(idFieldDtl.parseDetailApiName(), idFieldDtl.getSplitObjApiName());
                }
            }
        }
        Map<String, List<String>> newDetailDataIds = new HashMap<>();
        idResult.getDetailDataIds().forEach((k, v) -> {
            newDetailDataIds.put(nameMap.getOrDefault(k, CommonConstants.DEFAULT_ENTRY_NAME), v);
        });
        idResult.setDetailDataIds(newDetailDataIds);
    }


    /**
     *
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    public ErpObjectFieldEntity getIdField(String tenantId, String objApiName) {
        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, objApiName);
        if (idField == null) {
            log.warn("trace getIdField, ei:{}, erpobj:{} has not id field.", tenantId, objApiName);
            throw new ErpSyncDataException(I18NStringEnum.s142,tenantId);
        }
        return idField;
    }

    /**
     *
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    @Cached(expire = 60,cacheType = CacheType.LOCAL)
    private List<ErpObjectFieldEntity> getReferenceFields(String tenantId, String objApiName) {
        List<ErpObjectFieldEntity> referenceFields = erpFieldManager.findByObjApiNameAndType(tenantId, objApiName
                , Collections.singleton(ErpFieldTypeEnum.object_reference));
        return referenceFields;
    }

    public void convertId2Erp(SyncDataContextEvent doWriteMqData) {
        String tenantId = doWriteMqData.getDestTenantId();
        String mainId = doWriteMqData.getDestDataId();
        String objApiName = doWriteMqData.getDestObjectApiName();
        ErpObjectFieldEntity idField = getIdField(tenantId, objApiName);
        CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
        String saveId = compositeIdExtend.getSaveId(mainId);
        doWriteMqData.setDestDataId(saveId);
    }
}
