package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpHistoryTaskLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.HistoryTaskDuplicateDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpHistoryTaskLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.HistoryTaskDuplicateDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CreateErpHistoryDataTaskArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.IntegrationSimpleViewInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18 17:57:45
 */
public abstract class AbstractErpHistoryTaskServiceImpl {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    protected ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    protected TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private ErpHistoryTaskLogDao erpHistoryTaskLogDao;

    @Autowired
    private HistoryTaskDuplicateDataDao historyTaskDuplicateDataDao;
    @Autowired
    private RedisDataSource redisDataSource;

    public abstract ErpHistoryDataTaskTypeEnum taskType();

    public abstract boolean checkParamsError(com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult task);

    public abstract boolean checkTaskValid(ErpHistoryDataTaskEntity task);

    public abstract boolean saveTaskSuccess(String tenantId, com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity);

    public abstract boolean editTaskSuccess(String tenantId, com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity);

    public abstract Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task);

    public boolean createHistoryTask(String tenantId, List<ErpHistoryDataTaskEntity> entities) {

        erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(entities);
        List<ErpHistoryTaskLog> logList = Lists.newArrayList();
        for (ErpHistoryDataTaskEntity entity : entities) {
            ErpHistoryTaskLog historyTaskLog = BeanUtil.copy(entity, ErpHistoryTaskLog.class);
            //日期转换
            historyTaskLog = historyTaskLog.convertTimeEntity(entity);
            historyTaskLog.setTaskId(entity.getId());
            logList.add(historyTaskLog);
        }
        //上传日志
        erpHistoryTaskLogDao.batchInsert(tenantId, logList);
        return true;
    }

    public boolean editHistorySnapshotTask(String tenantId, ErpHistoryDataTaskEntity entity) {
        erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateEntityById(entity);
        ErpHistoryTaskLog erpHistoryTaskLog = BeanUtil.copy(entity, ErpHistoryTaskLog.class);
        erpHistoryTaskLog = erpHistoryTaskLog.convertTimeEntity(entity);
        erpHistoryTaskLogDao.updateLastErpHistoryByTaskId(tenantId, entity.getDataCenterId(), entity.getId(), erpHistoryTaskLog);
        return true;
    }

    public Result<String> createErpHistoryDataTask(String tenantId, String dcId, Integer userId, CreateErpHistoryDataTaskArg arg, String lang) {
        com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult task = arg.getTask();
        if (StringUtils.isBlank(task.getObjApiName()) || StringUtils.isBlank(task.getTaskName()) || StringUtils.isBlank(task.getRemark()) || task.getTaskType() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (task.getExecuteTime() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), I18NStringEnum.s358);
        }

        if (checkParamsError(task)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        //用量控制
        String limitMsg = checkLimit(tenantId, arg);
        if (limitMsg != null) {
            return Result.newError(ResultCodeEnum.ERROR_MSG, limitMsg);
        }
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        BeanUtils.copyProperties(task, entity);
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dcId);
        entity.setTaskNum(CommonConstants.TASK_KEY + System.currentTimeMillis());//不能随意修改，后续从traceId获取编码会取CommonConstants.TASK_KEY及后面的13位
        if (Objects.equals(TenantType.ERP, ErpHistoryDataTaskTypeEnum.valueOf(arg.getTask().getTaskType()).getSourceType())) {
            ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, task.getObjApiName());
            if (relationshipEntity == null) {
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
            }
            entity.setRealObjApiName(relationshipEntity.getErpRealObjectApiname());
        } else {
            entity.setRealObjApiName(arg.getTask().getObjApiName());
        }
        entity.setNeedStop(false);
        //创建时为等待执行状态
        entity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
        entity.setTraceId(TraceUtil.get() == null ? "1" : TraceUtil.get());
        if (CollectionUtils.isNotEmpty(arg.getTask().getIntegrationResults())) {
            List<String> ployDetails = arg.getTask().getIntegrationResults().stream().map(IntegrationSimpleViewInfoResult::getId).collect(Collectors.toList());
            ListStringData listStringData = ListStringData.newListStringData(ployDetails);
            entity.setRelatedPloyDetailId(listStringData);
        }
        //按id同步
        entity.setId(com.fxiaoke.api.IdGenerator.get());
        //上面的先创建，也会先执行
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(entity.getCreateTime());
        entity.setExecuteTime(task.getExecuteTime());
        return saveTaskSuccess(tenantId, task, entity) ? Result.newSuccess() : Result.newError(ResultCodeEnum.SYSTEM_ERROR);
    }

    private String checkLimit(String tenantId, CreateErpHistoryDataTaskArg arg) {
        if (StrUtil.equals(arg.getSkipLimitCheckSecret(), "!@#$")) {
            //秘钥绕开
            return null;
        }
        int notEndCount = erpHistoryDataTaskDao.countByStatus(tenantId, ErpHistoryDataTaskStatusEnum.ERP_HISTORY_TASK_STATUS_NOT_END);
        if (notEndCount > 10) {
            return I18NStringEnum.kNotEndTaskExcessive.getText();
        }
        String redisKey = CommonConstant.REDIS_COUNT_DAILY_HISTORY_TASK_CREATE + tenantId;
        Long dailyCount = redisDataSource.incrAndExpire(redisKey, 1, TimeUnit.DAYS.toSeconds(1), "AbstractErpHistoryTaskServiceImpl");
        if (dailyCount > 100) {
            return I18NStringEnum.kDailyTaskCreateExcessive.getText();
        }
        return null;
    }

    public Result<String> editErpHistoryDataTask(String tenantId, String dcId, Integer userId, CreateErpHistoryDataTaskArg arg, String lang) {
        com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult task = arg.getTask();
        if (StringUtils.isBlank(task.getObjApiName()) || StringUtils.isBlank(task.getTaskName()) || StringUtils.isBlank(task.getRemark()) || task.getTaskType() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (task.getExecuteTime() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), I18NStringEnum.s358);
        }
        if (checkParamsError(task)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        List<Integer> taskStatusEnum = ErpTaskStatusArgEnum.waitExecuteTaskEnum().stream().map(ErpHistoryDataTaskStatusEnum::getStatus).collect(Collectors.toList());
        ErpHistoryDataTaskEntity erpHistoryDataTaskEntity = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(arg.getTask().getId());
        if (ObjectUtils.isEmpty(erpHistoryDataTaskEntity) || !taskStatusEnum.contains(erpHistoryDataTaskEntity.getTaskStatus())) {
            return Result.newError(ResultCodeEnum.TASK_NOT_SUPPORT_EDIT);
        }
        List<String> ployDetails = task.getIntegrationResults().stream().map(IntegrationSimpleViewInfoResult::getId).collect(Collectors.toList());
        ListStringData listStringData = ListStringData.newListStringData(ployDetails);
        erpHistoryDataTaskEntity.setRelatedPloyDetailId(listStringData);
        erpHistoryDataTaskEntity.setRemark(task.getRemark());
        erpHistoryDataTaskEntity.setTaskName(task.getTaskName());
        erpHistoryDataTaskEntity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
        erpHistoryDataTaskEntity.setTaskType(task.getTaskType());
        erpHistoryDataTaskEntity.setExecuteTime(task.getExecuteTime());
        erpHistoryDataTaskEntity.setUpdateTime(System.currentTimeMillis());
        erpHistoryDataTaskEntity.setPriority(task.getPriority());
        return editTaskSuccess(tenantId, task, erpHistoryDataTaskEntity) ? Result.newSuccess() : Result.newError(ResultCodeEnum.SYSTEM_ERROR);
    }



    protected void incDuplicateTime(String tenantId, String taskNum, final List<SyncDataContextEvent> erpObjDataResultList) {
        final List<String> erpIds = erpObjDataResultList.stream()
                .map(event -> event.getSourceData().getString("erp_id"))
                .collect(Collectors.toList());
        incDuplicateTimeByErpIds(tenantId, taskNum, erpIds);
    }

    protected void incDuplicateTimeByErpIds(String tenantId, String taskNum, final List<String> erpIds) {
        if (CollectionUtils.isEmpty(erpIds)) {
            return;
        }

        final Map<String, Integer> dataIdCount = erpIds.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.reducing(0, e -> 1, Integer::sum)));
        final Set<String> exists = historyTaskDuplicateDataDao.findExists(tenantId, taskNum, Lists.newArrayList(dataIdCount.keySet()));

        // 已存在的+n
        final Map<Integer, List<String>> existsMap = exists.stream()
                .collect(Collectors.groupingBy(dataIdCount::remove));
        existsMap.forEach((time, dataIds) -> historyTaskDuplicateDataDao.incDuplicateTime(tenantId, taskNum, dataIds, time));

        if (MapUtils.isEmpty(dataIdCount)) {
            return;
        }

        final long now = System.currentTimeMillis();
        // 不存在的新增
        final List<HistoryTaskDuplicateDataEntity> collect = dataIdCount.entrySet().stream()
                .map(entry -> {
                    final HistoryTaskDuplicateDataEntity entity = new HistoryTaskDuplicateDataEntity();
                    entity.setTenantId(tenantId);
                    entity.setTaskNum(taskNum);
                    entity.setDataId(entry.getKey());
                    entity.setDuplicateTime(entry.getValue());
                    entity.setCreateTime(now);
                    return entity;
                }).collect(Collectors.toList());
        historyTaskDuplicateDataDao.create(collect);
    }

    public void afterConvert2Vo(ErpHistoryDataTaskResult copy, ErpHistoryDataTaskEntity entity) {

    }
}
