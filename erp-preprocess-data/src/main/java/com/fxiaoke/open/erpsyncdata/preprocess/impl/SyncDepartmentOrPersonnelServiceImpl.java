package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmObjectApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AfterWritePersonnelMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AutoBindEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDepartmentOrPersonnelService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 16:59 2020/12/19
 * @Desc:
 */
@Service
@Slf4j
public class SyncDepartmentOrPersonnelServiceImpl implements SyncDepartmentOrPersonnelService {
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CrmDataManager crmDataManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ErpObjManager erpObjManager;

    final String userIdField = "user_id";
    final String phoneField = "phone";

    @Override
    public void afterWriteDepartmentOrPersonnel2Crm(SyncDataContextEvent message) {
        String tenantId = message.getDestTenantId();
        String syncPloyDetailSnapshotId = message.getSyncPloyDetailSnapshotId();
        String dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId, syncPloyDetailSnapshotId);
        boolean addOrUpdate = EventTypeEnum.ADD.getType() == message.getDestEventType() || EventTypeEnum.UPDATE.getType() == message.getDestEventType();
        if (addOrUpdate && message.getWriteResult().isSuccess()) {//新增成功
            String syncDataId = message.getSyncDataId();
            SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getSimple(tenantId, syncDataId);
            String destDataId = message.getWriteResult().getDestDataId();
            if (ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(message.getDestObjectApiName())) {
                //部门特殊处理（同步后）
                HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId, i18NStringManager);
                com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> departments =
                        objectDataService.queryListByIds(headerObj, ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName(), Lists.newArrayList(destDataId));
                if (syncDataEntity != null && departments != null && departments.isSuccess() && CollectionUtils.isNotEmpty(departments.getData().getDataList())) {
                    ObjectData objectData = departments.getData().getDataList().get(0);
                    ErpFieldDataMappingEntity entity = new ErpFieldDataMappingEntity();
                    entity.setTenantId(tenantId);
                    entity.setDataType(ErpFieldTypeEnum.department);
                    entity.setErpDataId(syncDataEntity.getSourceDataId());
                    entity.setErpDataName(objectData.getName());
                    entity.setFsDataId(String.valueOf(objectData.get("dept_id")));
                    entity.setFsDataName(objectData.getName());
                    ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
                    entity.setChannel(connectInfo.getChannel());
                    entity.setDataCenterId(dcId);
                    ErpFieldDataMappingEntity oldEntity = ifExist(entity);
                    if (oldEntity == null) {//新增
                        entity.setId(idGenerator.get());
                        entity.setCreateTime(System.currentTimeMillis());
                        entity.setUpdateTime(entity.getCreateTime());
                        int insert = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(entity);
                        if (insert != 1) {
                            log.info("department erpFieldDataMappingDao.insert failed entity={}", entity);
                        }
                    } else {
                        entity.setId(oldEntity.getId());
                        entity.setUpdateTime(System.currentTimeMillis());
                        int update = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(entity);
                        if (update != 1) {
                            log.info("department erpFieldDataMappingDao.updateByIdAdmin failed entity={}", entity);
                        }
                    }
                }
            } else if (ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(message.getDestObjectApiName())) {
                //人员特殊处理（同步后）
                HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId, i18NStringManager);
                com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> personnels =
                        objectDataService.queryListByIds(headerObj, ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName(), Lists.newArrayList(destDataId));
                if (syncDataEntity != null && personnels != null && personnels.isSuccess() && CollectionUtils.isNotEmpty(personnels.getData().getDataList())) {
                    ObjectData objectData = personnels.getData().getDataList().get(0);
                    EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
                    employeeMappingResult.setFsEmployeeId(Integer.valueOf(String.valueOf(objectData.get(userIdField))));
                    employeeMappingResult.setFsEmployeeName(objectData.getName());
                    employeeMappingResult.setFsEmployeePhone(String.valueOf(objectData.get(phoneField)));
                    employeeMappingResult.setFsEmployeeStatus(1);
                    employeeMappingResult.setErpEmployeeId(syncDataEntity.getSourceDataId());
                    employeeMappingResult.setErpEmployeeName(employeeMappingResult.getFsEmployeeName());
                    employeeMappingResult.setErpEmployeePhone(employeeMappingResult.getFsEmployeePhone());
                    ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
                    employeeMappingResult.setChannel(connectInfo.getChannel());
                    employeeMappingResult.setDataCenterId(dcId);
                    updateEmployeeMappingResult(tenantId, employeeMappingResult);
                }
            }
        }
    }

    public ErpFieldDataMappingEntity ifExist(ErpFieldDataMappingEntity erpFieldDataMappingEntity) {
        String tenantId = erpFieldDataMappingEntity.getTenantId();
        String dataCenterId = erpFieldDataMappingEntity.getDataCenterId();
        ErpFieldTypeEnum dataType = erpFieldDataMappingEntity.getDataType();
        String fsDataId = erpFieldDataMappingEntity.getFsDataId();
        String erpDataId = erpFieldDataMappingEntity.getErpDataId();
        List<ErpFieldDataMappingEntity> oldEntry = erpFieldDataMappingManager
                .listNoSearch(tenantId, dataCenterId, dataType, fsDataId, null);
        if (CollectionUtils.isNotEmpty(oldEntry)) {
            return oldEntry.get(0);
        }
        List<ErpFieldDataMappingEntity> oldEntry1 = erpFieldDataMappingManager
                .listNoSearch(tenantId, dataCenterId, dataType, null, erpDataId);
        if (CollectionUtils.isNotEmpty(oldEntry1)) {
            return oldEntry1.get(0);
        }
        return null;
    }

    private void updateEmployeeMappingResult(String tenantId, EmployeeMappingResult employeeMappingResult) {
        ErpFieldDataMappingEntity erpFieldDataMappingEntity = new ErpFieldDataMappingEntity();
        erpFieldDataMappingEntity.setChannel(employeeMappingResult.getChannel());
        erpFieldDataMappingEntity.setDataType(ErpFieldTypeEnum.employee);
        erpFieldDataMappingEntity.setFsDataId(employeeMappingResult.getFsEmployeeId() == null ? "" :
                String.valueOf(employeeMappingResult.getFsEmployeeId()));
        erpFieldDataMappingEntity.setFsDataName(employeeMappingResult.getFsEmployeeName() == null ? "" : employeeMappingResult.getFsEmployeeName());
        erpFieldDataMappingEntity.setErpDataId(employeeMappingResult.getErpEmployeeId());
        erpFieldDataMappingEntity.setErpDataName(employeeMappingResult.getErpEmployeeName());
        erpFieldDataMappingEntity.setTenantId(tenantId);
        erpFieldDataMappingEntity.setDataCenterId(employeeMappingResult.getDataCenterId());
        ErpFieldDataMappingEntity oldEntity = ifExist(erpFieldDataMappingEntity);
        if (oldEntity == null) {
            erpFieldDataMappingEntity.setId(idGenerator.get());
            erpFieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int insertResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpFieldDataMappingEntity);
            if (insertResult != 1) {
                log.info("personnel erpFieldDataMappingDao.insert failed entity={}", erpFieldDataMappingEntity);
            }
        } else {
            erpFieldDataMappingEntity.setId(oldEntity.getId());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int updateResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpFieldDataMappingEntity);
            if (updateResult == 1) {
                log.info("personnel erpFieldDataMappingDao.updateByIdAdmin failed entity={}", erpFieldDataMappingEntity);
            }
        }
    }

    @Override
    public void autoBindEmployeeMapping(AutoBindEmployeeMapping.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getDataCenterIds())) {
            return;
        }

        final String tenantId = arg.getTenantId();
        final Map<String, List<String>> allEmployeeAutoBindFields = configCenterConfig.getAllEmployeeAutoBindFields(tenantId);

        final ObjectData objectData = crmDataManager.getById(tenantId, CrmObjectApiName.Employee_API_NAME, arg.getObjectId());
        if (Objects.isNull(objectData)) {
            log.warn("updateEmployeeMapping, crmObjById is null, tenantId:{}, objectId:{}", tenantId, arg.getObjectId());
            return;
        }

        allEmployeeAutoBindFields.entrySet().stream()
                .filter(entry -> arg.getDataCenterIds().contains(entry.getKey()))
                .filter(entry -> StringUtils.isNotBlank(objectData.getString(entry.getValue().get(0))))
                .forEach(entry -> {
                    final String dcId = entry.getKey();
                    final String erpUserIdField = entry.getValue().get(0);
                    String erpUserNameFiled = entry.getValue().get(1);
                    erpUserNameFiled = StringUtils.isNotBlank(erpUserNameFiled) ? erpUserNameFiled : erpUserIdField;
                    try {
                        updateEmployeeMappingResult(tenantId, dcId, objectData, erpUserIdField, erpUserNameFiled);
                    } catch (Exception e) {
                        log.error("updateEmployeeMapping error tenantId:{} dcId:{} erpUserIdField:{} erpUserNameFiled:{} objectData:{}", tenantId, dcId, erpUserIdField, erpUserNameFiled, objectData, e);
                    }
                });
    }

    private void updateEmployeeMappingResult(String tenantId, String dcId, ObjectData objectData, String erpUserIdField, String erpUserNameFiled) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if (connectInfo == null) {
            return;
        }

        final String erpEmployeeId = objectData.getString(erpUserIdField);
        final String erpEmployeeName = objectData.getString(erpUserNameFiled);
        if (StringUtils.isBlank(erpEmployeeId) || StringUtils.isBlank(erpEmployeeName)) {
            return;
        }

        EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
        employeeMappingResult.setFsEmployeeId(Integer.valueOf(String.valueOf(objectData.get(userIdField))));
        employeeMappingResult.setFsEmployeeName(objectData.getName());
        employeeMappingResult.setFsEmployeePhone(String.valueOf(objectData.get(phoneField)));
        employeeMappingResult.setFsEmployeeStatus(1);
        employeeMappingResult.setErpEmployeePhone(employeeMappingResult.getFsEmployeePhone());
        employeeMappingResult.setErpEmployeeId(erpEmployeeId);
        employeeMappingResult.setErpEmployeeName(erpEmployeeName);
        employeeMappingResult.setChannel(connectInfo.getChannel());
        employeeMappingResult.setDataCenterId(dcId);
        updateEmployeeMappingResult(tenantId, employeeMappingResult);
        //jar连接器处理
        connectorProcess(connectInfo, employeeMappingResult);
    }

    @Override
    public void allEmployeeAutoBindFields(String tenantId, String dcId, String erpUserIdField, String erpUserNameFiled) {
        final Filter filter = new Filter();
        filter.setOperator(Filter.OperatorContants.EQ);
        filter.setFieldName("status");
        filter.setFieldValues(Lists.newArrayList("0"));
        crmDataManager.handleAllData(tenantId, CrmObjectApiName.Employee_API_NAME,
                objectData -> updateEmployeeMappingResult(tenantId, dcId, objectData, erpUserIdField, erpUserNameFiled), filter);
    }

    private void connectorProcess(ErpConnectInfoEntity connectInfo, EmployeeMappingResult employeeMappingResult) {
        try {
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
            erpDataManager.processChangeEmployeeMapping(employeeMappingResult, connectInfo);
        } catch (Exception e) {
            log.warn("processChangeEmployeeMapping error", e);
        }
    }

    @Override
    public void afterWritePersonnelMapping(AfterWritePersonnelMapping.Arg arg) {
        final String tenantId = arg.getTenantId();

        final ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, arg.getErpSplitObjectApiName());

        final ObjectData objectData = crmDataManager.getById(tenantId, CrmObjectApiName.Employee_API_NAME, arg.getEmployeeObjectId());
        if (Objects.isNull(objectData)) {
            log.warn("updateEmployeeMapping, crmObjById is null, tenantId:{}, objectId:{}", tenantId, arg.getEmployeeObjectId());
            return;
        }
        EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
        employeeMappingResult.setFsEmployeeId(Integer.valueOf(String.valueOf(objectData.get(userIdField))));
        employeeMappingResult.setFsEmployeeName(objectData.getName());
        employeeMappingResult.setFsEmployeePhone(String.valueOf(objectData.get(phoneField)));
        employeeMappingResult.setFsEmployeeStatus(1);
        employeeMappingResult.setErpEmployeeId(arg.getErpEmployeeId());
        employeeMappingResult.setErpEmployeeName(arg.getErpEmployeeName());
        employeeMappingResult.setErpEmployeePhone(employeeMappingResult.getFsEmployeePhone());
        employeeMappingResult.setChannel(relation.getChannel());
        employeeMappingResult.setDataCenterId(relation.getDataCenterId());

        updateEmployeeMappingResult(tenantId, employeeMappingResult);
    }
}
