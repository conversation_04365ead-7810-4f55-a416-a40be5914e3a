package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SendEventMqRecord;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/6/1
 */
@Service
@Slf4j
@Data
public class ProbeErpDataManager {
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;

    private final ExecutorService  executorService= new NamedThreadPoolExecutor("erpProbeManager",20,30,10000, new ThreadPoolExecutor.CallerRunsPolicy());
    private final Map<String, Long> lastSendMsgTime = Maps.newHashMap();
    /**
     * 改为同步调用
     * 轮训临时库mongo的数据，丢给聚合框架的MQ。
     *
     * @param erpObjDatumResults
     * @param needSendDetailEvent
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public Integer asyncSendErpDataMq(List<SyncDataContextEvent> erpObjDatumResults, boolean needSendDetailEvent) {
        /**
         * 如果从ERP拿到的数据是主从结构，那么主和从是拆开后，逐条发送给聚合框架MQ的。
         * 因此，在后续的处理步骤中，写道CRM之前，有时候是需要补齐数据的。
         * */
        List<SyncDataContextEvent> syncDataContextEvents = buildBatchSendErpObjDataArg(erpObjDatumResults, needSendDetailEvent);
        //监控一下时间
        StopWatch stopWatch = new StopWatch("asyncSendErpDataEvent stopwatch");
        stopWatch.start();
        SendEventMqRecord record = SendEventMqRecord.init();
        CountDownLatch countDownLatch = new CountDownLatch(syncDataContextEvents.size());
        for (SyncDataContextEvent erpEventData : syncDataContextEvents) {
            executorService.submit(() -> {
                sendOneEvent(record, erpEventData);
                countDownLatch.countDown();
            });
        }
        boolean await = false;
        try {
            await = countDownLatch.await(5, TimeUnit.MINUTES);
            return syncDataContextEvents.size();
        } catch (InterruptedException e) {
            log.error("countDownLatch await error", e);
            return 0;
        } finally {
            stopWatch.stop();
            log.info("async batch send erp Obj data,await:{},result:{},cost:{}", await, record, stopWatch);
        }
    }


    public void sendOneEvent(SendEventMqRecord record, SyncDataContextEvent erpEventData) {
        String dataId = erpEventData.getSourceData().getId();
        String traceId = TraceUtil.addChildTrace(dataId);
        BatchSendEventDataArg.EventData eventData = new BatchSendEventDataArg.EventData();
        BeanUtils.copyProperties(erpEventData, eventData);

        Result<Void> result = Result.copy(eventTriggerService.batchSendEventData2DispatcherMqByContext(Lists.newArrayList(erpEventData)));
        record.addResult(result, dataId, traceId);
        log.debug("send one event,result:{},dataId:{},traceId:{}", result, dataId, traceId);
        TraceUtil.removeChildTrace(dataId);
    }


    public List<SyncDataContextEvent> buildBatchSendErpObjDataArg(List<SyncDataContextEvent> erpObjDatumResults, boolean needSendDetailEvent) {
        //避免出现list的ConcurrentModificationException
        List<SyncDataContextEvent> convertDataContext=Lists.newArrayList();
        for (SyncDataContextEvent erpObjDataResult : erpObjDatumResults) {
            String parentLogId=erpObjDataResult.getSyncLogId();
            int i=0;//区分主从数据
            //主数据
            erpObjDataResult.setSourceTenantType(TenantType.ERP);
            SyncDataContextEvent syncDataContextEvent=new SyncDataContextEvent();
            BeanUtils.copyProperties(erpObjDataResult,syncDataContextEvent);
            syncDataContextEvent.setDetailData(null);//去掉从对象，主从拆开;
            if(!StringUtils.isEmpty(parentLogId)){
                syncDataContextEvent.setSyncLogId(LogIdUtil.buildChildLogId(parentLogId,i++));
            }
            convertDataContext.add(syncDataContextEvent);
            //从对象数据
            if (!needSendDetailEvent || erpObjDataResult.getDetailData() == null) {
                continue;
            }
            //从对象数据
            for (List<ObjectData> detailDatas : erpObjDataResult.getDetailData().values()) {
                for (ObjectData detailData : detailDatas) {
                    SyncDataContextEvent eventData = new SyncDataContextEvent();
                    eventData.setSourceEventType(erpObjDataResult.getSourceEventType());
                    eventData.setSourceData(detailData);
                    eventData.setSourceTenantType(TenantType.ERP);
                    if(!StringUtils.isEmpty(parentLogId)){
                        eventData.setSyncLogId(LogIdUtil.buildChildLogId(parentLogId,i++));
                    }
                    eventData.setDataReceiveType(erpObjDataResult.getDataReceiveType());
                    eventData.setDataVersion(erpObjDataResult.getDataVersion());
                    eventData.setDataReceiveType(erpObjDataResult.getDataReceiveType());
                    convertDataContext.add(eventData);
                }
            }
        }
        return convertDataContext;
    }

    /**函数功能： 轮询ERP的时候，连续5次返回完全相同的ID。
     * 出现这种情况，要及时停掉轮询，并发送通知给值班，联系实施人员。
     * @param curPageErpObjData  当前分页的erp数据 list
     * @param lastPageErpObjData  传空list即可，内部会自动维护。
     * @return true: 存在连续的完全相同额分页，  false:不存在连续的完全相同的分页(可能是没有id重复，也可能是部分id重复)。
     * */
    public boolean isAllIDRepeatAsLastPage(String tenantId,
                                           String erpSplitObjApiName,
                                           Integer offset, Integer limit,
                                           List<SyncDataContextEvent> curPageErpObjData,
                                           List<String> lastPageErpObjData) {
        try {
            ErpObjectFieldEntity idField = idFieldConvertManager.getIdField(tenantId, erpSplitObjApiName);
            List<String> curPageErpIdList = curPageErpObjData.stream().filter(data -> data.getSourceData().getString(idField.getFieldApiName()) != null)
                    .map(data -> data.getSourceData().getString(idField.getFieldApiName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(curPageErpIdList)) {
                curPageErpIdList = curPageErpObjData.stream().filter(data -> data.getSourceData().getString("erp_id") != null)
                        .map(data -> data.getSourceData().getString("erp_id")).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(curPageErpIdList)) {
                lastPageErpObjData.clear();
                return false;
            }
            if (CollectionUtils.isEmpty(lastPageErpObjData)) {
                lastPageErpObjData.addAll(curPageErpIdList);
                return false;
            }

            boolean sameIdResult = false;
            if (lastPageErpObjData.size() == curPageErpIdList.size()) {
                lastPageErpObjData.removeAll(curPageErpIdList);
                if (CollectionUtils.isEmpty(lastPageErpObjData)) {
                    sameIdResult =  true;
                }
            }
            lastPageErpObjData.clear();
            lastPageErpObjData.addAll(curPageErpIdList);
            return sameIdResult;
        } catch (Exception e) {
            return false;
        }
    }

    public void sendSuperAdminNoticeAllIDSameMsg(String tenantId, String erpObjApiName) {
        //发送消息
        try {
            StringBuilder noticeMsgbuilder = new StringBuilder();
            String msg = " ";
            msg += "ei: " + tenantId + " obj: " + erpObjApiName + ", all page id same!";
            msg += i18NStringManager.getByEi(I18NStringEnum.s2220, tenantId);

            noticeMsgbuilder.append(msg).append(System.lineSeparator());

            SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder().tenantId(tenantId).msg(noticeMsgbuilder.toString()).build();
            //服务号通知研发值班
            notificationService.sendSuperAdminNotice(sendAdminNoticeArg);
        } catch (Exception e) {
            log.error("sendSuperAdminNoticeMsg get exception:{}, ",
                    Optional.ofNullable(e.getMessage()).map((s) -> s.length() > 300 ? s.substring(0, 300) : s).orElse(" null exception"));
        }
    }   
}
