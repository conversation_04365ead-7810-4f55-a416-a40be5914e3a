package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.alibaba.fastjson2.JSON;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.Operate;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18 17:57:45
 */
@Component
@Slf4j
public class FilterCrmHistoryTaskServiceImpl extends AbstractErpHistoryTaskServiceImpl {
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Override
    public ErpHistoryDataTaskTypeEnum taskType() {
        return ErpHistoryDataTaskTypeEnum.TYPE_CRM_FILTER;
    }

    @Override
    public boolean checkParamsError(ErpHistoryDataTaskResult task) {
        return CollectionUtils.isEmpty(task.getCrmFilters()) ||
                task.getCrmFilters().stream().anyMatch(crmFilter ->
                        StringUtils.isBlank(crmFilter.getObjectApiName())
                                || CollectionUtils.isEmpty(crmFilter.getFilters())
                                || crmFilter.getFilters().stream().anyMatch(CollectionUtils::isEmpty)
                                || crmFilter.getFilters().stream().anyMatch(filter -> filter.stream().anyMatch(filterData ->
                                StringUtils.isBlank(filterData.getFieldApiName())
                                        || StringUtils.isBlank(filterData.getOperate())
                                        || CollectionUtils.isEmpty(filterData.getFieldValue())))
                );
    }

    @Override
    public boolean checkTaskValid(ErpHistoryDataTaskEntity task) {
        return StringUtils.isBlank(task.getFilterString());
    }

    @Override
    public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        final ErpConnectInfoEntity crmDc = erpConnectInfoManager.getOrCreateCrmDc(tenantId);
        // dcId设置为crm的dcId, 用于后面的任务管理菜单页面搜索
        entity.setDataCenterId(crmDc.getId());
        setCrmsFilter(entity, task.getCrmFilters());
        return createHistoryTask(tenantId, Lists.newArrayList(entity));
    }

    @Override
    public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        final ErpConnectInfoEntity crmDc = erpConnectInfoManager.getOrCreateCrmDc(tenantId);
        // dcId设置为crm的dcId, 用于后面的任务管理菜单页面搜索
        entity.setDataCenterId(crmDc.getId());
        setCrmsFilter(entity, task.getCrmFilters());
        return editHistorySnapshotTask(tenantId, entity);
    }

    @Override
    public void afterConvert2Vo(ErpHistoryDataTaskResult copy, ErpHistoryDataTaskEntity entity) {
        copy.setCrmFilters(gson.fromJson(entity.getFilterString(), TypeToken.getParameterized(List.class, ErpHistoryDataTaskResult.CrmFilters.class).getType()));
        copy.setFilterString(null);
    }

    @Override
    public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
        //初始化logid
        final String realObjApiName = task.getRealObjApiName();
        syncLogManager.initLogId(tenantId, realObjApiName);

        AtomicLong totalDataSize = new AtomicLong(0L);
        task.setTotalDataSize(0L);
        return doTaskByCrmFilter(tenantId, task, (objectApiName, dataList) -> {
            // 发送聚合框架
            sendCrmObj2DispatcherMq(task.getTenantId(), objectApiName, dataList);
            //每次更新一下总数，只是更新实例，最后在处理结果更新到db。
            task.setTotalDataSize(totalDataSize.addAndGet(dataList.size()));
            // crm->erp 暂时不做限速
            // pollingDataSpeedRateLimitManager.acquireQueryCrmHistoryData(tenantId, (long) dataList.size());
            return Result.newSuccess();
        });
    }

    private Gson gson = new GsonBuilder().setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE).create();

    @Nullable
    protected Result<Void> doTaskByCrmFilter(String tenantId, ErpHistoryDataTaskEntity task, BiFunction<String, List<ObjectData>, Result<Void>> crmDataListHandler) {
        final SearchTemplateQuery query = new SearchTemplateQuery();
        // 设置不检查数据权限
        query.setDataRolePermission(false);
        final int limit = 1000;
        query.setLimit(limit);
        query.setOffset(0);
        query.setOrders(Lists.newArrayList(new SearchTemplateQueryOrderBy("_id", true)));

        HeaderObj headerObj = new HeaderObj(Integer.parseInt(tenantId), CrmConstants.SYSTEM_USER);
        final String taskFilterString = task.getFilterString();
        final List<ErpHistoryDataTaskResult.CrmFilters> crmFilters = gson.fromJson(taskFilterString, TypeToken.getParameterized(List.class, ErpHistoryDataTaskResult.CrmFilters.class).getType());

        // 只查正常的数据,不查作废数据
        query.addFilter(ObjectDescribeContants.TENANT_ID, Lists.newArrayList(tenantId), FilterOperatorEnum.EQ);
        query.addFilter("is_deleted", Lists.newArrayList("0"), FilterOperatorEnum.EQ);

        int i = 0;
        for (ErpHistoryDataTaskResult.CrmFilters crmFilter : crmFilters) {
            final List<Wheres> wheres = convert2Where(crmFilter.getFilters());
            query.setWheres(wheres);
            final String objectApiName = crmFilter.getObjectApiName();
            String maxId = null;

            log.info("create crm filter arg:{} filter:{} ", JSON.toJSONString(query), taskFilterString);
            for (; i < 10_000; i++) {
                ErpHistoryDataTaskEntity byId = erpHistoryDataTaskDao.findById(task.getId());
                if (byId != null && byId.getNeedStop()) {//中断
                    return Result.newError(ResultCodeEnum.STOP_HISTORY_TASK);
                }

                if (StringUtils.isNotBlank(maxId)) {
                    query.addFilter("_id", Lists.newArrayList(maxId), FilterOperatorEnum.GT);
                }

                final com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> result = objectDataService.queryBySearchTemplate(headerObj, objectApiName, query);
                if (BooleanUtils.isNotTrue(result.isSuccess())) {
                    //轮询返回错误太多，打成warn日志,如果不是第一页，打成error日志。
                    if (i != 0) {
                        log.error("objectDataService queryByFilter error, tenantId:{}, arg:{}, result:{}", tenantId, query, result);
                    } else {
                        log.warn("objectDataService queryByFilter error, tenantId:{}, arg:{}, result:{}", tenantId, query, result);
                    }
                    task.setRemark(task.getRemark() + result.getMessage());
                    return new Result<>(String.valueOf(result.getCode()), result.getMessage(), null);
                }

                final List<ObjectData> dataList = result.getData().getQueryResult().getData();
                if (result.getData().getQueryResult().getTotalNumber() <= 0 || CollectionUtils.isEmpty(dataList)) {
                    break;
                }

                maxId = dataList.get(dataList.size() - 1).getId();

                final Result<Void> process = crmDataListHandler.apply(objectApiName, dataList);
                if (!process.isSuccess()) {
                    return process;
                }
            }
        }
        return Result.newSuccess();
    }

    private void sendCrmObj2DispatcherMq(String tenantId, String objApiName, List<ObjectData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        final List<BatchSendEventDataArg.EventData> collect = dataList.stream().map(data -> {
            SyncDataContextEvent eventData = new SyncDataContextEvent();
            com.fxiaoke.open.erpsyncdata.common.data.ObjectData objectData = com.fxiaoke.open.erpsyncdata.common.data.ObjectData.convert(data);
            eventData.setSourceData(objectData);
            eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
            eventData.setSourceTenantType(TenantType.CRM);
            eventData.setSyncLogId(syncLogManager.getInitLogId(tenantId, objApiName));
            eventData.setDataReceiveType(DataReceiveTypeEnum.PAAS_META_EVENT.getType());
            return SyncDataContextUtils.convertSendArgResultByContext(eventData);
        }).collect(Collectors.toList());
        BatchSendEventDataArg arg = new BatchSendEventDataArg(collect);
        List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
        eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
    }

    private List<Wheres> convert2Where(List<List<FilterData>> filterList) {
        return filterList.stream()
                .map(filters -> {
                    final Wheres wheres = new Wheres();
                    wheres.setConnector(Where.CONN.OR.toString());
                    filters.forEach(filter -> addFilter(wheres, filter));
                    return wheres;
                }).collect(Collectors.toList());
    }

    private void addFilter(Wheres wheres, FilterData filter) {
        // BETWEEN 特殊处理
        if (Objects.equals(filter.getOperate(), Operate.BETWEEN)) {
            wheres.addFilter(filter.getFieldApiName(), Lists.newArrayList(filter.getFieldValue().get(0).toString()), FilterOperatorEnum.GTE);
            wheres.addFilter(filter.getFieldApiName(), Lists.newArrayList(filter.getFieldValue().get(1).toString()), FilterOperatorEnum.LT);
            return;
        }

        final List<?> fieldValue = filter.getFieldValue();
        final List<String> collect = fieldValue.stream().map(Object::toString).collect(Collectors.toList());
        wheres.addFilter(filter.getFieldApiName(), collect, convert2FilterOperatorEnum(filter.getOperate()));
    }

    private FilterOperatorEnum convert2FilterOperatorEnum(String operate) {
        switch (operate) {
            case Operate.EQ:
                return FilterOperatorEnum.EQ;
            case Operate.N:
                return FilterOperatorEnum.N;
            case Operate.GT:
                return FilterOperatorEnum.GT;
            case Operate.LT:
                return FilterOperatorEnum.LT;
            case Operate.GTE:
                return FilterOperatorEnum.GTE;
            case Operate.LTE:
                return FilterOperatorEnum.LTE;
            case Operate.LIKE:
                return FilterOperatorEnum.LIKE;
            case Operate.IN:
                return FilterOperatorEnum.IN;
            default:
                return FilterOperatorEnum.valueOf(operate);
        }
    }

    protected void setCrmsFilter(ErpHistoryDataTaskEntity entity, List<ErpHistoryDataTaskResult.CrmFilters> crmFilters) {
        resetFilterLongValue(crmFilters);
        entity.setFilterString(gson.toJson(crmFilters));
    }

    /**
     * 因为前端解析使用的是 {@link com.fxiaoke.open.erpsyncdata.web.interceptor.CepGsonConverter#CepGsonConverter()}
     * 导致前端传过来的Long类型被转为科学计数法了,需要将其重新转回Long类型
     */
    protected void resetFilterLongValue(List<ErpHistoryDataTaskResult.CrmFilters> crmFilters) {
        crmFilters.stream()
                .map(ErpHistoryDataTaskResult.CrmFilters::getFilters)
                .flatMap(Collection::stream)
                .flatMap(Collection::stream)
                .filter(filterData -> {
                    final List<?> fieldValue1 = filterData.getFieldValue();
                    return CollectionUtils.isNotEmpty(fieldValue1) && fieldValue1.stream().allMatch(s -> Objects.nonNull(s) && s instanceof Double && ((Double) s == ((Double) s).longValue()));
                })
                .forEach(filterData -> {
                    final List<?> fieldValue = filterData.getFieldValue();
                    final List<Long> collect = fieldValue.stream()
                            .map(s -> ((Double) s).longValue())
                            .collect(Collectors.toList());
                    filterData.setFieldValue(collect);
                });
    }
}
