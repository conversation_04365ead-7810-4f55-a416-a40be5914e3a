package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardDetailId;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardInvalidData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardRecoverData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.context.FromErp2TempCtx;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.CrmObjectManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ErpDataPreprocessManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.SpecialFieldPreprocessManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.SplitObjectManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.NodeFromErp2TempProcessor;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SplitObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpTempResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpProcessedDataService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Service("erpDataPreprocessService")
@Slf4j
public class ErpDataPreprocessServiceImpl implements ErpDataPreprocessService {
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private SpecialFieldPreprocessManager specialFieldPreprocessManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private SplitObjectManager splitObjectManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private CrmObjectManager crmObjectManager;
    @Autowired
    private ErpProcessedDataService erpProcessedDataService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpDataPreprocessManager erpDataPreprocessManager;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private GetByIdBreakManager getByIdBreakManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private NodeFromErp2TempProcessor nodeFromErp2TempProcessor;

    /**
     * 获取erp对象数据,包含从对象数据
     * 轮询使用，会有筛除数据的操作
     *
     * @param arg
     * @return
     */
    @Override
    public Result<ListErpObjDataResult> listErpObjDataByTime(TimeFilterArg arg) {
        String splitObjApiName = arg.getObjAPIName();
        String realApiName = idFieldConvertManager.getRealObjApiName(arg.getTenantId(), splitObjApiName);
        String dataCenterId = idFieldConvertManager.getDataCenterId(arg.getTenantId(), splitObjApiName);
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), dataCenterId);
        //填充自定义筛选条件
        fillFilter(arg, connectInfo);
        //浅克隆
        TimeFilterArg timeFilterArg = arg.clone();
        timeFilterArg.setObjAPIName(realApiName);
        if (timeFilterArg.getOperationType() != EventTypeEnum.UPDATE.getType()
                && timeFilterArg.getOperationType() != EventTypeEnum.ADD.getType() && timeFilterArg.getOperationType() != EventTypeEnum.INVALID.getType()) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR, i18NStringManager.getByEi(I18NStringEnum.s1031,arg.getTenantId())
                    + EventTypeEnum.getNameByEiAndType(i18NStringManager,timeFilterArg.getTenantId(),timeFilterArg.getOperationType()));
        }
        //改为使用节点方式执行
        FromErp2TempCtx context = new FromErp2TempCtx()
                .setBasic(timeFilterArg.getTenantId(), dataCenterId)
                .setSplitObjApiName(splitObjApiName)
                .setTimeFilterArg(timeFilterArg);
        nodeFromErp2TempProcessor.processMessage(context);
        //兼容旧接口返回：这里轮询时实际上不需要返回数据了的，暂时先不处理
        return context.getResult();
    }

    /**
     * 从临时库获取数据
     * 注意：这里会判断中间表，方便上层判断发送逻辑
     * @param arg
     * @return
     */
    @Override
    @InvokeMonitor(tenantId = "#arg.tenantId", dcId = "", invokeType = InvokeTypeEnum.ERPDSS, count = "#result?.getData()?.erpObjDataResultList?.size()?:0", objAPIName = "#arg.objAPIName", action = ActionEnum.GET_FROM_TEMP, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public Result<ListErpTempResult> listErpObjDataFromMongo(QueryTempTimeFilterArg arg) {
        String splitObjApiName = arg.getObjAPIName();
        String realApiName = idFieldConvertManager.getRealObjApiName(arg.getTenantId(), splitObjApiName);
        LogIdUtil.setRealObjApiName(realApiName);
        String dataCenterId = idFieldConvertManager.getDataCenterId(arg.getTenantId(), splitObjApiName);
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), dataCenterId);
        QueryTempTimeFilterArg timeFilterArg = new QueryTempTimeFilterArg();
        BeanUtils.copyProperties(arg, timeFilterArg);
        timeFilterArg.setObjAPIName(realApiName);
        if (timeFilterArg.getOperationType() != EventTypeEnum.UPDATE.getType()
                && timeFilterArg.getOperationType() != EventTypeEnum.ADD.getType()
                && timeFilterArg.getOperationType() != EventTypeEnum.INVALID.getType()) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR, i18NStringManager.getByEi(I18NStringEnum.s1031,arg.getTenantId())
                    + EventTypeEnum.getNameByEiAndType(i18NStringManager,arg.getTenantId(),timeFilterArg.getOperationType()));
        }
        Result<ListErpTempResult> dataListRes = erpTempDataManager.listErpObjDataFromMongo(timeFilterArg, dataCenterId);
        if (dataListRes == null || !dataListRes.isSuccess() || dataListRes.getData() == null || CollectionUtils.isEmpty(dataListRes.getData().getErpObjDataResultList())) {
            return dataListRes;
        }
        //在manager来判断是否complete
//        dataListRes.getData().setComplete(timeFilterArg.getLimit() > dataListRes.getData().getErpObjDataResultList().size());
        //对象拆分处理.注意 对象拆分的manager可能会改变erp对象的APIName.
        List<SyncDataContextEvent> origiErpObjDataResultList = dataListRes.getData().getErpObjDataResultList();
        List<SyncDataContextEvent> newErpObjDataResultList = Lists.newArrayList();
        for (SyncDataContextEvent obj : origiErpObjDataResultList) {
            obj.getSourceData().putTenantId(timeFilterArg.getTenantId());
            List<SyncDataContextEvent> newObjList = splitObjectManager.erpRealObj2SplitObjData(obj, dataCenterId);
            //根据传参筛选出需要的对象数据。
            newObjList.removeIf(v -> !v.getSourceData().getApiName().equals(splitObjApiName));
            newErpObjDataResultList.addAll(newObjList);
        }
        // K3C渠道需要清理冗余字段
        if (connectInfo.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD)) {
            dealUselessField(dataListRes, arg);
        }
        //检查数据合法性
        erpDataPreprocessManager.checkListResult(realApiName, timeFilterArg.getTenantId(), newErpObjDataResultList);
        //转换id字段，查找关联、主从字段复合Id处理
        for (SyncDataContextEvent erpObjDataResult : newErpObjDataResultList) {
            idFieldConvertManager.convertField2Crm(timeFilterArg.getTenantId(), erpObjDataResult);
        }
        dataListRes.getData().setErpObjDataResultList(newErpObjDataResultList);
        //根据中间表是否有映射关系，指定事件类型,新增或者更新
        boolean createOrUpdate = EventTypeEnum.ADD.getType() == timeFilterArg.getOperationType() || EventTypeEnum.UPDATE.getType() == timeFilterArg.getOperationType();
        if (createOrUpdate) {
            SyncPloyDetailSnapshotEntity entity = syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(timeFilterArg.getTenantId(), timeFilterArg.getSnapshotId());
            List<SyncDataContextEvent> erpObjDataResults = Lists.newArrayList();
            List<String> idList = dataListRes.getData().getErpObjDataResultList().stream().map(v -> v.getSourceData().getId()).distinct().collect(Collectors.toList());
            Map<String, Boolean> id2Created = syncDataMappingsManager.listCreatedMapping(entity.getSourceTenantId(),
                    entity.getSourceObjectApiName(), idList, entity.getDestObjectApiName());
            for (SyncDataContextEvent erpObjDataVO : dataListRes.getData().getErpObjDataResultList()) {
                //每一条数据
                ObjectData objectData = erpObjDataVO.getSourceData();//主数据
                if (!id2Created.getOrDefault(objectData.getId(), false)) {
                    erpObjDataVO.setSourceEventType(EventTypeEnum.ADD.getType());
                    processeTempAddEventData(realApiName, timeFilterArg, erpObjDataVO);
                } else {
                    erpObjDataVO.setSourceEventType(EventTypeEnum.UPDATE.getType());
                }
                //如有需要，在上层根据事件判断发送的逻辑
                erpObjDataResults.add(erpObjDataVO);
            }
            dataListRes.getData().setErpObjDataResultList(erpObjDataResults);
        } else {//除新增更新外的其他事件类型
            dataListRes.getData().getErpObjDataResultList().forEach(data -> data.setSourceEventType(timeFilterArg.getOperationType()));
        }

        //特殊字段转换
        List<SyncDataContextEvent> results = specialFieldPreprocessManager.convertErpFieldValue2CrmList(timeFilterArg.getTenantId(), dataCenterId, splitObjApiName, dataListRes.getData().getErpObjDataResultList());
        dataListRes.getData().setErpObjDataResultList(results);
        Result<Boolean> need=erpProcessedDataService.needCompareDetail(connectInfo.getTenantId(),connectInfo.getId(),connectInfo.getChannel(),splitObjApiName);
        if (need.isSuccess()&&need.getData()) {
            //查询更新时才走这个逻辑
            if (timeFilterArg.getOperationType().equals(EventTypeEnum.UPDATE.getType()) && CollectionUtils.isNotEmpty(results)) {
                Result<List<SyncDataContextEvent>> erpInvalidDetailList = erpProcessedDataService.getErpInvalidDetailList(timeFilterArg.getTenantId(),
                        dataCenterId, results);
                if (CollectionUtils.isNotEmpty(erpInvalidDetailList.getData())) {//被删除的明细数据
                    dataListRes.getData().getErpObjDataResultList().addAll(erpInvalidDetailList.getData());
                }
            }
        }
        //筛除不需要同步的数据
        if (createOrUpdate) {
            erpDataPreprocessManager.tryRemoveNotNeedSync(results, arg.getTenantId(), dataCenterId, connectInfo.getChannel(), arg.getSnapshotId());
        }
        return dataListRes;
    }


    private void processeTempAddEventData(String realApiName, QueryTempTimeFilterArg timeFilterArg, SyncDataContextEvent erpObjDataVO) {
        if (tenantConfigurationManager.inGroupWhiteList(timeFilterArg.getTenantId(),realApiName, TenantConfigurationTypeEnum.TOO_MANY_DETAILS_TENANTS_OBJS)) {
            //明细太多的对象配置,去掉超出的明细
            for (String apiName : erpObjDataVO.getDetailData().keySet()) {
                List<ObjectData> detail = erpObjDataVO.getDetailData().get(apiName);
                Integer limit = configCenterConfig.getSPECIAL_LIMIT_DETAIL_SIZE().getOrDefault(apiName,
                        configCenterConfig.getDEFAULT_LIMIT_DETAIL_SIZE());
                if (detail != null && detail.size() > limit) {
                    log.info("removeOutOfLimitDetail dataId={},detailApiName={},oldSize={},newSize={}", erpObjDataVO.getSourceData().getId(), apiName, detail.size(), limit);
                    detail = detail.stream().limit(limit).collect(Collectors.toList());
                    erpObjDataVO.getDetailData().put(apiName, detail);
                }
            }
        }
    }


    private void fillFilter(TimeFilterArg arg, ErpConnectInfoEntity connectInfo) {
        final String tenantId = arg.getTenantId();
        final String dataCenterId = connectInfo.getId();
        final String channel = connectInfo.getChannel().name();
        final String objAPIName = arg.getObjAPIName();
        ErpObjInterfaceUrlEnum queryType = Objects.equals(arg.getOperationType(), EventTypeEnum.INVALID.getType()) ? ErpObjInterfaceUrlEnum.queryInvalid : ErpObjInterfaceUrlEnum.queryMasterBatch;
        try {
            arg.setFilters(getFilterData(tenantId, dataCenterId, channel, objAPIName, queryType));
        } catch (Exception e) {
            //捕捉异常防止反序列化出错影响。
            log.error("trans filters error", e);
        }
    }

    @Override
    @Cached(cacheType = CacheType.LOCAL, expire = 10)
    public List<List<FilterData>> getFilterData(final String tenantId, final String dataCenterId, final String channel, final String objAPIName, ErpObjInterfaceUrlEnum queryType) {
        return Objects.equals(queryType, ErpObjInterfaceUrlEnum.queryInvalid) ?
                getInvalidFilterData(tenantId, dataCenterId, channel, objAPIName) :
                getFilterData(tenantId, dataCenterId, channel, objAPIName);
    }

    private List<List<FilterData>> getFilterData(final String tenantId, final String dataCenterId, final String channel, final String objAPIName) {
        return getFilterDataValue(tenantId, dataCenterId, channel).getOrDefault(objAPIName, new ArrayList<>());
    }

    private List<List<FilterData>> getInvalidFilterData(final String tenantId, final String dataCenterId, final String channel, final String objAPIName) {
        ErpTenantConfigurationEntity filter = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, TenantConfigurationTypeEnum.TIME_FILTER_INVALID.name());
        final String filterConfig;
        if (Objects.isNull(filter)) {
            filterConfig = initInvalidFilterDataConfig(tenantId, dataCenterId, channel);
        } else {
            filterConfig = StringUtils.isBlank(filter.getConfiguration()) ? "{}" : filter.getConfiguration();
        }

        Map<String, List<List<FilterData>>> filterDataValue = JacksonUtil.fromJson(filterConfig, new TypeReference<Map<String, List<List<FilterData>>>>() {
        });
        return filterDataValue.getOrDefault(objAPIName, new ArrayList<>());
    }

    private String initInvalidFilterDataConfig(final String tenantId, final String dataCenterId, final String channel) {
        ErpTenantConfigurationEntity filter = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, TenantConfigurationTypeEnum.TIME_FILTER_INVALID.name());
        if (Objects.nonNull(filter)) {
            return StringUtils.isBlank(filter.getConfiguration()) ? "{}" : filter.getConfiguration();
        }

        Map<String, List<List<FilterData>>> filterDataValue = getFilterDataValue(tenantId, dataCenterId, channel);
        final String json = JacksonUtil.toJson(filterDataValue);
        tenantConfigurationManager.updateConfig(tenantId, dataCenterId, channel, TenantConfigurationTypeEnum.TIME_FILTER_INVALID.name(), json);
        return json;
    }

    private Map<String, List<List<FilterData>>> getFilterDataValue(final String tenantId, final String dataCenterId, final String channel) {
        ErpTenantConfigurationEntity filter = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, TenantConfigurationTypeEnum.TIME_FILTER.name());
        if (Objects.nonNull(filter)) {
            final String configuration = StringUtils.isBlank(filter.getConfiguration()) ? "{}" : filter.getConfiguration();
            return JacksonUtil.fromJson(configuration, new TypeReference<Map<String, List<List<FilterData>>>>() {
            });
        }

        ErpTenantConfigurationEntity filterConfig = tenantConfigurationManager.findOne(tenantId, dataCenterId, channel, TenantConfigurationTypeEnum.LIST_FILTER.name());
        if (filterConfig != null && StringUtils.isNotBlank(filterConfig.getConfiguration())) {
            return convertListFilter2TimeFilterAndSave(tenantId, dataCenterId, channel, filterConfig);
        }
        return new HashMap<>();
    }

    private Map<String, List<List<FilterData>>> convertListFilter2TimeFilterAndSave(final String tenantId, final String dataCenterId, final String channel, final ErpTenantConfigurationEntity filterConfig) {
        Map<String, List<FilterData>> map = JacksonUtil.fromJson(filterConfig.getConfiguration(), new TypeReference<Map<String, List<FilterData>>>() {});
        final Map<String, List<List<FilterData>>> collect = map.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    final List<FilterData> value = entry.getValue();

                    // 将以前的错误operate转换为正确的operate
                    value.forEach(filterData -> {
                        if (StringUtils.isBlank(filterData.getOperate())) {
                            return;
                        }
                        if (filterData.getOperate().equals(Operate.IS)) {
                            filterData.setOperate(Operate.EQ);
                        } else if (filterData.getOperate().equals(Operate.IS_NOT)) {
                            filterData.setOperate(Operate.N);
                        }
                    });

                    List<FilterData> orFilters = value.stream()
                            .filter(f -> (BooleanUtils.isTrue(f.getIsVariableBetween())))
                            .collect(Collectors.toList());
                    List<FilterData> andFilters = value.stream()
                            .filter(f -> BooleanUtils.isNotTrue(f.getIsVariableBetween()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(orFilters)) {
                        List<List<FilterData>> result = new ArrayList<>();
                        result.add(andFilters);
                        return result;
                    } else {
                        return orFilters.stream().map(filterData -> {
                            final List<FilterData> filterList = Lists.newArrayList(andFilters);
                            filterList.add(filterData);
                            return filterList;
                        }).collect(Collectors.toList());
                    }
                }));
        tenantConfigurationManager.updateConfig(tenantId, dataCenterId, channel, TenantConfigurationTypeEnum.TIME_FILTER.name(), JacksonUtil.toJson(collect));
        return collect;
    }

    @Override
    public void setFilterData(final String tenantId, final String dataCenterId, final String channel, final String objAPIName, final List<List<FilterData>> filterData, ErpObjInterfaceUrlEnum queryType) {
        final TenantConfigurationTypeEnum filterEnum;
        if (Objects.equals(queryType, ErpObjInterfaceUrlEnum.queryInvalid)) {
            filterEnum = TenantConfigurationTypeEnum.TIME_FILTER_INVALID;
        } else {
            // 初始化查询作废
            initInvalidFilterDataConfig(tenantId, dataCenterId, channel);
            filterEnum = TenantConfigurationTypeEnum.TIME_FILTER;
        }

        final Map<String, List<List<FilterData>>> filterDataValue = getFilterDataValue(tenantId, dataCenterId, channel);
        filterDataValue.put(objAPIName, filterData);

        tenantConfigurationManager.updateConfig(tenantId, dataCenterId, channel, filterEnum.name(), JSON.toJSONString(filterDataValue));
    }

    /**
     * 清理冗余字段数据
     *
     * @param dataListRes
     * @param arg
     */
    private void dealUselessField(Result<? extends ListErpObjDataResult> dataListRes, QueryTempTimeFilterArg arg) {
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(arg.getTenantId(), arg.getSnapshotId());
        SyncPloyDetailData syncPloyDetailData = syncPloyDetailSnapshotEntity.getSyncPloyDetailData();
        Set<String> masterField = Sets.newHashSet();
        for (FieldMappingData fieldMappingData : syncPloyDetailData.getFieldMappings()) {
            if (StringUtils.isEmpty(fieldMappingData.getSourceApiName())) {
                continue;
            }
            masterField.add(fieldMappingData.getSourceApiName());
        }
        ErpObjectFieldEntity erpObjectFieldEntity = erpFieldManager.findIdField(arg.getTenantId(), arg.getObjAPIName());
        masterField.add("object_describe_api_name");
        if (syncPloyDetailData.getSyncConditions().getFilters() != null) {
            for (List<FilterData> filterDataList : syncPloyDetailData.getSyncConditions().getFilters()) {
                if (filterDataList != null) {
                    for (FilterData filterData : filterDataList) {
                        masterField.add(filterData.getFieldApiName());
                    }
                }
            }
        }
        if (erpObjectFieldEntity != null) {
            if (StringUtils.isNotEmpty(erpObjectFieldEntity.getFieldExtendValue())) {
                CompositeIdExtend compositeIdExtend = GsonUtil.fromJson(erpObjectFieldEntity.getFieldExtendValue(), CompositeIdExtend.class);
                if (compositeIdExtend.isComposite()) {
                    for (String field : compositeIdExtend.getCompositeFields()) {
                        masterField.add(field);
                    }
                }
            }
            masterField.add(erpObjectFieldEntity.getFieldApiName());
        }
        masterField.addAll(ConfigCenter.NOT_NEED_FILTER_FIELD);
        Map<String, Set<String>> detailFieldMap = Maps.newHashMap();
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : syncPloyDetailData.getDetailObjectMappings()) {
            Set<String> detailFieldSet = Sets.newHashSet();
            for (FieldMappingData fieldMappingData : detailObjectMappingData.getFieldMappings()) {
                if (StringUtils.isEmpty(fieldMappingData.getSourceApiName())) {
                    continue;
                }
                detailFieldSet.add(fieldMappingData.getSourceApiName());
            }
            erpObjectFieldEntity = erpFieldManager.findIdField(arg.getTenantId(), detailObjectMappingData.getSourceObjectApiName());
            if (erpObjectFieldEntity != null) {
                if (StringUtils.isNotEmpty(erpObjectFieldEntity.getFieldExtendValue())) {
                    CompositeIdExtend compositeIdExtend = GsonUtil.fromJson(erpObjectFieldEntity.getFieldExtendValue(), CompositeIdExtend.class);
                    if (compositeIdExtend.isComposite()) {
                        for (String field : compositeIdExtend.getCompositeFields()) {
                            detailFieldSet.add(field);
                        }
                    }
                }
                detailFieldSet.add(erpObjectFieldEntity.getFieldApiName());
            }
            detailFieldSet.addAll(ConfigCenter.NOT_NEED_FILTER_FIELD);
            detailFieldSet.add("object_describe_api_name");
            detailFieldMap.put(detailObjectMappingData.getSourceObjectApiName(), detailFieldSet);
        }
        for (SyncConditionsData syncConditionsData : syncPloyDetailData.getDetailObjectSyncConditions()) {
            if (syncConditionsData.getFilters() != null) {
                for (List<FilterData> filterDataList : syncConditionsData.getFilters()) {
                    if (filterDataList != null) {
                        for (FilterData filterData : filterDataList) {
                            detailFieldMap.get(syncConditionsData.getApiName()).add(filterData.getFieldApiName());
                        }
                    }
                }
            }
        }
        List<SyncDataContextEvent> erpObjDataResults = Lists.newArrayList();
        for (SyncDataContextEvent oldErpObjDataResult : dataListRes.getData().getErpObjDataResultList()) {
            ObjectData oldObjectData = oldErpObjDataResult.getSourceData();
            SyncDataContextEvent newErpObjDataResult = new SyncDataContextEvent();
            ObjectData newObjectData = new ObjectData();
            for (String field : masterField) {
                newObjectData.put(field, oldObjectData.get(field));
            }
            newErpObjDataResult.setSourceData(newObjectData);
            Map<String, List<ObjectData>> newDetailDataMap = Maps.newHashMap();
            for (Map.Entry<String, Set<String>> entry : detailFieldMap.entrySet()) {
                List<ObjectData> oldDetailObjectDatas = oldErpObjDataResult.getDetailData().get(entry.getKey());
                List<ObjectData> newDetailObjectDatas = Lists.newArrayList();
                if (oldDetailObjectDatas != null) {
                    for (ObjectData oldDetailObjectData : oldDetailObjectDatas) {
                        ObjectData newDetailObjectData = new ObjectData();
                        for (String field : entry.getValue()) {
                            newDetailObjectData.put(field, oldDetailObjectData.get(field));
                        }
                        newDetailObjectDatas.add(newDetailObjectData);
                    }
                }
                newDetailDataMap.put(entry.getKey(), newDetailObjectDatas);
            }
            newErpObjDataResult.setDetailData(newDetailDataMap);
            erpObjDataResults.add(newErpObjDataResult);
        }
        dataListRes.getData().setErpObjDataResultList(erpObjDataResults);
    }

    @Override
    public Result<SyncDataContextEvent> getErpObjDataFromMongoIfExist(ErpIdArg erpIdArg) {
        return getErpObjDataById(erpIdArg, 3);
    }

    /**
     * 1:getErpObjDataById,2:getErpObjDataByIdFromErp,3:getErpObjDataFromMongoIfExist
     * how2get 参数为3的都会先从mongo获取数据，获取不到才掉接口获取
     * how2get 参数为1的会根据配置，可能从自定义函数/推送表/专用组件等等获取数据
     * how2get 参数为2的会从专用组件获取数据
     */
    private Result<SyncDataContextEvent> getErpObjDataById(ErpIdArg erpIdArg, int how2get) {
        String tenantId = erpIdArg.getTenantId();
        String splitObjApiName = erpIdArg.getObjAPIName();
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, splitObjApiName);
        String realObjApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();
        if (ErpObjSplitTypeEnum.DETAIL_TYPES.contains(relation.getSplitType())) {
            //明细直接返回空
            return new Result<>();
        }
        erpIdArg.setObjAPIName(realObjApiName);
        Result<SyncDataContextEvent> result = null;
        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId,dataCenterId, splitObjApiName, realObjApiName);
        switch (how2get) {
            case 1:
                result = erpDataService.getErpObjDataById(erpIdArg, dataCenterId, idFieldKey);
                break;
            case 3:
                result = erpDataService.getErpObjDataFromMongoIfExist(erpIdArg, dataCenterId, idFieldKey);
                break;
            default:
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        result.getData().getSourceData().putTenantId(erpIdArg.getTenantId());
        if(StringUtils.isBlank(result.getData().getSyncLogId())&&StringUtils.isNotBlank(LogIdUtil.get())){
            result.getData().setSyncLogId(LogIdUtil.get());
        }
        List<SyncDataContextEvent> erpObjDataResults = splitObjectManager.erpRealObj2SplitObjData(result.getData(), dataCenterId);

        Optional<SyncDataContextEvent> any = erpObjDataResults.stream()
                .filter(v -> v.getSourceData().getApiName().equals(splitObjApiName)).findAny();
        //增加id等必须字段
        for (SyncDataContextEvent erpObjDataResult : erpObjDataResults) {
            idFieldConvertManager.convertField2Crm(erpIdArg.getTenantId(), erpObjDataResult);
        }
        if (any.isPresent()) {
            SyncDataContextEvent resultData = any.get();
            //去掉超过limit的明细
            erpDataPreprocessManager.removeOutOfLimitDetail(realObjApiName, erpIdArg, resultData);
            resultData.setSourceEventType(EventTypeEnum.UPDATE.getType());
            //特殊字段转换
            specialFieldPreprocessManager.convertErpFieldValue2Crm(tenantId, dataCenterId, splitObjApiName, resultData);
            return new Result<>(any.get());
        } else {
            log.error("get erp obj data error，arg:{},splitResult:{}", erpIdArg, erpObjDataResults);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 获取erp单条数据
     * <p>
     * 无法支持获取明细单条数据
     *
     * @param erpIdArg
     * @return
     */
    @Override
    public Result<SyncDataContextEvent> getErpObjDataById(ErpIdArg erpIdArg) {
        return getErpObjDataById(erpIdArg, 1);
    }

    @Override
    public Result<List<SyncDataContextEvent>> getReSyncObjDataById(ErpIdArg erpIdArg) {
        String tenantId = erpIdArg.getTenantId();
        String splitObjApiName = erpIdArg.getObjAPIName();
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, splitObjApiName);
        String realObjApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();
        if (ErpObjSplitTypeEnum.DETAIL_TYPES.contains(relation.getSplitType())) {
            //明细直接返回空
            Result<List<SyncDataContextEvent>> emptyResult = new Result<>();
            emptyResult.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s1032,erpIdArg.getTenantId()));
            return emptyResult;
        }
        erpIdArg.setObjAPIName(realObjApiName);

        Result<List<SyncDataContextEvent>> result = null;
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId, dataCenterId, splitObjApiName, realObjApiName);
        result = erpDataService.getReSyncObjDataById(erpIdArg, dataCenterId, idFieldKey);
        if (!result.isSuccess()) {
            if (!StringUtils.equalsIgnoreCase(result.getErrCode(), ResultCodeEnum.GET_BY_ID_BREAK.getErrCode())) {
                getByIdBreakManager.increaseFailedCount(tenantId,
                        dataCenterId,
                        connectInfo.getChannel(),
                        realObjApiName,
                        result.getErrMsg());
            }
            return Result.copy(result);
        }
        List<SyncDataContextEvent> allData = Lists.newArrayList();
        for (SyncDataContextEvent erpObjDataResult : result.getData()) {
            if(StringUtils.isBlank(erpObjDataResult.getSyncLogId())&&StringUtils.isNotBlank(LogIdUtil.get())){
                erpObjDataResult.setSyncLogId(LogIdUtil.get());
            }
            erpObjDataResult.getSourceData().putTenantId(erpIdArg.getTenantId());
            List<SyncDataContextEvent> erpObjDataResults = splitObjectManager.erpRealObj2SplitObjData(erpObjDataResult, dataCenterId);
            //增加id等必须字段

            Optional<SyncDataContextEvent> any = erpObjDataResults.stream()
                    .filter(v -> v.getSourceData().getApiName().equals(splitObjApiName)).findAny();

            for (SyncDataContextEvent erpObjDataResult1 : erpObjDataResults) {
                idFieldConvertManager.convertField2Crm(erpIdArg.getTenantId(), erpObjDataResult1);
            }
            if (any.isPresent()) {
                SyncDataContextEvent resultData = any.get();
                resultData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                //特殊字段转换
                specialFieldPreprocessManager.convertErpFieldValue2Crm(tenantId, dataCenterId, splitObjApiName, resultData);
                allData.add(any.get());
            } else {
                log.error("get erp obj data error，arg:{},splitResult:{}", erpIdArg, erpObjDataResults);
            }
        }
        //根据中间表是否有映射关系，指定事件类型,新增或者更新
        if (EventTypeEnum.ADD.getType() == erpIdArg.getSourceEventType() || EventTypeEnum.UPDATE.getType() == erpIdArg.getSourceEventType()) {//新增或者更新
            SyncPloyDetailSnapshotEntity entity = syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(tenantId, erpIdArg.getSyncPloyDetailSnapshotId());
            for (SyncDataContextEvent erpObjDataVO : allData) {//每一条数据
                ObjectData objectData = erpObjDataVO.getSourceData();//主数据
                boolean exitMappingAndCreated = syncDataMappingsManager.exitMappingAndCreated(entity.getSourceTenantId(),
                        entity.getSourceObjectApiName(),
                        objectData.getId(), entity.getDestObjectApiName());
                if (!exitMappingAndCreated) {
                    erpObjDataVO.setSourceEventType(EventTypeEnum.ADD.getType());
                } else {
                    erpObjDataVO.setSourceEventType(EventTypeEnum.UPDATE.getType());
                }
            }
        } else {//除新增更新外的其他事件类型
            allData.forEach(data -> data.setSourceEventType(erpIdArg.getSourceEventType()));
        }
        Result<Boolean> need=erpProcessedDataService.needCompareDetail(tenantId,dataCenterId,connectInfo.getChannel(),splitObjApiName);
        if (need.isSuccess()&&need.getData()) {
            List<SyncDataContextEvent> updateDataList=allData.stream().filter(data->EventTypeEnum.UPDATE.getType()==data.getSourceEventType()).collect(Collectors.toList());
            //更新的数据
            if (CollectionUtils.isNotEmpty(updateDataList)) {
                Result<List<SyncDataContextEvent>> erpInvalidDetailList = erpProcessedDataService.getErpInvalidDetailList(tenantId,
                        dataCenterId, updateDataList);
                if (CollectionUtils.isNotEmpty(erpInvalidDetailList.getData())) {//被删除的明细数据
                    allData.addAll(erpInvalidDetailList.getData());
                }
            }
        }
        return Result.newSuccess(allData);
    }

    /**
     * 新建Erp对象数据
     * 可能为：新建不拆分主对象及明细拆分从对象及字段拆分从对象数据，新建查找关联拆分明细数据（明细接口）
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<ErpIdResult> createErpObjData(SyncDataContextEvent doWriteMqData) {
        doWriteMqData = BeanUtil.deepCopy(doWriteMqData, doWriteMqData.getClass());
        String tenantId = doWriteMqData.getDestTenantId();
        String objApiName = doWriteMqData.getDestObjectApiName();
        String dataCenterId = idFieldConvertManager.getDataCenterId(tenantId, objApiName);
        //特殊类型字段转换
        doWriteMqData = specialFieldPreprocessManager.convertCrmFieldValue2Erp(tenantId, dataCenterId, doWriteMqData);
        //更新sync_data表
        adminSyncDataDao.setTenantId(tenantId).updateDestDataById(tenantId, doWriteMqData.getSyncDataId(), doWriteMqData.getDestData());
        String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, objApiName);
        List<ErpObjExtendDto> erpObjExtendDtls = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryObjExtendDTO(tenantId, dataCenterId, realApiName);
        //转换id字段和查找关联
        idFieldConvertManager.convertField2Erp(doWriteMqData, dataCenterId);
        //拆分对象转换，转换为真实apiName
        SplitObjResult.Erp2Crm result = splitObjectManager.createErpSplitObj2RealObj(doWriteMqData, erpObjExtendDtls, dataCenterId);
        Result<ErpIdResult> idResult;
        switch (result.getMasterObjSplitType()) {
            case DETAIL2LOOKUP_SPLIT:
                //查找关联拆分
                //主对象id字段赋值
                ErpObjExtendDto mainObjDto = ErpObjExtendDto.findDtoBySplitType(erpObjExtendDtls, ErpObjSplitTypeEnum.NOT_SPLIT);
                result.getDoWriteMqData().getDestData().put(mainObjDto.getIdFieldName(), result.getDoWriteMqData().getDestData().getId());
                //查找关联拆分使用明细单独接口
                Result<StandardDetailId> erpObjDetailData = erpDataService.createErpObjDetailData(result.getDoWriteMqData(), dataCenterId);
                if (!erpObjDetailData.isSuccess()) {
                    return Result.copy(erpObjDetailData);
                }
                ErpObjExtendDto destObjExtend = ErpObjExtendDto.findDtoByApiName(erpObjExtendDtls, objApiName);
                ErpIdResult idResultData = new ErpIdResult();
                //查找关联拆分的对象，id在明细里面
                idResultData.setMasterDataId(erpObjDetailData.getData().getDetailDataId().get(destObjExtend.parseDetailApiName()));
                idResult = new Result<>(idResultData);
                break;
            case FIELD_SPLIT:
                idResult = erpDataService.updateErpObjData(result.getDoWriteMqData(), dataCenterId);
                break;
            default:
                idResult = erpDataService.createErpObjData(result.getDoWriteMqData(), dataCenterId);
                //转换明细的apiName
                idFieldConvertManager.convertDetailApiName(idResult.getData(), erpObjExtendDtls);
        }
        return idResult;
    }

    /**
     * 处理ERP明细事件，实际为触发主对象上次的数据同步
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<Void> processErpDetailEvent(SyncDataContextEvent doWriteMqData) {
        String syncDataId = doWriteMqData.getSyncDataId();
        String tenantId = doWriteMqData.getDestTenantId();
        String destObjApiName = doWriteMqData.getDestObjectApiName();
        //查ERP的主对象信息
        List<ErpObjectFieldEntity> referenceFields = erpFieldManager.findByObjApiNameAndType(tenantId, destObjApiName
                , Collections.singleton(ErpFieldTypeEnum.master_detail));
        if (referenceFields.isEmpty()) {
            log.error("not found master detail field,tenantId:{},destObjApiName:{}", tenantId, destObjApiName);
            return Result.newSuccess();
        }
        ErpObjectFieldEntity destMasterDetailField = referenceFields.get(0);
        String destMainObjApiName = destMasterDetailField.getFieldExtendValue();
        SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, syncDataId);
        ObjectData sourceData = syncDataEntity.getSourceData();
        String crmObjApiName = syncDataEntity.getSourceObjectApiName();
        FieldDescribe masterDetailField = crmObjectManager.getMasterDetailField(tenantId, crmObjApiName);
        String mainApiName = masterDetailField.getTargetApiName();
        String mainDetailFieldApiName = masterDetailField.getApiName();
        String mainObjId = sourceData.getString(mainDetailFieldApiName);
        //查找主数据最新的同步记录
        List<SyncDataEntity> mainSyncDatas = this.getMainLastSyncData(tenantId, mainApiName, mainObjId, destMainObjApiName);
        if (!mainSyncDatas.isEmpty()) {
            SyncDataEntity lastMainSyncData = mainSyncDatas.get(0);
            if (!SyncDataStatusEnum.isDoingOrWaitting(lastMainSyncData.getStatus())) {
                //重新同步上次主数据同步记录
                SyncDataContextEvent eventData = new SyncDataContextEvent();
                eventData.setSourceData(lastMainSyncData.getSourceData());
                //一定是修改类型
                eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                eventData.setSourceTenantType(TenantType.CRM);
                eventData.setDataReceiveType(DataReceiveTypeEnum.PAAS_META_EVENT.getType());
                //不能设置快照Id，不然可能会修改了策略不生效。
                Result2<Void> voidResult = eventTriggerService.batchSendEventData2DispatcherMqByContext(Lists.newArrayList(eventData));
                log.info("send mq,result:{}", voidResult);
            }
        }
        //恢复mapping
        String sourceDataId = syncDataEntity.getSourceDataId();
        String destObjectApiName = syncDataEntity.getDestObjectApiName();
        SyncDataMappingsEntity mapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, crmObjApiName, destObjectApiName, sourceDataId);
        if (mapping != null) {
                syncDataMappingsDao.setTenantId(tenantId).updateById(tenantId, mapping.getId(), mapping.getLastSyncDataId(), SyncDataStatusEnum.WRITE_FAILED.getStatus(),
                        mapping.getLastSourceDataVserion(), System.currentTimeMillis());
        }
        return Result.newSuccess();
    }

    private List<SyncDataEntity> getMainLastSyncData(String tenantId, String sourceMainObjApiName, String mainObjId, String destMainObjApiName) {
        SyncDataMappingsEntity bySourceData = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, sourceMainObjApiName, destMainObjApiName, mainObjId);
        if (bySourceData != null && StringUtils.isNotBlank(bySourceData.getLastSyncDataId())) {
            SyncDataEntity byId = adminSyncDataDao.getById(tenantId, bySourceData.getLastSyncDataId());
            if (byId != null) {
                return Lists.newArrayList(byId);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 更新Erp对象数据
     * 可能为：更新不拆分主对象数据（不带明细），更新查找关联拆分明细数据（明细接口），
     * 暂不支持更新明细拆分从对象数据，字段拆分从对象数据。
     *
     * @param doWriteMqData
     * @return
     */
    @Override
    public Result<ErpIdResult> updateErpObjData(SyncDataContextEvent doWriteMqData) {
        doWriteMqData = BeanUtil.deepCopy(doWriteMqData, doWriteMqData.getClass());
        String tenantId = doWriteMqData.getDestTenantId();
        String objApiName = doWriteMqData.getDestObjectApiName();
        //判断erp数据是否已创建，没创建等待一分钟，重新判断，仅一次
//        judgeErpObjDataIsCreated(doWriteMqData);
        String dataCenterId = idFieldConvertManager.getDataCenterId(tenantId, objApiName);
        //特殊类型字段转换
        doWriteMqData = specialFieldPreprocessManager.convertCrmFieldValue2Erp(tenantId, dataCenterId, doWriteMqData);
        //更新sync_data表
        adminSyncDataDao.setTenantId(tenantId).updateDestDataById(tenantId, doWriteMqData.getSyncDataId(), doWriteMqData.getDestData());
        String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, objApiName);
        List<ErpObjExtendDto> erpObjExtendDtls = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryObjExtendDTO(tenantId, dataCenterId, realApiName);
        //转换id字段和查找关联
        idFieldConvertManager.convertField2Erp(doWriteMqData, dataCenterId);
        //拆分对象转换，转换为真实apiName
        SplitObjResult.Erp2Crm result = splitObjectManager.updateErpSplitObj2RealObj(doWriteMqData, erpObjExtendDtls, dataCenterId);
        Result<ErpIdResult> idResult;
        if (result.getMasterObjSplitType().equals(ErpObjSplitTypeEnum.DETAIL2LOOKUP_SPLIT)) {
            //主对象id字段赋值
            ErpObjExtendDto mainObjDto = ErpObjExtendDto.findDtoBySplitType(erpObjExtendDtls, ErpObjSplitTypeEnum.NOT_SPLIT);
            result.getDoWriteMqData().getDestData().put(mainObjDto.getIdFieldName(),
                    result.getDoWriteMqData().getDestData().getId());
            //查找关联拆分使用明细单独接口
            Result<StandardDetailId> erpObjDetailData = erpDataService.updateErpObjDetailData(result.getDoWriteMqData(), dataCenterId);
            ErpObjExtendDto destObjExtend = ErpObjExtendDto.findDtoByApiName(erpObjExtendDtls, objApiName);
            ErpIdResult idResultData = new ErpIdResult();
            //查找关联拆分的对象，id在明细里面
            idResultData.setMasterDataId(erpObjDetailData.getData().getDetailDataId().get(destObjExtend.parseDetailApiName()));
            idResult = new Result<>(idResultData);
        } else {
            idResult = erpDataService.updateErpObjData(result.getDoWriteMqData(), dataCenterId);
            //转换明细的apiName
            idFieldConvertManager.convertDetailApiName(idResult.getData(), erpObjExtendDtls);
        }
        return idResult;
    }

//    /**
//     * 需要调用get接口
//     *
//     * @param tenantId
//     * @param objApiName
//     * @param idResult
//     */
//    private void convertIdResult2Crm(String tenantId, String objApiName, ErpIdResult idResult) {
//        log.info("ErpDataPreprocessServiceImpl.convertIdResult2Crm,objApiName={},idResult={}", objApiName, idResult);
//        if (idResult == null) {
//            return;
//        }
//        ErpObjectFieldEntity idField = idFieldConvertManager.getIdField(tenantId, objApiName);
//        CompositeIdExtend idExtent = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());
//        //对复合id重新获取id
//        if (idExtent.isComposite()) {
//            ErpIdArg erpIdArg = new ErpIdArg();
//            erpIdArg.setTenantId(tenantId);
//            erpIdArg.setObjAPIName(objApiName);
//            erpIdArg.setDataId(idResult.getMasterDataId());
//            Result<SyncDataContextEvent> erpObjDataById = getErpObjDataById(erpIdArg);
//            if (!erpObjDataById.isSuccess()) {
//                AfterSystemProcessModel afterSystemProcessModel= AfterSystemProcessModel.builder().afterProcessType(AfterSystemProcessEnum.COMPLEXID_READ_FAIL.name()).processReason("复合ID查询数据失败，请检查getById接口").build();
//                syncLogManager.saveLog(tenantId, SyncLogTypeEnum.AFTER_SYSTEM_PROCESS, SyncLogStatusEnum.SYNC_FAIL.getStatus(),afterSystemProcessModel);
//                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s259.getI18nKey(),
//                        tenantId,
//                        String.format(I18NStringEnum.s259.getI18nValue(), erpObjDataById.getErrMsg()),
//                        Lists.newArrayList(erpObjDataById.getErrMsg())),
//                        null,
//                        null);
//            }
//            SyncDataContextEvent data = erpObjDataById.getData();
//            idResult.setMasterDataId(data.getSourceData().getId());
//            for (String detailApiName : idResult.getDetailDataIds().keySet()) {
//                ErpObjectFieldEntity didField = idFieldConvertManager.getIdField(tenantId, detailApiName);
//                CompositeIdExtend didExtent = CompositeIdExtend.getByIdField(didField.getFieldExtendValue());
//                if (didExtent.isComposite()) {
//                    List<String> detailIds = data.getDetailData().get(detailApiName).stream()
//                            .map(ObjectData::getId).collect(Collectors.toList());
//                    idResult.getDetailDataIds().put(detailApiName, detailIds);
//                }
//            }
//        }
//    }

//    private void judgeErpObjDataIsCreated(SyncDataContextEvent doWriteMqData) {
//        String tenantId = doWriteMqData.getTenantId();
//        SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, doWriteMqData.getSyncDataId());
//        if (syncDataEntity != null) {
//            SyncDataMappingsEntity objDataMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getDestObjectApiName(), syncDataEntity.getSourceDataId());
//            if (objDataMapping != null && objDataMapping.getIsCreated()) {
//                doWriteMqData.setDestDataId(objDataMapping.getDestDataId());
//                doWriteMqData.getDestData().putId(objDataMapping.getDestDataId());
//                if (syncDataEntity.getDestData() != null) {
//                    syncDataEntity.getDestData().putId(objDataMapping.getDestDataId());
//                }
//                syncDataEntity.setDestDataId(objDataMapping.getDestDataId());
////                adminSyncDataDao.setTenantId(tenantId).updateDestDataAndDestDataId(tenantId, syncDataEntity.getId(), syncDataEntity.getDestDataId(),
////                        syncDataEntity.getDestData(), System.currentTimeMillis());
//                log.info("ErpObjDataIsCreated id={}", syncDataEntity.getDestDataId());
//            } else {
//                try {
//                    sleep(1000 * 60);//阻塞一分钟,重新查询mapping表
//                    objDataMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getDestObjectApiName(), syncDataEntity.getSourceDataId());
//                    if (objDataMapping != null && objDataMapping.getIsCreated()) {
//                        doWriteMqData.setDestDataId(objDataMapping.getDestDataId());
//                        doWriteMqData.getDestData().putId(objDataMapping.getDestDataId());
//                        syncDataEntity.setDestDataId(objDataMapping.getDestDataId());
//                        if (syncDataEntity.getDestData() != null) {
//                            syncDataEntity.getDestData().putId(objDataMapping.getDestDataId());
//                        }
////                        adminSyncDataDao.setTenantId(tenantId).updateDestDataAndDestDataId(tenantId, syncDataEntity.getId(), syncDataEntity.getDestDataId(),
////                                syncDataEntity.getDestData(), System.currentTimeMillis());
//                        log.info("ErpObjDataIsCreated id={}", syncDataEntity.getDestDataId());
//                    }
//                } catch (Exception e) {
//                    log.info("judgeErpObjDataIsCreated failed", e);
//                }
//            }
//        }
//    }

    /**
     * DoWriteMqData 在作废时的示例数据：
     * {"destDataId":"6088007843999700011c17e2","destEventType":3,"destObjectApiName":"erpProduct","destTenantId":"82335",
     * "sourceTenantId":"82335","syncDataId":"b5a668aa672640e5b79338251dfecaeb"}
     */
    @Override
    public Result<String> invalidErpObjData(SyncDataContextEvent doWriteMqData) {
        doWriteMqData = BeanUtil.deepCopy(doWriteMqData, doWriteMqData.getClass());
        String tenantId = doWriteMqData.getDestTenantId();
        String destApiName = doWriteMqData.getDestObjectApiName();
        //更新sync_data表
        adminSyncDataDao.setTenantId(tenantId).updateDestDataById(tenantId, doWriteMqData.getSyncDataId(), doWriteMqData.getDestData());
        //转换id字段和查找关联
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, destApiName);
        String realMasterApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();
        idFieldConvertManager.convertId2Erp(doWriteMqData);
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryObjExtendDTO(tenantId, dataCenterId, realMasterApiName);
        //key:splitAPiName value: objExtend
        Map<String, ErpObjExtendDto> map = erpObjExtendDtos.stream()
                .collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, v -> v, (u, v) -> u));
        StandardInvalidData standardInvalidData = new StandardInvalidData();
        standardInvalidData.setObjAPIName(realMasterApiName);
        //增加作废人作废时间
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, doWriteMqData.getSyncDataId());
        SyncPloyDetailSnapshotEntity snapshot =
                syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(tenantId, syncDataEntity.getSyncPloyDetailSnapshotId());
        convertLastModify(tenantId, destApiName, standardInvalidData, connectInfoEntity, syncDataEntity, snapshot);
        Result<String> result;
        if (relation.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)) {
            //主对象作废,目标数据id就是主对象id
            standardInvalidData.setMasterId(map.get(destApiName).getIdFieldName(), doWriteMqData.getDestDataId());
            result = erpDataService.invalidErpObjData(standardInvalidData, tenantId, dataCenterId, snapshot.getId());
        } else if (relation.getSplitType().equals(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT)) {
            //从对象作废
            Pair<String, String> pair = findDestErpMasterApiNameAndIdByDetailSyncDataId(syncDataEntity, snapshot);
            if (pair == null) {
                return Result.newSuccess(i18NStringManager.getByEi(I18NStringEnum.s1033,tenantId));
            }
            standardInvalidData.setMasterId(map.get(pair.getLeft()).getIdFieldName(), pair.getRight());
            standardInvalidData.setDetailId(map.get(destApiName).parseDetailApiName(), doWriteMqData.getDestDataId());
            result = erpDataService.invalidErpObjDetailData(standardInvalidData, tenantId, dataCenterId, snapshot.getId());
        } else {
            result = Result.newSuccess();
        }
        return result;
    }

    private void convertLastModify(String tenantId, String destApiName, StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfoEntity,
                                   SyncDataEntity syncDataEntity, SyncPloyDetailSnapshotEntity snapshot) {
        ObjectData sourceData = syncDataEntity.getSourceData();
        ObjectData masterFieldVal = standardInvalidData.getMasterFieldVal();
        masterFieldVal.putApiName(destApiName);
        masterFieldVal.putTenantId(tenantId);
        //最后修改人，即作废人
        Object lastModifiedBy = sourceData.get("last_modified_by");
        if (lastModifiedBy != null) {
            specialFieldPreprocessManager.covertFieldValueEmpOrDepart(connectInfoEntity, lastModifiedBy, ErpFieldTypeEnum.employee,
                    "last_modified_by", masterFieldVal, true, "");
            Optional<FieldMappingData> lastModOp =
                    snapshot.getSyncPloyDetailData().getFieldMappings().stream().filter(v -> "last_modified_by".equals(v.getSourceApiName())).findAny();
            //存在最后修改人映射
            lastModOp.ifPresent(fieldMappingData -> masterFieldVal.put(fieldMappingData.getDestApiName(), masterFieldVal.get("last_modified_by")));
            Object lastModifiedTime = sourceData.get("last_modified_time");
            masterFieldVal.put("last_modified_time", lastModifiedTime);
            //存在最后修改时间映射
            Optional<FieldMappingData> lastModTiOp =
                    snapshot.getSyncPloyDetailData().getFieldMappings().stream().filter(v -> "last_modified_time".equals(v.getSourceApiName())).findAny();
            //存在最后修改时间映射
            lastModTiOp.ifPresent(fieldMappingData -> {
                masterFieldVal.put(fieldMappingData.getDestApiName(), lastModifiedTime);
                ErpObjectFieldEntity fieldEntity=erpFieldManager.findByFieldApiName(connectInfoEntity.getId(),connectInfoEntity.getTenantId(),destApiName,fieldMappingData.getDestApiName());
                specialFieldPreprocessManager.covertFieldValueDateFormat(connectInfoEntity, lastModifiedTime,
                        ErpFieldTypeEnum.valueOf(fieldMappingData.getDestType()),
                        fieldMappingData.getDestApiName(), masterFieldVal, true,fieldEntity);
            });
        }
    }

    /**
     * 通过明细syncDataId找目标ERP主对象数据Id
     *
     * @param syncDataEntity
     * @param snapshot
     * @return 中间对象apiName，目标数据Id
     */
    private Pair<String, String> findDestErpMasterApiNameAndIdByDetailSyncDataId(SyncDataEntity syncDataEntity,
                                                                                 SyncPloyDetailSnapshotEntity snapshot) {
        String detailObjApiName = syncDataEntity.getSourceObjectApiName();
        String tenantId = syncDataEntity.getSourceTenantId();
        FieldDescribe masterDetailField = crmObjectManager.getMasterDetailField(tenantId, detailObjApiName);
        String masterCrmDataId = syncDataEntity.getSourceData().getString(masterDetailField.getApiName());
        //先找出所有mapping
        SyncDataMappingsEntity masterDataMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId,
                snapshot.getSourceObjectApiName(), snapshot.getDestObjectApiName(), masterCrmDataId);
        if (masterDataMapping == null) {
            //没找到主数据映射，可能是删除了，返回null，后面返回成功。
            log.warn("not found master mapping,masterCrmDataId:{},masterDetailField:{}", masterCrmDataId, masterDetailField);
            return null;
        }
        return Pair.of(snapshot.getDestObjectApiName(), masterDataMapping.getDestDataId());
    }

    //删除也需要调用下作废接口
    @Override
    public Result<String> delete2InvalidErpObjData(SyncDataContextEvent doWriteMqData) {
        doWriteMqData = BeanUtil.deepCopy(doWriteMqData, doWriteMqData.getClass());
        String tenantId = doWriteMqData.getDestTenantId();
        String destApiName = doWriteMqData.getDestObjectApiName();
        //转换id字段和查找关联
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, destApiName);
        String realMasterApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();
        String destDataId = doWriteMqData.getDestDataId();//记录数据表的destDataId.因为下面的convertId2Erp会转成真实的id
        idFieldConvertManager.convertId2Erp(doWriteMqData);
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryObjExtendDTO(tenantId, dataCenterId, realMasterApiName);
        //key:splitAPiName value: objExtend
        Map<String, ErpObjExtendDto> map = erpObjExtendDtos.stream()
                .collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, v -> v, (u, v) -> u));
        StandardInvalidData standardInvalidData = new StandardInvalidData();
        standardInvalidData.setObjAPIName(realMasterApiName);
        //增加作废人作废时间
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getById(tenantId, doWriteMqData.getSyncDataId());
        SyncPloyDetailSnapshotEntity snapshot =
                syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(tenantId, syncDataEntity.getSyncPloyDetailSnapshotId());
        convertLastModify(tenantId, destApiName, standardInvalidData, connectInfoEntity, syncDataEntity, snapshot);
        Result<String> result = Result.newSuccess();
        boolean isMasterInvalid = true;
        if (relation.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)) {
            //主对象作废,目标数据id就是主对象id
            standardInvalidData.setMasterId(map.get(destApiName).getIdFieldName(), doWriteMqData.getDestDataId());
            result = erpDataService.invalidErpObjData(standardInvalidData, tenantId, dataCenterId, snapshot.getId());
        } else if (relation.getSplitType().equals(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT)) {
            isMasterInvalid = false;
            if (syncDataEntity.getSourceTenantType().equals(TenantType.CRM)) {
                //从对象作废
                Pair<String, String> pair = findDestErpMasterApiNameAndIdByDetailSyncDataId(syncDataEntity, snapshot);
                if (ObjectUtils.isNotEmpty(pair)) {
                    standardInvalidData.setMasterId(map.get(pair.getLeft()).getIdFieldName(), pair.getRight());
                }
            } else {
                standardInvalidData.setMasterId(map.get(snapshot.getSourceObjectApiName()).getIdFieldName(),
                        doWriteMqData.getMasterMappingsData().getDestDataId());
            }
            standardInvalidData.setDetailId(map.get(destApiName).parseDetailApiName(), doWriteMqData.getDestDataId());
            result = erpDataService.invalidErpObjDetailData(standardInvalidData, tenantId, dataCenterId, snapshot.getId());
        }
        //不管erp是否执行成功失败。都执行删除sync_mappings的操作
        String sourceObjectApiName = snapshot.getSourceObjectApiName();
        String destObjectApiName = snapshot.getDestObjectApiName();
        if (!isMasterInvalid) {
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : snapshot.getSyncPloyDetailData().getDetailObjectMappings()) {
                if (detailObjectMapping.getSourceObjectApiName().equals(destApiName) || detailObjectMapping.getDestObjectApiName().equals(destApiName)) {
                    sourceObjectApiName = detailObjectMapping.getSourceObjectApiName();
                    destObjectApiName = detailObjectMapping.getDestObjectApiName();
                }
            }
        }
        deleteCrm2erpDataDao(tenantId, destDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
        deleteErp2CrmDataDao(tenantId, destDataId, dataCenterId, sourceObjectApiName, destObjectApiName);
        return result;
    }

    @Override
    public Result<String> deleteErpObjData(SyncDataContextEvent doWriteMqData) {
        doWriteMqData = BeanUtil.deepCopy(doWriteMqData, doWriteMqData.getClass());
        String tenantId = doWriteMqData.getDestTenantId();
        String destApiName = doWriteMqData.getDestObjectApiName();
        //转换id字段和查找关联
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, destApiName);
        String realMasterApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();
        idFieldConvertManager.convertId2Erp(doWriteMqData);
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryObjExtendDTO(tenantId, dataCenterId, realMasterApiName);
        //key:splitAPiName value: objExtend
        Map<String, ErpObjExtendDto> map = erpObjExtendDtos.stream()
                .collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, v -> v, (u, v) -> u));
        StandardInvalidData standardInvalidData = new StandardInvalidData();
        standardInvalidData.setObjAPIName(realMasterApiName);
        Result<String> result;
        if (relation.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)) {
            //主对象删除,目标数据id就是主对象id
            standardInvalidData.setMasterId(map.get(destApiName).getIdFieldName(), doWriteMqData.getDestDataId());
            result = erpDataService.deleteErpObjData(standardInvalidData,tenantId,dataCenterId,doWriteMqData.getSyncPloyDetailSnapshotId());
        } else {//不支持单独删除明细
            result = Result.newSuccess();
        }
        return result;
    }
    @Override
    public Result<String> recoverErpObjData(SyncDataContextEvent doWriteData) {
        String tenantId = doWriteData.getDestTenantId();
        String destApiName = doWriteData.getDestObjectApiName();
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, destApiName);
        String realMasterApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryObjExtendDTO(tenantId, dataCenterId, realMasterApiName);
        //key:splitAPiName value: objExtend
        Map<String, ErpObjExtendDto> map = erpObjExtendDtos.stream()
                .collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, v -> v, (u, v) -> u));
        StandardRecoverData standardRecoverData = new StandardRecoverData();
        standardRecoverData.setObjAPIName(realMasterApiName);
        Result<String> result;
        if (relation.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)) {
            //主对象恢复,目标数据id就是主对象id
            standardRecoverData.setMasterId(map.get(destApiName).getIdFieldName(), doWriteData.getDestDataId());
            result = erpDataService.recoverErpObjData(standardRecoverData, tenantId, dataCenterId, doWriteData.getSyncPloyDetailSnapshotId());
        } else {//直接返回成功
            result = Result.newSuccess();
        }
        return result;
    }

    @Override
    public Result<Void> deleteCrm2erpDataDao(String tenantId, String erpDataId, String dataCenterId, String sourceApiName, String destApiName) {
        //删除sync_data数据
        int mappingsCount = syncDataMappingManager.deleteMappingsByDestId(tenantId, erpDataId, sourceApiName, destApiName);
        int mappingsCountConverse = syncDataMappingManager.deleteMappingsByDestId(tenantId, erpDataId, destApiName, sourceApiName);
        log.info("deleteCrm2erpData dowrite erp data:{},mappingsCount result:{},mappingsCountConverse:{}", erpDataId, mappingsCount, mappingsCountConverse);
        return Result.newSuccess();
    }


    @Override
    public Result<Void> deleteErp2CrmDataDao(String tenantId, String erpDataId, String dataCenterId, String sourceApiName, String destApiName) {
        //删除sync_data数据
        int mappingsCount = syncDataMappingManager.deleteMappingsBySourceId(tenantId, erpDataId, sourceApiName, destApiName);
        int mappingsCountConverse = syncDataMappingManager.deleteMappingsBySourceId(tenantId, erpDataId, destApiName, sourceApiName);
        log.info("deleteErp2CrmData dowrite erp data:{},mappingsCount result:{},mappingsCountConverse:{}", erpDataId, mappingsCount, mappingsCountConverse);
        return Result.newSuccess();
    }


}
