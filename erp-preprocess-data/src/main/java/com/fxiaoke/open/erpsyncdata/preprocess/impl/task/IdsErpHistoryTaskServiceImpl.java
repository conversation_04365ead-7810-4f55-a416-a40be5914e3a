package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.Send2DispatcherSpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18 17:57:45
 */
@Component
@Slf4j
public class IdsErpHistoryTaskServiceImpl extends AbstractErpHistoryTaskServiceImpl {
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    protected RedisDataSource redisDataSource;
    @Autowired
    private ProbeErpDataManager probeErpDataManager;
    @Autowired
    private PollingDataSpeedRateLimitManager pollingDataSpeedRateLimitManager;
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private Send2DispatcherSpeedLimitManager send2DispatcherSpeedLimitManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * id任务分页最小值
     */
    private int minPageSizeIdTask = 100;

    @Override
    public ErpHistoryDataTaskTypeEnum taskType() {
        return ErpHistoryDataTaskTypeEnum.TYPE_IDS;
    }

    @Override
    public boolean checkParamsError(ErpHistoryDataTaskResult task) {
        return CollectionUtils.isEmpty(task.getDataIds());
    }

    @Override
    public boolean checkTaskValid(ErpHistoryDataTaskEntity task) {
        return StrUtil.isBlank(task.getDataIds());
    }

    @Override
    public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        entity.setDataIds(JSONObject.toJSONString(task.getDataIds()));
        return createHistoryTask(tenantId,Lists.newArrayList(entity));
    }

    @Override
    public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        entity.setDataIds(JSONObject.toJSONString(task.getDataIds()));
        return editHistorySnapshotTask(tenantId,entity);
    }

    @Override
    public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
        return syncErpHistoryDataByIds(tenantId, task);
    }

    public Result<Void> syncErpHistoryDataByIds(String tenantId, ErpHistoryDataTaskEntity task) {
        //初始化logid
        String preLogId = syncLogManager.initLogId(tenantId, task.getRealObjApiName());
        StringBuilder remark = new StringBuilder();
        if (StrUtil.isEmpty(task.getDataIds())) {
            return Result.newSuccess();
        }
        List<String> allDataIds = JSONObject.parseArray(task.getDataIds(), String.class);
        //支持暂停，取offset作为起始位置
        long offset = ObjectUtil.defaultIfNull(task.getOffset(), 0L);
        List<String> dataIds = CollUtil.sub(allDataIds, (int) offset, allDataIds.size());
        if (dataIds.isEmpty()) {
            return Result.newSuccess();
        }
        Long totalDataSize = ObjectUtil.defaultIfNull(task.getTotalDataSize(), 0L);
        Long numPerInterval = send2DispatcherSpeedLimitManager.getNumPerInterval(tenantId);
        //至少一百条再检查一次
        int pageSize = Integer.max(numPerInterval.intValue(), minPageSizeIdTask);
        List<List<String>> dataIdPages = ListUtil.split(dataIds, pageSize);
        //获取所有出来快照
        List<SyncPloyDetailSnapshotEntity> snapshotEntities = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNewestBySourceTenantIdAndSrouceObjectApiName(tenantId,
                task.getObjApiName(), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        String destObjsStr = snapshotEntities.stream().map(v -> v.getDestObjectApiName()).collect(Collectors.joining(","));
        remark.append(i18NStringManager.getByEi(I18NStringEnum.s1283,tenantId)).append(destObjsStr).append(",").append(System.lineSeparator());
        //每个快照发送一次数据
        int syncLogIdSuffix = 0;
        for (List<String> dataIdPage : dataIdPages) {
            //每一页检查一下分发框架堆积
            if (send2DispatcherSpeedLimitManager.needSendDispatcherNum(tenantId) < 0) {
                //待分发数据过多
                return Result.newError(ResultCodeEnum.MANY_WAITING_DISPATCHER);
            }
            //历史任务限速，sleep线程
            pollingDataSpeedRateLimitManager.acquireQueryErpHistoryData(tenantId, (long) dataIdPage.size());
            //每页都查看数据库看看是否需要停止
            ErpHistoryDataTaskEntity byId = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(task.getId());
            if (byId != null && byId.getNeedStop()) {//中断
                return Result.newError(ResultCodeEnum.STOP_HISTORY_TASK);
            }
            Integer detailObjCount=0;
            for (String dataId : dataIdPage) {
                if(detailObjCount!=0&&detailObjCount>1000){//所有集成流，主+从数量每次累加超过1000时，也检查一下
                    if (send2DispatcherSpeedLimitManager.needSendDispatcherNum(tenantId) < 0) {
                        //待分发数据过多
                        return Result.newError(ResultCodeEnum.MANY_WAITING_DISPATCHER);
                    }
                    detailObjCount=0;
                }
                try {
                    LogIdUtil.buildChildLogIdRestId(LogIdUtil.get(), syncLogIdSuffix++);
                    for (SyncPloyDetailSnapshotEntity snapshotEntity : snapshotEntities) {
                        String destObjApiName = snapshotEntity.getDestObjectApiName();
                        //每个快照发送一次数据
                        ErpIdArg erpIdArg = new ErpIdArg();
                        erpIdArg.setTenantId(tenantId);
                        erpIdArg.setObjAPIName(task.getObjApiName());
                        erpIdArg.setIncludeDetail(true);
                        erpIdArg.setDataId(dataId);
                        erpIdArg.setSyncPloyDetailSnapshotId(snapshotEntity.getId());
                        erpIdArg.setSourceEventType(EventTypeEnum.ADD.getType());
                        Result<List<SyncDataContextEvent>> erpObjDataResult = erpDataPreprocessService.getReSyncObjDataById(erpIdArg);
                        if (erpObjDataResult.isSuccess() && erpObjDataResult.getData() != null) {
                            boolean needSendDetailEvent = !configCenterConfig.getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET().contains(destObjApiName);
                            probeErpDataManager.asyncSendErpDataMq(erpObjDataResult.getData(), needSendDetailEvent);
                            detailObjCount = detailObjCount + getErpObjDataDetailSize(erpObjDataResult.getData(), needSendDetailEvent);
                        } else {
                            if(remark.length()< CommonConstant.REMARK_SIZE_LIMIT){
                                remark.append(i18NStringManager.getByEi2(I18NStringEnum.s1284.getI18nKey(),
                                                tenantId,
                                                String.format(I18NStringEnum.s1284.getI18nValue(), dataId,destObjApiName),
                                                Lists.newArrayList(dataId,destObjApiName)))
                                        .append(erpObjDataResult.getErrMsg()).append(System.lineSeparator());
                            }
                            if (StrUtil.equals(erpObjDataResult.getErrCode(), ResultCodeEnum.GET_BY_ID_BREAK.getErrCode())) {
                                log.info("getById 已熔断");
                                break;
                            }
                        }
                        LogIdUtil.reset(preLogId);
                    }
                } catch (Exception e) {
                    log.info("get id error", e);
                    if(remark.length()< CommonConstant.REMARK_SIZE_LIMIT){
                        remark.append(i18NStringManager.getByEi2(I18NStringEnum.s1285.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s1285.getI18nValue(), dataId),
                                Lists.newArrayList(dataId))).append(e.getMessage()).append(System.lineSeparator());
                    }
                } finally {
                    task.setTotalDataSize(++totalDataSize);
                    task.setOffset(++offset);
                }
            }
        }
        task.setRemark(remark.toString());
        return Result.newSuccess();
    }

    private Integer getErpObjDataDetailSize(List<SyncDataContextEvent> data, boolean needSendDetailEvent) {
        Integer count=0;
        for (SyncDataContextEvent syncDataContextEvent : data) {
            count++;
            if(needSendDetailEvent){
                if(!CollectionUtils.isEmpty(syncDataContextEvent.getDetailData())){
                    for(String detailObj:syncDataContextEvent.getDetailData().keySet()){
                        if(syncDataContextEvent.getDetailData().get(detailObj)!=null){
                            count=count+syncDataContextEvent.getDetailData().get(detailObj).size();
                        }
                    }
                }
            }
        }
        return count;
    }
}
