package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18 17:57:45
 */
@Component
@Slf4j
public class CrmFilterErpIdsHistoryTaskServiceImpl extends FilterCrmHistoryTaskServiceImpl {
    @Autowired
    private PollingDataSpeedRateLimitManager pollingDataSpeedRateLimitManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;

    @Override
    public ErpHistoryDataTaskTypeEnum taskType() {
        return ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER;
    }

    @Override
    public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        setCrmsFilter(entity, task.getCrmFilters());
        return createHistoryTask(tenantId, Lists.newArrayList(entity));
    }

    @Override
    public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        setCrmsFilter(entity, task.getCrmFilters());
        return editHistorySnapshotTask(tenantId, entity);
    }

    @Override
    public void afterConvert2Vo(ErpHistoryDataTaskResult copy, ErpHistoryDataTaskEntity entity) {
        super.afterConvert2Vo(copy, entity);
        copy.setDataIds(null);
    }

    @Override
    public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
        //初始化logid
        syncLogManager.initLogId(tenantId, task.getRealObjApiName());

        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId,task.getDataCenterId(), task.getObjApiName(), task.getRealObjApiName());
        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, task.getObjApiName());
        CompositeIdExtend compositeIdExtend = CompositeIdExtend.getByIdField(idField.getFieldExtendValue());

        final String realObjApiName = task.getRealObjApiName();
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setObjAPIName(realObjApiName);
        erpIdArg.setIncludeDetail(true);
        erpIdArg.setSourceEventType(EventTypeEnum.ADD.getType());

        //每个快照发送一次数据
        AtomicLong totalDataSize = new AtomicLong(0L);
        task.setTotalDataSize(0L);
        List<String> dataIds = new ArrayList<>();
        return doTaskByCrmFilter(tenantId, task, (objectApiName, dataList) -> {
            // 获取需要同步的dataIds
            final List<String> erpIds = getNeedSyncErpIds(tenantId, task, objectApiName, dataList, compositeIdExtend);
            if (CollectionUtils.isEmpty(erpIds)) {
                return Result.newSuccess();
            }

            // 写入临时库
            for (String dataId : erpIds) {
                erpIdArg.setDataId(dataId);
                Result<SyncDataContextEvent> erpObjData = erpDataService.getErpObjDataById(erpIdArg, task.getDataCenterId(), idFieldKey);
                if (!erpObjData.isSuccess()) {
                    //轮询返回错误太多，打成warn日志,如果不是第一页，打成error日志。
                    if (totalDataSize.get() != 0) {
                        log.error("getStandardErpObjDataById error, dcId:{}, erpIdArg:{}, result:{}", task.getDataCenterId(), erpIdArg, erpObjData);
                    } else {
                        log.warn("getStandardErpObjDataById error, dcId:{}, erpIdArg:{}, result:{}", task.getDataCenterId(), erpIdArg, erpObjData);
                    }
                    if (StringUtils.isNotBlank(task.getRemark())) {
                        task.setRemark(task.getRemark() + erpObjData.getErrMsg());
                    }
                    return Result.copy(erpObjData);
                }
            }
            // 更新时间,之后才会进轮询
            erpTempDataDao.batchUpdateLastSyncTimeByDataIds(tenantId, task.getDataCenterId(), realObjApiName, erpIds);

            //计算查询出来的重复数据
            incDuplicateTimeByErpIds(task.getTenantId(), task.getTaskNum(), erpIds);

            //每次更新一下总数，只是更新实例，最后在处理结果更新到db。
            final long total = totalDataSize.addAndGet(erpIds.size());
            task.setTotalDataSize(total);
            task.setOffset(total);
            dataIds.addAll(erpIds);
            task.setDataIds(String.join(";", dataIds));

            pollingDataSpeedRateLimitManager.acquireQueryErpHistoryData(tenantId, (long) erpIds.size());
            return Result.newSuccess();
        });
    }

    @NotNull
    private List<String> getNeedSyncErpIds(String tenantId, ErpHistoryDataTaskEntity task, String objectApiName, List<ObjectData> dataList, CompositeIdExtend compositeIdExtend) {
        return dataList.stream()
                // 暂时只支持主对象,从对象需要找到对应的从对象apiName,暂不支持
                .map(data -> syncDataMappingManager.getMapping2Way(tenantId, tenantId, objectApiName, data.getId(), tenantId, task.getObjApiName()))
                // Pair: <crm->erp(需要isCreate=true), erp->crm>
                .filter(pair -> Objects.nonNull(pair.getRight()) || (Objects.nonNull(pair.getLeft()) && pair.getLeft().getIsCreated()))
                .map(pair -> Objects.nonNull(pair.getRight()) ? pair.getRight().getSourceDataId() : pair.getLeft().getDestDataId())
                .distinct()
                .collect(Collectors.toList());
    }
}
