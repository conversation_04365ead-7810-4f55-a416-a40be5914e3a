package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SplitObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum.*;

/**
 * Created by fengyh on 2020/8/18.
 * <p>
 * 对象拆分管理， 负责对 ERP真实对象和拆分对象进行管理。
 * 该模块主要负责 erp真实对象和拆分对象之间的数据转换。
 * erp真实对象是erp专用组件API访问实际ERP系统时要用到。
 * erp拆分对象是dss底层平台要用到。
 */
@Component
@Slf4j
public class SplitObjectManagerImpl implements SplitObjectManager {

    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private SplitObjectManagerImpl splitObjectManager;
    @Autowired
    private ErpObjManager erpObjManager;

    /**
     * 对 allSplitObjList 进行分组。
     *
     * @return: map<erp拆分对象主对象 ， List < 拆分从对象 ErpObjectRelationshipEntity>;
     */

    @Cached(timeUnit = TimeUnit.MINUTES,expire = 2,cacheType = CacheType.LOCAL)
    @LogLevel
    public Map<String, List<ErpObjectRelationshipEntity>> groupSplitObjRelation(String tenantId,String dataCenterId,
                                                                                 List<ErpObjectRelationshipEntity> allSplitObjList) {

        /**有主从关联字段的对象，我们都认为是从对象，不考虑主从多层嵌套的场景。* */
        ErpObjectFieldEntity detailSplitObjArg = ErpObjectFieldEntity.builder().tenantId(tenantId).dataCenterId(dataCenterId).
                fieldDefineType(ErpFieldTypeEnum.master_detail).build();
        List<ErpObjectFieldEntity> detailSplitObjList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(detailSplitObjArg);

        //包含了主从的拆分对象的列表。
        List<String> allObjApiNameList = allSplitObjList.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
        //只包含拆分从对象的map。map<拆分从对象apiname, ErpObjectFieldEntity>
        Map<String, ErpObjectFieldEntity> splitDetailObjFieldMap = detailSplitObjList.stream()
                .filter(v -> allObjApiNameList.contains(v.getErpObjectApiName()))
                .collect(Collectors.toMap(ErpObjectFieldEntity::getErpObjectApiName, v -> (v),(u,v) -> u));

        //支持作为主对象的拆分类型的对象
        List<String> splitMasterObjAPINameList = allSplitObjList.stream()
                .filter(v-> MAIN_OBJ_TYPES.contains(v.getSplitType()))
                .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());

        Map<String, List<ErpObjectRelationshipEntity>> resultMap = Maps.newHashMap();
        for (String mobj : splitMasterObjAPINameList) {
            resultMap.put(mobj, Lists.newArrayList());
        }

        for (ErpObjectRelationshipEntity dEntity : allSplitObjList) {
            if (splitDetailObjFieldMap.keySet().contains(dEntity.getErpSplitObjectApiname())) {
                ErpObjectFieldEntity detailSplitObjEntity = splitDetailObjFieldMap.get(dEntity.getErpSplitObjectApiname());
                //主从关联字段，主对象apiname在fieldExtendValue中。
                String masterSplitObj = detailSplitObjEntity.getFieldExtendValue();
                resultMap.get(masterSplitObj).add(dEntity);
            }
        }
        return resultMap;
    }

    //同一个拆分批次下， erp真实对象数据转拆分对象数据
    private List<SyncDataContextEvent> doErpRealObj2SplitObjData(SyncDataContextEvent data,String dataCenterId,
                                                             List<ErpObjectRelationshipEntity> erpObjectRelationshipEntityList) {

        List<SyncDataContextEvent> resultList = Lists.newArrayList();
        String tenantId = data.getSourceData().getTenantId();

        //map<erp拆分对象APIname, erp从对象entry>
        Map<String, String> erpSplitAPIAndEntryMap = splitObjectManager.getErpSplitAPIAndEntryMap(tenantId, dataCenterId);

        /**Map<erp拆分对象主对象， List<erp拆分对象从对象的relation>
         * 之所以拆分为这种结构，是因为 erp拆分对象中， 主从对象要放在一起处理，不能分开.
         * 如果拆分对象没有从对象，那么value就是一个空list.
         **/
        Map<String, List<ErpObjectRelationshipEntity>> groupRelationMap = splitObjectManager.groupSplitObjRelation(tenantId,dataCenterId, erpObjectRelationshipEntityList);

        //按照erp拆分对象主对象逐个处理。
        for (String masterSplitObjAPIName : groupRelationMap.keySet()) {
            List<ErpObjectRelationshipEntity> erpSplitDetailObjRelationshipList = groupRelationMap.get(masterSplitObjAPIName);

            //处理要放到erp拆分对象主数据的部分。erp拆分对象主对象的数据 要么来自erp真实对象(不拆分)， 要么是erp明细拆分到查找关联的拆分独立对象。
            SyncDataContextEvent erpObjDataResult = new SyncDataContextEvent();
            ErpObjectRelationshipEntity masterRelationEntity =
                    erpObjectRelationshipEntityList.stream().filter(v -> v.getErpSplitObjectApiname().equals(masterSplitObjAPIName)).collect(Collectors.toList()).get(0);
            if (masterRelationEntity.getSplitType() == DETAIL2LOOKUP_SPLIT) {
                //把data中该拆分对象的数据找出来。放到主对象。
                ErpObjectEntity result = erpObjManager.getErpObj(tenantId,dataCenterId,masterSplitObjAPIName);
                //getErpObjectExtendValue 是erp对象的entry, 和erp拆分对象apiname在erp_object表中对应。
                String detailObj = result.getErpObjectExtendValue();
                List<ObjectData> detailObjList = data.getDetailData().get(detailObj);
                for (ObjectData obj : detailObjList) { //erp每条明细，变成crm主对象的一条数据.
                    ObjectData masterData = BeanUtil.deepCopy(obj, ObjectData.class);
                    masterData.putApiName(masterSplitObjAPIName);
                    SyncDataContextEvent erpMasterObjDataResult = new SyncDataContextEvent();
                    erpMasterObjDataResult.setSourceEventType(data.getSourceEventType());
                    erpMasterObjDataResult.setSourceData(masterData);
                    erpMasterObjDataResult.setDataReceiveType(data.getDataReceiveType());//类型添加
                    erpMasterObjDataResult.setSyncLogId(data.getSyncLogId());//全链路添加。
                    erpMasterObjDataResult.setSourceData(masterData);
                    resultList.add(erpMasterObjDataResult);
                }

                //这种拆分类型不需要处理拆分对象的明细了。
                continue;
            }

            if (masterRelationEntity.getSplitType() == SUB_DETAIL_LOOKUP_DETAIL) {
                //把data中该拆分对象的数据找出来。放到主对象。
                ErpObjectEntity result = erpObjManager.getErpObj(tenantId,dataCenterId,masterSplitObjAPIName);
                SubDetailExtend subDetailExtend = SubDetailExtend.parse(result.getErpObjectExtendValue());
                List<ObjectData> detailObjList = data.getDetailData().get(subDetailExtend.getRealObjectApiName());
                for (ObjectData obj : detailObjList) {
                    //erp每条明细，变成crm主对象的一条数据.
                    ObjectData masterData = BeanUtil.copy(obj, ObjectData.class);
                    masterData.putApiName(masterSplitObjAPIName);
                    SyncDataContextEvent erpMasterObjDataResult = new SyncDataContextEvent();
                    erpMasterObjDataResult.setSourceEventType(data.getSourceEventType());
                    erpMasterObjDataResult.setSourceData(masterData);
                    erpMasterObjDataResult.setSyncLogId(data.getSyncLogId());//全链路添加。
                    erpMasterObjDataResult.setDataReceiveType(data.getDataReceiveType());//类型添加
                    resultList.add(erpMasterObjDataResult);
                }
                //这种拆分类型不需要处理拆分对象的明细了。
                continue;
            }

            if (masterRelationEntity.getSplitType() == NOT_SPLIT) {
                ObjectData masterSourceData = BeanUtil.deepCopy(data.getSourceData(), ObjectData.class);
                masterSourceData.putApiName(masterSplitObjAPIName);
                erpObjDataResult.setSourceData(masterSourceData);
            } else {
                //erp拆分主对象，既不是 not_split, 也不是 DETAIL2LOOKUP_SPLIT， 那就是我们还不支持的类型。
                throw new IllegalStateException("Unexpected split type: " + masterRelationEntity.getSplitType());
            }


            Map<String, List<ObjectData>> detailDataMap = Maps.newHashMap();
            //处理从数据
            for (ErpObjectRelationshipEntity detailEntity : erpSplitDetailObjRelationshipList) {
                //从对象逐一处理
                switch (detailEntity.getSplitType()) {
                    case NOT_SPLIT:
                        throw new IllegalStateException("Unexpected split type: " + detailEntity.getSplitType());

                    case DETAIL2DETAIL_SPLIT:
                    case SUB_DETAIL_LOOKUP_DETAIL:
                        //定位到要提取的data中的和当前relationentity 从对象相关的数据
                        String erpDetailEntryName = erpSplitAPIAndEntryMap.get(detailEntity.getErpSplitObjectApiname());
                        List<ObjectData> detailData = data.getDetailData().getOrDefault(erpDetailEntryName, new ArrayList<>());
                        List<ObjectData> newDetailData = Lists.newArrayList();
                        for (ObjectData dObj : detailData) {
                            ObjectData dData = BeanUtil.deepCopy(dObj, ObjectData.class);
                            //吧erp的entryname替换为erp从对象的apiname
                            dData.putApiName(detailEntity.getErpSplitObjectApiname());
                            newDetailData.add(dData);
                        }

                        detailDataMap.put(detailEntity.getErpSplitObjectApiname(), newDetailData);
                        break;


                    default:
                        throw new IllegalStateException("Unexpected split type: " + detailEntity.getSplitType());
                }
            }
            erpObjDataResult.setDetailData(detailDataMap);
            erpObjDataResult.setAllObjCount(data.getAllObjCount());
            erpObjDataResult.setSyncLogId(data.getSyncLogId());
            erpObjDataResult.setDataReceiveType(data.getDataReceiveType());
            erpObjDataResult.setDataVersion(data.getDataVersion());
            erpObjDataResult.setLocale(data.getLocale());
            resultList.add(erpObjDataResult);
        }
        return resultList;
    }


    @Cached(timeUnit = TimeUnit.MINUTES,expire = 2,cacheType = CacheType.LOCAL)
    @LogLevel
    public Map<String, String> getErpSplitAPIAndEntryMap(String tenantId, String dataCenterId) {
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectDao.queryAllObjExtendDTOByDc(tenantId, dataCenterId);
        Map<String, String> erpSplitAPIAndEntryMap = erpObjExtendDtos.stream().collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, ErpObjExtendDto::parseDetailApiName,(u, v)->u));
        return erpSplitAPIAndEntryMap;
    }

    /**
     * 按照拆分批次分组 relationship
     * 返回 Map<拆分批次， List<erp真实对象和拆分对象关系>>
     **/
    private Map<Integer, List<ErpObjectRelationshipEntity>> groupErp2CrmSplitSeq(ErpObjDataResult data,String dataCenterId) {
        String tenantID = data.getSourceData().getTenantId();
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantID,dataCenterId);
        ErpChannelEnum channel = erpConnectInfoEntity.getChannel();

        /**
         * 这里要按照 erpRealObjectApiname 过滤，因为对象拆分，只会有erp的拆分为crm， 不会有crm的对象拆为erp的多个对象。
         * 因此，erpRealObjectApiname 作为条件才可以查到 全量 erp真实对象和拆分对象的关系。
         * */
        ErpObjectRelationshipEntity erpObjectRelationshipEntityArg = ErpObjectRelationshipEntity.builder()
                .tenantId(tenantID)
                .dataCenterId(dataCenterId)
                .channel(channel)
                .erpRealObjectApiname(data.getSourceData().getApiName())
                .build();

        List<String> erpRealAPINameList = Lists.newArrayList(data.getSourceData().getApiName());
        erpRealAPINameList.addAll(data.getDetailData().keySet());

        List<ErpObjectRelationshipEntity> allErpObjectRelationshipEntityResult = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantID))
                .queryList(erpObjectRelationshipEntityArg);

        Map<Integer, List<ErpObjectRelationshipEntity>> groupBySplitSeqMap =
                allErpObjectRelationshipEntityResult.stream().collect(Collectors.groupingBy(ErpObjectRelationshipEntity::getSplitSeq));

        return groupBySplitSeqMap;
    }

    @Override
    public List<SyncDataContextEvent> erpRealObj2SplitObjData(SyncDataContextEvent data, String dataCenterId) {

        List<SyncDataContextEvent> resultList = Lists.newArrayList();
        //map<拆分批次， List<ErpObjectRelationshipEntity>
        Map<Integer, List<ErpObjectRelationshipEntity>> groupBySplitSeqMap = splitObjectManager.groupErp2CrmSplitSeq(data.getSourceData().getTenantId(), data.getSourceData().getApiName(),dataCenterId);

        /**区分拆分批次处理，一个批次的拆分放到一起处理. 这样做可以平滑的支持数据分流。
         * 数据分流到2个对象，就会有2个批次的拆分，数据分流到3个对象，就会有3个批次的拆分。
         * 这样在处理具体的拆分对象逻辑时，就不需要再考虑数据分流了.
         * 不然的话，同时考虑两层数据拆分，复杂度很高。
         * **/
        for (Integer splitSeq : groupBySplitSeqMap.keySet()) {
            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntityList = groupBySplitSeqMap.get(splitSeq);
            List<SyncDataContextEvent> doSplitToDetailResultList = doErpRealObj2SplitObjData(data,dataCenterId, erpObjectRelationshipEntityList);
            resultList.addAll(doSplitToDetailResultList);
        }

        return resultList;
    }


    /**
     * 按照拆分批次分组 relationship
     * 返回 Map<拆分批次， List<erp真实对象和拆分对象关系>>
     **/
    @Cached(timeUnit = TimeUnit.MINUTES,expire = 2,cacheType = CacheType.LOCAL)
    @LogLevel
    public Map<Integer, List<ErpObjectRelationshipEntity>> groupErp2CrmSplitSeq(String tenantID,String apiName,String dataCenterId) {
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantID,dataCenterId);
        ErpChannelEnum channel = erpConnectInfoEntity.getChannel();

        /**
         * 这里要按照 erpRealObjectApiname 过滤，因为对象拆分，只会有erp的拆分为crm， 不会有crm的对象拆为erp的多个对象。
         * 因此，erpRealObjectApiname 作为条件才可以查到 全量 erp真实对象和拆分对象的关系。
         * */
        ErpObjectRelationshipEntity erpObjectRelationshipEntityArg = ErpObjectRelationshipEntity.builder()
                .tenantId(tenantID)
                .dataCenterId(dataCenterId)
                .channel(channel)
                .erpRealObjectApiname(apiName)
                .build();
        List<ErpObjectRelationshipEntity> allErpObjectRelationshipEntityResult = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantID)).queryList(erpObjectRelationshipEntityArg);

        Map<Integer, List<ErpObjectRelationshipEntity>> groupBySplitSeqMap =
                allErpObjectRelationshipEntityResult.stream().collect(Collectors.groupingBy(ErpObjectRelationshipEntity::getSplitSeq));

        return groupBySplitSeqMap;
    }

    /**
     * crm->erp创建数据时使用
     *
     * @param data
     * @param erpObjExtendDtls
     * @return
     */
    @Override
    public SplitObjResult.Erp2Crm createErpSplitObj2RealObj(SyncDataContextEvent data, List<ErpObjExtendDto> erpObjExtendDtls,String dataCenterId) {
        String tenantId = data.getDestTenantId();
        SyncDataContextEvent newData = BeanUtil.deepCopy(data, SyncDataContextEvent.class);
        //key:splitAPiName value: objExtend
        Map<String, ErpObjExtendDto> map = erpObjExtendDtls.stream()
                .collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, v -> v, (u, v) -> u));
        String destApiName = data.getDestObjectApiName();
        ErpObjExtendDto destObjExtend = map.get(destApiName);
        String destRealApiName = destObjExtend.getRealObjApiName();
        if(newData.getNeedReturnField()!=null){
            if(newData.getNeedReturnField().containsKey(destApiName)){
                newData.getNeedReturnField().put(destRealApiName,newData.getNeedReturnField().get(destApiName));
                newData.getNeedReturnField().remove(destApiName);
            }
        }
        IdFieldKey idFieldKey=idFieldKeyManager.buildIdFieldKey(tenantId,dataCenterId,destApiName,destRealApiName);
        //去除不是一个批次的。
        erpObjExtendDtls.removeIf(v -> !v.getSplitSeq().equals(destObjExtend.getSplitSeq()));
        //主对象的对象扩展信息
        ErpObjExtendDto masterObjExtend = ErpObjExtendDto.findDtoBySplitType(erpObjExtendDtls, NOT_SPLIT);
        switch (destObjExtend.getSplitType()) {
            case NOT_SPLIT:
                //新建主从数据,包括字段拆分，直接替换apiName即可
                newData.setDestObjectApiName(destRealApiName);
                newData.getDestData().putApiName(destRealApiName);
                //用于标识字段拆分是否已经取值
                boolean fieldSplitDone = false;
                for (Map.Entry<String, ObjectData> entry : newData.getDestDetailSyncDataIdAndDestDataMap().entrySet()) {
                    ObjectData detailData = entry.getValue();
                    String detailApiName = detailData.getApiName();
                    ErpObjExtendDto detailObjExtend = map.get(detailApiName);
                    if (ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT.equals(detailObjExtend.getSplitType())) {
                        if(newData.getNeedReturnField()!=null){
                            if(newData.getNeedReturnField().containsKey(detailApiName)){
                                newData.getNeedReturnField().put(detailObjExtend.parseDetailApiName(),newData.getNeedReturnField().get(detailApiName));
                                newData.getNeedReturnField().remove(detailApiName);
                            }
                        }
                        //明细拆分
                        detailData.putApiName(detailObjExtend.parseDetailApiName());
                    } else if (!fieldSplitDone && ErpObjSplitTypeEnum.FIELD_SPLIT.equals(detailObjExtend.getSplitType())) {
                        //字段拆分，主数据字段没有值才会补充进去。
                        detailData.forEach((dk, dv) -> newData.getDestData().putIfAbsent(dk, dv));
                        fieldSplitDone = true;
                    }
                }
                break;
            case DETAIL2LOOKUP_SPLIT:
                //查找关联拆分,源主对象数据作为目标的明细数据，目标的主对象数据只要id字段和apiName
                detailLookupSplit2Real(data, tenantId, newData, destObjExtend, destRealApiName, masterObjExtend);
                break;
            case DETAIL2DETAIL_SPLIT:
                throw new ErpSyncDataException(ResultCodeEnum.DETAIL_MODIFY_NO_SUPPORT_NOW,tenantId);
            case FIELD_SPLIT:
                //字段拆分对象作为从对象新增，主对象必定已经同步。
                String masterDetailField = getMasterDetailField(tenantId, destApiName, masterObjExtend.getSplitObjApiName());
                String masterId = String.valueOf(data.getDestData().get(masterDetailField));
                fieldSplit2Real(tenantId, newData, masterId, destRealApiName,dataCenterId,idFieldKey);
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + destObjExtend.getSplitType());
        }
        SplitObjResult.Erp2Crm erp2Crm = new SplitObjResult.Erp2Crm();
        erp2Crm.setMasterObjSplitType(destObjExtend.getSplitType());
        erp2Crm.setDoWriteMqData(newData);
        return erp2Crm;
    }

    @Override
    public SplitObjResult.Erp2Crm updateErpSplitObj2RealObj(SyncDataContextEvent data, List<ErpObjExtendDto> erpObjExtendDtls,String dataCenterId) {
        String tenantId = data.getDestTenantId();
        SyncDataContextEvent newData = BeanUtil.deepCopy(data, SyncDataContextEvent.class);
        //key:splitAPiName value: objExtend
        Map<String, ErpObjExtendDto> map = erpObjExtendDtls.stream()
                .collect(Collectors.toMap(ErpObjExtendDto::getSplitObjApiName, v -> v, (u, v) -> u));
        String destApiName = data.getDestObjectApiName();
        ErpObjExtendDto destObjExtend = map.get(destApiName);
        String destRealApiName = destObjExtend.getRealObjApiName();
        if(newData.getNeedReturnField()!=null){
            if(newData.getNeedReturnField().containsKey(destApiName)){
                newData.getNeedReturnField().put(destRealApiName,newData.getNeedReturnField().get(destApiName));
                newData.getNeedReturnField().remove(destApiName);
            }
        }
        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId, dataCenterId, destApiName, destRealApiName);
        //去除不是一个批次的。
        erpObjExtendDtls.removeIf(v -> !v.getSplitSeq().equals(destObjExtend.getSplitSeq()));
        //主对象的对象扩展信息
        ErpObjExtendDto masterObjExtend = ErpObjExtendDto.findDtoBySplitType(erpObjExtendDtls, NOT_SPLIT);
        switch (destObjExtend.getSplitType()) {
            case NOT_SPLIT:
                //更新主对象数据,此时无从对象数据，直接替换apiName即可
                newData.setDestObjectApiName(destRealApiName);
                newData.getDestData().putApiName(destRealApiName);
                //用于标识字段拆分是否已经取值
                boolean fieldSplitDone = false;
                for (Map.Entry<String, ObjectData> entry : newData.getDestDetailSyncDataIdAndDestDataMap().entrySet()) {
                    ObjectData detailData = entry.getValue();
                    String detailApiName = detailData.getApiName();
                    ErpObjExtendDto detailObjExtend = map.get(detailApiName);
                    if (ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT.equals(detailObjExtend.getSplitType())) {
                        if(newData.getNeedReturnField()!=null){
                            if(newData.getNeedReturnField().containsKey(detailApiName)){
                                newData.getNeedReturnField().put(detailObjExtend.parseDetailApiName(),newData.getNeedReturnField().get(detailApiName));
                                newData.getNeedReturnField().remove(detailApiName);
                            }
                        }
                        //明细拆分
                        detailData.putApiName(detailObjExtend.parseDetailApiName());
                    } else if (!fieldSplitDone && ErpObjSplitTypeEnum.FIELD_SPLIT.equals(detailObjExtend.getSplitType())) {
                        //字段拆分，主数据字段没有值才会补充进去。
                        detailData.forEach((dk, dv) -> newData.getDestData().putIfAbsent(dk, dv));
                        fieldSplitDone = true;
                    }
                }
                break;
            case DETAIL2LOOKUP_SPLIT:
                //查找关联拆分,源主对象数据作为目标的明细数据，目标的主对象数据只要id字段和apiName
                detailLookupSplit2Real(data, tenantId, newData, destObjExtend, destRealApiName, masterObjExtend);
                break;
            case DETAIL2DETAIL_SPLIT:
                throw new ErpSyncDataException(ResultCodeEnum.DETAIL_MODIFY_NO_SUPPORT_NOW,tenantId);
            case FIELD_SPLIT:
                //字段拆分对象的id和主对象的id一致，调用接口获取erp数据后，把字段拆分的对象数据放进去更新
                fieldSplit2Real(tenantId, newData, newData.getDestData().getId(), destRealApiName,dataCenterId,idFieldKey);
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + destObjExtend.getSplitType());
        }
        SplitObjResult.Erp2Crm erp2Crm = new SplitObjResult.Erp2Crm();
        erp2Crm.setMasterObjSplitType(destObjExtend.getSplitType());
        erp2Crm.setDoWriteMqData(newData);
        return erp2Crm;
    }

    private void fieldSplit2Real(String tenantId, SyncDataContextEvent newData, String masterDataId, String destRealApiName,String dataCenterId, IdFieldKey idFieldKey) {
        newData.setDestObjectApiName(destRealApiName);
        newData.getDestData().putApiName(destRealApiName);
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setObjAPIName(destRealApiName);
        erpIdArg.setDataId(masterDataId);
        erpIdArg.setIncludeDetail(false);
        Result<SyncDataContextEvent> result = erpDataService.getErpObjDataById(erpIdArg,dataCenterId,idFieldKey);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(result.getErrMsg(),result.getI18nKey(),tenantId);
        }
        ObjectData masterData = result.getData().getSourceData();
        masterData.putAll(newData.getDestData());
        newData.setDestData(masterData);
    }

    private void detailLookupSplit2Real(SyncDataContextEvent data, String tenantId, SyncDataContextEvent newData,
                                        ErpObjExtendDto destObjExtend, String destRealApiName,
                                        ErpObjExtendDto masterObjExtend) {
        //查找关联拆分,源主对象数据作为目标的明细数据，目标的主对象数据只要id字段和apiName
        //获取主对象id
        String masterIdField = getLookupSplitMasterIdField(tenantId, destObjExtend.getSplitObjApiName(), masterObjExtend.getSplitObjApiName());
        String masterId = String.valueOf(data.getDestData().get(masterIdField));

        //erp主数据只需要带上apiName和id还有idField的code
        ObjectData masterValue = new ObjectData();
        masterValue.putId(masterId);
        masterValue.put(masterObjExtend.getIdFieldName(), masterId);
        masterValue.putApiName(destRealApiName);
        newData.setDestObjectApiName(destRealApiName);
        newData.setDestData(masterValue);

        //data的数据处理好放入从数据
        LinkedHashMap<String, ObjectData> newDetailFieldValMap = new LinkedHashMap<>();
        ObjectData detailData = data.getDestData();
        detailData.putApiName(destObjExtend.parseDetailApiName());
        newDetailFieldValMap.put(data.getSyncDataId(), detailData);
        newData.setDestDetailSyncDataIdAndDestDataMap(newDetailFieldValMap);
    }

    private String getMasterDetailField(String tenantId, String destSplitApiName, String masterSplitApiName) {

        //erp对象描述，我们取查找关联主对象的第一个。
        ErpObjectFieldEntity erpSplitObjArg = ErpObjectFieldEntity.builder()
                .tenantId(tenantId).erpObjectApiName(destSplitApiName)
                .fieldDefineType(ErpFieldTypeEnum.master_detail).fieldExtendValue(masterSplitApiName).build();
        List<ErpObjectFieldEntity> erpSplitObjFieldDescList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(erpSplitObjArg);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(erpSplitObjFieldDescList)) {
            throw new ErpSyncDataException(I18NStringEnum.s170,tenantId);
        }
        String masterIdField = erpSplitObjFieldDescList.get(0).getFieldApiName();
        return masterIdField;
    }

    private String getLookupSplitMasterIdField(String tenantId, String destSplitApiName, String masterSplitApiName) {

        //erp对象描述，我们取查找关联主对象的第一个。
        ErpObjectFieldEntity erpSplitObjArg = ErpObjectFieldEntity.builder()
                .tenantId(tenantId).erpObjectApiName(destSplitApiName)
                .fieldDefineType(ErpFieldTypeEnum.object_reference).fieldExtendValue(masterSplitApiName).build();
        List<ErpObjectFieldEntity> erpSplitObjFieldDescList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(erpSplitObjArg);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(erpSplitObjFieldDescList)) {
            throw new ErpSyncDataException(I18NStringEnum.s171,tenantId);
        }
        String masterIdField = erpSplitObjFieldDescList.get(0).getFieldApiName();
        return masterIdField;
    }

}
