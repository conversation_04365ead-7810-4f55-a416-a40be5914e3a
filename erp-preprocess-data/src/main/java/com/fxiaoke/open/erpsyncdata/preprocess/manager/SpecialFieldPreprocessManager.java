package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.mutable.MutableObj;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.common.Holder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3FieldExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ErpTempDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.ref.Reference;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 14:02 2020/8/26
 * @Desc:
 */
@Slf4j
@Service
public class SpecialFieldPreprocessManager {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static String ERP_FIELD_CONVERT_SUFFIX="%s__r";
    /**
     * 特殊字段类型转换（erp->crm）
     *
     * @param tenantId
     * @param erpObjDataVOS
     * @return
     */
    @LogLevel(LogLevelEnum.DEBUG)
    public List<SyncDataContextEvent> convertErpFieldValue2CrmList(String tenantId, String dataCenterId, String splitObjApiName, List<SyncDataContextEvent> erpObjDataVOS) {
        for (SyncDataContextEvent erpObjDataVO : erpObjDataVOS) {//每一条数据
            convertErpFieldValue2Crm(tenantId, dataCenterId, splitObjApiName, erpObjDataVO);
        }
        return erpObjDataVOS;
    }

    /**
     * 特殊字段类型转换（erp->crm）
     *
     * @param tenantId
     * @param erpObjDataVO
     */
    public void convertErpFieldValue2Crm(String tenantId, String dataCenterId, String splitObjApiName, SyncDataContextEvent erpObjDataVO) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        ObjectData objectData = erpObjDataVO.getSourceData();//主数据
        //获取mongoId
        MutableObj<ErpObjectFieldEntity> lastFieldHolder = new MutableObj<>();
        try {
            Map<String, ErpObjectFieldEntity> erpObjFieldMap = getErpObjFieldMap(tenantId, dataCenterId, objectData.getApiName());
            convertFieldValue(tenantId, connectInfo, objectData, erpObjFieldMap, false, lastFieldHolder);
            if (erpObjDataVO.getDetailData() != null) {//明细数据
                for (List<ObjectData> detailDatas : erpObjDataVO.getDetailData().values()) {
                    for (ObjectData detailData : detailDatas) {
//                        log.info("detail data apiName:{}", detailData.getApiName());
                        Map<String, ErpObjectFieldEntity> detailFieldMap = getErpObjFieldMap(tenantId, dataCenterId, detailData.getApiName());
                        convertFieldValue(tenantId, connectInfo, detailData, detailFieldMap, false, null);
                    }
                }
            }
        } catch (Exception e) {
            ErpObjectFieldEntity lastField = lastFieldHolder.get();
            String mongoId = objectData.getString("mongo_id");
            String fullMsg = ExceptionUtil.getMessage(e);
            if (lastField != null && mongoId != null) {
                try {
                    fullMsg = updateTempStatus(tenantId, dataCenterId, splitObjApiName, e, lastField, mongoId);
                } catch (Exception e1) {
                    log.error("convertErpFieldValue2Crm handler error has error", e);
                }
            }
            //抛出新异常
            throw new ErpSyncDataException(e, fullMsg);
        }
    }

    private String updateTempStatus(String tenantId, String dataCenterId, String splitObjApiName, Exception e, ErpObjectFieldEntity lastField, String mongoId) {
        String errorMsg;
        if (e instanceof ErpSyncDataException) {
            errorMsg = ((ErpSyncDataException) e).getText();
        } else {
            errorMsg = ExceptionUtil.getMessage(e);
        }
        String fullMsg = I18NStringEnum.kFieldConvertError.indexedFormat(lastField.getErpObjectApiName(), lastField.getFieldApiName(), errorMsg);

        NodeContext<?> context = NodeHelper.getContext();
        List<String> streamIds;
        if (context != null && CollUtil.isNotEmpty(context.getStreamIds())) {
            streamIds = context.getStreamIds();
        } else {
            //先补录一下streamIds
            streamIds = syncPloyDetailManager.listEnableStreamIdsBySrcObj(tenantId, dataCenterId, splitObjApiName, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        }
        log.info("convertErpFieldValue2Crm exception,tenant:{},streamId:{},lastField:{},msg:{}", tenantId, streamIds, lastField, ExceptionUtil.getMessage(e));
        if (CollUtil.isNotEmpty(streamIds)) {
            for (String streamId : streamIds) {
                erpTempDataManager.updateStatusByIds(tenantId, streamId, Lists.newArrayList(mongoId), ErpTempDataStatusEnum.STATUS_NOT_TRIGGER.getStatus(), fullMsg);
            }
        }
        return fullMsg;
    }

    /**
     * 特殊字段类型转换
     *
     * @param connectInfo
     * @param objectData
     * @param erpObjFieldMap
     * @param crm2erp
     * @param lastFieldHolder
     */

    public void convertFieldValue(String tenantId, ErpConnectInfoEntity connectInfo, ObjectData objectData,
                                  Map<String, ErpObjectFieldEntity> erpObjFieldMap,
                                  boolean crm2erp, @Nullable MutableObj<ErpObjectFieldEntity> lastFieldHolder) {
        lastFieldHolder = lastFieldHolder == null ? new MutableObj<>() : lastFieldHolder;
        for (ErpObjectFieldEntity fieldEntity : erpObjFieldMap.values()) {//objectData有可能没有的字段
            ErpFieldTypeEnum fieldType = fieldEntity.getFieldDefineType();
            String fieldKey = fieldEntity.getFieldApiName();
            Object oldValue = objectData.get(fieldKey);
            lastFieldHolder.set(fieldEntity);
            switch (fieldType) {
                case text:
                    convertFieldValueText(connectInfo,oldValue,fieldKey,objectData,crm2erp,fieldEntity);
                    break;
            }
        }
        //objectData没有的字段不处理
        for (String fieldKey : objectData.keySet()) {
            ErpObjectFieldEntity fieldEntity = erpObjFieldMap.get(fieldKey);
            if (fieldEntity == null) {
                continue;
            }
            lastFieldHolder.set(fieldEntity);
            ErpFieldTypeEnum fieldType = fieldEntity.getFieldDefineType();
            Object oldValue = objectData.get(fieldKey);
            if (fieldType == null) {
                continue;
            }
            if (Objects.isNull(oldValue)) {
                continue;
            }

            //特殊格式校验
            checkSpecialFieldValue(objectData, tenantId, crm2erp, fieldKey);

            switch (fieldType) {
                case department:
                case employee:
                    covertFieldValueEmpOrDepart(connectInfo, oldValue, fieldType, fieldKey, objectData, crm2erp,
                            fieldEntity.getFieldExtendValue());
                    break;
                case date:
                case date_time:
                    covertFieldValueDateFormat(connectInfo, oldValue, fieldType, fieldKey, objectData, crm2erp,fieldEntity);
                    break;
                case country:
                case province:
                case city:
                case district:
                case town:
                    covertFieldValueDefault(connectInfo, oldValue, fieldType, fieldKey, objectData, crm2erp);
                    break;
                case user:
                    k3DataManager.covertFieldValueUser(connectInfo, oldValue, fieldKey, objectData, crm2erp);
                    break;
                case number:
                case currency:
                    covertFieldValueBigDecimal(connectInfo, oldValue, fieldKey, objectData, crm2erp,fieldEntity);
                    break;
                case category:
                    convertFieldValue2Category(connectInfo, oldValue, fieldKey, objectData, crm2erp);
                    break;
                case select_many:
                    convertFieldValueSelectMany(connectInfo, oldValue, fieldKey, objectData, crm2erp);
                    break;
                case employee_many:
                    convertFieldValueEmployeeMany(connectInfo, oldValue, fieldKey, objectData, crm2erp,fieldEntity.getFieldExtendValue());
                    break;
                case object_reference:
                    checkObjectReferenceValid(connectInfo,oldValue,fieldKey,objectData,crm2erp);
                    break;
                case object_reference_many:
                    convertFieldValueObjectReferenceMany(connectInfo,oldValue,fieldKey,objectData,crm2erp);
                    break;
                default:
                    break;
            }
        }
    }

    public void convertFieldValueId(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp, ErpObjectFieldEntity fieldEntity) {
        if(!crm2erp){
            splicingFormulaField(connectInfo,oldValue,fieldKey,objectData,crm2erp,fieldEntity);
            if(objectData.get(fieldKey)!=null&&!objectData.get(fieldKey).equals(objectData.getId())){//重新设置_id
                objectData.putId(objectData.get(fieldKey).toString());
            }
        }
    }

    private void splicingFormulaField(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp, ErpObjectFieldEntity fieldEntity) {
        if(fieldEntity!=null&&StringUtils.isNotBlank(fieldEntity.getFieldExtendValue())
                &&fieldEntity.getFieldExtendValue().contains("{")){
            JSONObject extend=JSONObject.parseObject(fieldEntity.getFieldExtendValue());
            String splicingFormula=extend.getString("splicing_formula");
            if(StringUtils.isNotBlank(splicingFormula)){
                String regex="$";
                String splicingStr="\\+";
                StringBuffer newValue=new StringBuffer();
                String[] splits=splicingFormula.split(splicingStr);
                for(String split:splits){
                    if(split!=null&&split.startsWith(regex)&&split.endsWith(regex)){
                        String splicingFieldKey=split.substring(1,split.length()-1);
                        Object value=objectData.get(splicingFieldKey);
                        if(value!=null){
                            newValue.append(value);
                        }
                    }else{
                        newValue.append(split);
                    }
                }
                objectData.put(fieldKey, newValue.toString());
            }
        }
    }

    public void covertFieldValueBigDecimal(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp, ErpObjectFieldEntity fieldEntity) {
        if(tenantConfigurationManager.isTenantNotChangeBigDecimal(connectInfo.getTenantId())){
            return;
        };

        //失败设置null
        BigDecimal bigDecimal =null;
        try {
            if (oldValue instanceof BigDecimal){
                bigDecimal = (BigDecimal) oldValue;
            }else {
                bigDecimal = new BigDecimal(String.valueOf(oldValue));
            }
            //这里转数字
            if(StringUtils.isNotBlank(fieldEntity.getFieldExtendValue())
                    && fieldEntity.getFieldExtendValue().contains("{")){
                JSONObject extend=JSONObject.parseObject(fieldEntity.getFieldExtendValue());
                Integer roundMode=extend.getInteger("round_mode");
                if(roundMode!=null){
                    bigDecimal=bigDecimal.setScale(roundMode, RoundingMode.HALF_UP);
                }
            }
        }catch (Exception e){
            log.warn("covertFieldValueBigDecimal failed,oldvalue:{}",oldValue);
        }
        objectData.put(fieldKey, bigDecimal);
    }

    public void convertFieldValueText(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp, ErpObjectFieldEntity fieldEntity) {
        try{
            if(!crm2erp){
                splicingFormulaField(connectInfo,oldValue,fieldKey,objectData,crm2erp,fieldEntity);
                if(fieldEntity!=null&&StringUtils.isNotBlank(fieldEntity.getFieldExtendValue())
                        &&fieldEntity.getFieldExtendValue().contains("{")){//erp->crm文本字段
                    JSONObject extend=JSONObject.parseObject(fieldEntity.getFieldExtendValue());
                    Boolean isMainAttribute=extend.getBoolean("isMainAttribute");
                    if(isMainAttribute!=null&&isMainAttribute){
                        objectData.put("name",oldValue);//把该字段的值设置为name主属性的值,如果name已存在，那么会被覆盖
                        if(objectData.get(fieldKey)!=null){//有可能拼接后改变了
                            objectData.put("name",objectData.get(fieldKey));
                        }
                    }
                }
            }
        }catch (Exception e){
            log.warn("convertFieldValueText exception e={}",e);
        }

    }

    /**
     * 检查查找关联字段是否合法
     */
    private void  checkObjectReferenceValid(ErpConnectInfoEntity connectInfo,Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp){
        if (!crm2erp){
            if (connectInfo.getChannel() == ErpChannelEnum.ERP_K3CLOUD) {
                if (oldValue != null && ConfigCenter.TO_NULL_K3_DATA_ID.contains(oldValue.toString())) {
                    //不能remove因为上层用了for循环
                    objectData.put(fieldKey,null);
                }
            }
        } else {
            if(oldValue instanceof List) {
                List<String> valueList = (List<String>)oldValue;
                if(CollectionUtils.isNotEmpty(valueList)) {
                    objectData.put(fieldKey,valueList.get(0));
                }
            }
        }
    }

    private void convertFieldValueObjectReferenceMany(ErpConnectInfoEntity connectInfo,
                                               Object oldValue,
                                               String fieldKey,
                                               ObjectData objectData,
                                               boolean crm2erp) {
        log.info("SpecialFieldPreprocessManager.convertFieldValueObjectReferenceMany,oldValue={},fieldKey={},objectData={}",
                oldValue,fieldKey,objectData);
        if (oldValue == null||StringUtils.isBlank(oldValue.toString())) return;
        if (crm2erp) {
            if(ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())){
                List<Map<String,String>> multiList = new ArrayList<>();
                if(oldValue instanceof List){
                    List<String> list=(List)oldValue;
                    for(int i=0;i<list.size();i++){
                        Map<String,String> multiMap = new HashMap<>();
                        multiMap.put("FNumber",list.get(i));
                        multiList.add(multiMap);
                    }
                }
                objectData.put(fieldKey, multiList);
            }
        } else {
            List<Object> list= null;
            List<String> oldList = null;
            if (oldValue instanceof List) {
                list= (List) oldValue;
                if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                    oldList = new ArrayList<>();
                    for(Object item : list) {
                        Map<String,Object> map = (Map<String,Object>)item;
                        Map<String,Object> childMap = (Map<String,Object>)map.get(fieldKey);
                        oldList.add(MapUtils.getString(childMap,"Number"));
                    }
                } else {
                    oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
                }
            } else {//string->list
                list = Lists.newArrayList((oldValue.toString()).split(","));
                oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
            }
            objectData.put(fieldKey, oldList);
        }
        log.info("SpecialFieldPreprocessManager.convertFieldValueEmployeeMany,objectData={}", objectData);
    }

    private void convertFieldValueSelectMany(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp) {
        log.info("SpecialFieldPreprocessManager.convertFieldValueSelectMany,oldValue={},fieldKey={},objectData={}",
                oldValue,fieldKey,objectData);
        if (oldValue == null||StringUtils.isBlank(oldValue.toString())) return;
        if (crm2erp) {
            if(ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())){
                StringBuffer newValue=new StringBuffer();
                if(oldValue instanceof List){
                    List<String> list=(List)oldValue;
                    for(int i=0;i<list.size();i++){
                        newValue.append(list.get(i));
                        if(i!=(list.size()-1)){
                            newValue.append(",");
                        }
                    }
                }
                objectData.put(fieldKey, newValue.toString());
            }
        } else {
            if(ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())){
                if(oldValue instanceof String){
                    List<String> newValue=Lists.newArrayList((oldValue.toString()).split(","));
                    objectData.put(fieldKey, newValue);
                }
            }
        }
        log.info("SpecialFieldPreprocessManager.convertFieldValueSelectMany,objectData={}", objectData);
    }

    private void convertFieldValueEmployeeMany(ErpConnectInfoEntity connectInfo,
                                               Object oldValue,
                                               String fieldKey,
                                               ObjectData objectData,
                                               boolean crm2erp,
                                               String extendValue) {
        log.info("SpecialFieldPreprocessManager.convertFieldValueEmployeeMany,oldValue={},fieldKey={},objectData={},extendValue={}",
                oldValue,fieldKey,objectData,extendValue);
        if (oldValue == null||StringUtils.isBlank(oldValue.toString())) return;
        //前置K3Datamanger.convertK3DataErpOrDept里面已经做过人员的转换，判断有__r则直接返回__r的数据。
        String convertKey= String.format(ERP_FIELD_CONVERT_SUFFIX,fieldKey);
        if (crm2erp) {
            if(ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())){
                extendValue = StringUtils.equalsIgnoreCase(extendValue,"[]") ? null: extendValue;
                List<Map<String,String>> multiList = new ArrayList<>();
                if(oldValue instanceof List){
                    List<String> list=(List)oldValue;
                    for(int i=0;i<list.size();i++){
                        String oldEmp = list.get(i);

                        String newStr = getConvertedFieldValue(connectInfo, ErpFieldTypeEnum.employee, oldEmp, crm2erp,false);
                        String formId = StringUtils.removeEndIgnoreCase(objectData.getApiName(), ".BillHead");
                        String orgNoField = configCenterConfig.getK3_DEFAULT_ORG_FIELD().get(formId);
                        if (orgNoField == null) {
                            orgNoField = "OrgNo";
                        }
                        newStr = k3DataManager.getK3OperatorNo(connectInfo.getTenantId(),
                                connectInfo.getId(),
                                newStr,
                                extendValue,
                                objectData.getString(orgNoField),
                                objectData.getString("ERP_Dept"));//从这个固定的字段取ERP部门的名称

                        String numberApiName = "FNumber";//业务员编码字段
                        if (StringUtils.isNotBlank(extendValue)) {
                            try {
                                K3FieldExtend.EmpInfo empInfo = JacksonUtil.fromJson(extendValue, K3FieldExtend.EmpInfo.class);
                                if(empInfo!=null) {
                                    if (K3FieldExtend.OperatorTypeEnum.EMP.equals(empInfo.getOperatorType())) {
                                        //员工编码字段
                                        numberApiName = "FStaffNumber";
                                    }
                                }
                            } catch (Exception e) {
                            }
                        }

                        Map<String,String> multiMap = new HashMap<>();
                        multiMap.put(numberApiName,newStr);
                        multiList.add(multiMap);
                    }
                }
                objectData.put(fieldKey, multiList);
            } else {
                if(oldValue instanceof List) {
                    List<String> list=(List)oldValue;
                    List<String> newValueList = new ArrayList<>();
                    for(String fsUserId : list) {
                        String newValue = null;
                        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                                erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                                        connectInfo.getId(), ErpFieldTypeEnum.employee, fsUserId, null);
                        if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getErpDataId())) {
                            newValue = erpFieldDataMappingEntities.get(0).getErpDataId();//如果找到多个，取第一个(最新的一个)
                        }
                        //如果找到人员映射，就取转换后的人员编码，如果找不到人员映射，就丢弃
                        if(StringUtils.isNotEmpty(newValue)) {
                            newValueList.add(newValue);
                        }
                    }
                    log.info("SpecialFieldPreprocessManager.convertFieldValueEmployeeMany,crm->erp,newValueList={}",newValueList);
                    objectData.put(fieldKey, newValueList);
                }
            }
        } else {
            ErpFieldTypeEnum fieldType = ErpFieldTypeEnum.employee;
            List<Object> list= null;
            List<String> oldList = null;
            List<String> newList = Lists.newArrayList();
            if (oldValue instanceof List) {
                list= (List) oldValue;
                if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                    oldList = new ArrayList<>();
                    for(Object item : list) {
                        Map<String,Object> map = (Map<String,Object>)item;
                        Map<String,Object> childMap = (Map<String,Object>)map.get(fieldKey);
                        oldList.add(MapUtils.getString(childMap,"Number"));
                    }
                } else {
                    oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
                }
            } else {//string->list
                list = Lists.newArrayList((oldValue.toString()).split(","));
                oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
            }

            if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                if(objectData.get(convertKey)==null){
                    log.info("convertFieldValueEmployeeMany channel process field{}",oldValue);
                    for (String oldsStr : oldList) {
                        oldsStr = dealK3CloudOperatorErp2Crm(oldsStr, connectInfo);
                        String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp,true);
                        newList.add(newStr);
                    }
                }else {
                    newList=(List<String>) objectData.get(convertKey);
                }
            } else {
                for (String oldsStr : oldList) {
                    String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp,false);
                    newList.add(newStr);
                }
            }
            objectData.put(fieldKey, newList);
        }
        log.info("SpecialFieldPreprocessManager.convertFieldValueEmployeeMany,objectData={}", objectData);
    }


    public void convertFieldValue2Category(ErpConnectInfoEntity connectInfo, Object oldValue, String fieldKey, ObjectData objectData, boolean crm2erp) {
        if (oldValue == null) return;
        ErpFieldDataMappingEntity entity = null;
        if (crm2erp) {
            entity = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(connectInfo.getTenantId())).findByDataId(connectInfo.getTenantId(),connectInfo.getId(),
                    ErpFieldTypeEnum.category, oldValue.toString(), null);
            log.info("SpecialFieldPreprocessServiceImpl.convertFieldValue2Category,entity={}", entity);
            if (entity == null) return;

            objectData.put(fieldKey, entity.getErpDataId());
        } else {
            entity = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(connectInfo.getTenantId())).findByDataId(connectInfo.getTenantId(),connectInfo.getId(),
                    ErpFieldTypeEnum.category, null, oldValue.toString());
            log.info("SpecialFieldPreprocessServiceImpl.convertFieldValue2Category,entity={}", entity);
            if (entity == null) return;

            objectData.put(fieldKey, entity.getFsDataId());
        }
    }



    public void covertFieldValueDefault(ErpConnectInfoEntity connectInfo, Object oldValue, ErpFieldTypeEnum fieldType, String fieldKey,
                                        ObjectData objectData, boolean crm2erp) {
        if (oldValue instanceof List) {
            List<Object>list= (List) oldValue;
            List<String> oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
            List<String> newList = Lists.newArrayList();
            for (String oldsStr : oldList) {
                String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp,false);
                newList.add(newStr);
            }
            objectData.put(fieldKey, newList);
        } else {
            String newStr = getConvertedFieldValue(connectInfo, fieldType, oldValue.toString(), crm2erp,false);
            objectData.put(fieldKey, newStr);
        }
    }

    /**
     * K3C业务员转换
     * @param connectInfo
     * @param oldValue
     * @param fieldType
     * @param fieldKey
     * @param objectData
     * @param crm2erp
     * @param extendValue
     */
    public void covertFieldValueEmpOrDepart(ErpConnectInfoEntity connectInfo, Object oldValue, ErpFieldTypeEnum fieldType,
                                            String fieldKey,
                                            ObjectData objectData, boolean crm2erp, String extendValue) {
        log.debug("SpecialFieldPreprocessManager.covertFieldValueEmpOrDepart,oldValue={},fieldKey={},fieldType={},crm2erp={},extendValue={},objectData={}",
                oldValue,fieldKey,fieldType,crm2erp,extendValue,objectData);
        if (crm2erp) {//crm->erp
            String oldEmp;
            if (oldValue instanceof List) {//list->string
                List<Object>list= (List) oldValue;
                List<String> oldList = list.stream().map(v->v.toString()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(oldList)) {
                    oldEmp = oldList.get(0);
                } else {
                    return;
                }
            } else {
                oldEmp = oldValue.toString();
            }
            String newStr = getConvertedFieldValue(connectInfo, fieldType, oldEmp, crm2erp,false);
            if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())&& ErpFieldTypeEnum.employee.equals(fieldType)) {//仅仅是人员才走这个逻辑
                log.debug("SpecialFieldPreprocessManager.covertFieldValueEmpOrDepart,objectData={}",objectData);
                String formId = StringUtils.removeEndIgnoreCase(objectData.getApiName(), ".BillHead");
                String orgNoField = configCenterConfig.getK3_DEFAULT_ORG_FIELD().get(formId);
                if (orgNoField == null) {
                    orgNoField = "OrgNo";
                }
                newStr = k3DataManager.getK3OperatorNo(connectInfo.getTenantId(),
                        connectInfo.getId(),
                        newStr,
                        extendValue,
                        objectData.getString(orgNoField),
                        objectData.getString("ERP_Dept"));//从这个固定的字段取ERP部门的名称
            }
            objectData.put(fieldKey, newStr);
        } else {//erp-crm
            //前置K3Datamanger.convertK3DataErpOrDept里面已经做过人员的转换，判断有__r则直接返回__r的数据。
            String convertKey= String.format(ERP_FIELD_CONVERT_SUFFIX,fieldKey);
            if (oldValue instanceof List) {
                List<Object>list= (List) oldValue;
                if(CollectionUtils.isNotEmpty(list)){//不为空才进行处理
                    List<String> oldList = list.stream().filter(item ->item!=null).map(v->v.toString()).collect(Collectors.toList());
                    List<String> newList = Lists.newArrayList();
                    if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                        if(objectData.get(convertKey)==null){
                            //临时添加log。新增的数据理论上来说应该是在K3Manager里面做了empde处理
                            log.debug("covertFieldValueEmpOrDepart channel process field{}",oldValue);
                            for (String oldsStr : oldList) {
                                oldsStr = dealK3CloudOperatorErp2Crm(oldsStr, connectInfo);
                                String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp,true);
                                newList.add(newStr);
                            }
                        }else {
                            newList= (List<String>) objectData.get(convertKey);
                        }
                    } else {
                        for (String oldsStr : oldList) {
                            String newStr = getConvertedFieldValue(connectInfo, fieldType, oldsStr, crm2erp,false);
                            newList.add(newStr);
                        }
                    }
                    objectData.put(fieldKey, newList);
                }
            } else {//string->list
                String newStr=null;
                if(objectData.get(convertKey)==null){
                    String old = oldValue.toString();
                    if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
                        old = dealK3CloudOperatorErp2Crm(old, connectInfo);
                    }
                     newStr = getConvertedFieldValue(connectInfo, fieldType, old, crm2erp,true);
                    objectData.put(fieldKey, Lists.newArrayList(newStr));
                }else {
                    objectData.put(fieldKey, objectData.get(convertKey));
                }
            }
        }
    }

    private String dealK3CloudOperatorErp2Crm(String value, ErpConnectInfoEntity connectInfo) {
        FilterData filterData2 = FilterData.builder().fieldApiName("FNumber").fieldValue(Arrays.asList(value)).operate("IN").build();
        List<FilterData> filterDataList = Lists.newArrayList();
        filterDataList.add(filterData2);
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FStaffId.FNumber,FNumber");
        queryArg.setFormId(ObjectApiNameEnum.K3CLOUD_OPERATOR.getObjApiName());
        queryArg.addAndFilters(filterDataList);
        Result<List<K3Model>> result = k3DataManager.queryK3ObjData(connectInfo.getTenantId(), connectInfo.getId(), queryArg);
        if (result.getData() != null && result.getData().size() > 0) {
            return String.valueOf(result.getData().get(0).get("FStaffId.FNumber"));
        }
        return value;
    }

    static Pattern pattern = Pattern.compile("[0-9]*");

    /** @see DateFormatSymbols#patternChars*/
    static Pattern patternChars = Pattern.compile("[GyMdkHmsSEDFwWahKzZYuXL]*");

    public static void covertFieldValueDateFormat(ErpConnectInfoEntity connectInfo, Object oldValue, ErpFieldTypeEnum fieldType, String fieldKey,ObjectData objectData, boolean crm2erp, ErpObjectFieldEntity fieldEntity) {
        if (Objects.isNull(oldValue)) {
            return;
        }

        final String format = getFieldDateFormat(connectInfo.getChannel(), fieldType, fieldEntity);
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);

        String famatValue = String.valueOf(oldValue);
        if (crm2erp) {
            if ((pattern.matcher(famatValue).matches())) {
                objectData.put(fieldKey, dateFormat.format(new Date(new BigDecimal(famatValue).longValue())));
            } else if ((Pattern.compile("[+-]*\\d+\\.?\\d*[Ee]*[+-]*\\d+").matcher(famatValue).matches())) {
                // 科学计数法
                BigDecimal bd = new BigDecimal(famatValue);
                String str = bd.toPlainString();
                Long timestamp = Long.parseLong(str);
                objectData.put(fieldKey, dateFormat.format(new Date(timestamp)));
            }
            return;
        }

        if (StringUtils.isNumeric(famatValue) && (!patternChars.matcher(format).matches() || !Objects.equals(famatValue.length(), format.length()))) {
            // 兼容旧逻辑: 全数字时,如果格式也全是替换符且长度一致,按格式转换; 否则不转换
            return;
        }

        try {
            Date fsDate = dateFormat.parse(String.valueOf(oldValue));
            objectData.put(fieldKey, fsDate == null ? null : fsDate.getTime());
        } catch (ParseException e) {
            log.warn("dateFormat error, {}", e.toString());
        }
    }

    @NotNull
    private static String getFieldDateFormat(ErpChannelEnum channel, ErpFieldTypeEnum fieldType, ErpObjectFieldEntity fieldEntity) {
        if (fieldEntity != null && StringUtils.isNotBlank(fieldEntity.getFieldExtendValue())
                && fieldEntity.getFieldExtendValue().contains("{")) {
            JSONObject extend = JSONObject.parseObject(fieldEntity.getFieldExtendValue());
            final String format = extend.getString("format");
            if (StringUtils.isNotBlank(format)) {
                return format;
            }
        }

        if (fieldType == ErpFieldTypeEnum.date_time && channel.equals(ErpChannelEnum.ERP_K3CLOUD)) {
            return "yyyy-MM-dd'T'HH:mm:ss";
        }

        return Objects.requireNonNull(fieldType) == ErpFieldTypeEnum.date ? "yyyy-MM-dd" : "yyyy-MM-dd HH:mm:ss";
    }

    /**
     * @param tenantId
     * @param objApiName
     * @return
     */
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public Map<String, ErpObjectFieldEntity> getErpObjFieldMap(String tenantId,String dataCenterId, String objApiName) {
        List<ErpObjectFieldEntity> objFields = erpFieldManager.queryAllField(tenantId,dataCenterId, objApiName);
        Map<String, ErpObjectFieldEntity> map = objFields.stream().collect(Collectors
                .toMap(ErpObjectFieldEntity::getFieldApiName, v -> v, (u, v) -> u));
        return map;
    }

    /**
     * 获取转换后的字段值，如果找到多个，取第一个(最新的一个)
     *
     * @param connectInfo
     * @param fieldType
     * @param fieldValue
     * @param crm2erp
     * @return
     */
    public String getConvertedFieldValue(ErpConnectInfoEntity connectInfo, ErpFieldTypeEnum fieldType, String fieldValue, boolean crm2erp,boolean needConvert) {
        if (fieldType==null){
            //按道理都进不来
            return null;
        }
        if (connectInfo == null || connectInfo.getTenantId() == null || fieldValue == null) {
            return fieldType.defaultValue(null, false, null,needConvert);
        }
        if (crm2erp) {//crm转erp
            List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                    erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                            connectInfo.getId(), fieldType, fieldValue, null);
            if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getErpDataId())) {
                return erpFieldDataMappingEntities.get(0).getErpDataId();//如果找到多个，取第一个(最新的一个)
            }
        } else {//erp转crm
            List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                    erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                            connectInfo.getId(), fieldType, null, fieldValue);
            if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getFsDataId())) {
                return erpFieldDataMappingEntities.get(0).getFsDataId();
            }
        }
        return fieldType.defaultValue(fieldValue, crm2erp, connectInfo.getChannel(),needConvert);
    }

    /**
     * 查询erp对象字段
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    public List<ErpObjectFieldEntity> queryErpObjFields(String tenantId, String objApiName) {
        ErpObjectFieldEntity arg = ErpObjectFieldEntity.builder().tenantId(String.valueOf(tenantId)).erpObjectApiName(objApiName).build();
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        return erpObjectFieldEntities;
    }

    /**
     * 特殊字段类型转换（crm->erp）
     *
     * @param tenantId
     * @param doWriteData
     * @return
     */
    public SyncDataContextEvent convertCrmFieldValue2Erp(String tenantId, String dataCenterId, SyncDataContextEvent doWriteData) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        ObjectData objectData = doWriteData.getDestData();//主数据
        String erpActualObj = getErpActualObj(tenantId,dataCenterId, objectData.getApiName());
        Map<String, ErpObjectFieldEntity> erpObjFieldMap = getErpObjFieldMap(tenantId,dataCenterId, erpActualObj);
        convertFieldValue(tenantId, connectInfo, objectData, erpObjFieldMap, true, null);
        if (doWriteData.getDestDetailSyncDataIdAndDestDataMap() != null) {//明细数据
            for (ObjectData detailData : doWriteData.getDestDetailSyncDataIdAndDestDataMap().values()) {
                String erpDetailActualObj = getErpActualObj(tenantId,dataCenterId, detailData.getApiName());
                Map<String, ErpObjectFieldEntity> detailFieldMap = getErpObjFieldMap(tenantId,dataCenterId, erpDetailActualObj);
                convertFieldValue(tenantId, connectInfo, detailData, detailFieldMap, true, null);
            }
        }
        return doWriteData;
    }

    public String getErpActualObj(String tenantId,String dataCenterId, String oldObjApiName) {
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(oldObjApiName)) {
            return null;
        }
        ErpObjectEntity erpObjectEntityArg = ErpObjectEntity.builder().tenantId(tenantId).dataCenterId(dataCenterId).build();
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(erpObjectEntityArg);
        for (ErpObjectEntity erpObjectEntity : erpObjectEntities) {
            if (oldObjApiName.equals(erpObjectEntity.getErpObjectApiName())) {//自身就是真实对象
                return oldObjApiName;
            }
        }

        ErpObjectRelationshipEntity erpObjectRelationshipEntityArg = ErpObjectRelationshipEntity.builder().tenantId(tenantId).dataCenterId(dataCenterId).build();
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(erpObjectRelationshipEntityArg);
        for (ErpObjectRelationshipEntity entity : erpObjectRelationshipEntities) {
            if (oldObjApiName.equals(entity.getErpSplitObjectApiname())) {//分割对象->真实对象
                return entity.getErpRealObjectApiname();
            }
        }
        return null;
    }

    /**
     * 特殊格式校验，目前只校验erp2crm
     */
    private void checkSpecialFieldValue(ObjectData objectData, String tenantId, boolean crm2erp, String fieldKey) {
        if(crm2erp || ObjectUtils.isEmpty(objectData)) {
            return;
        }

        try {
            if(fieldKey.equals("version")) {
                objectData.getVersion();
            }
        } catch (Exception e) {
            log.warn("SpecialFieldPreprocessManager.checkSpecialFieldValue.locale:{},error {}.", TraceUtil.getLocale(), e.toString());
            String msg = i18NStringManager.get(I18NStringEnum.s5204, TraceUtil.getLocale(), tenantId);
            throw new ErpSyncDataException(msg + " " + e.getMessage());
        }
    }
}
