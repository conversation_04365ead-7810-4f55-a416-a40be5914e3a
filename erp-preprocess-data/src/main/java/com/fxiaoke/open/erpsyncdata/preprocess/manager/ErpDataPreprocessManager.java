package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/6/1
 */
@Component
@Slf4j
public class ErpDataPreprocessManager {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;

    /**
     * 检查数据合法性
     *
     * @param tenantId
     * @param dataListRes
     */
    public void checkListResult(String realObjApiName, String tenantId, List<SyncDataContextEvent> dataListRes) {
        //明细太多的对象配置，不校验明细数量

        if (tenantConfigurationManager.inGroupWhiteList(tenantId,realObjApiName, TenantConfigurationTypeEnum.TOO_MANY_DETAILS_TENANTS_OBJS)) {
            return;
        }
        try {
            List<String> objectIds= Lists.newArrayList();
            //移除明细超出限制的数据
            dataListRes.removeIf(data -> {
                for (Map.Entry<String, List<ObjectData>> entry : data.getDetailData().entrySet()) {
                    String apiName = entry.getKey();
                    List<ObjectData> details = entry.getValue();
                    Integer limit = configCenterConfig.getSPECIAL_LIMIT_DETAIL_SIZE().getOrDefault(apiName,
                            configCenterConfig.getDEFAULT_LIMIT_DETAIL_SIZE());
                    if (details != null && details.size() > limit) {
                        if (data.getSourceData().get("mongo_id") != null) {
                            objectIds.add(data.getSourceData().get("mongo_id").toString());
                        }
                        idFieldConvertManager.convertField2Crm(tenantId, data);
                        monitorReportManager.sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum.end, DataNodeNameEnum.ObjOverDetailLimit, tenantId, data.getSourceData().getApiName(),
                                data.getSourceData().getApiName(), data.getSourceData().getId(), data.getDataVersion(), null, System.currentTimeMillis(), 1000,
                                i18NStringManager.getByEi(I18NStringEnum.s840,tenantId));
                        //如果超过限制，会打日志并remove
                        log.warn("this erp obj over detail limit,mainData:{},detail api name:{}，detail size:{}", data.getSourceData(), apiName, details.size());
                        return true;
                    }
                }
                return false;
            });
            if(CollectionUtils.isNotEmpty(objectIds)){
                erpTempDataManager.updateStatusByIds(tenantId,null,objectIds,null,
                        i18NStringManager.getByEi(I18NStringEnum.s840,tenantId));
            }
        } catch (Exception e) {
            log.error("check remove over limit detail failed,e:", e);
        }
    }

    /**
     * 移除需要同步的数据
     * 为避免对db访问过大，尽量采取批量调用dao处理的方式
     *
     * @param results  L
     * @param tenantId
     * @param dcId
     * @param channel
     * @param snapId
     */
    public void tryRemoveNotNeedSync(List<SyncDataContextEvent> results, String tenantId, String dcId, ErpChannelEnum channel, String snapId) {
        try {
            removeNotNeedSync(results, tenantId, dcId, channel, snapId);
        } catch (Exception e) {
            log.error("tryRemoveNotNeedSync get error", e);
        }
    }

    private void removeNotNeedSync(List<SyncDataContextEvent> results, String tenantId, String dcId, ErpChannelEnum channel, String snapId) {
        if (results.isEmpty()) {
            return;
        }
        String configType = TenantConfigurationTypeEnum.ENABLE_REMOVE_NOT_NEED_SYNC.name();
        ErpTenantConfigurationEntity enableRemoveConfig = tenantConfigurationManager.findOne(tenantId, dcId, channel.toString(), configType);
        if (enableRemoveConfig == null) {
            return;
        }
        SyncPloyDetailSnapshotEntity snapshot = syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(tenantId, snapId);
        String masterErpApiName = snapshot.getSourceObjectApiName();
        //key：erpApiName,value:需要移除的id列表
        Map<String, Set<String>> needRemoveDetailIdMap = new HashMap<>();
        //移除主数据Id
        Set<String> needRemoveMasterIds = new HashSet<>();
        Iterable<String> split = Splitter.on(",").split(enableRemoveConfig.getConfiguration());
        SyncPloyDetailData syncPloyDetailData = snapshot.getSyncPloyDetailData();
        for (String needCheckErpApiName : split) {
            if (needCheckErpApiName.equals(masterErpApiName)) {
                List<ObjectData> needCheckMasterObjs = results.stream().map(SyncDataContextEvent::getSourceData).collect(Collectors.toList());
                List<FieldMappingData> fieldMappings = syncPloyDetailData.getFieldMappings();
                needRemoveMasterIds = getNeedRemoveIdMap(tenantId, needCheckErpApiName, snapshot.getDestObjectApiName(), snapId, needCheckMasterObjs, fieldMappings);
            } else {
                List<ObjectData> detailObjs = results.stream()
                        .flatMap(v -> MapUtils.emptyIfNull(v.getDetailData()).getOrDefault(needCheckErpApiName, new ArrayList<>()).stream())
                        .collect(Collectors.toList());
                Optional<DetailObjectMappingsData.DetailObjectMappingData> any = syncPloyDetailData.getDetailObjectMappings().stream().filter(v -> v.getSourceObjectApiName().equals(needCheckErpApiName)).findAny();
                if (!any.isPresent()) {
                    continue;
                }
                DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData = any.get();
                List<FieldMappingData> fieldMappings = detailObjectMappingData.getFieldMappings();
                String destObjectApiName = detailObjectMappingData.getDestObjectApiName();
                Set<String> detailIds = getNeedRemoveIdMap(tenantId, needCheckErpApiName, destObjectApiName, snapId, detailObjs, fieldMappings);
                if (!detailIds.isEmpty()) {
                    needRemoveDetailIdMap.put(needCheckErpApiName, detailIds);
                }
            }
        }
        //先移除明细
        needRemoveDetailIdMap.forEach((detailObjApiName, detailIds) -> {
            results.forEach(result -> {
                if (MapUtils.emptyIfNull(result.getDetailData()).containsKey(detailObjApiName)) {
                    List<ObjectData> detailObjs = result.getDetailData().get(detailObjApiName);
                    detailObjs.removeIf(detailObj -> detailIds.contains(detailObj.getId()));
                    if (detailObjs.isEmpty()) {
                        result.getDetailData().remove(detailObjApiName);
                    }
                }
            });
        });
        //再检查移除主对象 暂不支持从对象单独往上传输所以只有从对象也全部为空时才会移除
        if (CollectionUtils.isNotEmpty(needRemoveMasterIds)) {
            Set<String> finalNeedRemoveMasterIds = needRemoveMasterIds;
            results.removeIf(result -> result.getDetailData().isEmpty()
                    && finalNeedRemoveMasterIds.contains(result.getSourceData().getId()));
        }
    }

    private Set<String> getNeedRemoveIdMap(String tenantId, String erpApiName, String destObjApiName, String snapId,
                                           List<ObjectData> needCheckMasterObjs, List<FieldMappingData> fieldMappings) {
        if (needCheckMasterObjs.isEmpty()) {
            return new HashSet<>();
        }
        Map<String, ObjectData> newObjDataMap = needCheckMasterObjs.stream().collect(Collectors.toMap(ObjectData::getId, u -> u));
        List<String> erpIds = new ArrayList<>(newObjDataMap.keySet());
        List<SyncDataMappingsEntity> syncDataMappings = syncDataMappingsDao.setTenantId(tenantId).queryBySourceDataIdList(tenantId, erpApiName, erpIds, destObjApiName);
        //筛选需要检查的mapping，不检查未创建成功或者上次同步不成功的数据
        syncDataMappings.removeIf(v -> !v.getIsCreated() || v.getLastSyncStatus() != SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
        if (syncDataMappings.isEmpty()) {
            return new HashSet<>();
        }
        List<String> lastSyncDataIds = syncDataMappings.stream().map(SyncDataMappingsEntity::getLastSyncDataId).collect(Collectors.toList());
        List<SyncDataEntity> syncDataEntities = adminSyncDataDao.setTenantId(tenantId).listByIds(tenantId, lastSyncDataIds);
        Map<String, SyncDataEntity> syncDataMap = syncDataEntities.stream().collect(Collectors.toMap(SyncDataEntity::getId, u -> u));
        Set<String> needRemoveIds = new HashSet<>();
        for (SyncDataMappingsEntity syncDataMapping : syncDataMappings) {
            SyncDataEntity syncDataEntity = syncDataMap.get(syncDataMapping.getLastSyncDataId());
            if (syncDataEntity == null
                    || syncDataEntity.getSourceData() == null
                    || !snapId.equals(syncDataEntity.getSyncPloyDetailSnapshotId())) {
                //不是同一个快照的数据，不触发检查。
                continue;
            }
            ObjectData oldSourceData = syncDataEntity.getSourceData();
            ObjectData newData = newObjDataMap.get(syncDataMapping.getSourceDataId());
            boolean allFieldNoChanged = fieldMappings.stream().allMatch(fieldMapping -> {
                String field = fieldMapping.getSourceApiName();
                return JacksonUtil.equalObjField(oldSourceData.get(field), newData.get(field));
            });
            if (allFieldNoChanged) {
                //移除所有字段都没有变化的数据
                needRemoveIds.add(syncDataMapping.getSourceDataId());
            }
        }
        return needRemoveIds;
    }

    public void removeOutOfLimitDetail(String realObjApiName, ErpIdArg erpIdArg, SyncDataContextEvent erpObjDataVO) {
        String tenantId = erpIdArg.getTenantId();

        if (erpObjDataVO != null
                && tenantConfigurationManager.inGroupWhiteList(tenantId, realObjApiName, TenantConfigurationTypeEnum.TOO_MANY_DETAILS_TENANTS_OBJS)) {
            //如果明细超过limit，只保留limit条
            for (String apiName : erpObjDataVO.getDetailData().keySet()) {
                List<ObjectData> detail = erpObjDataVO.getDetailData().get(apiName);
                Integer limit = configCenterConfig.getSPECIAL_LIMIT_DETAIL_SIZE().getOrDefault(apiName,
                        configCenterConfig.getDEFAULT_LIMIT_DETAIL_SIZE());
                if (detail != null && detail.size() > limit) {
                    log.info("removeOutOfLimitDetail dataId={},detailApiName={},oldSize={},newSize={}",erpIdArg.getDataId(),apiName,detail.size(),limit);
                    detail = detail.stream().limit(limit).collect(Collectors.toList());
                    erpObjDataVO.getDetailData().put(apiName, detail);
                }
            }
        }
    }
}
