package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.arg.v3.PaasV3FindSimpleArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.MetadataControllerServiceV3;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.SyncDataDependData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.converter.helpers.AviatorHelper;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpReSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.SyncTrace;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.CompleteDataWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.OverrideOuterService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class ErpOverrideOuterServiceImpl implements OverrideOuterService {

    @Autowired
    private MetadataControllerServiceV3 metadataControllerServiceV3;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private ErpReSyncDataMongoDao erpReSyncDataMongoDao;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private AviatorHelper aviatorHelper;
    @Autowired
    private I18NStringManager i18NStringManager;



    @Override
    public Result2<List<ObjectData>> listDetailDatasByIdAndFilter(String sourceTenantId,
                                                                  String destTenantId,
                                                                  Integer tenantType,
                                                                  String objectApiName,
                                                                  String masterFieldApiName,
                                                                  String masterObjectId,
                                                                  SyncConditionsData detailSyncConditionsData,
                                                                  Integer limit,
                                                                  Integer offset,
                                                                  String dataCenterId) {
        if (TenantTypeEnum.CRM.getType() == tenantType) {
            //按CRM列表排序
            return listCrmDetailsOrder(sourceTenantId, destTenantId, objectApiName, masterFieldApiName, masterObjectId, detailSyncConditionsData, limit, offset);
        } else {
            return listErpDetailDatasByIdAndFilter(sourceTenantId, destTenantId, tenantType, objectApiName, masterFieldApiName, masterObjectId, detailSyncConditionsData, limit, offset,dataCenterId);
        }
    }

    public Result2<List<ObjectData>> listCrmDetailsOrder(String sourceTenantId,
                                                         String destTenantId,
                                                         String objectApiName,
                                                         String masterFieldApiName,
                                                         String masterObjectId,
                                                         SyncConditionsData detailSyncConditionsData,
                                                         Integer limit,
                                                         Integer offset) {
        List<List<FilterData>> filters = detailSyncConditionsData.getFilters();
        List<List<FilterData>> filtersResult = outerServiceFactory.get(TenantTypeEnum.CRM.getType()).changeVariableFilter(filters, sourceTenantId, destTenantId);
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        if (filtersResult != null && filtersResult.size() != 0) {
            List<Wheres> wheres = new ArrayList<>();
            for (List<FilterData> orFilters : filtersResult) {
                List<Filter> andFilters = new ArrayList<>();
                //添加主对象条件
                andFilters.add(buildFilter(masterFieldApiName, FilterOperatorEnum.EQ.getValue(), Lists.newArrayList(masterObjectId)));
                for (FilterData filterData : orFilters) {
                    andFilters.add(buildFilter(filterData.getFieldApiName(), filterData.getOperate(),
                            filterData.getFieldValue()));
                }
                wheres.add(this.buildWheresByFilters(andFilters));
            }
            searchQuery.setWheres(wheres);
        } else {
            searchQuery.addFilter(masterFieldApiName, Lists.newArrayList(masterObjectId), FilterOperatorEnum.EQ);
        }
        searchQuery.setOffset(offset);
        searchQuery.setLimit(limit);
        //明细排序
        SearchTemplateQueryOrderBy orderBy = new SearchTemplateQueryOrderBy("order_by",true);
        orderBy.setIsNullLast(true);
        searchQuery.getOrders().add(orderBy);
        searchQuery.setPermissionType(0);
        searchQuery.setSearchSource("db");
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(sourceTenantId), CrmConstants.SYSTEM_USER);
        PaasV3FindSimpleArg paasV3FindSimpleArg = new PaasV3FindSimpleArg();
        paasV3FindSimpleArg.setSearchQueryInfo(searchQuery);
        Page<com.fxiaoke.crmrestapi.common.data.ObjectData>
                result = metadataControllerServiceV3.getDataV3FindSimple(headerObj,objectApiName,paasV3FindSimpleArg).getData();
        List<ObjectData> objectDataList = BeanUtil2.deepCopyList(result.getDataList(), ObjectData.class);
        return Result2.newSuccess(objectDataList);
    }


    private Result2<List<ObjectData>> listErpDetailDatasByIdAndFilter(String sourceTenantId,
                                                                      String destTenantId,
                                                                      Integer tenantType,
                                                                      String objectApiName,
                                                                      String masterFieldApiName,
                                                                      String masterObjectId,
                                                                      SyncConditionsData detailSyncConditionsData,
                                                                      Integer limit,
                                                                      Integer offset,
                                                                      String dataCenterId) {

        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId(masterObjectId);
        erpIdArg.setTenantId(sourceTenantId);
        erpIdArg.setIncludeDetail(true);
        Result2<String> masterApiNameRes = getMasterObjectApiName(sourceTenantId, tenantType, objectApiName);
        if (masterApiNameRes == null || masterApiNameRes.getData() == null) {
            return Result2.newErrorByI18N(-1, I18NStringEnum.s47);
        }
        erpIdArg.setObjAPIName(masterApiNameRes.getData());
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<SyncDataContextEvent> erpObjDataById = erpDataPreprocessService.getErpObjDataFromMongoIfExist(erpIdArg);

        if (!erpObjDataById.isSuccess()) {
            return Result2.newError(-1, erpObjDataById.getErrMsg());
        }
        if (erpObjDataById.getData() != null && erpObjDataById.getData().getDetailData() != null) {//过滤掉不符合数据范围的明细
            String expression = ConditionUtil.parseToOrExpression(detailSyncConditionsData.getFilters());
            List<ObjectData> details = erpObjDataById.getData().getDetailData().getOrDefault(objectApiName, new ArrayList<>());
            List<ObjectData> newDetails = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(details)) {
                for (ObjectData objectData : details) {
                    Boolean isMatch = (Boolean) aviatorHelper.execute(expression,
                            objectData,
                            sourceTenantId,
                            dataCenterId,
                            objectApiName,
                            null,
                            true);
                    if (isMatch) {
                        newDetails.add(objectData);
                    }
                }
            }
            return Result2.newSuccess(newDetails);
        }
        return Result2.newError(-1, "not found detail data");

    }

    private Result2<String> getMasterObjectApiName(String tenantId, Integer tenantType, String objectApiName) {
        ErpObjectFieldEntity record = new ErpObjectFieldEntity();
        record.setTenantId(tenantId);
        record.setErpObjectApiName(objectApiName);
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(record);
        for (ErpObjectFieldEntity entity : erpObjectFieldEntities) {
            if (ErpFieldTypeEnum.master_detail.equals(entity.getFieldDefineType())) {
                return Result2.newSuccess(entity.getFieldExtendValue());
            }
        }
        return Result2.newSuccess();
    }

    private Filter buildFilter(String fieldName, String operator, List<String> fieldValues) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }

    private Wheres buildWheresByFilters(List<Filter> filters) {
        Wheres wheres = new Wheres();
        wheres.setFilters(filters);
        return wheres;
    }

    @Override
    @LogLevel
    public Result2<Boolean> needConsumePaasObjectDataMq(String sourceTenantId) {
        ErpSyncDataBackStageEnvironmentEnum tenantAllModelEnv = tenantEnvManager.getTenantAllModelEnv(sourceTenantId);
        return Result2.newSuccess(ConfigCenter.ServiceEnvironment.equals(tenantAllModelEnv.getEnvironment()));
    }

    @Override
    public Result2<Void> completeWriteUpdateStatus(CompleteDataWriteMqData message) {
        String tenantId = message.getTenantId();
        for (CompleteDataWriteMqData.WriteResult detailWriteResult : message.getDetailWriteResults()) {
            updateDetailStatus(tenantId, message.getDestEventType(), detailWriteResult);
        }
        updateStatus(tenantId, message.getDestEventType(), message.getWriteResult());
        return Result2.newSuccess();
    }

    private void updateStatus(String tenantId, Integer destEventType, CompleteDataWriteMqData.WriteResult writeResult) {
        String syncDataId = writeResult.getSyncDataId();
        try {
            String destDataName = null;
            if (writeResult.getSimpleSyncData()!=null){
                destDataName = writeResult.getSimpleSyncData().getDestDataName();
            }
            if (!writeResult.isSuccess()) {
                syncDataManager.updateBySyncDataId(tenantId,syncDataId,destDataName,SyncDataStatusEnum.WRITE_FAILED.getStatus(),writeResult.getErrMsg(), String.valueOf(writeResult.getErrCode()));
                return;
            }
            int newStatus = SyncDataStatusEnum.WRITE_SUCCESS.getStatus();
            String remark = SyncDataEntity.SuccessRemark;
            if (destEventType == EventTypeEnum.ADD.getType()) {
                syncDataManager
                        .updateStatusAndDestDataIdAndCreatedBySuccess(tenantId, writeResult.getSimpleSyncData(), writeResult.getDestDataId(), newStatus, remark);
            } else {
                syncDataManager.updateBySyncDataId(tenantId, syncDataId,destDataName, newStatus, remark, null);
            }
        } catch (Exception e) {
            log.warn("Exception updateStatus error,writeResult=" + writeResult, e);
            syncDataManager.updateToError(tenantId, syncDataId, SyncDataStatusEnum.WRITE_FAILED.getStatus(),
                    i18NStringManager.getByEi(I18NStringEnum.s1155,tenantId) + writeResult.getDestDataId(), I18NStringEnum.s1155.name());
        }
    }

    private void updateDetailStatus(String tenantId, Integer destEventType, CompleteDataWriteMqData.WriteResult writeResult) {
        String syncDataId = writeResult.getSyncDataId();
        try {
            String destDataName = null;
            if (writeResult.getSimpleSyncData()!=null){
                destDataName = writeResult.getSimpleSyncData().getDestDataName();
            }
            if (!writeResult.isSuccess()) {
                syncDataManager.updateBySyncDataId(tenantId, syncDataId, destDataName,SyncDataStatusEnum.WRITE_FAILED.getStatus(),
                        writeResult.getErrMsg(), String.valueOf(writeResult.getErrCode()));
                return;
            }
            int newStatus = SyncDataStatusEnum.WRITE_SUCCESS.getStatus();
            String remark = SyncDataEntity.SuccessRemark;
            if (destEventType == EventTypeEnum.ADD.getType()) {
                syncDataManager.updateStatusAndDestDataIdAndCreatedBySuccess(tenantId,
                        writeResult.getSimpleSyncData(),
                        writeResult.getDestDataId(),
                        newStatus,
                        remark);
            } else {
                SyncDataMappingsEntity mappingBySyncDataId = syncDataMappingsManager.findMappingBySyncDataId(tenantId, syncDataId);
                if (newStatus == SyncDataStatusEnum.WRITE_SUCCESS.getStatus()
                        && mappingBySyncDataId != null
                        && !mappingBySyncDataId.getIsCreated()) {
                    //当原来的数据是未创建成功的，更新destDataId
                    syncDataManager
                            .updateStatusAndDestDataIdAndCreatedBySuccess(tenantId, writeResult.getSimpleSyncData(), writeResult.getDestDataId(), newStatus, remark);
                } else {
                    syncDataManager.updateBySyncDataId(tenantId, syncDataId,destDataName, newStatus, remark, null);
                }

            }
        } catch (Exception e) {
            log.warn("Exception updateStatus error,writeResult=" + writeResult, e);
            syncDataManager.updateToError(tenantId, syncDataId, SyncDataStatusEnum.WRITE_FAILED.getStatus(),
                    i18NStringManager.getByEi(I18NStringEnum.s1155,tenantId) + writeResult.getDestDataId(), I18NStringEnum.s1155.name());
        }
    }

    @Override
    public Result2<Void> updateErpTempDataByIds(String tenantId, String ployDetailId, String mongoId, Integer status, String remark) {
        if (StringUtils.isNotBlank(mongoId)) {
            erpTempDataManager.updateStatusByIds(tenantId, ployDetailId, Lists.newArrayList(mongoId), status, remark);
        }
        return Result2.newSuccess();
    }




    /**
     * trace级别不会打印，防止arg或result在log中序列化失败
     *
     * @param remark        存放url
     * @param doWriteMqData
     * @param arg
     * @param result
     * @param callTime
     * @param returnTime
     * @param status        1成功调用，2失败调用
     * @return
     */
    @Override
    @LogLevel(LogLevelEnum.TRACE)
    public Result2<Void> saveInterfaceLog(String remark, SyncDataContextEvent syncDataContextEvent, Object arg, Object result, long callTime, long returnTime, int status) {
        String resultStr;
        String argStr;
        try {
            resultStr = GsonUtil.toJson(result);
        } catch (Exception e) {
            resultStr = "result is not serializable by gson";
        }
        try {
            argStr = GsonUtil.toJson(arg);
        } catch (Exception e) {
            argStr = "arg is not serializable by gson";
        }
        log.debug("save interfacelog begin,remark:{},dowriteMqData:{},arg:{},result:{},callTime:{},returnTime:{},status:{}", remark, syncDataContextEvent, argStr, resultStr, callTime, returnTime, status);
        String snapshotId = syncDataContextEvent.getSyncPloyDetailSnapshotId();
        String syncDataId = syncDataContextEvent.getSyncDataId();
        Integer eventType = syncDataContextEvent.getDestEventType();
        String tenantId = syncDataContextEvent.getTenantId();
        String objApiName = syncDataContextEvent.getDestObjectApiName();
        String dcId;
        if (snapshotId != null) {
            dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId,snapshotId);
        } else {
            //作废等情况没有传输快照id
            dcId = dataCenterManager.getDataCenterBySyncDataId(tenantId, syncDataId);
        }
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(syncDataId).build());
        ErpObjInterfaceUrlEnum type;
        switch (eventType) {
            case 1:
                type = ErpObjInterfaceUrlEnum.crmCreate;
                break;
            case 2:
                type = ErpObjInterfaceUrlEnum.crmUpdate;
                break;
            case 3:
                type = ErpObjInterfaceUrlEnum.crmInvalid;
                break;
            default:
                //其他类型暂不保存
                return Result2.newSuccess();
        }
        interfaceMonitorManager.saveErpInterfaceMonitor(tenantId, dcId, objApiName, type.name(), argStr, resultStr, status, callTime, returnTime, remark, TraceUtil.get(), returnTime - callTime, null);
        return Result2.newSuccess();
    }

    @Override
    public Result2<Void> saveDependData(String tenantId, SyncDataDependData syncDataDependData) {
        if(tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.NO_NEED_DO_RE_SYNC_DATA_TENANT)){
            return Result2.newSuccess();
        }
        try {
            if (syncDataDependData != null && syncDataDependData.getObjApiName() != null && syncDataDependData.getDataId() != null) {
                ErpReSyncData erpReSyncData = erpReSyncDataMongoDao.getByDataId(tenantId, syncDataDependData.getObjApiName(), syncDataDependData.getDataId(),syncDataDependData.getPloyDetailId());
                if (erpReSyncData == null) {
                    ErpReSyncData entity = new ErpReSyncData();
                    entity.setId(new ObjectId());
                    entity.setTenantId(tenantId);
                    entity.setPloyDetailId(syncDataDependData.getPloyDetailId());
                    entity.setObjApiName(syncDataDependData.getObjApiName());
                    entity.setDataId(syncDataDependData.getDataId());
                    entity.setSyncDataId(syncDataDependData.getSyncDataId());
                    CheckMessageListData checkMessageData = new CheckMessageListData();
                    List<CheckMessageData> checkMessageDataList = BeanUtil2.copyList(syncDataDependData.getDependMappingDataList(), CheckMessageData.class);
                    checkMessageData.addAll(checkMessageDataList);
                    entity.setCheckMessage(checkMessageData);
                    entity.setType(ErpReSyncDataType.DEPENDMAPPING);
                    entity.setStatus(ErpReSyncDataStatus.WAIT);
                    entity.setLocale(syncDataDependData.getLocale());
                    entity.setCreateTime(System.currentTimeMillis());
                    entity.setUpdateTime(entity.getCreateTime());
                    entity.setExpireTime(new Date());
                    int insert = erpReSyncDataMongoDao.insert(tenantId,entity);
                } else {
                    erpReSyncData.setSyncDataId(syncDataDependData.getSyncDataId());
                    List<CheckMessageData> checkMessageDataList = BeanUtil2.copyList(syncDataDependData.getDependMappingDataList(), CheckMessageData.class);
                    erpReSyncData.setLocale(StringUtils.isBlank(syncDataDependData.getLocale()) ? erpReSyncData.getLocale() : syncDataDependData.getLocale());
                    if (erpReSyncData.getCheckMessage() != null) {
                        erpReSyncData.getCheckMessage().clear();
                        erpReSyncData.getCheckMessage().addAll(checkMessageDataList);
                    } else {
                        CheckMessageListData checkMessageData = new CheckMessageListData();
                        checkMessageData.addAll(checkMessageDataList);
                        erpReSyncData.setCheckMessage(checkMessageData);
                    }

                    erpReSyncData.setUpdateTime(System.currentTimeMillis());
                    erpReSyncDataMongoDao.updateErpReSyncData(tenantId,erpReSyncData);
                }
            }
        } catch (Exception e) {
            log.warn("saveDependData failed syncDataDependData={} e={}", syncDataDependData,e);
        }
        return Result2.newSuccess();
    }

    @Override
    @Cached(expire = 120, cacheType = CacheType.LOCAL,localLimit = 500)
    public Map<String, String> getObjectMainAttribute(String tenantId) {
        Map<String,String> result= Maps.newHashMap();
        List<ErpObjectFieldEntity> objectFieldList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getObjectFieldByTenantId(tenantId,null);
        for(ErpObjectFieldEntity entity:objectFieldList){
            if(ErpFieldTypeEnum.text.equals(entity.getFieldDefineType())&&StringUtils.isNotBlank(entity.getFieldExtendValue())){
                try{
                    JSONObject extend=JSONObject.parseObject(entity.getFieldExtendValue());
                    Boolean isMainAttribute=extend.getBoolean("isMainAttribute");
                    if(isMainAttribute!=null&&isMainAttribute){
                        result.put(entity.getErpObjectApiName(),entity.getFieldApiName());
                    }
                }catch (Exception e){

                }
            }
        }
        return result;
    }

}
