package com.fxiaoke.open.erpsyncdata.preprocess.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.TriggerFlowConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataMonitoredException;
import com.fxiaoke.open.erpsyncdata.preprocess.service.remote.AuditLogger;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmRequestBaseParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponseMessage;
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchCreateObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchUpdateObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.CrmObjectDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
@Slf4j
@Data
@Service
public class CrmRemoteManagerImpl implements CrmRemoteService {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private AuditLogger auditLogger;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<List<ObjectData>> listByIdSelective(String tenantId, String objectApiName, Collection<String> ids, Collection<String> fields) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        findV3Arg.setSelectFields(new ArrayList<>(fields));
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        //子阳这边说底层已经做过优化，会自动选择该走DB还是ES
        searchQueryInfo.setSearchSource("db");
        searchQueryInfo.addFilter("_id", new ArrayList<>(ids), FilterOperatorEnum.IN);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2", "-1", "0", "1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> resultResult = objectDataServiceV3.queryList(headerObj, findV3Arg);
        ObjectDataQueryListResult result = resultResult.getData();
        if (result == null || result.getQueryResult() == null || result.getQueryResult().getDataList() == null) {
            return Result.newSuccess(Collections.emptyList());
        }
        List<ObjectData> objData = Opt.ofNullable(result)
                .map(v -> v.getQueryResult())
                .map(v -> v.getDataList())
                .orElseGet(ArrayList::new)
                .stream().map(ObjectData::convert)
                .collect(Collectors.toList());
        return Result.newSuccess(objData);
    }

    @Override
    public Result<CrmObjectDataResult> createObjectData(CrmRequestBaseParam baseParam, Map<String, Object> objectDataMap,
                                                Map<String, Object> extParamMap) {
        String restFulUrl = HttpUrlUtils.buildCreateObjectDataUrl(baseParam.getApiName(), extParamMap);
        Map<String, String> headerMap =
                HttpUrlUtils.buildHeaderMap(baseParam.getEnterpriseId(), baseParam.getCurrentEmployeeId(), HttpUrlUtils.SOURCE_HEADER);
        HttpResponseMessage httpResponseMessage;
        try {
            httpResponseMessage = OkHttpUtils.sendHttpPost(restFulUrl, headerMap, GsonUtil.toJson(objectDataMap));
        } catch (Exception exception) {
            log.error("call add object service {}, headerMap {}, objectDataMap {}, exception!", restFulUrl,
                    headerMap, objectDataMap, exception);
            if (exception.getCause() instanceof SocketTimeoutException) {
                return Result.newSuccess(new CrmObjectDataResult(CrmResultCodeEnum.INTERNAL_SERVICE_TIMEOUT));
            } else {
                return Result.newSuccess(new CrmObjectDataResult(CrmResultCodeEnum.SYSTEM_ERROR));
            }
        }
        CrmObjectDataResult crmObjectDataResult = CrmObjectCommonUtils.resolveResponse(httpResponseMessage,
                Thread.currentThread().getStackTrace()[1].getMethodName());
        if (CrmObjectCommonUtils.isResultMapSuccess(crmObjectDataResult)) {
            DocumentContext documentContext =
                    JsonPath.using(Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL,
                            Option.SUPPRESS_EXCEPTIONS)).parse(httpResponseMessage.getContent());
            Map<String, Object> validationRule;
            String id;
            Map<String, List<Map>> newObjectData;
            validationRule = documentContext.read("$.result.validationRuleMessage");
            id = documentContext.read("$.result.objectData._id");
            newObjectData = documentContext.read("$.result.newObjectData");
            // 被验证规则校验住,返回错误信息和错误码
            String validationMessage = CrmObjectCommonUtils.parseValidationRule(validationRule);
            if (StringUtils.isNotEmpty(validationMessage)) {
                crmObjectDataResult = new CrmObjectDataResult(CrmResultCodeEnum.VALIDATE_RULE_ERROR.getErrorCode(),
                        validationMessage, CrmResultCodeEnum.VALIDATE_RULE_ERROR.getErrorMessage());
            } else if (Objects.nonNull(id)) {
                crmObjectDataResult.setDataId(id);
                if ("SPUObj".equals(baseParam.getApiName())) {
                    // 商品对象附加返回同步创建产品的id
                    crmObjectDataResult.setExtraData(documentContext.read("$.result.objectData.create_Product_id"));
                }
                // 当有从对象返回时，返回从对象的信息
                if (Objects.equals(extParamMap.get("includeDetailIds"), Boolean.TRUE)) {
                    crmObjectDataResult.setData(CrmObjectCommonUtils.getAddObjectDetailDataIds(newObjectData));
                }
            } else {
                log.error("can not read object id from response!");
                return Result.newSuccess(new CrmObjectDataResult(CrmResultCodeEnum.SYSTEM_ERROR));
            }
        }
        return Result.newSuccess(crmObjectDataResult);
    }


    @Override
    public Result<CrmObjectDataResult> updateObjectData(CrmRequestBaseParam baseParam, Map<String, Object> dataMap,
                                                Map<String, Object> extParamMap) {
        String restFulUrl = HttpUrlUtils.buildUpdateObjectDataUrl(baseParam.getApiName());
        Map<String, String> headerMap =
                HttpUrlUtils.buildHeaderMap(baseParam.getEnterpriseId(), baseParam.getCurrentEmployeeId(), HttpUrlUtils.SOURCE_HEADER);
        HttpResponseMessage httpResponseMessage;
        try {
            httpResponseMessage = OkHttpUtils.sendHttpPost(restFulUrl, headerMap, GsonUtil.toJson(dataMap));
            log.info("CrmRemoteManagerImpl.updateObjectData,httpResponseMessage={}",httpResponseMessage);
        } catch (Exception exception) {
            log.error("call update object service {}, headerMap {}, objectDataMap {}, exception!", restFulUrl,
                    headerMap, dataMap, exception);
            if (exception.getCause() instanceof SocketTimeoutException) {
                return Result.newSuccess(new CrmObjectDataResult(CrmResultCodeEnum.INTERNAL_SERVICE_TIMEOUT));
            } else {
                return Result.newSuccess(new CrmObjectDataResult(CrmResultCodeEnum.SYSTEM_ERROR));
            }
        }
        CrmObjectDataResult crmObjectDataResult = CrmObjectCommonUtils.resolveResponse(httpResponseMessage,
                Thread.currentThread().getStackTrace()[1].getMethodName());
        if (CrmObjectCommonUtils.isResultMapSuccess(crmObjectDataResult)) {
            DocumentContext documentContext =
                    JsonPath.using(Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL,
                            Option.SUPPRESS_EXCEPTIONS)).parse(httpResponseMessage.getContent());
            Map<String, Object> validationRule = documentContext.read("$.result.validationRuleMessage");
            String validationMessage = CrmObjectCommonUtils.parseValidationRule(validationRule);
            if (StringUtils.isNotEmpty(validationMessage)) {
                return Result.newSuccess(new CrmObjectDataResult(CrmResultCodeEnum.VALIDATE_RULE_ERROR.getErrorCode(), validationMessage,
                        CrmResultCodeEnum.VALIDATE_RULE_ERROR.getErrorMessage()));
            }
        }
        return Result.newSuccess(crmObjectDataResult);
    }


    @Override
    public Result<JsonObject> BOMObjTreeRelatedListV1(CrmRequestBaseParam baseParam, Map<String, Object> dataMap,
                                                      Map<String, Object> extParamMap) {
        String restFulUrl = HttpUrlUtils.buildBOMObjTreeRelatedListV1Url();
        Map<String, String> headerMap =
                HttpUrlUtils.buildHeaderMap(baseParam.getEnterpriseId(), baseParam.getCurrentEmployeeId(), HttpUrlUtils.SOURCE_HEADER);
        HttpResponseMessage httpResponseMessage;
        try {
            httpResponseMessage = OkHttpUtils.sendHttpPost(restFulUrl, headerMap, GsonUtil.toJson(dataMap));
        } catch (Exception exception) {
            log.error("call update object service {}, headerMap {}, objectDataMap {}, exception!", restFulUrl,
                    headerMap, dataMap, exception);
            if (exception.getCause() instanceof SocketTimeoutException) {
                return Result.newError(ResultCodeEnum.SERVER_BUSY);
            } else {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
        }
        Result result=Result.newSuccess();
        String content = httpResponseMessage.getContent();
        if (Strings.isNullOrEmpty(content)) {
            result.setErrCode(httpResponseMessage.getHttpcode());
            result.setErrMsg(httpResponseMessage.getMessage());
            return result;
        }

        JSONObject jsonObject = JSONObject.parseObject(content);
        Integer errorCode = jsonObject.getInteger("errCode");
        if(errorCode==null) {
            if(jsonObject.containsKey("code")) {
                errorCode = jsonObject.getInteger("code");
            }
        }

        String errorMessage = jsonObject.getString("errMessage");
        if(StringUtils.isEmpty(errorMessage)) {
            if(jsonObject.containsKey("errMsg")) {
                errorMessage = jsonObject.getString("errMsg");
            }
            if(jsonObject.containsKey("message")) {
                errorMessage = jsonObject.getString("message");
            }
        }
        result.setErrCode(String.valueOf(errorCode));
        result.setErrMsg(errorMessage);

        if (errorCode != 0) {
            return result;
        }

        result.setData(jsonObject);

        return result;
    }

    @Override
    public Result<JsonObject> BomDeploy(CrmRequestBaseParam baseParam, Map<String, Object> dataMap,
                                                      Map<String, Object> extParamMap) {
        String restFulUrl = HttpUrlUtils.buildBomDeployUrl();
        Map<String, String> headerMap =
                HttpUrlUtils.buildHeaderMap(baseParam.getEnterpriseId(), baseParam.getCurrentEmployeeId(), HttpUrlUtils.SOURCE_HEADER);
        HttpResponseMessage httpResponseMessage;
        try {
            httpResponseMessage = OkHttpUtils.sendHttpPost(restFulUrl, headerMap, GsonUtil.toJson(dataMap));
        } catch (Exception exception) {
            log.error("call update object service {}, headerMap {}, objectDataMap {}, exception!", restFulUrl,
                    headerMap, dataMap, exception);
            if (exception.getCause() instanceof SocketTimeoutException) {
                return Result.newError(ResultCodeEnum.SERVER_BUSY);
            } else {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
        }
        Result result=Result.newSuccess();
        String content = httpResponseMessage.getContent();
        if (Strings.isNullOrEmpty(content)) {
            result.setErrCode(httpResponseMessage.getHttpcode());
            result.setErrMsg(httpResponseMessage.getMessage());
            return result;
        }

        JSONObject jsonObject = JSONObject.parseObject(content);
        Integer errorCode = jsonObject.getInteger("errCode");
        if(errorCode==null) {
            if(jsonObject.containsKey("code")) {
                errorCode = jsonObject.getInteger("code");
            }
        }

        String errorMessage = jsonObject.getString("errMessage");
        if(StringUtils.isEmpty(errorMessage)) {
            if(jsonObject.containsKey("errMsg")) {
                errorMessage = jsonObject.getString("errMsg");
            }
            if(jsonObject.containsKey("message")) {
                errorMessage = jsonObject.getString("message");
            }
        }
        result.setErrCode(String.valueOf(errorCode));
        result.setErrMsg(errorMessage);

        if (errorCode != 0) {
            return result;
        }

        result.setData(jsonObject);

        return result;
    }

    @Override
    public Result<JSONObject> checkModuleStatus(String tenantId, String key) {
        Map<String, Object> dataMap= Maps.newHashMap();
        dataMap.put("moduleCode",key);
        String url = HttpUrlUtils.buildCheckModuleStatusUrl();
        Map<String, String> headerMap =
                HttpUrlUtils.buildHeaderMap(tenantId, CommonConstant.SUPER_ADMIN_USER , HttpUrlUtils.ERP_SYNC_DATA);
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        try {
            response = proxyHttpClient.postUrl(url, dataMap, headerMap, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            if ("0".equals(resultObj.getString("errCode"))) {
                //调用接口成功
                return Result.newSuccess(resultObj.getJSONObject("result"));
            } else {
                //请求失败
                log.warn("post failed,url:{},param:{},header:{},response:{}", url, dataMap, headerMap, response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED);
            }
        } catch (Exception e) {
            log.error("post error,url:{},headerMap:{},params:{}", url, headerMap, dataMap, e);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        }
    }

    @Override
    public BatchCreateObjectResult batchCreateObject(CrmRequestBaseParam reqBaseParam, List<ObjectData> dataList, TriggerFlowConfig triggerFlowConfig) {
        String url = HttpUrlUtils.buildBatchCreateObjectUrl();
        HttpResponse response = sendHttpRequest(reqBaseParam, dataList, url, triggerFlowConfig);
        CrmObjectDataResult result = CrmObjectCommonUtils.resolveErrCodeFormatResponse(response);
        BatchCreateObjectResult batchCreateObjectResult = new BatchCreateObjectResult();
        if (CrmResultCodeEnum.SUCCESS.getErrorCode() == result.getErrorCode()) {
            /* 创建成功后只返回数据的id列表 */
            List<Map<String, Object>> objectList =
                    JsonPath.using(Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL,
                            Option.SUPPRESS_EXCEPTIONS)).parse(response.getBody()).read("$.result");
            List<String> objectIdList;
            if (CollectionUtils.isNotEmpty(objectList)) {
                objectIdList = Lists.newArrayListWithExpectedSize(objectList.size());
                objectList.forEach(objectMap -> objectIdList.add(String.valueOf(objectMap.get("_id"))));
            } else {
                objectIdList = Collections.EMPTY_LIST;
            }
            batchCreateObjectResult.setDataIdList(objectIdList);

            try {
//            添加审计日志(修改记录)
                final String tenantId = String.valueOf(reqBaseParam.getEnterpriseId());
                final String operatorId = String.valueOf(reqBaseParam.getCurrentEmployeeId());
                auditLogger.recordAddLog(tenantId, operatorId, reqBaseParam.getApiName(), dataList);
            } catch (Exception e) {
                log.error("CrmRemoteManagerImpl auditLogger.recordLog add error, reqBaseParam:{}", reqBaseParam, e);
            }
        } else {
            log.warn("batchCreateObject failed, dataList:{} result:{}", dataList, result);
            batchCreateObjectResult.setErrorCode(result.getErrorCode());
            batchCreateObjectResult.setErrorMessage(result.getErrorMessage());
        }
        return batchCreateObjectResult;
    }

    @Override
    public Result2<List<ObjectData>> batchGetObjectData(String tenantId, String objectApiName, List<String> objectDataIds) {
        if (CollectionUtil.isEmpty(objectDataIds)) {
            return Result2.newSuccess();
        }
        FindV3Arg findV3Arg = new FindV3Arg();
        try {
            HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);

            findV3Arg.setDescribeApiName(objectApiName);
            findV3Arg.setIncludeInvalid(true);
            SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
            searchQueryInfo.setPermissionType(0);
            searchQueryInfo.setSearchSource("db");
            searchQueryInfo.addFilter("_id", objectDataIds, FilterOperatorEnum.IN);
            //作废、正常、已删除数据都查询
            searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2", "-1", "0", "1"), FilterOperatorEnum.IN);
            findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> listResult =
                    objectDataServiceV3.queryList(headerObj, findV3Arg);

            if (!listResult.isSuccess()) {
                log.warn("batchGetObjectData error :{}:{}", listResult.getCode(), listResult.getMessage());
                return Result2.newError(listResult.getCode(), listResult.getMessage());
            }

            List<ObjectData> result = new ArrayList<>();
            Optional.ofNullable(listResult.getData())
                    .map(ObjectDataQueryListResult::getQueryResult)
                    .map(ObjectDataQueryListResult.QueryResult::getDataList)
                    .ifPresent(list -> list.stream().map(ObjectData::convert).forEach(result::add));
            return Result2.newSuccess(result);
        } catch (Exception e) {
            log.warn("batchGetObjectData error :{}:{}", e.getMessage(), JSONObject.toJSONString(findV3Arg));
            return Result2.newError(com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum.UNKNOWN.getErrCode(), e.getMessage());
        }
    }

    @Override
    public BatchUpdateObjectResult batchUpdateObject(CrmRequestBaseParam reqBaseParam, List<ObjectData> dataList, TriggerFlowConfig triggerFlowConfig) {
//        获取修改前数据,用于日志记录
        List<ObjectData> beforeList = new ArrayList<>();
        final List<String> ids = dataList.stream().map(ObjectData::getId).distinct().collect(Collectors.toList());
        final String tenantId = String.valueOf(reqBaseParam.getEnterpriseId());
        try {
            final Result2<List<ObjectData>> crmDataList = batchGetObjectData(tenantId, reqBaseParam.getApiName(), ids);
            if (crmDataList.isSuccess()) {
                beforeList = crmDataList.getData();
            }
        } catch (Exception e) {
            log.error("CrmRemoteManagerImpl getOldData error, reqBaseParam:{}", reqBaseParam, e);
        }

        String url = HttpUrlUtils.buildBatchUpdateObjectUrl();
        /* 构造header */
        HttpResponse response = sendHttpRequest(reqBaseParam, dataList, url, triggerFlowConfig);
        CrmObjectDataResult result = CrmObjectCommonUtils.resolveErrCodeFormatResponse(response);
        if (CrmResultCodeEnum.SUCCESS.getErrorCode() != result.getErrorCode()) {
            log.warn("batchUpdateObject failed, dataList:{} result:{}", dataList, result);
            return new BatchUpdateObjectResult(result.getErrorCode(), result.getErrorMessage());
        }

        if (CollectionUtils.isEmpty(beforeList)) {
            return new BatchUpdateObjectResult();
        }

        try {
//            添加审计日志(修改记录)
            List<ObjectData> afterDataList = dataList;
            if (beforeList.size()!= ids.size()) {
                final Set<String> foundIds = beforeList.stream().map(ObjectData::getId).collect(Collectors.toSet());
                ids.removeAll(foundIds);
                log.warn("CrmRemoteManagerImpl batchUpdateObject beforeSize!=dataSize, tenantId:{} ApiName:{} notFoundIds:{}", tenantId, reqBaseParam.getApiName(), ids);
                afterDataList = dataList.stream().filter(data ->foundIds.contains(data.getId())).collect(Collectors.toList());
            }
            final String operatorId = String.valueOf(reqBaseParam.getCurrentEmployeeId());
            auditLogger.recordModifyLog(tenantId, operatorId, reqBaseParam.getApiName(), beforeList, afterDataList);
        } catch (Exception e) {
            log.error("CrmRemoteManagerImpl auditLogger.recordLog update error, reqBaseParam:{}", reqBaseParam, e);
        }

        return new BatchUpdateObjectResult();
    }

    /**
     * 发送http请求
     *
     * @param reqBaseParam
     * @param dataList
     * @param url
     * @param triggerFlowConfig
     * @return
     */
    private HttpResponse sendHttpRequest(CrmRequestBaseParam reqBaseParam, List<ObjectData> dataList,
                                         String url, TriggerFlowConfig triggerFlowConfig) {
        /* 构造header */
        Map<String, String> headerMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_ONE);
        headerMap.put("Content-type", "application/json;charset=utf-8");
        /* 构造body */
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_TWO);
        Map<String, Object> contextMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_TWO);
        contextMap.put("x-fs-userInfo", String.valueOf(reqBaseParam.getCurrentEmployeeId()));
        contextMap.put("x-fs-ei", String.valueOf(reqBaseParam.getEnterpriseId()));
        contextMap.put("errorMessageUseDataId", true);
        //支持工作流，不支持审批流
        contextMap.put("triggerWorkflow", String.valueOf(triggerFlowConfig.checkTriggerWorkFlow()));
        paramMap.put("context", contextMap);
        paramMap.put("objectDataList", dataList);
        try {
            return OkHttpUtils.postNonblocking(url, headerMap, JSONObject.toJSON(paramMap).toString());
        } catch (IOException e) {
            throw ErpSyncDataException.wrap(e);
        }
    }


}
