package com.fxiaoke.open.oasyncdata.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * oa对接日志
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OAMessageTypeVO implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    /**
     * 选项类型
     */
    private String objApiName;

    /**
     * 选项描述
     */

    private String description;

    /**
     * 场景type
     */
    private String type;


}