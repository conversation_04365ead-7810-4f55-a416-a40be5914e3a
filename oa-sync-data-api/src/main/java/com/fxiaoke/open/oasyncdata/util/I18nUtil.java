package com.fxiaoke.open.oasyncdata.util;


import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


/**
 * 国际化相关的工具类
 */
@Slf4j
public class I18nUtil {
    public static ThreadLocal<String> threadLocal = new ThreadLocal<>();

    static {
        try {
            I18nClient.getInstance().initWithTags("eip");
        } catch (Throwable e) {
            log.warn("init error", e);
        }
    }

    public static String get(String key) {
        String value = I18nClient.getInstance().get(key, 0, getLanguage());
        if (value == null) {
            throw new IllegalStateException("I18N value not found, key:" + key);
        }
        return value;
    }

    public static String get(String key, String defaultVal) {
        if(Strings.isNullOrEmpty(key)){
            return key;
        }
        String value = I18nClient.getInstance().get(key, 0, getLanguage());
        if (value == null) {
            return defaultVal;
        }
        return value;
    }

    public static String getLanguage() {
        String language;
        String step = "ThreadLocal";
        try {
            language = threadLocal.get();
            if (StringUtils.isNotEmpty(language)) {
                return language;
            }
            step = "TraceContext";
            language = TraceContext.get().getLocale();
            if (StringUtils.isNotEmpty(language)) {
                return language;
            }
//            step = "FcpContext";
//            FcpRequest fcpRequest = DefaultFcpServiceContextManager.getInstance().getContext().getFcpRequest();
//            if (fcpRequest != null) {
//                Locale locale = DefaultFcpServiceContextManager.getInstance().getContext().getAuthInfo().getLocale();
//                language = locale.toLanguageTag();
//            } else {
//                language = "zh-CN";
//                log.debug("get language Exception, use default language: {} step: {}", language, step);
//            }
        } catch (Throwable e) {
            language = "zh-CN";
            log.warn("get language Exception, use default language: {} step: {}", language, step, e);
        }
        return language;
    }

//    public static String getKeyByResultCode(ResultCode resultCode) {
//        return "eip.common.resultcode_" + resultCode.getErrorCode() + ".description";
//    }

//    public static String getKeyByCommonEnum(Enum e, String projectName) {
//        //获取属性名
//        StackTraceElement[] temp = Thread.currentThread().getStackTrace();
//        StackTraceElement a = temp[2];
//        String methodName = a.getMethodName();
//        String fieldName = methodName.replace("get", "").toLowerCase();
//        Class clazz = e.getClass();
//        return "eip." + projectName + "." + clazz.getSimpleName().toLowerCase() + "_" + e.name().toLowerCase() + "." + fieldName;
//    }
}
