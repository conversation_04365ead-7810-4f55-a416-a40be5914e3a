package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.crmrestapi.common.data.TypeHashMap;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ObjectData extends TypeHashMap<String, Object> {
    public static ObjectData convert(Map map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        ObjectData objectData = new ObjectData();
        objectData.putAll(map);
        return objectData;
    }

    public String getApiName() {
        return this.getString("object_describe_api_name");
    }

    public void putApiName(String apiName) {
        this.put("object_describe_api_name", apiName);
    }

    public String getId() {
        return this.getString("_id");
    }

    public void putId(String id) {
        this.put("_id", id);
    }

    public void safePutFrom(ObjectData from, String key) {
        if (from.containsKey(key)) {
            this.put(key, from.get(key));
        }
    }

    public List<String> getOwner() {
        Object ownerId = this.get("owner");
        if (ownerId == null) {
            return new ArrayList<>(0);
        }
        return (List<String>) ownerId;
    }

    public List<String> getOutOwner() {
        Object outOwnerId = this.get("out_owner");
        if (outOwnerId == null) {
            return new ArrayList<>(0);
        }
        return (List<String>) outOwnerId;
    }

    public void putOwner(List<String> ownerId) {
        this.put("owner", ownerId);
    }

    public List<String> getCreatedBy() {
        Object createdBy = this.get("created_by");
        if (createdBy == null) {
            return new ArrayList<>(0);
        }
        return (List<String>) createdBy;
    }

    public void putCreatedBy(List<String> createdBy) {
        this.put("created_by", createdBy);
    }

    public void putTenantId(String tenantId) {
        this.put("tenant_id", tenantId);
    }

    public void putOutTenantId(Long outTenantId) {
        this.put("out_tenant_id", outTenantId);
    }

    public String getName() {
        return this.getString("name");
    }

    public String getTenantId() {
        return this.getString("tenant_id");
    }

    public Long getVersion() {
        return this.getLong("version");
    }

    public String getLifeStatus() {
        return this.getString("life_status");
    }

    public String getLastModifiedBy() {
        Object lastModifiedBy = this.get("last_modified_by");
        if (lastModifiedBy == null) {
            return null;
        }
        if (lastModifiedBy instanceof List) {
            return ((List) lastModifiedBy).get(0).toString();
        }
        return lastModifiedBy.toString();
    }

    public String getRecordType() {
        return this.getString("record_type");
    }

    public String getMapValue(String apiName, String key) {
        Map map = getMap(apiName + "__r");
        if (map == null) {
            return null;
        }
        return (String) map.get(key);
    }

    public Object getReferName(String apiName) {
        return this.getString(apiName + "__r");
    }

    public void putLastModifiedBy(String lastModifyBy) {
        this.put("last_modified_by", Lists.newArrayList(lastModifyBy));
    }
}
