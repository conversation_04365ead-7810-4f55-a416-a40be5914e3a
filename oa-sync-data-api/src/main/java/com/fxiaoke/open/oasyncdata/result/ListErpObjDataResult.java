package com.fxiaoke.open.oasyncdata.result;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Data
@ApiModel("erp对象数据列表结果")
public class ListErpObjDataResult implements Serializable {
    private static final long serialVersionUID = -7995893623707208968L;
    @ApiModelProperty("是否全部数据")
    private boolean complete = true;
    @ApiModelProperty("erp数据列表")
    private List<ErpObjDataResult> erpObjDataResultList ;

    @Override
    public String toString() {
        if (erpObjDataResultList.size()>200){
            return String.format("dataList size:%s,first:%s", erpObjDataResultList.size(), JSON.toJSONString(erpObjDataResultList.get(0)));
        }
        return JSON.toJSONString(this);
    }
}
