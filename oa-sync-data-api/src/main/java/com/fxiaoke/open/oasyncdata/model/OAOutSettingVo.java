package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/1/10 15:36
 * @Version 1.0
 */
@Data
public class OAOutSettingVo implements Serializable {

    private static final long serialVersionUID = -5387809623137830022L;
    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 开启状态（1。开启 2.关闭）
     */
    private String status;

    /**
     * 对象类型
     * @see ObjectApiEnum
     */
    private String objApiName;
}
