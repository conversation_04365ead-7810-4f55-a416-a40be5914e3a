package com.fxiaoke.open.oasyncdata.model;

import com.google.common.base.MoreObjects;

/**
 * Http请求返回
 *
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
public class HttpResponseMessage {

    /**
     * http 响应码
     */
    private String httpcode;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应内容
     */
    private String content;

    public String getHttpcode() {
        return httpcode;
    }

    public void setHttpcode(String httpcode) {
        this.httpcode = httpcode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("httpcode", httpcode).add("message", message)
            .add("content", content).toString();
    }

}
