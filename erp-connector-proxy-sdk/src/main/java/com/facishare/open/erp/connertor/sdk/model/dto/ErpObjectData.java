package com.facishare.open.erp.connertor.sdk.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/28 10:46:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErpObjectData implements Serializable {
    private Map<String, Object> master;
    private Map<String, List<Map<String, Object>>> details;
}
