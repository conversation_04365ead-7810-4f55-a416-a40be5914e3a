package com.facishare.open.erp.connertor.sdk.model;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/3/20 19:23:20
 */
public interface GetUserToken {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg extends Base.Arg {
        private String appId;
        private String appSecret;
        private String redirectUri;
        private String verificationCode;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Result extends Base.Result {
        private String name;
        private String connectParam;
        private Long expireTime;
    }
}
