package com.facishare.open.erp.connertor.sdk.controller;

import com.facishare.open.erp.connertor.sdk.model.GetById;
import com.facishare.open.erp.connertor.sdk.model.ListByTime;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/3/28 17:24:33
 */
public interface IConnectorController {
    @RequestMapping("getById")
    GetById.Result getById(@RequestBody GetById.Arg arg);

    @RequestMapping("listByTime")
    ListByTime.Result listByTime(@RequestBody ListByTime.Arg arg);
}
