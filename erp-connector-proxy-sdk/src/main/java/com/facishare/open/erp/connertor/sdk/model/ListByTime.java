package com.facishare.open.erp.connertor.sdk.model;

import com.facishare.open.erp.connertor.sdk.model.dto.ErpObjectData;
import com.facishare.open.erp.connertor.sdk.model.dto.Filter;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 19:23:20
 */
public interface ListByTime {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg extends Base.ConnectorArg {
        @ApiModelProperty("对象apiName")
        private String objAPIName;
        @ApiModelProperty("对象扩展值")
        private String extendValue;
        @ApiModelProperty("排序")
        private String order;

        @ApiModelProperty("最小时间")
        private Long startTime;

        @ApiModelProperty("最大时间")
        private Long endTime;

        /**
         * operate可参考
         *
         * @see com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum
         */
        @ApiModelProperty("其他筛选条件")
        private List<Filter> filters;

        @ApiModelProperty("是否包含明细数据")
        private boolean includeDetail = true;

        @ApiModelProperty("偏移量")
        private Integer offset = 0;

        @ApiModelProperty("限制数量")
        private Integer limit = 100;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Result extends Base.MonitorResult {
        /**
         * 总数，暂不使用
         */
        private Integer totalNum = 0;
        private List<ErpObjectData> erpObjectData;
    }
}
