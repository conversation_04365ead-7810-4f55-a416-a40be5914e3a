package com.facishare.open.erp.connertor.sdk.model.facebook;

import com.facishare.open.erp.connertor.sdk.model.Base;
import com.facishare.open.erp.connertor.sdk.model.dto.IdAndName;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20 19:23:20
 */
public interface GetAllForm {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg extends Base.ConnectorArg {
        private String pageId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Result extends Base.Result {
        private List<IdAndName> forms;
    }
}
