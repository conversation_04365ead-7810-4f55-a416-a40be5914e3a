package com.facishare.open.erp.connertor.sdk.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.open.erp.connertor.sdk.annotation.TenantId;
import lombok.*;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/22 14:53:08
 */
public interface Base {
    @Data
    class Arg implements Serializable {
        @TenantId
        private String tenantId;

        @JSONField(serialize = false)
        private String connectId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        public static final int ERP_ERROR = -1;
        public static final int SYSTEM_ERROR = -2;

        private int errCode;
        private String errMessage;

        public static <T extends Result> T error(final int errCode, final String errMessage, final Class<T> returnType) throws InstantiationException, IllegalAccessException {
            final T tResult = returnType.newInstance();
            tResult.setErrCode(errCode);
            tResult.setErrMessage(errMessage);

            return tResult;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class ConnectorArg extends Arg {
        private String connectParam;

        @JSONField(serialize = false)
        private Long expireTime;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class MonitorResult extends Result {
        private String url;
        private Map<String,String> headerMap;
        private Serializable params;

        private String responseBody;
        private Integer responseStatus;

        private Long cost;
    }

}
