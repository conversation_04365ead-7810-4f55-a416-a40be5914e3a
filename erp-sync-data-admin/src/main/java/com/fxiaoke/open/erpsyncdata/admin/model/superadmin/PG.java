package com.fxiaoke.open.erpsyncdata.admin.model.superadmin;

import cn.hutool.core.lang.Dict;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/6/9
 */
public class PG {

    @Data
    @Accessors(chain = true)
    public static class TableIndexInfo {
        private String dbName;
        private String tenantId;
        private String tableName;
        private String indexName;
        /**
         * 索引语句
         */
        private String indexDef;

        /**
         * 索引名称格式，替换tenantId为{tenantId}
         */
        private String indexNamePattern;
        /**
         * 是否存在连接信息
         */
        private boolean existConnectInfo;

        /**
         * 嵌套方便展示表
         */
        private List<TableIndexInfo> children;
    }


    /**
     * 批量修改索引的参数
     */
    @Data
    public static class ModIndexArg {
        private String dbName;

        /**
         * 限定企业,逗号分割
         */
        private String tenantIdStr;

        /**
         * 索引语句
         */
        private String indexDef;

        /**
         * 索引名称
         */
        private String indexNamePattern;
        /**
         * 是否唯一索引，默认false
         */
        private Boolean uniqueIndex=false;
    }

    @Data
    public static class SqlArg {
        private String dbName;
        private String sql;
        private int limit = 20;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString(callSuper = true)
    @FieldNameConstants
    public static class BatchUpdateArg extends SqlArg {
        /**
         * 更新值来源
         */
        private List<Dict> items;
        /**
         * 查询结果id列
         */
        private String resultIdField;
        /**
         * 新值列
         */
        private String newValueField;
        /**
         * 表名
         */
        private String tableName;
        /**
         * 更新的id列名
         */
        private String updateIdField;
        /**
         * 更新列
         */
        private String updateField;
    }


    @Data
    public static class CompareIndexArg {
        private String dbName;

        /**
         * 限定企业
         */
        private String tenantIdStr;

        /**
         * 是否删除索引，默认是false
         */
        private Boolean deleteIndex=false;
    }

    @Data
    public static class BrushTableDataArg {
        /**
         * 指向目标数据库
         */
        private String destDataBaseTenantId;

        /**
         * 限定企业
         */
        private String sourceTenantId;

    }

}
