package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate.K3UltimateSubscribeEventModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpCustomInterfaceEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ApiFormatResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
public class ApiFormatResult2 implements Serializable {
    @ApiModelProperty("对象apiName")
    public String objApiName;
    @ApiModelProperty("API接口类型")
    public ErpObjInterfaceUrlEnum apiType;
    @ApiModelProperty("API接口类型名称")
    private String apiTypeName;
    @ApiModelProperty("当前选中的接口类型")
    private ErpObjInterfaceTypeEnum selectedInterfaceType;
    @ApiModelProperty("标准接口数据")
    private ApiFormatResult standardApi;
    @ApiModelProperty("自定义接口数据")
    private ErpCustomInterfaceEntity customApi;
    @ApiModelProperty("自定义函数数据")
    public CustomFunc customFunc;
    @ApiModelProperty("事件订阅")
    public K3UltimateSubscribeEventModel subscribeEventModel;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomFunc implements Serializable {
        @ApiModelProperty("自定义函数apiName")
        public String funcApiName;
    }
}
