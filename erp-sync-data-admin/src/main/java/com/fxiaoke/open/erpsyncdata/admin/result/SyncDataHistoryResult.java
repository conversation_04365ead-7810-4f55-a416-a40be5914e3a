package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.admin.model.RecommendSolution;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncDataHistoryResult implements Serializable {
    private String id;
    @ApiModelProperty("操作人员工id")
    private String operatorId;
    @ApiModelProperty("操作人名称")
    private String operatorName;
    @ApiModelProperty("源行为的类型")
    private Integer sourceEventType;
    @ApiModelProperty("源行为的名称")
    private String sourceEventTypeName;
    @ApiModelProperty("目标行为的类型")
    private Integer destEventType;
    @ApiModelProperty("目标行为的名称")
    private String destEventTypeName;
    /**
     * {@link SyncStatusEnum}
     */
    @ApiModelProperty("同步状态")
    private Integer status;
    @ApiModelProperty("同步状态名称")
    private String statusName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作时间")
    private Long updateTime;
    @ApiModelProperty("同步日志id")
    private String syncLogId;
    @ApiModelProperty("同步快照id")
    private String syncDataId;
    @ApiModelProperty("最后同步节点及状态")
    private String lastSyncNodeAndStatus;
    @ApiModelProperty("版本")
    private String dataVersion;
    @ApiModelProperty("推荐解决方案")
    private List<RecommendSolution> recommendSolutions;
}
