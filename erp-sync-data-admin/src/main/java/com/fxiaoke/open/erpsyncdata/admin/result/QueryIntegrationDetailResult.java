package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.admin.data.DataCenterData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjDispatchPriorityConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/3/3 11:08 集成流的详情，包括校验的错误信息
 * @Version 1.0
 */
@Data
@ApiModel
public class QueryIntegrationDetailResult implements Serializable {


    @ApiModelProperty("集成流id")
    private String id;
    @ApiModelProperty("集成流名称")
    private String integrationStreamName;
    @ApiModelProperty("源系统节点")
    private SourceSystemNode sourceSystemNode;
    @ApiModelProperty("目标系统节点")
    private DestSystemNode destSystemNode;
    @ApiModelProperty("数据范围")
    private SyncConditionsNode syncConditionsNode;
    @ApiModelProperty("检查中间表节点")
    private CheckSyncDataMappingNode checkSyncDataMappingNode;
    @ApiModelProperty("通过源数据查询crm节点")
    private QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeBySource;
    @ApiModelProperty("字段映射")
    private FieldMappingNode fieldMappingNode;
    @ApiModelProperty("通过转换后数据查询crm节点")
    private QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeByDest;
    @ApiModelProperty("同步前自定义函数APIName")
    public BeforeFunctionNode beforeFunctionNode;
    @ApiModelProperty("同步中自定义函数APIName")
    public DurationFunctionApiNode durationFunctionApiNode;
    @ApiModelProperty("同步前自定义函数APIName")
    public AfterFunctionNode afterFunctionNode;
    @ApiModelProperty("同步规则")
    private SyncRulesWebData syncRules;
    @ApiModelProperty("回写Crm节点")
    private ReverseWriteNode reverseWriteNode;
    @ApiModelProperty("通知节点节点")
    private NotifyComplementNode notifyComplementNode;
    @ApiModelProperty("错误重试节点")
    private ReSyncErrorDataNode reSyncErrorDataNode;
    @ApiModelProperty("启用状态，1启用 2停用")
    private Integer status;
    @ApiModelProperty("状态名称，启用，停用")
    private String statusName;
    @ApiModelProperty("异常状态显示")
    private Boolean isValid;

    @Data
    @ApiModel
    public static class SourceSystemNode implements Serializable {
        @ApiModelProperty("源企业id")
        private List<TenantData> sourceTenantDatas = Lists.newArrayList();
        @ApiModelProperty("源企业类型")
        private Integer sourceTenantType;
        @ApiModelProperty("源企业主对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("源企业主对象名称")
        private String sourceObjectName;
        @ApiModelProperty("源数据中心")
        private DataCenterData sourceDc;
        @ApiModelProperty("源系统错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
        @ApiModelProperty("需要返回字段ApiName")
        private Map<String,List<String>> objApiName2NeedReturnFieldApiName;
        @ApiModelProperty("监听CRM具体dataSource事件")
        private NotPassDataSourceStatus notPassDataSourceStatus;
        @ApiModelProperty("是否开启对象优先级配置,不传值不会修改，传值才会修改")
        private Boolean enableObjDispatchPriorityConfig;
        @ApiModelProperty("对象分发优先级配置")
        private ObjDispatchPriorityConfig objDispatchPriorityConfig;
    }

    @Data
    @ApiModel
    public static class NotPassDataSourceStatus implements Serializable {
        @ApiModelProperty("状态，开启：true,关闭：false")
        private Boolean status=false;//开启导入触发同步，跟TENANT_NEED_PASS_DATASOURCE配置是相反的，配置配的是过滤的dataSource
        @ApiModelProperty("开启触发的动作")
        private List<String> dataSourceList= Lists.newArrayList();//跟TENANT_NEED_PASS_DATASOURCE配置是相反的，配置配的是过滤的dataSource
    }

    @Data
    @ApiModel
    public static class DestSystemNode implements Serializable {
        @ApiModelProperty("目标企业Id")
        private List<TenantData> destTenantDatas = Lists.newArrayList();
        @ApiModelProperty("目标企业类型")
        private Integer destTenantType;
        @ApiModelProperty("目标数据中心")
        private DataCenterData destDc;
        @ApiModelProperty("目标企业主对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("目标企业主对象名称")
        private String destObjectName;
        @ApiModelProperty("目标系统错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class SyncConditionsNode implements Serializable {
        @ApiModelProperty("主对象数据范围")
        private SyncConditionsData syncConditions;
        @ApiModelProperty("从对象数据范围")
        private List<SyncConditionsData> detailObjectSyncConditions;
        @ApiModelProperty("同步数据范围错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString(callSuper = true)
    public static class BeforeFunctionNode extends FunctionNode {
        @ApiModelProperty("同步前自定义函数APIName")
        private String beforeFuncApiName;
    }

    @Data
    @ApiModel
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString(callSuper = true)
    public static class DurationFunctionApiNode extends FunctionNode {
        @ApiModelProperty("同步中自定义函数APIName")
        private String duringFuncApiName;
    }

    @Data
    @ApiModel
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString(callSuper = true)
    public static class AfterFunctionNode extends FunctionNode {
        @ApiModelProperty("同步后自定义函数APIName")
        private String afterFuncApiName;
    }

    @Data
    @ApiModel
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FunctionNode implements Serializable {
        @ApiModelProperty("自定义函数命名空间")
        private String nameSpace;
        @ApiModelProperty("自定义函数错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class FieldMappingNode implements Serializable {
        @ApiModelProperty("主对象字段映射")
        private ObjectMappingResult fieldMappings;
        @ApiModelProperty("从对象相关信息")
        private List<ObjectMappingResult> detailObjectMappings = Lists.newArrayList();
        @ApiModelProperty("字段映射错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class ReverseWriteNode implements Serializable {
        @ApiModelProperty("主对象信息")
        private ObjectMappingResult fieldMappings;
        @ApiModelProperty("从对象信息")
        private List<ObjectMappingResult> detailObjectMappings = Lists.newArrayList();
        @ApiModelProperty("字段映射错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class SyncConditionsQueryDataNode implements Serializable{
        @ApiModelProperty("查询crm")
        private List<QueryObjectMappingResult> queryObjectMappingData; //查询crm,and
        //不支持明细
        @ApiModelProperty("同步条件")
        private Integer syncCondition; //同步条件，1：查到同步，2：查不到同步

        @ApiModelProperty("字段映射错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class CheckSyncDataMappingNode implements Serializable{
        @ApiModelProperty("查询crm")
        private QueryObjectMappingResult queryObjectMappingData; //查询crm
        @ApiModelProperty("对象字段映射到中间表")
        private List<IdFieldMappingResult> source2SyncDataMapping;//集成流源对象字段映射到中间表、查出来的目标对象字段映射到中间表
        @ApiModelProperty("明细")
        private List<DetailQueryData2SyncDataMappingResult> detailCheckSyncDataMappingData;//明细
        @ApiModelProperty("字段映射错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class QueryCrmObject2DestNode implements Serializable{
        @ApiModelProperty("查询crm")
        private List<QueryObjectToDestObjectResult> queryObjectToDestObject;
        @ApiModelProperty("明细")
        private List<List<QueryObjectToDestObjectResult>> detailQueryData2DestDataMapping;//明细
        @ApiModelProperty("字段映射错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

    @Data
    @ApiModel
    public static class NotifyComplementNode implements Serializable {
        @ApiModelProperty("通知类型")
        private List<NotifyType> notifyType;

        @ApiModelProperty("是否需要通知回写crm失败")
        private Boolean needNotifyReverseWrite2CrmFailed;

        @ApiModelProperty("是否需要通知同步后函数执行失败")
        private Boolean needNotifyAfterFuncFailed;

        @ApiModelProperty("通知状态条件")
        private List<Integer> notifyStatus;

        @ApiModelProperty("状态详情 通过  包含  xx关键字来筛选")
        private List<IntegrationStreamNodesData.NotifyConditionFilter> notifyConditionFilters;

        @ApiModelProperty("通知人员的列表")
        private List<Integer> notifyEmployees;

        @ApiModelProperty("通知人员的角色")
        private List<String> notifyRoles;

        @ApiModelProperty("通知的数据相关变量 数据负责人、数据创建人")
        private List<String> dataRelatedOwner;
        /**
         * 转换表达式语言
         */

        public String generateExpression(){
            if(CollectionUtils.isNotEmpty(notifyConditionFilters)){
                List<List<FilterData>> rulesWithOr=Lists.newArrayList();
                for (IntegrationStreamNodesData.NotifyConditionFilter notifyConditionFilter : notifyConditionFilters) {
                    FilterData filterData=new FilterData();
                    filterData.setFieldValue(Lists.newArrayList(notifyConditionFilter.getFieldValue()));
                    filterData.setOperate(notifyConditionFilter.getOperator());
                    filterData.setFieldType(FieldType.STRING);
                    filterData.setFieldApiName(notifyConditionFilter.getFieldApiName());
                    List<FilterData> andFilterData=Lists.newArrayList(filterData);
                    rulesWithOr.add(andFilterData);
                }
                String expression = ConditionUtil.parseToOrExpression(rulesWithOr);
                return expression;
            }
            return null;
        }
    }

    @Data
    @ApiModel
    public static class ReSyncErrorDataNode implements Serializable{
        @ApiModelProperty("重试条件")
        private String reSyncCondition;//未使用
        @ApiModelProperty("重试时间间隔")
        private Integer reSyncTimeInterval;//单位固定分钟
        @ApiModelProperty("是否立即重试")
        private Boolean reSyncRightNow;
        @ApiModelProperty("重试次数上限")
        private Integer reSyncTopLimit;
        @ApiModelProperty("自动重试节点错误信息")
        private UpdateIntegrationStreamResult.ErrorMsg errorMsg;
    }

}
