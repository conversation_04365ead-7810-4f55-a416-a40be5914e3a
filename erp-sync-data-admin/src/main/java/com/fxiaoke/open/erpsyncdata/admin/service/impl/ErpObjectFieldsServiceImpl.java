package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener.ErpObjectFieldsImportListener;
import com.fxiaoke.open.erpsyncdata.admin.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.model.K3CSaveType;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.*;
import com.fxiaoke.open.erpsyncdata.admin.result.DeleteErpObjFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.admin.utils.ExcelUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.MetaDataInfoManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoResult;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.*;

/**
 * <AUTHOR>
 * @Date: 14:02 2020/8/19
 * @Desc:
 */
@Service
@Slf4j
public class ErpObjectFieldsServiceImpl implements ErpObjectFieldsService {
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private MetaDataInfoManager metaDataInfoManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private FileManager fileManager;
    @Autowired
    @Lazy
    private FileService fileService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private UserCenterService userCenterService;

    /**
     * objApiName可以传空
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    public Result<List<ErpObjectFieldResult>> query(String tenantId, String objApiName, String dataCenterId) {
        List<ErpObjectFieldEntity> erpObjectFieldEntities;
        if (StringUtils.isNotEmpty(objApiName)) {
            //ErpObjectFieldEntity arg = ErpObjectFieldEntity.builder().tenantId(tenantId).dataCenterId(dataCenterId).erpObjectApiName(objApiName).build();
            erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findData(tenantId,dataCenterId,objApiName);
        } else {
            ErpObjectFieldEntity queryArg = ErpObjectFieldEntity.builder().tenantId(tenantId).dataCenterId(dataCenterId).build();
            erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryArg);
        }
        if (CollectionUtils.isEmpty(erpObjectFieldEntities)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        //去掉根据字段的创建时间进行排序的逻辑，新需求需要根据用户指定的顺序来显示
        //erpObjectFieldEntities = erpObjectFieldEntities.stream().sorted(Comparator.comparing(ErpObjectFieldEntity::getCreateTime)).collect(Collectors.toList());
        List<ErpObjectFieldResult> erpObjectFields = Lists.newArrayList();
        for (ErpObjectFieldEntity erpObjectFieldEntity : erpObjectFieldEntities) {
            ErpObjectFieldResult erpObjectFieldResult = convert2ErpObjectFieldResult(erpObjectFieldEntity);
            erpObjectFields.add(erpObjectFieldResult);
        }
        fillFieldExtend(erpObjectFields, tenantId,dataCenterId);
        return Result.newSuccess(erpObjectFields);
    }

    private static ErpObjectFieldResult convert2ErpObjectFieldResult(final ErpObjectFieldEntity erpObjectFieldEntity) {
        ErpObjectFieldResult erpObjectFieldResult = new ErpObjectFieldResult();
        BeanUtils.copyProperties(erpObjectFieldEntity, erpObjectFieldResult);
        String fieldExtendValueStr = erpObjectFieldEntity.getFieldExtendValue();
        if (StringUtils.isNotEmpty(fieldExtendValueStr)) {
            Object fieldExtendValue = GsonUtil.fromJson(fieldExtendValueStr, Object.class);
            erpObjectFieldResult.setFieldExtendValue(fieldExtendValue);
        }
        return erpObjectFieldResult;
    }

    public Result<ErpObjectFieldResult> queryById(String tenantId, String id) {
        ErpObjectFieldEntity entry = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTenantIdAndId(tenantId, id);
        if (entry == null) {
            return Result.newSuccess();
        }
        ErpObjectFieldResult erpObjectFieldResult = convert2ErpObjectFieldResult(entry);
        return Result.newSuccess(erpObjectFieldResult);
    }

    @Override
    public Result<List<ErpObjectFieldResult>> queryErpObjectFieldsByObjApiNameAndDcId(String tenantId, int userId, ErpObjectApiNameArg arg, String dataCenterId) {
        return query(tenantId, arg.getErpObjectApiName(), dataCenterId);
    }

    @Override
    public Result<QueryResult<List<ErpObjectFieldResult>>> pageErpObjectFieldsByObjApiName(String tenantId,String dataCenterId, int userId, QueryErpObjectFieldsArg pageArg) {
        QueryResult<List<ErpObjectFieldResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(pageArg.getPageNum());
        queryResult.setPageSize(pageArg.getPageSize());
        ErpFieldDataMappingEntity arg = new ErpFieldDataMappingEntity();
        arg.setTenantId(tenantId);
        arg.setDataType(ErpFieldTypeEnum.employee);
        Integer total = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).countByTenantIdAndObjectApiName(tenantId, pageArg.getErpObjectApiName(), pageArg.getQueryStr());
        queryResult.setTotal(total);
        if (total == 0) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<ErpObjectFieldEntity> erpObjectFieldEntities =
                erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryFieldsByTenantIdAndObjectApiName(tenantId, pageArg.getErpObjectApiName(), pageArg.getQueryStr(), queryResult.getPageSize(), (pageArg.getPageNum() - 1) * pageArg.getPageSize());
        if (CollectionUtils.isEmpty(erpObjectFieldEntities)) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<ErpObjectFieldResult> erpObjectFields = Lists.newArrayList();
        for (ErpObjectFieldEntity erpObjectFieldEntity : erpObjectFieldEntities) {
            ErpObjectFieldResult erpObjectFieldResult = convert2ErpObjectFieldResult(erpObjectFieldEntity);
            erpObjectFields.add(erpObjectFieldResult);
        }
        fillFieldExtend(erpObjectFields, tenantId,dataCenterId);
        queryResult.setDataList(erpObjectFields);
        return Result.newSuccess(queryResult);
    }

    public void fillFieldExtend(List<ErpObjectFieldResult> erpObjectFields, String tenantId, String dataCenterId) {
        Map<String, List<ErpObjectFieldResult>> collect = erpObjectFields.stream()
                .collect(Collectors.groupingBy(ErpObjectFieldResult::getErpObjectApiName));
        collect.forEach((objApiName, erpObjectFieldList) -> {
            //需要查询真实apiName
            ErpObjExtendDto objExtendDto = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findObjExtendDtoBySplit(tenantId, objApiName);
            if (objExtendDto == null) {
                log.info("field obj not found,tenantId:{},objApiName:{}", tenantId, objApiName);
                return;
            }
            String realApiName = objExtendDto.parseRealApiCode();
            if (CollectionUtils.isEmpty(erpObjectFieldList)) {
                return;
            }
            Map<String, ErpFieldExtendEntity> extendEntityMap = erpFieldManager.queryFieldMap(tenantId, dataCenterId, realApiName);
            for (ErpObjectFieldResult erpObjectField : erpObjectFieldList) {
                ErpFieldExtendEntity extend = extendEntityMap.get(erpObjectField.getFieldApiName());
                if (extend != null) {
                    erpObjectField.setSaveCode(extend.getSaveCode());
                    erpObjectField.setViewCode(extend.getViewCode());
                    erpObjectField.setQueryCode(extend.getQueryCode());
                    erpObjectField.setViewExtend(extend.getViewExtend());
                    erpObjectField.setSaveExtend(extend.getSaveExtend());
                }
            }
        });
    }

    @Override
    public Result<ErpObjectFieldResult> updateErpObjectField(String tenantId,
                                                             String dataCenterId,
                                                             int userId,
                                                             ErpObjectFieldResult erpObjectFieldResult) {
        erpObjectFieldResult.setDataCenterId(dataCenterId);

        //前端输入空格处理
        erpObjectFieldResult.setFieldApiName(StringUtils.trimToEmpty(erpObjectFieldResult.getFieldApiName()));
        erpObjectFieldResult.setViewCode(StringUtils.trim(erpObjectFieldResult.getViewCode()));
        erpObjectFieldResult.setSaveCode(StringUtils.trim(erpObjectFieldResult.getSaveCode()));
        erpObjectFieldResult.setQueryCode(StringUtils.trim(erpObjectFieldResult.getQueryCode()));

        ErpObjectFieldEntity erpObjectFieldEntity = new ErpObjectFieldEntity();
        BeanUtils.copyProperties(erpObjectFieldResult, erpObjectFieldEntity);
        erpObjectFieldEntity.setTenantId(tenantId);
        erpObjectFieldEntity.setDataCenterId(dataCenterId);
        erpObjectFieldEntity.setFieldExtendValue(GsonUtil.toJson(erpObjectFieldResult.getFieldExtendValue()));
        if(ErpChannelEnum.ERP_K3CLOUD.equals(erpObjectFieldResult.getChannel())
                &&"id".equals(erpObjectFieldResult.getFieldApiName())
                && erpObjectFieldResult.getErpObjectApiName().startsWith(K3CloudForm.SAL_SaleOrder+".BillHead")){//特殊逻辑，k3订单id字段不允许编辑
            return Result.newSystemError(I18NStringEnum.s722);
        }
        boolean isMainAttribute=false;
        if(ErpFieldTypeEnum.text.equals(erpObjectFieldEntity.getFieldDefineType())&&StringUtils.isNotBlank(erpObjectFieldEntity.getFieldExtendValue())){
            isMainAttribute = isMainAttribute(erpObjectFieldEntity);
            if(isMainAttribute){
                List<ErpObjectFieldEntity> objectFieldList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getObjectFieldByTenantId(tenantId,erpObjectFieldEntity.getErpObjectApiName());
                for(ErpObjectFieldEntity entity:objectFieldList){
                    if(ErpFieldTypeEnum.text.equals(entity.getFieldDefineType())&&StringUtils.isNotBlank(entity.getFieldExtendValue())){
                        boolean isMainAttributeField = isMainAttribute(entity);
                        if(isMainAttributeField){
                            if(!entity.getId().equals(erpObjectFieldEntity.getId())){//
                                return new Result<>(ResultCodeEnum.MAIN_ATTRIBUTE_EXISTED,entity.getFieldLabel()+"["+entity.getFieldApiName()+"]");
                            }
                        }
                    }
                }
            }
        }

        ErpObjectFieldEntity query = new ErpObjectFieldEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpObjectApiName(erpObjectFieldResult.getErpObjectApiName());
        query.setFieldApiName(erpObjectFieldResult.getFieldApiName());
        List<ErpObjectFieldEntity> entryByFieldApiName = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);

        if (StringUtils.isEmpty(erpObjectFieldResult.getId())) {//插入
            if (CollectionUtils.isNotEmpty(entryByFieldApiName)) {
                return Result.newError(ResultCodeEnum.THE_ERP_OBJECT_FIELD_EXIST);
            }
            erpObjectFieldEntity.setId(idGenerator.get());
            erpObjectFieldEntity.setCreateTime(System.currentTimeMillis());
            erpObjectFieldEntity.setUpdateTime(System.currentTimeMillis());
            int insertResult = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpObjectFieldEntity);
            if (insertResult == 1) {
                upsertExtend(tenantId,null, erpObjectFieldResult,isMainAttribute);
                return Result.newSuccess(queryById(tenantId, erpObjectFieldEntity.getId()).getData());
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        } else {//更新
            if (CollectionUtils.isNotEmpty(entryByFieldApiName) && entryByFieldApiName.stream().map(ErpObjectFieldEntity::getId).anyMatch(id -> !Objects.equals(id, erpObjectFieldEntity.getId()))) {
                return Result.newError(ResultCodeEnum.THE_ERP_OBJECT_FIELD_EXIST);
            }
            erpObjectFieldEntity.setCreateTime(System.currentTimeMillis());
            ErpObjectFieldEntity oldEntry=erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTenantIdAndId(tenantId, erpObjectFieldEntity.getId());
            int updateResult = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpObjectFieldEntity);
            if (updateResult == 1) {
                upsertExtend(tenantId,oldEntry, erpObjectFieldResult,isMainAttribute);
                return Result.newSuccess(queryById(tenantId, erpObjectFieldEntity.getId()).getData());
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        }
    }

    private static boolean isMainAttribute(ErpObjectFieldEntity erpObjectFieldEntity) {
        try {
            //是否主属性
            JSONObject extend=JSONObject.parseObject(erpObjectFieldEntity.getFieldExtendValue());
            return extend.getBooleanValue("isMainAttribute");
        }catch (Exception e){
            return false;
        }
    }

    public void upsertExtend(String tenantId,ErpObjectFieldEntity oldEntry, ErpObjectFieldResult erpObjectFieldResult,boolean isMainAttribute) {
        //需要查询真实apiName
        ErpObjExtendDto objExtendDto = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findObjExtendDtoBySplit(tenantId, erpObjectFieldResult.getErpObjectApiName());
        String realApiName = objExtendDto.parseRealApiCode();
        if(oldEntry!=null&&!oldEntry.getFieldApiName().equals(erpObjectFieldResult.getFieldApiName())){//如果旧的字段编码跟新的字段编码不一样，删除旧的扩展编码
            ErpFieldExtendEntity one = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findOne(tenantId,erpObjectFieldResult.getDataCenterId(), realApiName, oldEntry.getFieldApiName());
            if(one!=null){//删除
                erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(one.getId());
            }
        }
        ErpFieldExtendEntity arg = new ErpFieldExtendEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(erpObjectFieldResult.getDataCenterId());
        arg.setObjApiName(realApiName);
        //非id的主键，也当成主属性
        boolean isNumber = isMainAttribute
                || (ErpFieldTypeEnum.id.equals(erpObjectFieldResult.getFieldDefineType())
                && !StringUtils.equalsIgnoreCase(erpObjectFieldResult.getViewCode(), "Id"));
        if(isNumber){//是主属性
            arg.setErpFieldType("e12");
            List<ErpFieldExtendEntity> fieldExtendEntityList = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
            if(CollectionUtils.isNotEmpty(fieldExtendEntityList)){//把其他的e12清空
                for(ErpFieldExtendEntity extendEntity:fieldExtendEntityList){
                    erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateErpFieldTypeById(extendEntity.getId(),"");
                }
            }
        }
        arg.setFieldApiName(erpObjectFieldResult.getFieldApiName());
        arg.setFieldDefineType(erpObjectFieldResult.getFieldDefineType());
        if (StringUtils.isNotBlank(erpObjectFieldResult.getViewCode())
                || StringUtils.isNotBlank(erpObjectFieldResult.getSaveCode())
                || StringUtils.isNotBlank(erpObjectFieldResult.getQueryCode())) {
            //要么一起更新,要么直接取字段编码了,通过fieldApiName更新，否则直接插入
            arg.setFieldDefineType(erpObjectFieldResult.getFieldDefineType());
            arg.setViewCode(erpObjectFieldResult.getViewCode());
            arg.setViewExtend(JacksonUtil.toJson(erpObjectFieldResult.getViewExtend()));
            arg.setSaveCode(erpObjectFieldResult.getSaveCode());
            arg.setSaveExtend(JacksonUtil.toJson(erpObjectFieldResult.getSaveExtend()));
            arg.setQueryCode(erpObjectFieldResult.getQueryCode());
            erpFieldManager.upsertExtend(arg,isMainAttribute);
        }
    }

    public void deleteExtend(String tenantId, ErpObjectFieldEntity fieldEntity) {
        //需要查询真实apiName
        ErpObjExtendDto objExtendDto = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findObjExtendDtoBySplit(tenantId, fieldEntity.getErpObjectApiName());
        String realApiName = objExtendDto.parseRealApiCode();
        erpFieldManager.deleteFieldExtend(tenantId,fieldEntity.getDataCenterId(), realApiName, fieldEntity.getFieldApiName());
    }

    @Override
    public Result<String> deleteErpObjectFields(String tenantId, int userId, DeleteErpObjectFieldsArg deleteArg) {
        ErpObjectFieldEntity byId = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTenantIdAndId(tenantId, deleteArg.getId());
        if (!tenantId.equals(byId.getTenantId())) {
            return Result.newError(SYSTEM_ERROR);
        }
        if(ErpChannelEnum.ERP_K3CLOUD.equals(byId.getChannel())
                &&"id".equals(byId.getFieldApiName())
                && byId.getErpObjectApiName().startsWith(K3CloudForm.SAL_SaleOrder+".BillHead")){//特殊逻辑，k3订单id字段不允许删除
            return Result.newError(ResultCodeEnum.ERROR_MSG.getErrCode(), I18NStringEnum.s390);
        }
        int deleteResult = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, deleteArg.getId());
        if (deleteResult == 1) {
            //同时删除字段扩展
            deleteExtend(tenantId, byId);
            return Result.newSuccess();
        } else {
            return Result.newError(SYSTEM_ERROR);
        }
    }

    @Override
    public Result<List<DeleteErpObjFieldResult>> batchDeleteErpObjectFields(String tenantId, int userId, List<String> idList,String lang) {
        List<DeleteErpObjFieldResult> failedFieldList = new ArrayList<>();
        for(String id : idList) {
            ErpObjectFieldEntity byId = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTenantIdAndId(tenantId, id);
            if (Objects.isNull(byId)) {
                failedFieldList.add(new DeleteErpObjFieldResult(byId.getFieldApiName(),i18NStringManager.get(I18NStringEnum.s391,lang,tenantId)));
                continue;
            }
            if(ErpChannelEnum.ERP_K3CLOUD.equals(byId.getChannel())
                    &&"id".equals(byId.getFieldApiName())
                    && byId.getErpObjectApiName().startsWith(K3CloudForm.SAL_SaleOrder+".BillHead")){//特殊逻辑，k3订单id字段不允许删除
                failedFieldList.add(new DeleteErpObjFieldResult(byId.getFieldApiName(),i18NStringManager.get(I18NStringEnum.s390,lang,tenantId)));
                continue;
            }
            int deleteResult = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .deleteByEiAndId(tenantId, id);
            if (deleteResult == 1) {
                //同时删除字段扩展
                deleteExtend(tenantId, byId);
            } else {
                failedFieldList.add(new DeleteErpObjFieldResult(byId.getFieldApiName(),i18NStringManager.get(I18NStringEnum.s392,lang,tenantId)));
            }
        }

        return Result.newSuccess(failedFieldList);
    }

    @Override
    public Result<Void> processErpObjectField(ErpObjectFieldResult erpObjectField) {
        //单选字段/多选字段，选项去空去重
        if (erpObjectField.getFieldDefineType() != null &&
                (ErpFieldTypeEnum.select_one.name().equals(erpObjectField.getFieldDefineType().name()) || ErpFieldTypeEnum.select_many.name().equals(erpObjectField.getFieldDefineType().name()))) {
            List<ObjectFieldResult.Option> newOptions = Lists.newArrayList();
            Object selectStr = erpObjectField.getFieldExtendValue();
            if(selectStr==null){
                selectStr="[]";
            }
            List<ObjectFieldResult.Option> options = GsonUtil.fromJson(GsonUtil.toJson(selectStr), new TypeToken<List<ObjectFieldResult.Option>>() {
            }.getType());
            List<Object> labels = Lists.newArrayList();
            List<Object> values = Lists.newArrayList();
            for (ObjectFieldResult.Option option : options) {
                if (option==null || Objects.isNull(option.getLabel()) || Objects.isNull(option.getValue()) || StringUtils.isEmpty(option.getLabel().toString()) ||
                        StringUtils.isEmpty(option.getValue().toString()) || labels.contains(option.getLabel()) || values.contains(option.getValue())) {
                    continue;
                }
                labels.add(option.getLabel());
                values.add(option.getValue());
                newOptions.add(option);
            }
            erpObjectField.setFieldExtendValue(newOptions);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ErpObjectFieldResult> updateErpObjectFields(String tenantId, String dataCenterId, int userId, UpsertErpObjectFieldArg arg,String lang) {
        List<ErpObjectFieldResult> erpObjectFields = arg.getErpObjectFields();
        if (erpObjectFields==null){
            return Result.newSystemError(I18NStringEnum.s393);
        }
        // 校验只有一个主键字段
        final List<ErpObjectFieldResult> collect = erpObjectFields.stream()
                .filter(erpField -> Objects.equals(ErpFieldTypeEnum.id, erpField.getFieldDefineType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            if (collect.size() > 1) {
                return Result.newError(ResultCodeEnum.ERP_OBJECT_ID_DUPLICATE);
            }
            final ErpObjectFieldResult idFieldResult = collect.get(0);
            final String idFieldId = idFieldResult.getId();
            ErpObjectFieldEntity query = new ErpObjectFieldEntity();
            query.setTenantId(tenantId);
            query.setDataCenterId(dataCenterId);
            query.setErpObjectApiName(arg.getErpObjectApiName());
            query.setFieldDefineType(ErpFieldTypeEnum.id);
            List<ErpObjectFieldEntity> oldEntry = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            if (oldEntry.stream().map(ErpObjectFieldEntity::getId).anyMatch(id -> !Objects.equals(id, idFieldId))) {
                return Result.newError(ResultCodeEnum.ERP_OBJECT_ID_DUPLICATE);
            }
        }

        for (ErpObjectFieldResult erpObjectField : erpObjectFields) {
            erpObjectField.setErpObjectApiName(arg.getErpObjectApiName());
            erpObjectField.setChannel(arg.getChannel());
            processErpObjectField(erpObjectField);
            Result<ErpObjectFieldResult> updateErpObjectFieldsResult = updateErpObjectField(tenantId, dataCenterId, userId, erpObjectField);
            if (!updateErpObjectFieldsResult.isSuccess()) {
                log.info("updateErpObjectFieldsResult failed result={}", updateErpObjectFieldsResult);
                return updateErpObjectFieldsResult;
            }
        }
        return Result.newSuccess();
    }

    /**
     * 按字段顺序批量更新ERP对象字段信息，仅用于编辑对象字段页面，保存ERP对象排序后的字段信息
     * @param tenantId
     * @param dataCenterId
     * @param erpObjectRelationshipResult
     * @return
     */
    @Transactional
    @Override
    public Result<Void> updateErpObjectFieldsInOrder(String tenantId,
                                                     String dataCenterId,
                                                     ErpObjectRelationshipResult erpObjectRelationshipResult) {
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dataCenterId);
        List<ErpObjectFieldEntity> totalList = new ArrayList<>();
        for(ErpObjectDescResult erpObjectDescResult : erpObjectRelationshipResult.getFakeErpObject()) {
            ErpObjectFieldEntity entity = ErpObjectFieldEntity.builder()
                    .tenantId(tenantId)
                    .dataCenterId(dataCenterId)
                    .erpObjectApiName(erpObjectDescResult.erpObjectApiName)
                    .build();
            List<ErpObjectFieldEntity> fieldEntityList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(entity);
            if(fieldEntityList==null || fieldEntityList.isEmpty()) return Result.newError(PARAM_ERROR);

            totalList.addAll(fieldEntityList);
            erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByObjApiName(tenantId,erpObjectDescResult.erpObjectApiName,dataCenterId);
        }

        List<ErpObjectFieldEntity> totalFieldList = new ArrayList<>();
        erpObjectRelationshipResult.getFakeErpObject().forEach(erpObjectDescResult -> {
            erpObjectDescResult.erpObjectFields.forEach(erpObjectFieldResult -> {
                for(ErpObjectFieldEntity entity : totalList) {
                    if(entity.getId().equalsIgnoreCase(erpObjectFieldResult.id)) {
                        totalFieldList.add(entity);
                    }
                }
            });
        });

        long currentTime = System.currentTimeMillis();
        for(int i=0;i<totalFieldList.size();i++) {
            ErpObjectFieldEntity entity = totalFieldList.get(i);
            entity.setUpdateTime((long) i);
            entity.setCreateTime(currentTime);
        }

        int rows = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(totalFieldList);
        if(rows>0 && connectInfoEntity.getChannel()==ErpChannelEnum.ERP_K3CLOUD) {
            String realObjectApiName = erpObjectRelationshipResult.getFakeErpObject().get(0).getErpObjectExtendValue().toString();
            // 暂时只有主对象的时候,前端没给realObjectApiName
            if (StringUtils.isBlank(realObjectApiName)) {
                final String splitApiName = erpObjectRelationshipResult.getFakeErpObject().get(0).getErpObjectApiName();
                realObjectApiName = erpObjManager.getRealObjApiName(tenantId, splitApiName);
            }
            List<ErpFieldExtendEntity> list = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .queryByObjApiName(tenantId,dataCenterId,realObjectApiName);
            log.info("ErpObjectFieldsServiceImpl.updateErpObjectFieldsInOrder,realObjectApiName={},list.size={}",realObjectApiName,list.size());
            //更新字段扩展表中priority字段的值
            int sum = 0;
            for(int i=0;i<totalFieldList.size();i++) {
                ErpObjectFieldEntity entity = totalFieldList.get(i);
                int count = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updatePriorityField(tenantId,
                        dataCenterId,
                        realObjectApiName,
                        entity.getFieldApiName(),
                        10000L+i);
                sum+=count;
            }
            log.info("ErpObjectFieldsServiceImpl.updateErpObjectFieldsInOrder,sum={}",sum);
            return Result.newSuccess();
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ErpObjectRelationshipResult> queryErpObjectAndFieldsByActualObjAndDcId(String tenantId,
                                                                                         int userId,
                                                                                         ErpObjectDescResult arg,
                                                                                         String dataCenterId,
                                                                                         String lang) {
        //查对象信息
        final String erpObjectApiName = arg.getErpObjectApiName();
        final Integer splitSeq = arg.getSplitSeq();
        return queryErpObjectAndFieldsByActualObjAndDcId(tenantId, dataCenterId, erpObjectApiName, splitSeq, lang);
    }

    @Override
    public  Result<ErpObjectRelationshipResult> queryErpObjectAndFieldsByActualObjAndDcId(String tenantId, String dataCenterId, String erpObjectApiName, Integer splitSeq, String lang) {
        ErpObjectRelationshipResult erpObjectRelationshipResult = erpObjectService.getErpObjectRelationshipByRealObjApiName(tenantId, erpObjectApiName, splitSeq, dataCenterId, lang).safeData();
        List<ErpObjectDescResult> fakeErpObjects = Lists.newArrayList();
        for (ErpObjectDescResult fakeObject : erpObjectRelationshipResult.getFakeErpObject()) {
            //中间对象需要补充字段信息
            Result<List<ErpObjectFieldResult>> listResult = query(tenantId, fakeObject.getErpObjectApiName(), dataCenterId);
            fakeObject.setErpObjectFields(listResult.getData());
            //孙表也要补充
            if (fakeObject.getChildren()!=null) {
                for (ErpObjectDescResult subDetailObj : fakeObject.getChildren()) {
                    subDetailObj.setErpObjectFields(query(tenantId,subDetailObj.getErpObjectApiName(), dataCenterId).safeData());
                }
            }
            fakeErpObjects.add(fakeObject);
        }
        erpObjectRelationshipResult.setFakeErpObject(fakeErpObjects);
        return Result.newSuccess(erpObjectRelationshipResult);
    }


    @Override
    public Result<String> checkErpObjectFields(String tenantId, int userId, ErpObjectDescResult arg, String dataCenterId,String lang) {
        Result<ErpObjectRelationshipResult> erpObjectRelationshipResultResult = this.queryErpObjectAndFieldsByActualObjAndDcId(tenantId, userId, arg, dataCenterId,lang);
        if (erpObjectRelationshipResultResult == null || !erpObjectRelationshipResultResult.isSuccess()) {
            return Result.newError(PARAM_ERROR);
        }
        for (ErpObjectDescResult erpObjectDescResult : erpObjectRelationshipResultResult.getData().getFakeErpObject()) {
            boolean pass = false;
            //校验对象是否都存在id字段
            for (ErpObjectFieldResult erpObjectFieldResult : erpObjectDescResult.getErpObjectFields()) {
                if (ErpFieldTypeEnum.id.name().equals(erpObjectFieldResult.getFieldDefineType().name())) {
                    pass = true;
                    break;
                }
            }
            if (!pass) {
                Result<String> result = Result.newError(THE_OBJECT_NOT_EXIST_ID_FIELD);
                result.setErrMsg("[" + erpObjectDescResult.getErpObjectApiName() + "]" + result.getErrMsg());
                return result;
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<ErpFieldItemResult>> queryErpFieldItem(String tenantId, String dataCenterId, ErpFieldItemArg arg) {
        if (StringUtils.isAnyBlank(arg.getErpObjectApiName(), arg.getErpObjectFieldName()) || arg.getErpObjectApiName().split("[.]").length != 2) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String[] array = arg.getErpObjectApiName().split("[.]");
        ErpConnectInfoEntity ecie = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
        K3CloudConnectParam k3CloudConnectParam = JacksonUtil.fromJson(ecie.getConnectParams(), K3CloudConnectParam.class);
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, k3CloudConnectParam, dataCenterId);
        QueryBusinessInfoResult.BusinessInfo businessInfo = metaDataInfoManager.queryBusinessInfo(apiClient, new QueryBusinessInfoArg(array[0]));
        if (businessInfo != null && businessInfo.getEntrys() != null) {
            if ("BillHead".equals(array[1])) {
                array[1] = "FBillHead";
            }
            List<QueryBusinessInfoResult.BusinessInfo.EntrysBean> entrys = businessInfo.getEntrys();
            for (QueryBusinessInfoResult.BusinessInfo.EntrysBean entry : entrys) {
                if(StringUtils.equals(entry.getKey(), array[1]) && entry.getFields() != null){
                    List<QueryBusinessInfoResult.BusinessInfo.EntrysBean.FieldsBean> fields = entry.getFields();
                    for (QueryBusinessInfoResult.BusinessInfo.EntrysBean.FieldsBean field : fields) {
                        if(StringUtils.equals(field.getKey(), arg.getErpObjectFieldName()) && field.getExtendValues() != null){
                            List<ErpFieldItemResult> erpFieldItemResultList = new ArrayList<>();
                            List<QueryBusinessInfoResult.BusinessInfo.EntrysBean.FieldsBean.ExtendsBean> kys = field.getExtendValues();
                            for(QueryBusinessInfoResult.BusinessInfo.EntrysBean.FieldsBean.ExtendsBean ky : kys){
                                ErpFieldItemResult erpFieldItemResult = new ErpFieldItemResult();
                                erpFieldItemResult.setValue(ky.getValue());
                                erpFieldItemResult.setCaption(ky.getCaption());
                                erpFieldItemResult.setSeq(ky.getSeq());
                                erpFieldItemResultList.add(erpFieldItemResult);
                            }
                            return Result.newSuccess(erpFieldItemResultList);
                        }
                    }
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    @Deprecated
    public Result<ErpObjectFieldResult> queryK3cloudErpField(final String tenantId,
                                                             final String dataCenterId,
                                                             final String erpObjectApiName,
                                                             final String erpObjectFieldName,
                                                             String lang) {
        throw new ErpSyncDataException(i18NStringManager.get(I18NStringEnum.s158,lang,tenantId), null,null);
    }

    private void sendFieldExportResult(String tenantId,
                                       String dataCenterId,
                                       Integer userId,
                                       Result<BuildExcelFile.Result> excelFileResult,
                                       String lang) {
        String msg = excelFileResult.getErrMsg();
        if (excelFileResult.isSuccess()) {
            String fileName = i18NStringManager.get(I18NStringEnum.s724,lang,tenantId);
            try {
                fileName=URLEncoder.encode(fileName,"utf-8");
            } catch (Exception e) {
            }
            String downloadUrl = String.format(userCenterService.getDownloadFilePath(tenantId), excelFileResult.getData().getTnFilePath() + ".xlsx", fileName);
            msg = i18NStringManager.get(I18NStringEnum.s725,lang,tenantId) + downloadUrl;
        }
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(dataCenterId);
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s723,lang,tenantId) + LocalDateTime.now().toString());
        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,lang,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }

    @Override
    public Result<ImportExcelFile.FieldImportResult> batchImportErpObjectFields(ImportExcelFile.FieldImportArg arg,String lang) {
        final String tenantId = arg.getTenantId();
        ErpConnectInfoEntity entity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, arg.getDataCenterId());

        final List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjManager.listRelationsByRealApiName(tenantId, arg.getDataCenterId(), arg.getErpRealObjectApiName());
        final Set<String> splitObjApiNames = erpObjectRelationshipEntities.stream()
                .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                .collect(Collectors.toSet());

        ErpObjectFieldsImportListener listener = new ErpObjectFieldsImportListener(tenantId,
                arg.getDataCenterId(),
                entity.getChannel(),
                splitObjApiNames,
                this,
                i18NStringManager,
                lang);

        String sheetNameArg= ExcelUtils.getSheetName(arg.getErpRealObjectApiName());
        ReadExcel.Arg<ErpObjectFieldExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(ErpObjectFieldExcelVo.class);
        try {
            InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
            readExcelArg.setInputStream(inputStream);
        } catch (Exception e) {
            log.warn("excel文件读取失败", e);
        }
        readExcelArg.setSheetName(sheetNameArg);//可能是省略后的apiname
        readExcelArg.setType(ErpObjectFieldExcelVo.class);
        readExcelArg.setExcelType(ExcelTypeEnum.XLSX);
        fileManager.readExcelBySheetName(readExcelArg);

        if(CollectionUtils.isNotEmpty(listener.getFailedDataList())) {
            BuildExcelFile.Arg<ErpObjectFieldExcelVo2> excelFileArg = new BuildExcelFile.Arg<>();
            excelFileArg.setTenantId(tenantId);
            excelFileArg.setDataList(listener.getFailedDataList());
            excelFileArg.setFileName(i18NStringManager.get2(I18NStringEnum.s394.getI18nKey(),
                    lang,
                    tenantId,
                    i18NStringManager.getByEi2(I18NStringEnum.s394, tenantId, tenantId, LocalDateTime.now().toString()),
                    Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
            excelFileArg.setSheetNames(Collections.singletonList("sheet1"));
            Result<BuildExcelFile.Result> buildExcelFileResult = fileService.buildExcelFile(excelFileArg,lang);
            sendFieldExportResult(tenantId,arg.getDataCenterId(),arg.getUserId(),buildExcelFileResult,lang);
        } else {
            //发送企信消息
            SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
            sendTextNoticeArg.setTenantId(tenantId);
            sendTextNoticeArg.setReceivers(Collections.singletonList(arg.getUserId()));
            sendTextNoticeArg.setMsg(i18NStringManager.get(I18NStringEnum.s395,lang, tenantId));
            sendTextNoticeArg.setMsgTitle(i18NStringManager.get2(I18NStringEnum.s394.getI18nKey(),
                    lang,
                    tenantId,
                    i18NStringManager.getByEi2(I18NStringEnum.s394, tenantId, tenantId, LocalDateTime.now().toString()),
                    Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
            notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager,lang, tenantId),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
        }

        return Result.newSuccess(listener.getImportResult());
    }

    @Override
    public Result<Void> initFieldExtendData() {
        long startTime = System.currentTimeMillis();
        List<String> allTenantId = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId))
                .findAllTenantId();
        allTenantId.removeAll(Lists.newArrayList("0","01","000000","1"));
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,allTenantId.size={},allTenantId={}",allTenantId.size(),allTenantId);
        initFieldExtendData(allTenantId);
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,all tenant field extend data init success,costTime={}",System.currentTimeMillis()-startTime);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initFieldExtendData(List<String> eiList) {
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,eiList={}",eiList);
        List<String> failedEiList = new ArrayList<>();
        for(String ei : eiList) {
            try {
                List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei))
                        .findAllByTenantId(ei);
                log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,relationshipEntityList={}",relationshipEntityList);
                if(CollectionUtils.isEmpty(relationshipEntityList))
                    continue;
                log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init tenant obj field extend data start,ei={}",ei);
                Result<Long> result = initFieldExtendData(ei,relationshipEntityList);
                log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init tenant obj field extend data success,ei={},costTime={}",ei,result.getData());
            } catch (Exception e) {
                log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init tenant obj field extend data failed,ei={}",ei);
                failedEiList.add(ei);
            }
        }
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,failedEiList={}",failedEiList);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initFieldExtendData(String tenantId, String erpObjApiName) {
        List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findAllByRealObjectApiName(tenantId,erpObjApiName);
        if(CollectionUtils.isEmpty(relationshipEntityList))
            return Result.newSystemError(I18NStringEnum.s726);
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init obj field extend data start,ei={},erpObjApiName={}",tenantId,erpObjApiName);
        Result<Long> result = initFieldExtendData(tenantId,relationshipEntityList);
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init obj field extend data success,ei={},costTime={}",tenantId,result.getData());
        return Result.copy(result);
    }

    private Result<Long> initFieldExtendData(String tenantId, List<ErpObjectRelationshipEntity> relationshipEntityList) {
        if(CollectionUtils.isEmpty(relationshipEntityList))
            return Result.newSystemError(I18NStringEnum.s726);

        long startTime = System.currentTimeMillis();

        for(ErpObjectRelationshipEntity relationshipEntity : relationshipEntityList) {
            if(relationshipEntity.getChannel()!= ErpChannelEnum.ERP_K3CLOUD) continue;

            String dcId = relationshipEntity.getDataCenterId();

            log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,relationshipEntity={},dcId={}",relationshipEntity,relationshipEntity.getDataCenterId());
            ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getByIdAndTenantId(tenantId, dcId);
            if(connectInfoEntity==null) {
                log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,erp connect info not exist,dcId={}",relationshipEntity.getDataCenterId());
                continue;
            }

            ErpObjExtendDto objExtendDto = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findObjExtendDtoBySplit(tenantId, relationshipEntity.getErpSplitObjectApiname());
            if (objExtendDto == null) {
                log.info("field obj not found,tenantId:{},objApiName:{}", tenantId, relationshipEntity.getErpSplitObjectApiname());
                continue;
            }
            String realApiName;
            if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(objExtendDto.getSplitType())) {
                realApiName = objExtendDto.getRealObjApiName();
            } else {
                realApiName = objExtendDto.getExtentValue();
            }

            List<ErpFieldExtendEntity> erpFieldExtendEntityList = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .queryByObjApiName(tenantId, null, realApiName);

            log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init field extend begin,ei={},dcId={},realApiName={}",tenantId,dcId,realApiName);
            for(ErpFieldExtendEntity fieldExtendEntity : erpFieldExtendEntityList) {
                ErpFieldExtendEntity entity = new ErpFieldExtendEntity();
                BeanUtils.copyProperties(fieldExtendEntity,entity);
                try {
                    entity.setDataCenterId(dcId);
                    ErpFieldExtendEntity erpFieldExtend = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .findOne(tenantId, entity.getDataCenterId(), entity.getObjApiName(), entity.getFieldApiName());
                    if(erpFieldExtend!=null) {
                        if(!StringUtils.equalsIgnoreCase(erpFieldExtend.getViewCode(),entity.getViewCode())
                                || !StringUtils.equalsIgnoreCase(erpFieldExtend.getViewExtend(),entity.getViewExtend())
                                || !StringUtils.equalsIgnoreCase(erpFieldExtend.getQueryCode(),entity.getQueryCode())
                                || !StringUtils.equalsIgnoreCase(erpFieldExtend.getSaveCode(),entity.getSaveCode())
                                || !StringUtils.equalsIgnoreCase(erpFieldExtend.getSaveExtend(),entity.getSaveExtend())) {
                            erpFieldExtend.setViewCode(entity.getViewCode());
                            erpFieldExtend.setViewExtend(entity.getViewExtend());
                            erpFieldExtend.setQueryCode(entity.getQueryCode());
                            erpFieldExtend.setSaveCode(entity.getSaveCode());
                            erpFieldExtend.setSaveExtend(entity.getSaveExtend());
                            erpFieldExtend.setUsedQuery(entity.getUsedQuery());
                            int count = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                                    .updateById(erpFieldExtend);
                            log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,updateByIdAdmin,count={}",count);
                        }
                        continue;
                    }
                    entity.setId("erp_"+idGenerator.get());
                    int insert = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .insert(entity);
                    if(insert!=1) {
                        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,insert field extend data failed,entity={}",entity);
                    }
                } catch (Exception e) {
                    log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,insert field extend data failed,entity={},exception={}",entity,e.getMessage(),e);
                }
            }
            log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init field extend end,ei={},dcId={},realApiName={}",tenantId,dcId,realApiName);
        }

        long costTime = System.currentTimeMillis()-startTime;
        log.info("ErpObjectFieldsServiceImpl.initFieldExtendData,init obj field extend data success,ei={},costTime={}",tenantId,costTime);
        return Result.newSuccess(costTime);
    }

    @Override
    public IdSaveExtend getK3ObjectSaveExtend(final String tenantId, final String dcId, final String objectApiName) {
        final ErpFieldExtendEntity idFieldExtend = erpFieldManager.findIdFieldExtend(tenantId, dcId, objectApiName);
        if (Objects.isNull(idFieldExtend)) {
            return null;
        }
        return IdSaveExtend.of(objectApiName, idFieldExtend.getSaveExtend());
    }

    @Override
    public Result<Void> setK3ObjectAddStatus(final String tenantId, final String dcId, final String objectApiName, final K3CSaveType k3CSaveType,String lang) {
        final ErpFieldExtendEntity idFieldExtend = erpFieldManager.findIdFieldExtend(tenantId, dcId, objectApiName);
        if (Objects.isNull(idFieldExtend)) {
            Result<Void> result = Result.newError(THE_OBJECT_NOT_EXIST_ID_FIELD.getErrCode(),
                    I18NStringEnum.s386,
                    objectApiName);
            return result;
        }

        // id为编码,且为暂存,需要报错
        if (BooleanUtils.isTrue(k3CSaveType.getUseDraft()) && !checkNumFieldCanDraft(tenantId, dcId, objectApiName, idFieldExtend)) {
            Result<Void> result = Result.newError(OBJECT_ID_EXTEND_IS_NUMBER);
            result.setErrMsg("[" + objectApiName + "]" + result.getErrMsg());
            return result;
        }

        final IdSaveExtend saveExtend = IdSaveExtend.of(objectApiName, idFieldExtend.getSaveExtend());
        saveExtend.setUseDraft(k3CSaveType.getUseDraft());
        saveExtend.setIsAutoSubmitWithoutAudit(k3CSaveType.getIsAutoSubmitWithoutAudit());
        saveExtend.setIsAutoSubmitAndAudit(k3CSaveType.getIsAutoSubmitAndAudit());

        if (Objects.nonNull(saveExtend.getSubSystemId()) || Objects.nonNull(saveExtend.getIsEntryBatchFill()) || Objects.nonNull(saveExtend.getIsVerifyBaseDataField())) {
            saveExtend.setSubSystemId(null);
            saveExtend.setIsEntryBatchFill(null);
            saveExtend.setIsVerifyBaseDataField(null);
        }

        erpFieldExtendDao.updateSaveExtendById(idFieldExtend.getId(), JSON.toJSONString(saveExtend));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> setK3ObjectModifyStatus(final String tenantId, final String dcId, final String objectApiName, final K3CSaveType k3CSaveType,String lang) {
        final ErpFieldExtendEntity idFieldExtend = erpFieldManager.findIdFieldExtend(tenantId, dcId, objectApiName);
        if (Objects.isNull(idFieldExtend)) {
            Result<Void> result = Result.newError(THE_OBJECT_NOT_EXIST_ID_FIELD.getErrCode(),
                    I18NStringEnum.s386,
                    objectApiName);
            return result;
        }

        // 销售订单使用的是销售变更单,不支持修改状态
        if (Objects.equals(objectApiName, K3CloudForm.SAL_SaleOrder)) {
            Result<Void> result = Result.newError(UNSUPPORTED_OBJECT.getErrCode(),
                    I18NStringEnum.s387,
                    objectApiName);
            return result;
        }

        final IdSaveExtend saveExtend = IdSaveExtend.of(objectApiName, idFieldExtend.getSaveExtend());
        final String modifyStatus = Objects.isNull(k3CSaveType) || Objects.equals(k3CSaveType, K3CSaveType.MODIFY) ? null : k3CSaveType.getStatus();
        saveExtend.setModifyStatus(modifyStatus);
        erpFieldExtendDao.updateSaveExtendById(idFieldExtend.getId(), JSON.toJSONString(saveExtend));
        return Result.newSuccess();
    }

    @Override
    public Result<Map<String, Map<String, String>>> importObjectFiledApiNameData(String tenantId,
                                                                    String dataCenterId,
                                                                    ErpChannelEnum channel,
                                                                    String lang,
                                                                    //key:apiName,value:fakeApiName
                                                                    Map<String, String> erpObjectMap,
                                                                    List<ErpIntegrationStreamExcelVo> integrationStreamExcelVos) {
        String fakeObjApiName = null;
        Map<String, Map<String, String>> erpObjectFileErrorMap = new HashMap<>();
        ErpObjectFieldEntity query = new ErpObjectFieldEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        for (ErpIntegrationStreamExcelVo erpIntegrationStreamExcelVo : integrationStreamExcelVos) {
            Map<String, String> objectErrorMap = new HashMap<>();
            if(!erpObjectMap.containsKey(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName())) {
                //对象都不存在
                continue;
            }

            //主对象一定存在
            if (StringUtils.isEmpty(fakeObjApiName)) {
                fakeObjApiName = erpObjectMap.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName());
                log.info("ErpObjectFieldServiceImpl.importObjectFiledApiNameData,tenantId={},fakeObjApiName={}", tenantId, fakeObjApiName);
            }

            query.setErpObjectApiName(erpObjectMap.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
            query.setFieldApiName(erpIntegrationStreamExcelVo.getThirdPartyFieldApiName());
            List<ErpObjectFieldEntity> entryByFieldApiNames = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            //不存在就新增
            if (CollectionUtils.isNotEmpty(entryByFieldApiNames)) {
                continue;
            }
            //新增
            ErpObjectFieldResult result = new ErpObjectFieldResult();
            result.setFieldLabel(erpIntegrationStreamExcelVo.getThirdPartyFieldLabel());
            result.setDataCenterId(dataCenterId);
            result.setErpObjectApiName(erpObjectMap.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
            result.setChannel(channel);
            result.setFieldApiName(erpIntegrationStreamExcelVo.getThirdPartyFieldApiName());
            ErpFieldTypeEnum fieldDefineType;
            try {
                fieldDefineType = ErpFieldTypeEnum.valueOf(erpIntegrationStreamExcelVo.getThirdPartyFieldType());
            } catch (Exception e) {
                //报错
                objectErrorMap.put(erpIntegrationStreamExcelVo.getThirdPartyFieldApiName(), i18NStringManager.get(I18NStringEnum.s992, lang, tenantId));
                erpObjectFileErrorMap.put(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName(), objectErrorMap);
                continue;
            }
            result.setFieldDefineType(fieldDefineType);
            result.setFieldExtendValue(erpIntegrationStreamExcelVo.getThirdPartyFieldExtendInfo());
            result.setRequired(erpIntegrationStreamExcelVo.isThirdPartyFieldRequired());
            //新增之前需要校验扩展字段
            processErpObjectField(result);

            ErpObjectFieldEntity erpObjectFieldEntity = new ErpObjectFieldEntity();

            BeanUtils.copyProperties(result, erpObjectFieldEntity);
            erpObjectFieldEntity.setTenantId(tenantId);
            erpObjectFieldEntity.setDataCenterId(dataCenterId);
            erpObjectFieldEntity.setFieldExtendValue(GsonUtil.toJson(result.getFieldExtendValue()));

            if (erpObjectFieldEntity.getFieldDefineType() == ErpFieldTypeEnum.master_detail) {
                erpObjectFieldEntity.setFieldExtendValue(fakeObjApiName);
            }

            boolean isMainAttribute=false;
            if(ErpFieldTypeEnum.text.equals(erpObjectFieldEntity.getFieldDefineType())&&StringUtils.isNotBlank(erpObjectFieldEntity.getFieldExtendValue())){
                isMainAttribute = isMainAttribute(erpObjectFieldEntity);
                if(isMainAttribute){
                    List<ErpObjectFieldEntity> objectFieldList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getObjectFieldByTenantId(tenantId,erpObjectFieldEntity.getErpObjectApiName());
                    boolean isExistMain = Boolean.FALSE;
                    for(ErpObjectFieldEntity entity:objectFieldList){
                        if(ErpFieldTypeEnum.text.equals(entity.getFieldDefineType())&&StringUtils.isNotBlank(entity.getFieldExtendValue())){
                            boolean isMainAttributeField = isMainAttribute(entity);
                            if(isMainAttributeField){
                                if(!entity.getId().equals(erpObjectFieldEntity.getId())){
                                    //处理
                                    objectErrorMap.put(erpIntegrationStreamExcelVo.getThirdPartyFieldApiName(), String.format(i18NStringManager.get(ResultCodeEnum.MAIN_ATTRIBUTE_EXISTED.getI18nKey(), lang, tenantId, ResultCodeEnum.MAIN_ATTRIBUTE_EXISTED.getErrMsg()), entity.getFieldLabel()+"["+entity.getFieldApiName()+"]"));
                                    erpObjectFileErrorMap.put(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName(), objectErrorMap);
                                    isExistMain = Boolean.TRUE;
                                    break;
                                }
                            }
                        }
                    }
                    if (isExistMain) {
                        continue;
                    }
                }
            }

            erpObjectFieldEntity.setId(idGenerator.get());
            erpObjectFieldEntity.setCreateTime(System.currentTimeMillis());
            erpObjectFieldEntity.setUpdateTime(System.currentTimeMillis());
            int insertResult = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpObjectFieldEntity);
            if (insertResult != 1) {
                objectErrorMap.put(erpIntegrationStreamExcelVo.getThirdPartyFieldApiName(), i18NStringManager.get(THE_DATABASE_ERROR.getI18nKey(), lang, tenantId, THE_DATABASE_ERROR.getErrMsg()));
                erpObjectFileErrorMap.put(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName(), objectErrorMap);
            }
        }
        return Result.newSuccess(erpObjectFileErrorMap);
    }

    private boolean checkNumFieldCanDraft(final String tenantId, final String dcId, final String objectApiName, final ErpFieldExtendEntity idFieldExtend) {
        ErpFieldExtendEntity erpFieldExtendEntity = erpFieldManager.queryNumFieldExtend(tenantId, dcId, objectApiName);

        final String idFieldApiName = idFieldExtend.getFieldApiName();
        if (Objects.isNull(erpFieldExtendEntity) || !Objects.equals(erpFieldExtendEntity.getFieldApiName(), idFieldApiName)) {
            return true;
        }

        // 检查所有crm->erp 支持新增的 集成流
        final String splitObjApiName = erpObjManager.getMasterSplitObjApiName(tenantId, dcId, objectApiName);
        final List<SyncPloyDetailSnapshotData2> snapshotData = syncPloyDetailSnapshotService.listEnableSyncPloyDetailByDestApiName(tenantId, splitObjApiName, TenantTypeEnum.ERP.getType(), Lists.newArrayList(tenantId)).getData();
        final List<SyncPloyDetailData2> collect = snapshotData.stream()
                .map(SyncPloyDetailSnapshotData2::getSyncPloyDetailData)
                .filter(detailData2 -> detailData2.getSyncRules().getEvents().contains(EventTypeEnum.ADD.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return true;
        }

        // 确定对应对象的字段有编码字段映射
        return collect.stream().allMatch(detailData2 -> {
            // 获取字段映射
            List<FieldMappingData> fieldMappingData = Objects.equals(splitObjApiName, detailData2.getDestObjectApiName()) ?
                    detailData2.getFieldMappings() :
                    detailData2.getDetailObjectMappings().stream()
                            .filter(detailObjectMappingData -> Objects.equals(splitObjApiName, detailObjectMappingData.getDestObjectApiName()))
                            .findFirst()
                            .map(DetailObjectMappingsData.DetailObjectMappingData::getFieldMappings)
                            .orElseGet(ArrayList::new);

            return fieldMappingData.stream().anyMatch(data -> Objects.equals(idFieldApiName, data.getDestApiName()));
        });
    }
}
