package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/3 14:56:13
 */
public interface GetK3ObjectSaveExtend {

    @Data
    class Arg {
        /**
         * 真实对象apiName
         */
        private String objectApiName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {

        /**
         * 保存状态
         *
         * @see K3CSaveType
         */
        private String addType;
        /**
         * 修改状态
         *
         * @see K3CSaveType
         */
        private String modifyType;

        /**
         * 保存参数
         */
        private Map<String, Object> config;
    }


}
