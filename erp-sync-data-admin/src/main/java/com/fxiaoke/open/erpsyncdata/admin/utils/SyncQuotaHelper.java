package com.fxiaoke.open.erpsyncdata.admin.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum.UN_START_CONNECT_CHANNEL;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/16
 */
public class SyncQuotaHelper {
    /**
     * key:moduleCode 所有，包括内部和外部的都有了
     * value: 集成流配额
     */
    public Map<String, Long> moduleStreamQuotaMap;

    /**
     * key:moduleCode 所有，包括内部和外部的都有了
     * value: 中间表配额
     */
    public Map<String, Long> moduleMappingQuotaMap;
    /**
     * key: 资源包paraKey
     * value: 集成流配额
     */
    public Map<String, Long> pkgStreamQuotaMap;
    /**
     * key: 资源包paraKey
     * value: 中间表配额
     */
    public Map<String, Long> pkgMappingQuotaMap;
    /**
     * key:资源包的moduleCode
     * value:资源包para
     */
    public Map<String, Set<String>> pkgModuleParaMap;

    /**
     * 开启对比结果
     */
    public boolean enableCountCompare = false;

    /**
     * 执行任务的cron,仅对任务启动时有效
     */
    public String checkQuotaTaskCron = "15 3 * * 7";
    public int breakVacuumHour = 6;

    public static final SyncQuotaHelper LAZY_HOLDER = new SyncQuotaHelper();

    private SyncQuotaHelper() {
        ConfigFactory.getInstance().getConfig("erp-sync-data-all", new IniChangeListener("syncQuota") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                loadConfig(iniConfig);
            }
        });
    }

    private void loadConfig(IniConfig config) {
        moduleStreamQuotaMap = JSON.parseObject(config.get("moduleStreamQuotaMap", "{\"sap_data_sync_app\":30,\"k3_data_sync_app\":20,\"u8_data_sync_app\":20,\"standard_data_sync_app\":20,\"u8_eai_data_sync_app\":20}"), new TypeReference<LinkedHashMap<String, Long>>() {
        });
        moduleMappingQuotaMap = JSON.parseObject(config.get("moduleMappingQuotaMap", "{\"sap_data_sync_app\":3000000}"), new TypeReference<LinkedHashMap<String, Long>>() {
        });
        //对于channel有的集成平台连接器，都加上默认值,10集成流，100万数据
        for (Connector connector : AllConnectorUtil.getAllConnectorList()) {
            ErpChannelEnum channel = connector.getChannel();
            if (connector.getModuleCode() != null && !UN_START_CONNECT_CHANNEL.contains(channel)) {
                moduleStreamQuotaMap.putIfAbsent(connector.getModuleCode(), 20L);
                moduleMappingQuotaMap.putIfAbsent(connector.getModuleCode(), 1000000L);
            }
        }
        pkgStreamQuotaMap = JSON.parseObject(config.get("pkgStreamQuotaMap", "{\"data_sync_count_pkg_limit\":10}"), new TypeReference<LinkedHashMap<String, Long>>() {
        });
        pkgMappingQuotaMap = JSON.parseObject(config.get("pkgMappingQuotaMap", "{\"data_sync_count_limit\":1000000}"), new TypeReference<LinkedHashMap<String, Long>>() {
        });
        pkgModuleParaMap = JSON.parseObject(config.get("pkgModuleParaMap", "{\"data_sync_count_pkg_app\":[\"data_sync_count_pkg_limit\"],\"data_sync_count_app\":[\"data_sync_count_limit\"]}"), new TypeReference<LinkedHashMap<String, Set<String>>>() {
        });
        enableCountCompare = config.getBool("enableCountCompare", enableCountCompare);
        checkQuotaTaskCron = config.get("checkQuotaTaskCron", checkQuotaTaskCron);
        breakVacuumHour = config.getInt("breakVacuumHour", breakVacuumHour);
    }
}
