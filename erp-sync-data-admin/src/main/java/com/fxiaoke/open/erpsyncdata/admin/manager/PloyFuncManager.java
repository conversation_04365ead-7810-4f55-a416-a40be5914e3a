package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener.BaseListener;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ReadExcel;
import lombok.Data;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date: 14:21 2023/5/5
 * @Desc:
 */
@Component
public class PloyFuncManager {
    @Autowired
    private FileManager fileManager;


    public void getFuncDetail(FileInputStream fileInputStream,String file) throws IOException {
        FileInputStream excelFileInputStream = null;
        try {
            excelFileInputStream = new FileInputStream(file);
            XSSFWorkbook workbook = new XSSFWorkbook(excelFileInputStream);
            XSSFSheet sheet = workbook.getSheetAt(0);
            PloyFuncListener listener = new PloyFuncListener(sheet);
            //读取excel
            ReadExcel.Arg<PloyFuncExcelVo> readExcelArg = new ReadExcel.Arg<>();
            readExcelArg.setExcelListener(listener);
            readExcelArg.setType(PloyFuncExcelVo.class);
            readExcelArg.setInputStream(fileInputStream);
            fileManager.readExcel(readExcelArg);
            FileOutputStream excelFileOutPutStream = new FileOutputStream(file);
            workbook.write(excelFileOutPutStream);
            excelFileOutPutStream.flush();
            excelFileOutPutStream.close();
            excelFileInputStream.close();
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public static class PloyFuncListener<T extends PloyFuncExcelVo> extends BaseListener<T> {
        private OkHttpClient client = new OkHttpClient().newBuilder().build();
        private XSSFSheet newSheet;
        @Override
        public void invoke(T data, AnalysisContext context) {
            super.invoke(data, context);
            Integer rowNo = context.readRowHolder().getRowIndex();
            queryFunc(rowNo,data);
        }

        private void queryFunc(Integer rowNo, PloyFuncExcelVo data) {
            try {
                if(StringUtils.isNotBlank(data.getBeforeFuncApiName())){
                    String value=getFuncValue(data.getEa(),data.getBeforeFuncApiName());
                    setCellValue(rowNo,8,value);
                }
                if(StringUtils.isNotBlank(data.getDuringFuncApiName())){
                    String value=getFuncValue(data.getEa(),data.getDuringFuncApiName());
                    setCellValue(rowNo,9,value);
                }
                if(StringUtils.isNotBlank(data.getAfterFuncApiName())){
                    String value=getFuncValue(data.getEa(),data.getAfterFuncApiName());
                    setCellValue(rowNo,10,value);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private String getFuncValue(String ea, String funcApiName) {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n    \"api_name\": \"func_queryConsole__c\",\r\n    \"parameters\": [\r\n        {\r\n            \"name\": \"ea\",\r\n            \"type\": \"String\",\r\n            \"value\": \""+ea+"\"\r\n        },\r\n        {\r\n            \"name\": \"apiName\",\r\n            \"type\": \"String\",\r\n            \"value\": \""+funcApiName+"\"\r\n        },\r\n        {\r\n            \"name\": \"bindingObjectApiName\",\r\n            \"type\": \"String\",\r\n            \"value\": \"NONE\"\r\n        }\r\n    ]\r\n}");
            Request request = new Request.Builder()
                    .url("https://www.fxiaoke.com/FHH/EM1HNCRM/API/v1/object/function/service/controller?_fs_token=D3WqDJ8pCMKjPcHcPIqqEM9XBM9ZOZOjPMKoOZTZE3OsDpDc&traceId=E-E.fs.8110-1683198267751")
                    .method("POST", body)
                    .addHeader("Cookie", "_ga=GA1.2.1284721593.1565316226; AGL_USER_ID=d807ee29-f1c6-4ad8-8a30-aae450665259; guid=98b3c42f-4162-4ec4-b39d-927c34197e1c; LoginType=3; hy_data_2020_id=1784503205ee3-09fdce371ad9f1-4d724a11-2073600-1784503205f69; RB4e_2132_smile=1D1; RB4e_2132_nofavfid=1; guidm=0c9c51c4-6799-a31f-d722-b1816003337d; RouteUp=Search; utm_source=%E7%99%BE%E5%BA%A6; utm_campaign=%E5%93%81%E4%B8%93; utm_content=%E5%B7%A6%E4%BE%A7; search_keyword=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; userstatus=%E5%B7%B2%E7%99%BB%E9%99%86; mirrorId=0000; lang=zh-CN; RB4e_2132_viewid=tid_353; www.fxiaoke.com_yxt_fsWebsiteUid=7ad1d9f3db6245958e9a8955e00091b5; lang=zh-CN; RB4e_2132_ulastactivity=c321XE643VGFVyyPggW51pwuUGQYMCvedqp3HYPKmGAJRAuh1dBV; RB4e_2132_auth=3eafu97OGgmSRszypdQCjBciqJMbj2Jz6jaoSEY6plXe323jDM6XR5tQRPOcPvQJ8r%2F%2B8IrHc6QJBgrMtxw1EJg; RB4e_2132_lastcheckfeed=121%7C1675680515; RB4e_2132_home_diymode=1; RB4e_2132_st_t=121%7C1675680608%7C3c9d562d33632e2c1e11e3e31a1f69b6; RB4e_2132_st_p=121%7C1675680665%7C2c42d7b36b0788b00a7f6ded9b71828a; RB4e_2132_lip=*************%2C1675774005; utmParams_yxt=%7B%22%24utm_source%22%3A%22iUgcT4g6o%22%2C%22%24utm_medium%22%3A%22other%22%2C%22%24utm_term%22%3A%22pz%22%2C%22%24utm_content%22%3A%22%22%2C%22%24utm_campaign%22%3A%22%22%7D; support_cookie=1; identityCheckType=2; hy_data_2020_js_sdk=%7B%22distinct_id%22%3A%221784503205ee3-09fdce371ad9f1-4d724a11-2073600-1784503205f69%22%2C%22site_id%22%3A478%2C%22user_company%22%3A409%2C%22props%22%3A%7B%22%24latest_utm_source%22%3A%22%E7%99%BE%E5%BA%A6%22%2C%22%24latest_utm_medium%22%3A%22bdbrand%22%2C%22%24latest_utm_term%22%3A%22pz%22%7D%2C%22device_id%22%3A%221784503205ee3-09fdce371ad9f1-4d724a11-2073600-1784503205f69%22%7D; fsMarketX=7ad1d9f3db6245958e9a8955e00091b5; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1681205803; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2216c741e1b3caa9-07e448755ad492-49724916-2073600-16c741e1b3d326%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_utm_source%22%3A%22%E7%99%BE%E5%BA%A6%22%2C%22%24latest_utm_medium%22%3A%22bdbrand%22%2C%22%24latest_utm_campaign%22%3A%22%E5%93%81%E4%B8%93%22%2C%22%24latest_utm_content%22%3A%22%E5%B7%A6%E4%BE%A7%22%2C%22%24latest_utm_term%22%3A%22pz%22%7D%2C%22%24device_id%22%3A%2216cad1d37e5270-017dda2ffdca17-49724916-2073600-16cad1d37e6e1%22%7D; utm_medium=bdbrand; utm_term=pz; yxt_latest_referrer=; yxt_latest_referrer_host=; Hm_lpvt_06d5233541e92feb3cc8980700b1efa6=1682420314; mirrorIdm=0000; fs_token=D3WqDJ8pCMKjPcHcPIqqEM9XBM9ZOZOjPMKoOZTZE3OsDpDc; FSAuthX=0G60BAQDbW40001y01BvRCjra7krIDF5zuVvjrJ8FnziYwsbIlbJMcyzUV4nUXAUiBFGhpMvUf4gfHJezSNMivHpzAF6dEtddJRYSWUb2nXya4kTzHO1eJPckJ22tUlEq2aYj6fzOL1r6mpxXMQivoWblWNAAea8FcmAc2hZwUPiTZFLcvlvln7aFKcjYStpugPUOOM7SYzd5orWwTkUD7Ndg2yKbyGKiBSweSttl01q8lIoyonaqmC32KmD; FSAuthXC=0G60BAQDbW40001y01BvRCjra7krIDF5zuVvjrJ8FnziYwsbIlbJMcyzUV4nUXAUiBFGhpMvUf4gfHJezSNMivHpzAF6dEtddJRYSWUb2nXya4kTzHO1eJPckJ22tUlEq2aYj6fzOL1r6mpxXMQivoWblWNAAea8FcmAc2hZwUPiTZFLcvlvln7aFKcjYStpugPUOOM7SYzd5orWwTkUD7Ndg2yKbyGKiBSweSttl01q8lIoyonaqmC32KmD; sso_token=79deb559-5b66-4d6a-8745-5e4ee698fdd8; EPXId=dc5e804a8eb74fdf9ad74516cdbd5a65; JSESSIONID=732E44A1C21EB212B54C5EAF691E82CA")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = null;
            try {
                response = client.newCall(request).execute();
                JSONObject jsonObject = JSONObject.parseObject(response.body().string());
                return jsonObject.getJSONObject("Value").getJSONObject("functionResult").getJSONObject("function").getString("body");
            } catch (Exception e) {
                e.printStackTrace();
            }
            return "ERROR";
        }

        private void setCellValue(Integer rowNo, int cellNo, String value) {
            XSSFRow row = newSheet.getRow(rowNo);
            row.createCell(cellNo, CellType.STRING);
            row.getCell(cellNo).setCellValue(value);
        }


        public PloyFuncListener(XSSFSheet newSheet) {
            this.newSheet = newSheet;
        }
    }

    @Data
    public static class PloyFuncExcelVo {
        @ExcelProperty("tenant_id")
        private String ei;
        @ExcelProperty("ea")
        private String ea;
        @ExcelProperty("source_tenant_type")
        private Integer sourceTenantType;
        @ExcelProperty("source_object_api_name")
        private String sourceObjApiName;
        @ExcelProperty("dest_object_api_name")
        private String destObjApiName;
        @ExcelProperty("before_func_api_name")
        private String beforeFuncApiName;
        @ExcelProperty("during_func_api_name")
        private String duringFuncApiName;
        @ExcelProperty("after_func_api_name")
        private String afterFuncApiName;
    }
}
