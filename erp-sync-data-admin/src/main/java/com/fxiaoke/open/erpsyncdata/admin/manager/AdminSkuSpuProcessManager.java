package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminOuterService;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldTypeContants;
import com.fxiaoke.open.erpsyncdata.common.constant.SpuSkuConstant;
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/5/19.
 */
@Component
@Slf4j
public class AdminSkuSpuProcessManager {
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private AdminOuterService adminOuterService;

    public void processSkuSpu(SyncPloyDetailSnapshotEntity snapshotEntity, SyncPloyDetailEntity ployDetail, SyncPloyDetailData syncPloyDetailData, String destTenantId) {
        if ((SpuSkuConstant.SPU_OBJ.equals(ployDetail.getSourceObjectApiName()) && SpuSkuConstant.SPU_OBJ.equals(ployDetail.getDestObjectApiName())) || (
            SpuSkuConstant.PRODUCT_OBJ.equals(ployDetail.getSourceObjectApiName()) && SpuSkuConstant.PRODUCT_OBJ.equals(ployDetail.getDestObjectApiName()))) {
            String multipleUnitValue = adminOuterService.getConfigValueByKey(Integer.valueOf(destTenantId),"multiple_unit").getData();
            Boolean isOpenMutilUnit = false;
            if (multipleUnitValue.equals("1")) {
                isOpenMutilUnit = true;
                log.info("not open mutil unit");
            }
            String spuValue = adminOuterService.getConfigValueByKey(Integer.valueOf(destTenantId),"spu").getData();
            Boolean isOpenSpu = false;
            if (spuValue.equals("1")) {
                isOpenSpu = true;
                log.info("not open spu");
            }
            if (isOpenMutilUnit) {
                snapshotEntity.getDetailObjectSyncConditionsExpressions().put(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ, ConditionUtil.getTrueExpression());
                DetailObjectMappingsData.DetailObjectMappingData multiUnitRelatedObjData = new DetailObjectMappingsData.DetailObjectMappingData();
                multiUnitRelatedObjData.setSourceObjectApiName(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ);
                multiUnitRelatedObjData.setDestObjectApiName(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ);
                multiUnitRelatedObjData.setFieldMappings(this.getAndProcessFieldMapping(ployDetail.getFieldMappings(), ployDetail.getSourceObjectApiName(), ployDetail.getDestObjectApiName(),
                    Integer.valueOf(snapshotEntity.getDestTenantId()), SpuSkuConstant.MULTI_UNIT_RELATED_OBJ));
                syncPloyDetailData.getDetailObjectMappings().add(multiUnitRelatedObjData);
                SyncConditionsData multiUnitRelatedObjSyncConditionsData = new SyncConditionsData();
                multiUnitRelatedObjSyncConditionsData.setApiName(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ);
                multiUnitRelatedObjSyncConditionsData.setIsSyncForce(true);
                syncPloyDetailData.getDetailObjectSyncConditions().add(multiUnitRelatedObjSyncConditionsData);
            }
            if (isOpenSpu) {
                snapshotEntity.getDetailObjectSyncConditionsExpressions().put(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ, ConditionUtil.getTrueExpression());
                DetailObjectMappingsData.DetailObjectMappingData spuSkuSpecValueRelateObjData = new DetailObjectMappingsData.DetailObjectMappingData();
                spuSkuSpecValueRelateObjData.setSourceObjectApiName(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ);
                spuSkuSpecValueRelateObjData.setDestObjectApiName(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ);
                spuSkuSpecValueRelateObjData.setFieldMappings(this.getAndProcessFieldMapping(ployDetail.getFieldMappings(), ployDetail.getSourceObjectApiName(), ployDetail.getDestObjectApiName(),
                    Integer.valueOf(snapshotEntity.getDestTenantId()), SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ));
                syncPloyDetailData.getDetailObjectMappings().add(spuSkuSpecValueRelateObjData);
                SyncConditionsData spuSkuSpecValueRelateObjSyncConditionsData = new SyncConditionsData();
                spuSkuSpecValueRelateObjSyncConditionsData.setApiName(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ);
                spuSkuSpecValueRelateObjSyncConditionsData.setIsSyncForce(true);
                syncPloyDetailData.getDetailObjectSyncConditions().add(spuSkuSpecValueRelateObjSyncConditionsData);
            }
        }
    }

    public List<FieldMappingData> getAndProcessFieldMapping(FieldMappingsData masterFieldMappings, String sourceApiName, String destApiName, Integer tenantId, String apiName) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeService
            .getDescribe(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, SuperUserConstants.USER_ID), apiName);
        ControllerGetDescribeResult describes = result.getData();
        List<FieldMappingData> fieldMappings = new ArrayList<>();
        for (FieldDescribe fieldDescribe : describes.getDescribe().getFields().values()) {
            if (fieldDescribe.getDefineType().equals("system") || fieldDescribe.getType().equals(FieldType.DEPARTMENT)) {
                continue;
            }
            if (SpuSkuConstant.SPU_OBJ.equals(sourceApiName) && SpuSkuConstant.SPU_OBJ.equals(destApiName)) {
                if (SpuSkuConstant.SPU_ID.equals(fieldDescribe.getApiName())) {
                    fieldMappings.add(this.changeToMasterDetailMappingType(fieldDescribe, SpuSkuConstant.SPU_OBJ, SpuSkuConstant.SPU_OBJ));
                    continue;
                } else if (SpuSkuConstant.MULTI_UNIT_RELATED_OBJ.equals(apiName) && SpuSkuConstant.PRODUCT_ID.equals(fieldDescribe.getApiName())) {
                    fieldMappings.add(this.changeToObjectReferenceMappingType(fieldDescribe, SpuSkuConstant.PRODUCT_OBJ, SpuSkuConstant.PRODUCT_OBJ));
                    continue;
                } else if (SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ.equals(apiName) && SpuSkuConstant.SKU_ID.equals(fieldDescribe.getApiName())) {
                    fieldMappings.add(this.changeToObjectReferenceMappingType(fieldDescribe, SpuSkuConstant.PRODUCT_OBJ, SpuSkuConstant.PRODUCT_OBJ));
                    continue;
                }
            } else if (SpuSkuConstant.PRODUCT_OBJ.equals(sourceApiName) && SpuSkuConstant.PRODUCT_OBJ.equals(destApiName)) {
                if (SpuSkuConstant.MULTI_UNIT_RELATED_OBJ.equals(apiName) && SpuSkuConstant.PRODUCT_ID.equals(fieldDescribe.getApiName())) {
                    fieldMappings.add(this.changeToMasterDetailMappingType(fieldDescribe, SpuSkuConstant.PRODUCT_OBJ, SpuSkuConstant.PRODUCT_OBJ));
                    continue;
                } else if (SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ.equals(apiName) && SpuSkuConstant.SKU_ID.equals(fieldDescribe.getApiName())) {
                    fieldMappings.add(this.changeToMasterDetailMappingType(fieldDescribe, SpuSkuConstant.PRODUCT_OBJ, SpuSkuConstant.PRODUCT_OBJ));
                    continue;
                } else if (SpuSkuConstant.SPU_ID.equals(fieldDescribe.getApiName())) {
                    fieldMappings.add(this.changeToObjectReferenceMappingType(fieldDescribe, SpuSkuConstant.SPU_OBJ, SpuSkuConstant.SPU_OBJ));
                    continue;
                }
            }

            if (SpuSkuConstant.SPEC_VALUE_ID.equals(fieldDescribe.getApiName())) {
                fieldMappings.add(this.changeToObjectReferenceMappingType(fieldDescribe, SpuSkuConstant.SPECIFICATION_VALUE_OBJ, SpuSkuConstant.SPECIFICATION_VALUE_OBJ));
                continue;
            } else if (SpuSkuConstant.SPEC_ID.equals(fieldDescribe.getApiName())) {
                fieldMappings.add(this.changeToObjectReferenceMappingType(fieldDescribe, SpuSkuConstant.SPECIFICATION_OBJ, SpuSkuConstant.SPECIFICATION_OBJ));
                continue;
            } else if (SpuSkuConstant.UNIT_ID.equals(fieldDescribe.getApiName())) {
                for (FieldMappingData masterFieldMapping : masterFieldMappings) {
                    if (masterFieldMapping.getSourceApiName().equals(SpuSkuConstant.UNIT)&&masterFieldMapping.getDestApiName().equals(SpuSkuConstant.UNIT)){
                        FieldMappingData unitIdMapping = buildFieldMappingData(fieldDescribe);
                        unitIdMapping.setOptionMappings(masterFieldMapping.getOptionMappings());
                        fieldMappings.add(unitIdMapping);
                        break;
                    }
                }
                continue;
            }
            if (fieldDescribe.getType().equals(FieldTypeContants.SELECT_ONE) || fieldDescribe.getType().equals(FieldTypeContants.TRUE_OR_FALSE)) {
                FieldMappingData select = buildFieldMappingData(fieldDescribe);
                fieldMappings.add(select);
            } else {
                FieldMappingData fieldMappingData = new FieldMappingData();
                fieldMappingData.setDestApiName(fieldDescribe.getApiName());
                fieldMappingData.setDestType(fieldDescribe.getType());
                fieldMappingData.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
                fieldMappingData.setSourceApiName(fieldDescribe.getApiName());
                fieldMappingData.setSourceType(fieldDescribe.getType());
                fieldMappings.add(fieldMappingData);
            }
        }
        return fieldMappings;
    }

    private FieldMappingData buildFieldMappingData(FieldDescribe fieldDescribe) {
        FieldMappingData select = new FieldMappingData();
        select.setDestApiName(fieldDescribe.getApiName());
        select.setDestType(fieldDescribe.getType());
        select.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
        select.setSourceApiName(fieldDescribe.getApiName());
        select.setSourceType(fieldDescribe.getType());
        List<OptionMappingData> ops = Lists.newArrayList();
        for (Map<String, Object> optionsMap : fieldDescribe.getOptions()) {
            OptionMappingData op = new OptionMappingData();
            op.setSourceOption(optionsMap.get("value"));
            op.setDestOption(optionsMap.get("value"));
            ops.add(op);
        }
        select.setOptionMappings(ops);
        return select;
    }

    private FieldMappingData changeToMasterDetailMappingType(FieldDescribe fieldDescribe, String sourceTargetApiName, String destTargetApiName) {
        FieldMappingData fieldMappingData = new FieldMappingData();
        fieldMappingData.setDestApiName(fieldDescribe.getApiName());
        fieldMappingData.setDestType(FieldTypeContants.MASTER_DETAIL);
        fieldMappingData.setDestTargetApiName(destTargetApiName);
        fieldMappingData.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
        fieldMappingData.setSourceApiName(fieldDescribe.getApiName());
        fieldMappingData.setSourceType(FieldTypeContants.MASTER_DETAIL);
        fieldMappingData.setSourceTargetApiName(sourceTargetApiName);
        return fieldMappingData;
    }

    private FieldMappingData changeToObjectReferenceMappingType(FieldDescribe fieldDescribe, String sourceTargetApiName, String destTargetApiName) {
        FieldMappingData fieldMappingData = new FieldMappingData();
        fieldMappingData.setDestApiName(fieldDescribe.getApiName());
        fieldMappingData.setDestType(FieldTypeContants.OBJECT_REFERENCE);
        fieldMappingData.setDestTargetApiName(destTargetApiName);
        fieldMappingData.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
        fieldMappingData.setSourceApiName(fieldDescribe.getApiName());
        fieldMappingData.setSourceType(FieldTypeContants.OBJECT_REFERENCE);
        fieldMappingData.setSourceTargetApiName(sourceTargetApiName);
        return fieldMappingData;
    }
}
