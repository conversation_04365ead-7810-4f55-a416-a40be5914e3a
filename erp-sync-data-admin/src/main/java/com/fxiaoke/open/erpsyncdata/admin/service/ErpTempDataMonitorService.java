package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.IdListArg;
import com.fxiaoke.open.erpsyncdata.admin.result.AsyncGetTotalResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.DeleteErpTempDataMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetTempDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpHistoryTaskNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpTempDataMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpTempDataMonitorSimpleArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.RefreshTempDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 17:04 2021/8/11
 * @Desc:
 */
public interface ErpTempDataMonitorService {
    Result<QueryResult<List<ErpTempDataMonitorResult>>> queryTempDataMonitorList(String tenantId,
                                                                                 String dataCenterId,
                                                                                 Integer userId,
                                                                                 QueryErpTempDataMonitorArg arg,
                                                                                 String lang);

    Result<QueryResult<List<ErpTempDataMonitorSimpleMsgResult>>> queryAllTempDataMonitorList(String tenantId,String dataCenterId, Integer userId, QueryErpTempDataMonitorSimpleArg arg);

    Result<ErpTempDataMonitorResult> getTempDataMonitor(String tenantId, Integer userId, GetTempDataArg idArg, String lang);

    Result<List<ErpHistoryTaskNameResult>> queryAllHistoryTask(String tenantId,
                                                               String dataCenterId,
                                                               Integer userId,
                                                               QueryErpHistoryTaskNameArg arg,
                                                               String lang);

    Result<Long> removeTempDataMonitorByIdList(String tenantId, String dataCenterId, Integer userId, IdListArg arg);

    Result<Long> removeTempDataMonitorByFilter(String tenantId, String dataCenterId, Integer userId,String phone, DeleteErpTempDataMonitorArg arg);

    Result<ErpRealAndFakeApiNameResult> queryErpTrueAndFakeApiName(String tenantId, String dataCenterId,String streamId);

    Result<AsyncGetTotalResult> getTempDataMonitorListTotalSize(String tenantId,
                                                                String dataCenterId,
                                                                Integer userId,
                                                                String taskId,
                                                                QueryErpTempDataMonitorArg arg,
                                                                String lang);

    Result<AsyncGetTotalResult> getTotalByTaskId(String tenantId, String dataCenterId, Integer userId, String taskId);

    Result<Void> refreshTempDataByIds(String tenantId,String dcId,RefreshTempDataArg arg);
}
