package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/11 18:01:45
 */
public interface GetK3AllSaveStatus {
    @Data
    class Arg {

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<SaveStatus> addStatusList;

        private List<SaveStatus> modifyStatusList;

        /**
         * 预置保存参数Key
         */
        private List<KeyType> list;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class AddStatus {
        private Integer status;
        private List<ActionConf> actions;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class SaveStatus {
        private String status;
        private List<ActionConf> actions;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ActionConf {
        private Boolean selected;
        private Integer action;
        private String url;

        public ActionConf(final Integer action, final String url) {
            this.action = action;
            this.url = url;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class KeyType {
        private String key;
        /**
         * 1.数组 2.布尔 3.字符串
         */
        private int type;

        public static List<GetK3AllSaveStatus.KeyType> convert(Map<String, Integer> k3PresetSaveConfigKey) {
            return k3PresetSaveConfigKey.entrySet().stream()
                    .map(entry -> {
                        GetK3AllSaveStatus.KeyType keyType = new GetK3AllSaveStatus.KeyType();
                        keyType.setKey(entry.getKey());
                        keyType.setType(entry.getValue());
                        return keyType;
                    }).collect(Collectors.toList());
        }
    }
}
