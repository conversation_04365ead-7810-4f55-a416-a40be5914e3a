package com.fxiaoke.open.erpsyncdata.admin.remote;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.FindDescribeManageListArg;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.admin.data.ListObjectFieldsData;
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldTypeContants;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.common.util.CommonPoolUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CrmRemoteManager {
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;

    @ReloadableProperty("mapping.black.list")
    private String mappingBlackListStr;

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    public String getMasterObjectApiName(String sourceTenantId, String sourceApiName) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(sourceTenantId), CrmConstants.SYSTEM_USER);
        ObjectDescribe describeResult = objectDescribeService.getDescribe(headerObj, sourceApiName).getData().getDescribe();
        for (FieldDescribe fieldDescribe : describeResult.getFields().values()) {
            if (fieldDescribe.getType().equals(FieldTypeContants.MASTER_DETAIL) && fieldDescribe.getIsActive()) {
                return fieldDescribe.getTargetApiName();
            }
        }
        return null;
    }

    public ListObjectDescribeResult listObjectDescribes(String tenantId, List<String> apiNames) {
        ListObjectDescribeResult listObjectDescribeResult = objectDescribeService
                .list(HeaderObj.newInstance(Integer.valueOf(tenantId), SuperUserConstants.USER_ID), true, apiNames).getData();
        return listObjectDescribeResult;
    }

    public Map<String, ListObjectFieldsResult> listObjectFieldsWithFilterBlackList(String tenantId, List<String> objectApiNames) {
        List<String> mappingBlackList = JSON.parseObject(mappingBlackListStr, List.class);
        Map<String, ListObjectFieldsResult> dataList = Maps.newHashMap();
        List<ObjectDescribe> objectDescribes = this.listObjectAndFieldByApiNames(tenantId, objectApiNames);
        if (objectDescribes == null || objectDescribes.size() == 0) {
            return dataList;
        }
        for (ObjectDescribe objectDescribe : objectDescribes) {
            List<ObjectFieldResult> objectFieldResults = new ArrayList<>();
            for (FieldDescribe fieldDescribe : objectDescribe.getFields().values()) {
                if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                    continue;
                }
                if (mappingBlackList.contains(fieldDescribe.getApiName())) {
                    continue;
                }
                ObjectFieldResult objectFieldResult = new ObjectFieldResult();
                objectFieldResult.setApiName(fieldDescribe.getApiName());
                objectFieldResult.setType(fieldDescribe.getType());
                objectFieldResult.setDefineType(fieldDescribe.getDefineType());
                objectFieldResult.setIsRequired(fieldDescribe.isRequired());
                objectFieldResult.setIsUnique(fieldDescribe.isUnique());
                objectFieldResult.setLabel(fieldDescribe.getLabel());
                objectFieldResult.setTargetApiName(fieldDescribe.getTargetApiName());
                objectFieldResult.setQuoteFieldType(fieldDescribe.getQuoteFieldType());
                if (fieldDescribe.getType().equals(FieldTypeContants.QUOTE)) {
                    String[] quoteFieldArray = fieldDescribe.getQuoteField().split("\\.");
                    String quoteFieldApiName = quoteFieldArray[0].replaceAll("__r", "");
                    String quoteFieldTagetObjectFieldApiName = quoteFieldArray[1];
                    objectFieldResult.setSourceQuoteRealField(quoteFieldApiName);
                    objectFieldResult.setSourceQuoteFieldTargetObjectField(quoteFieldTagetObjectFieldApiName);
                    String quoteFieldTargetApiName = objectDescribe.getFields().get(quoteFieldApiName).getTargetApiName();
                    objectFieldResult.setSourceQuoteFieldTargetObjectApiName(quoteFieldTargetApiName);
                    if (fieldDescribe.getQuoteFieldType().equals(FieldTypeContants.SELECT_ONE) || fieldDescribe.getQuoteFieldType().equals(FieldTypeContants.SELECT_MANY)) {
                        List<Map<String, Object>> options = this.getObjectFieldOption(tenantId, quoteFieldTargetApiName, quoteFieldTagetObjectFieldApiName);
                        fieldDescribe.put("options", options);
                    }
                }

                Set<ObjectFieldResult.Option> options = null;
                if (fieldDescribe.getOptions() != null) {
                    options = new HashSet<>();
                    for (Map<String, Object> fieldOption : fieldDescribe.getOptions()) {
                        //not_usable类型的单选值，需要保留，不然，被禁用的字段，无法对接使用，比如:crm->erp方向，被禁用的单选值，客户也有对接到ERP的需求
//                        Boolean notUsable = (Boolean) fieldOption.get("not_usable");
//                        if (notUsable != null && notUsable) {
//                            continue;
//                        }
                        ObjectFieldResult.Option option = new ObjectFieldResult.Option();
                        option.setValue(fieldOption.get("value"));
                        option.setLabel(fieldOption.get("label"));
                        if (FieldTypeContants.RECORD_TYPE.equals(fieldDescribe.getType())) {
                            option.setValue(fieldOption.get("api_name"));
                        }
                        options.add(option);
                    }
                }
                if (FieldTypeContants.RECORD_TYPE.equals(fieldDescribe.getType())) {
                    objectFieldResult.setIsRequired(true);
                }
                objectFieldResult.setOptions(options);

                objectFieldResults.add(objectFieldResult);
            }

            ListObjectFieldsResult result = new ListObjectFieldsResult();
            result.setFields(objectFieldResults);
            result.setObjectApiName(objectDescribe.getApiName());
            result.setObjectName(objectDescribe.getDisplayName());
            dataList.put(objectDescribe.getApiName(), result);
        }
        return dataList;
    }

    public Map<String, String> listObjectNamesByApiNames(String tenantId, List<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return new HashMap<>();
        }
        apiNames = apiNames.stream().distinct().collect(Collectors.toList());
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
        Result<ListObjectDescribeResult> result = objectDescribeService.list(headerObj, false, apiNames);
        if (!result.isSuccess()) {
            return Maps.newHashMap();
        }
        List<ObjectDescribe> describe = result.getData().getDescribe();
        return describe.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getDisplayName));
    }

    public ObjectDescribe getObjectAndFieldByApiName(String tenantId, String apiName) {
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
        Result<ListObjectDescribeResult> result = objectDescribeService.list(headerObj, true, Lists.newArrayList(apiName));
        if (CollectionUtils.isEmpty(result.getData().getDescribe())) {
            return null;
        }
        return result.getData().getDescribe().get(0);
    }

    public List<ObjectDescribe> listObjectAndFieldByApiNames(String tenantId, List<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return new ArrayList<>(0);
        }
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
        return CommonPoolUtil.execute(apiNames, 30, x -> {
            Result<ListObjectDescribeResult> result = objectDescribeService.list(headerObj, true, x);
            return result.getData().getDescribe();
        });
    }

    public List<Map<String, Object>> getObjectFieldOption(String tenantId, String objectApiName, String fieldName) {
        List<ObjectDescribe> targetObjectDescribes = listObjectAndFieldByApiNames(tenantId, Lists.newArrayList(objectApiName));
        if (targetObjectDescribes == null || targetObjectDescribes.isEmpty()) {
            return new ArrayList<>(0);
        }

        FieldDescribe fieldDescribe = targetObjectDescribes.get(0).getFields().get(fieldName);
        if (fieldDescribe == null) {
            return new ArrayList<>(0);
        }
        return fieldDescribe.getOptions();
    }

    public List<ObjectDescribe> listMasterObjects(String tenantId) {
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
        return objectDescribeCrmService.findDescribeManageList(headerObj, new FindDescribeManageListArg()).getData().getObjectDescribeList();
    }

    public List<ObjectDescribe> listAllObjectByTenant(String tenantId) {
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
        Result<ListObjectDescribeResult> result = objectDescribeService.listObjectDescribe(headerObj, "CRM");
        return result.getData().getDescribe();
    }

    public List<ObjectDescribe> listAllObjectAndFieldsByTenant(String tenantId) {
        List<ObjectDescribe> allObjectDescribe = this.listAllObjectByTenant(tenantId);
        List<String> apiNames = allObjectDescribe.stream().filter(ObjectDescribe::isActive).filter(x -> !x.isDelete()).map(ObjectDescribe::getApiName).collect(Collectors.toList());
        return this.listObjectAndFieldByApiNames(tenantId, apiNames);
    }

    public List<ObjectData> listCrmObjectByFilter(String tenantId, String apiName, List<List<FilterData>> filters, int pageNumber, int pageSize) {
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);

        ControllerListArg arg = new ControllerListArg();

        SearchQuery searchQuery = new SearchQuery();
        if (filters != null && filters.size() != 0) {
            List<Wheres> wheres = new ArrayList<>();
            for (List<FilterData> orFilters : filters) {
                List<Filter> andFilters = new ArrayList<>();
                for (FilterData filterData : orFilters) {
                    andFilters.add(buildFilter(filterData.getFieldApiName(), filterData.getOperate(), filterData.getFieldValue()));
                }
                wheres.add(this.buildWheresByFilters(andFilters));
            }
            searchQuery.setWheres(wheres);
        }
        searchQuery.setOffset((pageNumber - 1) * pageSize);
        searchQuery.setLimit(pageSize);

        arg.setObjectDescribeApiName(apiName);
        arg.setSearchQuery(searchQuery);

        Page<com.fxiaoke.crmrestapi.common.data.ObjectData> result = metadataControllerService.list(headerObj, apiName, arg).getData();
        return BeanUtil2.deepCopyList(result.getDataList(), ObjectData.class);
    }

    private Wheres buildWheresByFilters(List<Filter> filters) {
        Wheres wheres = new Wheres();
        wheres.setFilters(filters);
        return wheres;
    }

    private Filter buildFilter(String fieldName, String operator, List<String> fieldValues) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }

    public Map<String, List<String>> queryEnterpriseLevelLabel(List<String> tenantIds) {
        Map<String, List<String>> tenantId2Level = new HashMap<>();
        List<List<String>> splitEis = CollUtil.split(tenantIds, 2000);
        for (List<String> eis : splitEis) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            HeaderObj headerObj = new HeaderObj(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID), CrmConstants.SYSTEM_USER);
            searchTemplateQuery.addFilter(ConfigCenter.FS_ACCOUNT_TENANT_ID, eis, "IN");
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setSearchSource("es");
            searchTemplateQuery.setLimit(2000);
            searchTemplateQuery.setNeedReturnCountNum(false);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> queryAccount;
            FindV3Arg findV3Arg = new FindV3Arg();
            findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchTemplateQuery));
            findV3Arg.setDescribeApiName(ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName());
            //指定返回字段
            findV3Arg.setSelectFields(CollUtil.newArrayList(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL_NEW, ConfigCenter.FS_ACCOUNT_TENANT_ID));
            //从客户对象获取
            try {
                queryAccount = objectDataServiceV3.queryList(headerObj, findV3Arg);
            } catch (Exception e) {
                return Maps.newHashMap();
            }
            log.info("query accountObj data arg:{},result:{}", searchTemplateQuery, queryAccount);
            if (queryAccount.isSuccess()
                    && queryAccount.getData() != null
                    && queryAccount.getData().getQueryResult() != null
                    && org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryAccount.getData().getQueryResult().getDataList())) {
                List<com.fxiaoke.crmrestapi.common.data.ObjectData> dataList = queryAccount.getData().getQueryResult().getDataList();
                for (com.fxiaoke.crmrestapi.common.data.ObjectData objectData : dataList) {
                    Object levels = objectData.get(ConfigCenter.FS_ACCOUNT_ENTERPRISE_LEVEL_NEW);
                    if (levels instanceof List){
                        //noinspection unchecked
                        tenantId2Level.putIfAbsent(objectData.getString(ConfigCenter.FS_ACCOUNT_TENANT_ID), (List<String>) levels);
                    }
                }
            }
        }
        return tenantId2Level;
    }

}
