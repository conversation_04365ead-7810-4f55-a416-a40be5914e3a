package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result3;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result4;

/**
 * OA连接器服务
 * <AUTHOR>
 * @date 2024.5.9
 */
public interface OAConnectorService {
    Result3<Void> checkAndInitFeiShuConnector(String tenantId,String dataCenterId);
    Result4<Void> checkAndInitQywxConnector(String tenantId,String dataCenterId);
}
