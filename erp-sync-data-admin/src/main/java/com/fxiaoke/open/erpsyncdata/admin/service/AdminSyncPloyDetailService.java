package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AdminSyncPloyDetailService {
    Result<ListBaseInfosByTypeResult> listByType(String tenantId,
                                                 String dataCenterId,
                                                 Integer type,
                                                 Integer status,
                                                 String apiName,
                                                 String searchText,
                                                 int pageNumber,
                                                 int pageSize,
                                                 String lang);

    Result<SyncPloyDetailResult> getById(String tenantId, String id, String lang);
    Result<SyncPloyDetailResult> getByIdWithCache(String tenantId, String id, String lang);
    Result<Void> updateValid(String tenantId, String id, Boolean valid);

    Result<SyncPloyDetailListByPloyIdResult> listByPloyId(String tenantId,
                                                          String ployId,
                                                          int pageNumber,
                                                          int pageSize,
                                                          String dataCenterId,
                                                          String lang);

    Result<List<SyncPloyDetailResult>> listByTenantId(String tenantId);

    Result<FieldMappingsResult> getFieldMappingsById(String tenantId, String id);

    Result<SyncRulesResult> getSyncRules(String tenantId, String id);

    Result<SyncConditionsResult> getSyncConditions(String tenantId, String id);

    Result<String> create(String tenantId, SyncPloyDetailCreateArg arg,String lang);

    Result<Void> delete(String tenantId, String id);

    Result<Void> update(String tenantId, SyncPloyDetailUpdateArg arg);

    Result<Void> updateStatus(String tenantId, Integer userId, String id, Integer status);

    Result<String> updateFieldMappings(String tenantId, SyncPloyDetailUpdateFieldMappingsArg arg);

    Result<Void> updateSyncRules(String tenantId, SyncPloyDetailUpdateSyncRulesArg arg);

    Result<Void> doLastSyncTime(String tenantId, String dataCenterId, String sourceObjApiName, SyncRulesData rulesData);

    Result<Void> updateSyncConditions(String tenantId, SyncPloyUpdateSyncConditionsArg arg);
    /**
     * 添加或修改自定义函数
     * @param tenantId 企业id
     * @param userId 用户id
     * @param arg
     * @return
     */
    Result<Void> saveOrUpdateCustomFunction(Integer tenantId, Integer userId, CustomFunctionCreateArg arg);

    Result<ListDistinctApiNameByTypeResult> listDistinctApiNamesByType(String loginUserTenantId,String dataCenterId, Integer type, Integer status, String apiName, Integer pageNumber, Integer pageSize);

    Result<List<SyncObjectResult>> listMasterSyncObjects(ListMasterObjectArg arg,String lang);

    Result<Void> insertPollingTime(String tenantId, String syncPloyDetailId, PollingIntervalApiDto pollingInterval);

    /**
     * 新增或移除推送对象列表？
     */
    Result<Void> startRulesAddOrRemoveApiNames(String tenantId,String sourceObjectApiName,String erpDataCenterId,List<String> syncTypeList);

    Result<Void> updateUsedQueryField(String tenantId, String dataCenterId, String sourceApiName, ErpChannelEnum erpChannelEnum);

    Result<Void> updateUsedQueryFieldByObjectApiName(String tenantId, String dataCenterId, String erpSourceApiName);

    Result<SyncPloyDetailResult> getSyncPloyDetailAndSourceDestApiName(String tenantId, String sourceObjectApiName, String destObjectApiName, Integer tenantType ) ;

    Result<List<SyncObjectResult>> listTenantAllCrmObjects(String tenantId);

    Result<List<Result<?>>> batchCheckAndUpdatePloyStatus(String tenantId, Integer userId,SyncPloyDetailUpdateStatusArg.BatchArg arg);

    @NotNull
    Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkAndUpdatePloyStatus(String tenantId,
                                                                                  Integer userId,
                                                                                  String dcId,
                                                                                  String id,
                                                                                  Integer status,
                                                                                  boolean needSyncDuringStop,
                                                                                  String lang);

    Result<Integer> countModifiedCrmData(String tenantId,String objApiName,Long beginTime,String lang);

    Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkAndDisablePloy(String tenantId,
                                                                             Integer userId,
                                                                             String dcId,
                                                                             String id,
                                                                             String lang);

    Result<Set<CheckAndUpdatePloyValidStatusDetailData>> updateIntegrationStreamInEnabled(String tenantId,
                                                                                          Integer loginUserId,
                                                                                          String dcId,
                                                                                          UpdateIntegrationStreamArg arg,
                                                                                          String lang);

    Result<List<SyncPloyDetailData>> getTop1000ByDcId(String tenantId, String dcId);

    Result<Void> checkInitPollingIntervalAndSyncTime(String dcId, String id, String tenantId, SyncPloyDetailResult ployDetailResult);

    /**
     * 获取集成流所有对象和字段
     * key:ObjApiName value:fields
     */
    Result<Map<String, Map<String, Set<Object>>>> getAllObjectAndFieldApiNameByPloy(List<SyncPloyDetailData> ployDetails);

    Result<List<SyncWalkingNodeResult>> getPloyDetailNodeSettings(String tenantId, String id, String lang);
}
