package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjCustomFunctionResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:56 2020/11/23
 * @Desc:
 */
public interface ErpObjCustomFunctionService {

    Result<List<ErpObjCustomFunctionResult>> query(String tenantId,String dataCenterId, int userId);
    Result<List<ErpObjCustomFunctionResult>> queryByApiName(String tenantId,String dataCenterId, int userId, String objApiName);

    List<String> queryAllFunctionApiNameByObjectApiName(String tenantId, String dataCenterId, List<String> objApiNameList);

    Result<String> update(String tenantId, int userId, ErpObjCustomFunctionResult erpObjCustomFunctionResult,String lang);

    Result<String> delete(String tenantId, int userId,String dcId, BaseArg deleteArg);
    //Result<String> execute(String tenantId, int userId, ErpObjCustomFunctionResult erpObjCustomFunctionResult);
}
