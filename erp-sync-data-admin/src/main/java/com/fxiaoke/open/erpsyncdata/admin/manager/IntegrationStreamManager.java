package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.facishare.restful.common.StopWatch;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.admin.utils.ResultConversionUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncForceConstant;
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjDispatchPriorityConfig;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.HttpUrlUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.PollingIntervalUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/4 17:38 集成流管理器，目前主要作用于校验节点的正确性
 * @Version 1.0
 */
@Slf4j
@Service
public class IntegrationStreamManager {


    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private FsCrmObjectService fsCrmObjectService;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private SyncPloyDetailAdminManager syncPloyDetailAdminManager;
    @Autowired
    private AdminNodeSettingService adminNodeSettingService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;

    /**
     * 统一校验逻辑
     */
    public Map<String, UpdateIntegrationStreamResult.ErrorMsg> commonVerifyStreamDetail(String tenantId,
                                                                                        SyncPloyDetailEntity syncPloyDetailEntity,
                                                                                        String lang) {
        StopWatch stopWatch = StopWatch.create("commonVerifyStreamDetail");
        Map<String, UpdateIntegrationStreamResult.ErrorMsg> errorMessageMap = Maps.newHashMap();
        //系统节点
        boolean isCRM2ERP = syncPloyDetailEntity.getSourceTenantType().equals(TenantType.CRM);
        String erpDcId = isCRM2ERP ? syncPloyDetailEntity.getDestDataCenterId() : syncPloyDetailEntity.getSourceDataCenterId();
        //检验crm对象是否被禁用
        String crmObjApiName = isCRM2ERP ? syncPloyDetailEntity.getSourceObjectApiName() : syncPloyDetailEntity.getDestObjectApiName();
        Result<Boolean> verifyCrmObjLifeStatus = this.verifyCrmObjLifeStatus(tenantId, crmObjApiName);
        stopWatch.lap("verifyCrmObjLifeStatus");
        List<IntegrationFieldVerifyResult> verifyCrmObjLifeStatusMessage = verifyCrmObjLifeStatus.getData() ? Lists.newArrayList(IntegrationFieldVerifyResult.newInstanceErrCode(ResultCodeEnum.CRM_OBJECT_INVALID)) : Lists.newArrayList();
        UpdateIntegrationStreamResult.ErrorMsg crmObjectMessage = new UpdateIntegrationStreamResult.ErrorMsg(verifyCrmObjLifeStatus.getData(), JSONObject.toJSONString(verifyCrmObjLifeStatusMessage));

        if (isCRM2ERP) {
            errorMessageMap.put("sourceSystemNode", crmObjectMessage);
        } else {
            errorMessageMap.put("destSystemNode", crmObjectMessage);
        }

        //校验数据范围的字段是否被禁用
        if (CollectionUtils.isNotEmpty(syncPloyDetailEntity.getSyncConditions().getFilters())) {
            ArrayList<SyncConditionsData> allConditions = Lists.newArrayList();
            allConditions.add(syncPloyDetailEntity.getSyncConditions());
            allConditions.addAll(syncPloyDetailEntity.getDetailObjectSyncConditions());
            List<IntegrationFieldVerifyResult> integrationFieldVerifyResults = this.verifyConditionFields(tenantId, allConditions, isCRM2ERP, erpDcId,lang);
            UpdateIntegrationStreamResult.ErrorMsg conditionMessage = new UpdateIntegrationStreamResult.ErrorMsg(false, StringUtils.EMPTY);
            if (CollectionUtils.isNotEmpty(integrationFieldVerifyResults)) {
                conditionMessage = new UpdateIntegrationStreamResult.ErrorMsg(true, JSONObject.toJSONString(integrationFieldVerifyResults));
            }
            errorMessageMap.put("syncConditionsNode", conditionMessage);
        }
        stopWatch.lap("verifyConditionFields");
        //校验函数列表是否可用
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getBeforeFuncApiName())) {
            UpdateIntegrationStreamResult.ErrorMsg beforeFunction = getErrorMsg(tenantId, syncPloyDetailEntity.getBeforeFuncApiName());
            errorMessageMap.put("beforeFunctionNode", beforeFunction);
        }
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getDuringFuncApiName())) {
            UpdateIntegrationStreamResult.ErrorMsg durationFunction = getErrorMsg(tenantId, syncPloyDetailEntity.getDuringFuncApiName());
            errorMessageMap.put("durationFunctionApiNode", durationFunction);
        }
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getAfterFuncApiName())) {
            UpdateIntegrationStreamResult.ErrorMsg afterFunction = getErrorMsg(tenantId, syncPloyDetailEntity.getAfterFuncApiName());
            errorMessageMap.put("afterFunctionNode", afterFunction);
        }
        stopWatch.lap("functionNode");
        //字段映射
        Result<List<IntegrationFieldVerifyResult>> fieldMappingResult = this.verifyFieldMapping(tenantId, syncPloyDetailEntity,lang);
        stopWatch.lap("verifyFieldMapping");
        boolean verifyFieldMappingStatus = CollectionUtils.isEmpty(fieldMappingResult.getData()) ? false : true;
        //TODO 字段映射暂时不设置
        UpdateIntegrationStreamResult.ErrorMsg fieldMappingError = new UpdateIntegrationStreamResult.ErrorMsg(verifyFieldMappingStatus, JSONObject.toJSONString(fieldMappingResult.getData()));
        errorMessageMap.put("fieldMappingNode", fieldMappingError);
        //回写crm节点
        if (syncPloyDetailEntity.getIntegrationStreamNodes() != null && syncPloyDetailEntity.getIntegrationStreamNodes().getReverseWriteNode() != null) {
            List<IntegrationFieldVerifyResult> reverseWriteResult = this.verifyReverseWrite(tenantId, syncPloyDetailEntity,lang);
            boolean verifyReverseWriteStatus = CollectionUtils.isEmpty(reverseWriteResult) ? false : true;
            UpdateIntegrationStreamResult.ErrorMsg reverseWriteError = new UpdateIntegrationStreamResult.ErrorMsg(verifyReverseWriteStatus, JSONObject.toJSONString(reverseWriteResult));
            errorMessageMap.put("reverseWriteNode", reverseWriteError);
        }
        stopWatch.lap("recallNode");
        stopWatch.log();
        return errorMessageMap;
    }

    private UpdateIntegrationStreamResult.ErrorMsg getErrorMsg(final String tenantId, final String beforeFuncApiName) {
        Result<Boolean> beforeFunctionMessage = this.verifyFunctionStatus(tenantId, beforeFuncApiName);
        List<IntegrationFieldVerifyResult> functionMessage = beforeFunctionMessage.getData() ? Lists.newArrayList(IntegrationFieldVerifyResult.newInstanceErrCode(ResultCodeEnum.VERIFY_STATUS_FAIL)) : Lists.newArrayList();
        UpdateIntegrationStreamResult.ErrorMsg beforeFunction = new UpdateIntegrationStreamResult.ErrorMsg(beforeFunctionMessage.getData(), JSONObject.toJSONString(functionMessage));
        return beforeFunction;
    }

    private List<IntegrationFieldVerifyResult> verifyReverseWrite(String tenantId, SyncPloyDetailEntity entity,String lang) {
        List<IntegrationFieldVerifyResult> fieldResult = Lists.newArrayList();
        //主对象反写字段
        if (CollectionUtils.isNotEmpty(entity.getIntegrationStreamNodes().getReverseWriteNode().getFieldMappings())) {
            Result<ListObjectFieldsResult> crmObjectResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, entity.getIntegrationStreamNodes().getReverseWriteNode().getDestObjectApiName(),lang);
            List<String> crmObjFieldList = Lists.newArrayList();
            if (crmObjectResult != null && crmObjectResult.getData() != null && CollectionUtils.isNotEmpty(crmObjectResult.getData().getFields())) {
                crmObjFieldList = crmObjectResult.getData().getFields().stream().map(ObjectFieldResult::getApiName).collect(Collectors.toList());
            }
            for (FieldMappingData fieldMappingData : entity.getIntegrationStreamNodes().getReverseWriteNode().getFieldMappings()) {
                if (!crmObjFieldList.contains(fieldMappingData.getDestApiName())) {
                    IntegrationFieldVerifyResult integrationFieldVerifyResult = new IntegrationFieldVerifyResult(ResultCodeEnum.CRM_OBJECT_FIELD_INVALID,
                            entity.getSourceObjectApiName(), fieldMappingData.getDestApiName());
                    fieldResult.add(integrationFieldVerifyResult);
                }
            }
        }
        //明细对象反写字段
        if (CollectionUtils.isNotEmpty(entity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings())) {
            for (DetailObjectMappingsData.DetailObjectMappingData detail : entity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings()) {
                Result<ListObjectFieldsResult> crmObjectResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, detail.getDestObjectApiName(),lang);
                List<String> crmObjFieldList = Lists.newArrayList();
                if (crmObjectResult != null && crmObjectResult.getData() != null && CollectionUtils.isNotEmpty(crmObjectResult.getData().getFields())) {
                    crmObjFieldList = crmObjectResult.getData().getFields().stream().map(ObjectFieldResult::getApiName).collect(Collectors.toList());
                }
                for (FieldMappingData fieldMappingData : detail.getFieldMappings()) {
                    if (StringUtils.isBlank(fieldMappingData.getDestApiName())) {
                        continue;
                    }
                    if (!crmObjFieldList.contains(fieldMappingData.getDestApiName())) {
                        IntegrationFieldVerifyResult integrationFieldVerifyResult = new IntegrationFieldVerifyResult(ResultCodeEnum.CRM_OBJECT_FIELD_INVALID,
                                detail.getDestObjectApiName(), fieldMappingData.getDestApiName());
                        fieldResult.add(integrationFieldVerifyResult);
                    }
                }
            }
        }
        return fieldResult;
    }

    /**
     * 校验 crm对象是否被禁用
     */
    public Result<Boolean> verifyCrmObjLifeStatus(String tenantId, String crmApiName) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), 1000);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(headerObj, crmApiName);
        Boolean active = true;
        if (describe.isSuccess()) {
            active = describe.getData().getDescribe().isActive() ? false : true;
            return Result.newSuccess(active);
        }
        return Result.newErrorWithData(ResultCodeEnum.CRM_OBJECT_INVALID, true);
    }

    /**
     * 校验函数是否被禁用或者删除。true则异常
     */
    public Result<Boolean> verifyFunctionStatus(String tenantId, String functionName) {
        String url = HttpUrlUtils.buildQueryFunctionUrl();
        Map<String, String> headerMap =
                HttpUrlUtils.buildHeaderMap(tenantId, CommonConstant.SUPER_ADMIN_USER, HttpUrlUtils.ERP_SYNC_DATA);
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        Map<String, Object> dataMap = Maps.newHashMap();
        try {
            dataMap.put("api_name", functionName);
            response = proxyHttpClient.postUrl(url, dataMap, headerMap, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
            JSONObject resultObj = JSONObject.parseObject(response.getBody());
            if ("0".equals(resultObj.getString("errCode"))) {
                //调用接口成功
                DocumentContext documentContext =
                        JsonPath.using(Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL,
                                Option.SUPPRESS_EXCEPTIONS)).parse(response.getBody());
                //不存在的函数，function返回是空的
                Object functionStatus = documentContext.read("$.result.function.is_active");
                if (ObjectUtils.isNotEmpty(functionStatus)) {//
                    boolean stauts = Boolean.valueOf(functionStatus.toString()) ? false : true;
                    return Result.newSuccess(stauts);
                }
                return Result.newSuccess(true);
            } else {
                //请求失败
                log.warn("post failed,url:{},param:{},header:{},response:{}", url, dataMap, headerMap, response);
                return Result.newErrorWithData(ResultCodeEnum.VERIFY_STATUS_FAIL, true);
            }
        } catch (Exception e) {
            log.error("post error,url:{},headerMap:{},params:{}", url, headerMap, dataMap, e);
            return Result.newErrorWithData(ResultCodeEnum.VERIFY_STATUS_FAIL, true);
        }
    }

    /**
     * 校验字段映射是否合法
     */
    public Result<List<IntegrationFieldVerifyResult>> verifyFieldMapping(String tenantId, SyncPloyDetailEntity syncPloyDetailEntity,String lang) {
        StopWatch stopWatch = StopWatch.create("masterAndSlaveFieldMapping");
        //检查CRM对象字段与映射字段 主对象，从对象结合一起搞
        final List<DetailObjectMappingsData.DetailObjectMappingData> allFieldMapping = new ArrayList<>(syncPloyDetailEntity.getDetailObjectMappings());
        DetailObjectMappingsData.DetailObjectMappingData masterObjectMapping = new DetailObjectMappingsData.DetailObjectMappingData();
        masterObjectMapping.setSourceObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
        masterObjectMapping.setDestObjectApiName(syncPloyDetailEntity.getDestObjectApiName());
        masterObjectMapping.setFieldMappings(syncPloyDetailEntity.getFieldMappings());
        allFieldMapping.add(masterObjectMapping);
        List<IntegrationFieldVerifyResult> fieldResult = Lists.newArrayList();

        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : allFieldMapping) {

            Set<String> mappingCRMObjSet = Sets.newHashSet();
            Set<String> mappingErpSet = Sets.newHashSet();
            String crmApiName = null;
            String erpApiName = null;
            if (syncPloyDetailEntity.getSourceTenantType().equals(TenantType.CRM)) {
                mappingCRMObjSet = detailObjectMappingData.getFieldMappings().stream().filter(item -> ObjectUtils.isNotEmpty(item.getSourceApiName())).map(item -> item.getSourceApiName()).collect(Collectors.toSet());
                mappingErpSet = detailObjectMappingData.getFieldMappings().stream().filter(item -> ObjectUtils.isEmpty(item.getDestApiName())).map(item -> item.getDestApiName()).collect(Collectors.toSet());
                crmApiName = detailObjectMappingData.getSourceObjectApiName();
                erpApiName = detailObjectMappingData.getDestObjectApiName();
            } else {
                mappingCRMObjSet = detailObjectMappingData.getFieldMappings().stream().filter(item -> ObjectUtils.isNotEmpty(item.getDestApiName())).map(item -> item.getDestApiName()).collect(Collectors.toSet());
                mappingErpSet = detailObjectMappingData.getFieldMappings().stream().filter(item -> ObjectUtils.isNotEmpty(item.getSourceApiName())).map(item -> item.getSourceApiName()).collect(Collectors.toSet());
                crmApiName = detailObjectMappingData.getDestObjectApiName();
                erpApiName = detailObjectMappingData.getSourceObjectApiName();
            }
            Result<ListObjectFieldsResult> crmObjectResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, crmApiName,lang);
            stopWatch.lap("listObjectFieldsWithFilterBlackList");
            String erpDcId = syncPloyDetailEntity.getSourceTenantType().equals(TenantType.CRM) ? syncPloyDetailEntity.getDestDataCenterId() : syncPloyDetailEntity.getSourceDataCenterId();
            Result<ListObjectFieldsResult> erpObjectResult = queryErpObjFieldMapping(tenantId, erpDcId, -1000, erpApiName);
            checkObjectMappingFieldsStatus(mappingCRMObjSet, crmObjectResult.getData(), fieldResult);
            checkObjectMappingFieldsStatus(mappingErpSet, erpObjectResult.getData(), fieldResult);
//            checkObjectFieldRequired(detailObjectMappingData.getFieldMappings(), crmObjectResult.getData(), fieldResult);
//            checkObjectFieldRequired(detailObjectMappingData.getFieldMappings(), erpObjectResult.getData(), fieldResult);

        }
        stopWatch.log();
        return Result.newSuccess(fieldResult);
    }


    //检查crm的映射字段是否被删除或者禁用
    public List<IntegrationFieldVerifyResult> checkObjectMappingFieldsStatus(Set<String> mappingObjObjSet, ListObjectFieldsResult objectResult, List<IntegrationFieldVerifyResult> fieldResult) {
        if (ObjectUtils.isEmpty(objectResult) || CollectionUtils.isEmpty(objectResult.getFields())) {
            return fieldResult;
        }
        Set<String> crmObjFieldSet = objectResult.getFields().stream().map(entry -> entry.getApiName()).collect(Collectors.toSet());
        mappingObjObjSet.removeAll(crmObjFieldSet);
        if (CollectionUtils.isNotEmpty(mappingObjObjSet)) {
            mappingObjObjSet.remove(null);//移除空元素
        }
        mappingObjObjSet.stream().forEach(item -> {
            IntegrationFieldVerifyResult integrationFieldVerifyResult = new IntegrationFieldVerifyResult(ResultCodeEnum.CRM_OBJECT_FIELD_INVALID,
                    objectResult.getObjectApiName(), item);
            fieldResult.add(integrationFieldVerifyResult);
        });
        return fieldResult;
    }


    //检查必填字段是否非填
    public Result<List<IntegrationFieldVerifyResult>> checkObjectFieldRequired(List<FieldMappingData> fieldMappingData, ListObjectFieldsResult objectResult, List<IntegrationFieldVerifyResult> fieldResult) {
        //有些字段required可能为null
        Set<ObjectFieldResult> requiredSet = objectResult.getFields().stream().filter(item -> ObjectUtils.isNotEmpty(item.getIsRequired())).filter(item -> item.getIsRequired()).collect(Collectors.toSet());
        Set<String> mappingSet = fieldMappingData.stream().map(item -> item.getDestApiName()).collect(Collectors.toSet());
        requiredSet.removeAll(mappingSet);
        requiredSet.stream().forEach(item -> {
            IntegrationFieldVerifyResult integrationFieldVerifyResult = new IntegrationFieldVerifyResult(ResultCodeEnum.OBJECT_REQUIRED_FIELD_NOT_MAPPING,
                    objectResult.getObjectApiName(), item.getApiName());
            fieldResult.add(integrationFieldVerifyResult);
        });
        return Result.newSuccess(fieldResult);
    }


    /**
     * 查询数据范围过滤
     */

    public List<IntegrationFieldVerifyResult> verifyConditionFields(String tenantId,
                                                                    List<SyncConditionsData> syncConditionsData,
                                                                    boolean isCrm2ERP,
                                                                    String erpDcId,
                                                                    String lang) {
        List<IntegrationFieldVerifyResult> conditionsFilter = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(syncConditionsData)) {
            for (SyncConditionsData syncConditionsDatum : syncConditionsData) {
                Set<String> conditionSet = Sets.newHashSet();
                if (ObjectUtils.isEmpty(syncConditionsDatum.getFilters())) continue;
                for (List<FilterData> filter : syncConditionsDatum.getFilters()) {
                    conditionSet.addAll(filter.stream().map(item -> item.getFieldApiName()).collect(Collectors.toSet()));
                }
                if (isCrm2ERP) {
                    Result<ListObjectFieldsResult> listObjectFieldsResultResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, syncConditionsDatum.getApiName(),lang);
                    conditionsFilter = checkObjectMappingFieldsStatus(conditionSet, listObjectFieldsResultResult.getData(), conditionsFilter);
                } else {
                    Result<ListObjectFieldsResult> listObjectFieldsResultResult = queryErpObjFieldMapping(tenantId, erpDcId, -10000, syncConditionsDatum.getApiName());
                    conditionsFilter = checkObjectMappingFieldsStatus(conditionSet, listObjectFieldsResultResult.getData(), conditionsFilter);
                }

            }
            return conditionsFilter;
        }
        return conditionsFilter;
    }

    /**
     * 查询ERP字段
     */
    public Result<ListObjectFieldsResult> queryErpObjFieldMapping(String tenantId, String dataCenterId, Integer userId, String erpObjectApiName) {
        Result<ErpObjectDescResult> erpObjectDescResult = erpObjectService.queryErpObjectByObjApiName(tenantId, dataCenterId, userId, erpObjectApiName);
        if (!erpObjectDescResult.isSuccess() || Objects.isNull(erpObjectDescResult.getData())) {
            return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
        }
        ErpObjectApiNameArg queryArg = new ErpObjectApiNameArg();
        queryArg.setErpObjectApiName(erpObjectApiName);
        Result<List<ErpObjectFieldResult>> listResult = erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(tenantId, userId, queryArg, dataCenterId);
        ListObjectFieldsResult listObjectFieldsResult = new ListObjectFieldsResult();
        if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
            listObjectFieldsResult = ResultConversionUtil.converseErpObjFelds(tenantId,listResult.getData());
            listObjectFieldsResult.setObjectName(erpObjectDescResult.getData().getErpObjectName());
            listObjectFieldsResult.setObjectApiName(erpObjectDescResult.getData().getErpObjectApiName());
        }
        return Result.newSuccess(listObjectFieldsResult);
    }

    /**
     * 保存集成流
     */
    public Result<UpdateIntegrationStreamResult> allUpdateIntegrationStream(String tenantId,
                                                                            UpdateIntegrationStreamArg updateIntegrationStreamArg,
                                                                            String erpDcId,
                                                                            String lang) {
        try {
            PollingIntervalApiDto pollingInterval = updateIntegrationStreamArg.getSyncRules().getPollingInterval();
            if (!PollingIntervalUtil.checkCron(pollingInterval.getCronExpression())) {
                return Result.newErrorByI18N(I18NStringEnum.s744.getI18nValue(), I18NStringEnum.s744.getI18nKey(),null);
            }
        } catch (Exception ignore) {
        }
        QueryIntegrationDetailResult.SourceSystemNode sourceSystemNode = updateIntegrationStreamArg.getSourceSystemNode();
        int syncType = sourceSystemNode.getSourceTenantType().equals(TenantType.CRM) ? TenantType.CRM : TenantType.ERP;//为什么不直接用updateIntegrationStreamArg.getSourceSystemNode().getSourceTenantType()

        //更新源节点优先级配置
        updateSouceNodeObjPriorityConfig(tenantId, sourceSystemNode);
        //节点修改不阻塞，但是统一报错。
        List<Pair<String, BaseResult>> resultPairs = new ArrayList<>();

        //更新字段映射
        SyncPloyDetailUpdateFieldMappingsArg syncPloyDetailUpdateFieldMappingsArg = new SyncPloyDetailUpdateFieldMappingsArg();

        //主对象映射
        ObjectMappingResult fieldMappings = updateIntegrationStreamArg.getFieldMappingNode().getFieldMappings();
        ObjectMappingResult.fillMappingConfig(fieldMappings);
        List<SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg> fieldMappingArgs = BeanUtil.deepCopyList(fieldMappings.getFieldMappings(), SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg.class);
        SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg updateMappingArg = new SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg();
        updateMappingArg.setFieldMappings(fieldMappingArgs);
        updateMappingArg.setSourceObjectApiName(sourceSystemNode.getSourceObjectApiName());
        updateMappingArg.setDestObjectApiName(updateIntegrationStreamArg.getDestSystemNode().getDestObjectApiName());
        syncPloyDetailUpdateFieldMappingsArg.setMasterObjectMapping(updateMappingArg);

        //从字段映射
        List<ObjectMappingResult> detailObjectMappings = updateIntegrationStreamArg.getFieldMappingNode().getDetailObjectMappings();
        for(ObjectMappingResult detail:detailObjectMappings){
            ObjectMappingResult.fillMappingConfig(detail);
        }
        List<SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg> objectMappingArgs = BeanUtil.deepCopyList(detailObjectMappings, SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg.class);
        syncPloyDetailUpdateFieldMappingsArg.setDetailObjectMappings(objectMappingArgs);
        syncPloyDetailUpdateFieldMappingsArg.setId(updateIntegrationStreamArg.getId());
        Result<Void> updateFieldMappingResult = adminNodeSettingService.updateFieldMapping(tenantId, syncPloyDetailUpdateFieldMappingsArg);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s745,lang,tenantId), updateFieldMappingResult));

        //更新数据范围,默认数据范围
        if (ObjectUtils.isEmpty(updateIntegrationStreamArg.getSyncConditionsNode())) {
            //如果节点被删除了 前端不传值，需要后台mock
            QueryIntegrationDetailResult.SyncConditionsNode syncConditionsNode = new QueryIntegrationDetailResult.SyncConditionsNode();
            com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData syncConditionsData = new com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData(sourceSystemNode.getSourceObjectApiName(), Lists.newArrayList(), SyncForceConstant.CLOSE,new QueryIntegrationDetailResult.SyncConditionsQueryDataNode());
            syncConditionsData.setFilters(Lists.newArrayList());
            syncConditionsData.setApiName(sourceSystemNode.getSourceObjectApiName());
            syncConditionsNode.setSyncConditions(syncConditionsData);
            //从对象
            List<com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData> conditionsDatas = Lists.newArrayList();
            updateIntegrationStreamArg.getFieldMappingNode().getDetailObjectMappings().forEach(item -> {
                com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData itemData = new com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData(item.getSourceObjectApiName(), Lists.newArrayList(), SyncForceConstant.CLOSE,new QueryIntegrationDetailResult.SyncConditionsQueryDataNode());
                conditionsDatas.add(itemData);
            });
            syncConditionsNode.setDetailObjectSyncConditions(conditionsDatas);
            updateIntegrationStreamArg.setSyncConditionsNode(syncConditionsNode);
        }
        SyncPloyUpdateSyncConditionsArg syncPloyUpdateSyncConditionsArg = new SyncPloyUpdateSyncConditionsArg();
        syncPloyUpdateSyncConditionsArg.setId(updateIntegrationStreamArg.getId());
        syncPloyUpdateSyncConditionsArg.setType(syncType);
        syncPloyUpdateSyncConditionsArg.setDetailObjectSyncConditions(updateIntegrationStreamArg.getSyncConditionsNode().getDetailObjectSyncConditions());
        syncPloyUpdateSyncConditionsArg.setSyncConditions(updateIntegrationStreamArg.getSyncConditionsNode().getSyncConditions());
        Result<Void> updateSyncConditionsResult = adminSyncPloyDetailService.updateSyncConditions(tenantId, syncPloyUpdateSyncConditionsArg);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s746,lang,tenantId), updateSyncConditionsResult));

        // 更新同步规则
        SyncPloyDetailUpdateSyncRulesArg syncPloyDetailUpdateSyncRulesArg = new SyncPloyDetailUpdateSyncRulesArg();
        syncPloyDetailUpdateSyncRulesArg.setId(updateIntegrationStreamArg.getId());
        syncPloyDetailUpdateSyncRulesArg.setDataCenterId(erpDcId);
        syncPloyDetailUpdateSyncRulesArg.setSyncRules(updateIntegrationStreamArg.getSyncRules());
        Result<Void> updateSyncRulesResult = adminSyncPloyDetailService.updateSyncRules(tenantId, syncPloyDetailUpdateSyncRulesArg);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s747,lang,tenantId), updateSyncRulesResult));

        //更新函数
        updateFunctionNode(tenantId, updateIntegrationStreamArg, resultPairs,lang);
        //更新集成流节点
        Result<String> updateIntegrationStreamNodesResult = adminNodeSettingService.updateIntegrationStreamNodes(tenantId, updateIntegrationStreamArg);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s748,lang,tenantId), updateIntegrationStreamNodesResult));
        //更新数据范围与数据映射的字段为used.
        adminSyncPloyDetailService.updateUsedQueryField(tenantId, erpDcId, sourceSystemNode.getSourceObjectApiName(), sourceSystemNode.getSourceDc().getDcChannel());
        //判断结果
        if (resultPairs.stream().anyMatch(v -> !v.getValue().isSuccess())) {
            String errMsg = resultPairs.stream().map(kv -> {
                String optName = kv.getKey();
                BaseResult result = kv.getValue();
                if (result.isSuccess()) {
                    return optName + i18NStringManager.get(I18NStringEnum.s6,lang,tenantId);
                } else {
                    return optName + i18NStringManager.get(I18NStringEnum.s7,lang,tenantId) +"：" + result.getErrMsg();
                }
            }).collect(Collectors.joining("\r\n"));
            return Result.newError(errMsg);
        }
        return Result.newSuccess();
    }

    private void updateSouceNodeObjPriorityConfig(String tenantId, QueryIntegrationDetailResult.SourceSystemNode sourceSystemNode) {
        //更新对象优先级配置
        Boolean enableObjDispatchPriorityConfig = sourceSystemNode.getEnableObjDispatchPriorityConfig();
        if (enableObjDispatchPriorityConfig == null) {
            return;
        }
        Map<String, ObjDispatchPriorityConfig> objConfigMap = plusTenantConfigManager.getObjConfig(tenantId, "0", TenantConfigurationTypeEnum.OBJ_DISPATCH_PRIORITY_CONFIG_MAP, new TypeReference<Map<String, ObjDispatchPriorityConfig>>() {
        });
        if (!enableObjDispatchPriorityConfig) {
            //关闭，移除配置
            if (objConfigMap == null || !objConfigMap.containsKey(sourceSystemNode.getSourceObjectApiName())) {
                return;
            }
            //移除,更新
            objConfigMap.remove(sourceSystemNode.getSourceObjectApiName());
            //为空时，删除配置,否则更新配置。
            if (objConfigMap.isEmpty()) {
                plusTenantConfigManager.deleteConvertConfig(tenantId, "0", TenantConfigurationTypeEnum.OBJ_DISPATCH_PRIORITY_CONFIG_MAP);
            } else {
                plusTenantConfigManager.upsertObjConfig(tenantId, "0", TenantConfigurationTypeEnum.OBJ_DISPATCH_PRIORITY_CONFIG_MAP, objConfigMap);
            }
        } else {
            //开启配置
            if (objConfigMap == null) {
                objConfigMap = new HashMap<>();
            }
            ObjDispatchPriorityConfig oldConfig = objConfigMap.get(sourceSystemNode.getSourceObjectApiName());
            ObjDispatchPriorityConfig newConfig = sourceSystemNode.getObjDispatchPriorityConfig();
            //完全一样时，不需要处理
            if (newConfig == null || newConfig.equals(oldConfig)) {
                return;
            }
            //计算一些额外的字段，这些字段不会影响equals
            if (CollUtil.isNotEmpty(newConfig.getPriorityExprList())) {
                //转换表达式
                for (ObjDispatchPriorityConfig.PriorityExpr priorityExpr : newConfig.getPriorityExprList()) {
                    String expr = ConditionUtil.parseToOrExpression(priorityExpr.getFilters());
                    priorityExpr.setExpr(expr);
                }
                //字段列表
                List<String> fields = newConfig.getPriorityExprList().stream()
                        .flatMap(v -> CollUtil.emptyIfNull(v.getFilters()).stream())
                        .flatMap(v -> v.stream())
                        .map(v -> v.getFieldApiName())
                        .distinct()
                        .collect(Collectors.toList());
                newConfig.setFields(fields);
            }
            objConfigMap.put(sourceSystemNode.getSourceObjectApiName(), newConfig);
            plusTenantConfigManager.upsertObjConfig(tenantId, "0", TenantConfigurationTypeEnum.OBJ_DISPATCH_PRIORITY_CONFIG_MAP, objConfigMap);
        }
    }


    private void updateFunctionNode(String tenantId,
                                    UpdateIntegrationStreamArg updateIntegrationStreamArg,
                                    List<Pair<String, BaseResult>> resultPairs,
                                    String lang) {
        if (ObjectUtils.isEmpty(updateIntegrationStreamArg.getBeforeFunctionNode())) {
            updateIntegrationStreamArg.setBeforeFunctionNode(new QueryIntegrationDetailResult.BeforeFunctionNode());
        }
        CustomFunctionCreateArg functionArg = new CustomFunctionCreateArg();
        functionArg.setCustomFuncType(1);
        functionArg.setId(updateIntegrationStreamArg.getId());
        functionArg.setCustomFuncApiName(updateIntegrationStreamArg.getBeforeFunctionNode().getBeforeFuncApiName());
        functionArg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s749,lang,tenantId), adminNodeSettingService.updateFunction(tenantId, functionArg)));

        //同步中函数
        if (ObjectUtils.isEmpty(updateIntegrationStreamArg.getDurationFunctionApiNode())) {
            updateIntegrationStreamArg.setDurationFunctionApiNode(new QueryIntegrationDetailResult.DurationFunctionApiNode());
        }
        functionArg.setCustomFuncApiName(updateIntegrationStreamArg.getDurationFunctionApiNode().getDuringFuncApiName());
        functionArg.setCustomFuncType(2);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s750,lang,tenantId), adminNodeSettingService.updateFunction(tenantId, functionArg)));
        //同步后函数
        if (ObjectUtils.isEmpty(updateIntegrationStreamArg.getAfterFunctionNode())) {
            updateIntegrationStreamArg.setAfterFunctionNode(new QueryIntegrationDetailResult.AfterFunctionNode());
        }
        functionArg.setCustomFuncApiName(updateIntegrationStreamArg.getAfterFunctionNode().getAfterFuncApiName());
        functionArg.setCustomFuncType(3);
        resultPairs.add(Pair.of(i18NStringManager.get(I18NStringEnum.s751,lang,tenantId), adminNodeSettingService.updateFunction(tenantId, functionArg)));
    }


}
