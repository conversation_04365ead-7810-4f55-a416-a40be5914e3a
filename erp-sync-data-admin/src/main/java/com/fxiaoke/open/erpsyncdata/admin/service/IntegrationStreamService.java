package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 16:22 2022/2/16
 * @Desc:
 */
public interface IntegrationStreamService {
    Result<String> createObjectMapping(String tenantId, CreateObjectMappingArg arg,String lang);

    Result<QueryResult<List<IntegrationStreamResult>>> queryIntegrationStreamList(String tenantId,
                                                                                  ListIntegrationStreamArg arg,
                                                                                  boolean queryFailDataCount,
                                                                                  String lang);

    Result<QueryResult<List<IntegrationViewResult>>> newQueryIntegrationStreamList(String tenantId,
                                                                                   ListIntegrationStreamArg arg,
                                                                                   boolean queryFailDataCount,
                                                                                   String lang);

    Result<QueryResult<List<IntegrationViewResult>>> singleQueryIntegrationStreamList(String tenantId, IdArg arg,String lang);

    Result<QueryIntegrationDetailResult> getIntegrationStreamDetail(String tenantId, IdArg arg,String lang);

    Result<IntegrationStreamResult> getStreamDetail(String tenantId, IdArg arg,String lang);

    Result<String> brush(String tenantId, boolean brushAll,String lang);

    Result<List<SyncObjectResult>> getIntegrationStreamCrmObjList(String tenantId, String lang);

    Result<List<SyncObjectResult>> getIntegrationStreamErpObjList(String tenantId, final String dcId);

    Result<Integer> updateIntegrationStreamName(String tenantId, UpdateIntegrationStreamArg.UpdateStreamNameArg arg);

    /**
     * 更新集成流
     * @param tenantId
     * @param updateIntegrationStreamArg
     * @return
     */
    Result<UpdateIntegrationStreamResult> allUpdateIntegrationStream(String tenantId,
                                                                     UpdateIntegrationStreamArg updateIntegrationStreamArg,
                                                                     String lang);

    Result<Map<String, UpdateIntegrationStreamResult.ErrorMsg>> commonVerifyMessage(String tenantId,String dataId,String lang);



    Result<List<UserOperatorLogResult>> queryStreamOpeartorLog(String tenantId, String dcId, String id,int offset,int limit,String lang);
    Result<List<IntegrationViewResult.InvalidInfoResult>> queryStreamStatusAndLastSyncTime(String tenantId,List<String> streamIds,String lang);

    Result<List<IntegrationViewResult.SyncFailResult>> querySyncFailCount(String tenantId,List<String> streamIds);

    Result<List<IntegrationViewResult.SyncFailResult>> querySyncFailCountByCache(String tenantId,List<String> streamIds);

    Result<Boolean> updateGetByIdStatus(String tenantId, String dcId, GetByIdApiStatusArg arg);

    Result<Boolean> updateMappingStatus(String tenantId, String dcId, IdArg arg);

    Result<Boolean> updateSyncDataStatus(String tenantId, String dcId, IdArg arg,String lang);

    Result<Boolean> queryGetByIdStatus(String tenantId, String dcId, GetByIdApiStatusArg arg);

    /**
     * 检查和更新集成流明细对象映射关系以及附加的关系
     * @param arg
     * @return
     */
    Result<Void> checkAndUpdateDetailObjMapping(UpdateIntegrationStreamArg.CheckAndUpdateDetailObjMappingArg arg,String lang);

    void fillFieldMappingsResult(final String tenantId, final String dataCenterId, final Integer sourceTenantType, final Integer destTenantType, final ObjectMappingResult masterObjectMapping, final List<ObjectMappingResult> detailObjectMappings, final String sourceTenantId, final String destTenantId);

    Result<PaasDataSourceConfigStatus> updateTenantNeedPassDataSourceConfig(String tenantId, PaasDataSourceConfigStatus arg);

    Result<PaasDataSourceConfigStatus> queryTenantNeedPassDataSourceConfig(String tenantId, PaasDataSourceConfigStatus arg);

    Result<String> createIntegrationStream(String tenantId, CreateObjectMappingArg arg, String lang);

    Result<ListObjectFieldsResult> getReverseWriteObjFieldApiNames(String tenantId, String dcId,Integer userId, String objectApiName, String lang);
}
