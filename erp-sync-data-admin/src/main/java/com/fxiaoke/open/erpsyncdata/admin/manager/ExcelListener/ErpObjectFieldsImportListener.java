package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpObjectFieldExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpObjectFieldExcelVo2;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
@Getter
public class ErpObjectFieldsImportListener extends BaseListener<ErpObjectFieldExcelVo> {
    private final List<ErpObjectFieldExcelVo2> failedDataList = new ArrayList<>();
    private ImportExcelFile.FieldImportResult importResult = new ImportExcelFile.FieldImportResult();

    private String tenantId;
    private String dataCenterId;
    private ErpChannelEnum channelEnum;
    private ErpObjectFieldsService erpObjectFieldsService;
    private I18NStringManager i18NStringManager;
    private String lang;
    private Set<String> splitObjApiNames;
    private StringBuilder errorMsg = new StringBuilder();

    public ErpObjectFieldsImportListener(String tenantId,
                                         String dataCenterId,
                                         ErpChannelEnum channelEnum,
                                         Set<String> splitObjApiNames, ErpObjectFieldsService erpObjectFieldsService,
                                         I18NStringManager i18NStringManager,
                                         String lang) {
        this.tenantId = tenantId;
        this.dataCenterId = dataCenterId;
        this.channelEnum = channelEnum;
        this.splitObjApiNames = splitObjApiNames;
        this.erpObjectFieldsService = erpObjectFieldsService;
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
    }

    private void verifyData(ErpObjectFieldExcelVo2 data) {
        if(StringUtils.isEmpty(data.getErpObjectApiName())) {
            data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s3623, lang, tenantId));
        } else if(StringUtils.isEmpty(data.getFieldLabel())) {
            data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s782, lang, tenantId));
        } else if(StringUtils.isEmpty(data.getFieldApiName())) {
            data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s783, lang, tenantId));
        } else if(StringUtils.isEmpty(data.getFieldDefineType())) {
            data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s784, lang, tenantId));
        } else if(ErpFieldTypeEnum.getFieldType(data.getFieldDefineType())==null) {
            data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s785, lang, tenantId));
        } else if(StringUtils.equalsIgnoreCase(data.getFieldDefineType(),FieldType.OBJECT_REFERENCE)
                || StringUtils.equalsIgnoreCase(data.getFieldDefineType(),FieldType.OBJECT_REFERENCE_MANY)) {
            if(StringUtils.isEmpty(data.getFieldExtendValue())) {
                data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s786, lang, tenantId));
            }
        } else if (!splitObjApiNames.contains(data.getErpObjectApiName())) {
            data.setErrorInfo(i18NStringManager.get(I18NStringEnum.s933, lang, tenantId));
        }
    }

    @Override
    public void invoke(ErpObjectFieldExcelVo data, AnalysisContext context) {
        super.invoke(data, context);
        ErpObjectFieldExcelVo2 data2 = new ErpObjectFieldExcelVo2();
        BeanUtils.copyProperties(data,data2);

        final String rowNum = String.valueOf(context.readRowHolder().getRowIndex() + 1);
        verifyData(data2);
        if(StringUtils.isNotEmpty(data2.getErrorInfo())) {
            failedDataList.add(data2);
            importResult.increaseFailed();
            errorMsg.append(i18NStringManager.get2(I18NStringEnum.s774, lang, tenantId, rowNum, data2.getErrorInfo())).append("\n");
            return;
        }

        ErpObjectFieldResult result = new ErpObjectFieldResult();
        BeanUtils.copyProperties(data2,result);
        result.setChannel(channelEnum);
        ErpFieldTypeEnum fieldDefineType;
        try {
            fieldDefineType = ErpFieldTypeEnum.valueOf(data2.getFieldDefineType());
        } catch (Exception e) {
            data2.setErrorInfo(e.getMessage());
            failedDataList.add(data2);
            importResult.increaseFailed();
            errorMsg.append(i18NStringManager.get2(I18NStringEnum.s774, lang, tenantId, rowNum, e.getMessage())).append("\n");
            return;
        }
        result.setFieldDefineType(fieldDefineType);
        erpObjectFieldsService.processErpObjectField(result);
        Result<ErpObjectFieldResult> result2 = erpObjectFieldsService.updateErpObjectField(tenantId, dataCenterId, 1000, result);
        if(!result2.isSuccess()) {
            data2.setErrorInfo(result2.getErrMsg());
            failedDataList.add(data2);
            importResult.increaseFailed();
            errorMsg.append(i18NStringManager.get2(I18NStringEnum.s774, lang, tenantId, rowNum, result2.getErrMsg())).append("\n");
            return;
        }
        importResult.increaseSuccess();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        String msg = i18NStringManager.get(I18NStringEnum.s1263, lang, tenantId) +
                i18NStringManager.get2(I18NStringEnum.s1149, lang, tenantId, String.valueOf(importResult.getSuccessCount()), String.valueOf(importResult.getFailedCount())) +
                "\n" +
                errorMsg.toString();
        importResult.setMessage(msg);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
    }
}
