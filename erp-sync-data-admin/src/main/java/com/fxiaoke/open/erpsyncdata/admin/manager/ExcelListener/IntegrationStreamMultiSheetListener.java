package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateObjectMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.manager.IntegrationStreamManager;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminNodeSettingService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/1
 */
@Slf4j
@Getter
public class IntegrationStreamMultiSheetListener extends BaseListener<ErpIntegrationStreamExcelVo> {
    private final String hideSheet = "hideSheet";
    private final String CRM = "CRM";
    private static final String THIRD_PARTY = "External";
    private Integer sum = 0;

    private String direction;

    private final String tenantId;

    private final String dataCenterId;

    private final ErpChannelEnum channel;

    private ImportExcelFile.Result importResult;

    private final List<ImportExcelFile.ErrorRow> errorRows;

    private final I18NStringManager i18NStringManager;

    private final String lang;

    private final ErpObjectDao erpObjectDao;

    private final ErpObjectRelationshipDao erpObjectRelationshipDao;

    private final Integer userId;

    private final ErpObjectFieldsService erpObjectFieldsService;

    private final ErpConnectInfoDao erpConnectInfoDao;

    private final IntegrationStreamService integrationStreamService;

    private final AdminNodeSettingService adminNodeSettingService;

    private final ErpObjectService erpObjectService;



    public IntegrationStreamMultiSheetListener(String tenantId,
                                               ErpChannelEnum channel,
                                               String dataCenterId,
                                               I18NStringManager i18NStringManager,
                                               String lang,
                                               ErpObjectDao erpObjectDao,
                                               ErpObjectRelationshipDao erpObjectRelationshipDao,
                                               Integer userId,
                                               ErpObjectFieldsService erpObjectFieldsService,
                                               ErpConnectInfoDao erpConnectInfoDao,
                                               IntegrationStreamService integrationStreamService,
                                               AdminNodeSettingService adminNodeSettingService,
                                               ErpObjectService erpObjectService) {
        this.importResult = new ImportExcelFile.Result();
        this.errorRows = new LinkedList<>();
        this.tenantId = tenantId;
        this.channel = channel;
        this.dataCenterId = dataCenterId;
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
        this.erpObjectDao = erpObjectDao;
        this.erpObjectRelationshipDao = erpObjectRelationshipDao;
        this.userId = userId;
        this.erpObjectFieldsService = erpObjectFieldsService;
        this.erpConnectInfoDao = erpConnectInfoDao;
        this.integrationStreamService = integrationStreamService;
        this.adminNodeSettingService = adminNodeSettingService;
        this.erpObjectService = erpObjectService;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if(StringUtils.isEmpty(direction)) {
            direction = headMap.get(1);
        }
    }

    @Override
    public void invoke(ErpIntegrationStreamExcelVo data, AnalysisContext context) {
        if (sum < 2) {
            sum = sum + 1;
            return;
        }

        //校验基本信息
        String errorMsg = verifyData(data);
        if (StringUtils.isNotEmpty(errorMsg)) {
            ImportExcelFile.ErrorRow errorRow = new ImportExcelFile.ErrorRow();
            //可能是多表格的问题，跟实际行数差一个
            errorRow.setRowNo(context.readRowHolder().getRowIndex() + 1);
            errorRow.setErrMsg(errorMsg + "：" + i18NStringManager.get(I18NStringEnum.s233,
                    lang,
                    tenantId));
            errorRows.add(errorRow);
        } else {
            //去掉空格
            dealBankData(data);
        }

        super.invoke(data, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //从这里获取到sheetName
        String sheetName = context.readSheetHolder().getSheetName();
        log.info("IntegrationStreamMultiSheetListener.doAfterAllAnalysed,tenantId={},sheetName={}", tenantId, sheetName);

        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        //格式不对不处理
        if (CollectionUtils.isNotEmpty(errorRows)) {
            importResult.setInvokedNum(dataList.size());
            importResult.setImportErrorRows(errorRows);
            dealResultData();
            return;
        }

        //集成流方向，判断方向是否正确
        if(!direction.equalsIgnoreCase(CRM) && !direction.equalsIgnoreCase(THIRD_PARTY)) {
            importResult.setPrintMsg(i18NStringManager.get(I18NStringEnum.s4021, lang, tenantId));
            dealResultData();
            return;
        }

        log.info("IntegrationStreamMultiSheetListener.doAfterAllAnalysed,tenantId={},direction={}", tenantId, direction);

        //crm数据中心
        ErpConnectInfoEntity entity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getCRMConnectInfo(tenantId, ErpChannelEnum.CRM.toString());
        String crmDataCenterId = entity.getId();
        log.info("IntegrationStreamMultiSheetListener.doAfterAllAnalysed,tenantId={},crmDataCenterId={},dateCenterId={}", tenantId, crmDataCenterId, dataCenterId);

        //处理对象
        Result<Map<String, String>> objectApiNameDataMapResult = erpObjectService.importObjectApiNameData(tenantId, userId, dataCenterId, channel, lang, dataList);
        if(!objectApiNameDataMapResult.isSuccess()) {
            importResult.setPrintMsg(objectApiNameDataMapResult.getErrMsg());
            dealResultData();
            return;
        }

        //获取对象的映射关系
        Map<String, String> thirdPartyObjectMapping = getThirdPartyObjectMapping();

        //处理对象字段
        Result<Map<String, Map<String, String>>> objectFiledApiNameDataMap = erpObjectFieldsService.importObjectFiledApiNameData(tenantId, dataCenterId, channel, lang, thirdPartyObjectMapping, dataList);
        if(!objectFiledApiNameDataMap.isSuccess()) {
            importResult.setPrintMsg(objectFiledApiNameDataMap.getErrMsg());
            dealResultData();
            return;
        }

        //创建集成流
        Set<String> isHasAddObject = new HashSet<>();
        CreateObjectMappingArg objectMappingArg = new CreateObjectMappingArg();
        List<CreateObjectMappingArg.ObjectMapping> detailObjectMappings = new LinkedList<>();
        for (ErpIntegrationStreamExcelVo erpIntegrationStreamExcelVo : dataList) {
            //主对象
            if (StringUtils.isEmpty(objectMappingArg.getSourceDataCenterId())) {
                if (direction.equalsIgnoreCase(CRM)) {
                    objectMappingArg.setSourceDataCenterId(crmDataCenterId);
                    objectMappingArg.setDestDataCenterId(dataCenterId);
                    objectMappingArg.setSourceObjectApiName(erpIntegrationStreamExcelVo.getCrmObjectApiName());
                    objectMappingArg.setDestObjectApiName(thirdPartyObjectMapping.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
                } else {
                    objectMappingArg.setSourceDataCenterId(dataCenterId);
                    objectMappingArg.setDestDataCenterId(crmDataCenterId);
                    objectMappingArg.setSourceObjectApiName(thirdPartyObjectMapping.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
                    objectMappingArg.setDestObjectApiName(erpIntegrationStreamExcelVo.getCrmObjectApiName());
                }
                isHasAddObject.add(erpIntegrationStreamExcelVo.getCrmObjectApiName());
            }

            //从对象
            if (!isHasAddObject.contains(erpIntegrationStreamExcelVo.getCrmObjectApiName())) {
                if (thirdPartyObjectMapping.containsKey(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName())) {
                    //从对象也存在
                    CreateObjectMappingArg.ObjectMapping objectMapping = new CreateObjectMappingArg.ObjectMapping();
                    if (direction.equalsIgnoreCase(CRM)) {
                        objectMapping.setSourceObjectApiName(erpIntegrationStreamExcelVo.getCrmObjectApiName());
                        objectMapping.setDestObjectApiName(thirdPartyObjectMapping.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
                    } else {
                        objectMapping.setSourceObjectApiName(thirdPartyObjectMapping.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
                        objectMapping.setDestObjectApiName(erpIntegrationStreamExcelVo.getCrmObjectApiName());
                    }
                    detailObjectMappings.add(objectMapping);
                }
                isHasAddObject.add(erpIntegrationStreamExcelVo.getCrmObjectApiName());
            }
        }
        objectMappingArg.setDetailObjectMappings(detailObjectMappings);
        //设置触发事件
        SyncRulesWebData syncRules = new SyncRulesWebData();
        syncRules.setSyncTypeList(Lists.newArrayList(SyncTypeEnum.get.name()));
        if (direction.equalsIgnoreCase(CRM)) {
            syncRules.setEvents(Sets.newHashSet(1, 2));
        } else {
            syncRules.setEvents(Sets.newHashSet(1, 2, 3));
        }
        objectMappingArg.setSyncRules(syncRules);
        //集成流名称
        objectMappingArg.setIntegrationStreamName(sheetName);

        Result<String> integrationStreamResult = integrationStreamService.createIntegrationStream(tenantId, objectMappingArg, lang);
        if (!integrationStreamResult.isSuccess()) {
            //失败处理
            importResult.setPrintMsg(integrationStreamResult.getErrMsg());
            dealResultData();
            return;
        }

        //处理字段映射，需要精确到行   list 行数 = i + 4
        Result<ImportExcelFile.Result> fieldMappingResult = adminNodeSettingService.importFieldMapping(tenantId, lang, dataCenterId, crmDataCenterId, integrationStreamResult.getData(), dataList, direction, objectApiNameDataMapResult.getData(), objectFiledApiNameDataMap.getData(), thirdPartyObjectMapping);
        if (!fieldMappingResult.isSuccess()) {
            //失败处理
            importResult.setPrintMsg(fieldMappingResult.getErrMsg());
            dealResultData();
            return;
        }

        importResult = fieldMappingResult.getData();
        dealResultData();
        log.info("analyse excels finished,context:{}", context);
    }

    private Map<String, String> getThirdPartyObjectMapping() {
        //批次默认为1，查找主对象下的全部从对象
        Integer splitSeq = 1;
        String actualErpObjectApiName = dataList.get(0).getThirdPartyObjectApiName();
        ErpObjectRelationshipEntity queryAllObject = new ErpObjectRelationshipEntity();
        queryAllObject.setTenantId(tenantId);
        queryAllObject.setDataCenterId(dataCenterId);
        queryAllObject.setSplitSeq(splitSeq);
        queryAllObject.setErpRealObjectApiname(actualErpObjectApiName);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryAllObject);
        List<String> erpAllObjects = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
        //此时还需要到erp_object表查询真实对象的数据
        List<ErpObjectEntity> erpObjectList = erpObjectDao.queryByApiNames2(tenantId, dataCenterId, erpAllObjects);

        //key:apiName,value:fakeApiName
        Map<String, String> erpObjectMap = erpObjectList.stream()
                .collect(Collectors.toMap(
                        entity -> StringUtils.isEmpty(entity.getErpObjectExtendValue()) ? actualErpObjectApiName : entity.getErpObjectExtendValue(),
                        ErpObjectEntity::getErpObjectApiName,
                        (existingValue, newValue) -> existingValue
                ));
        return erpObjectMap;
    }

    private String verifyData(ErpIntegrationStreamExcelVo data) {
        String errorMsg = null;
        if(StringUtils.isEmpty(data.getCrmObjectApiName())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s1051,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s4024,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getCrmFileApiName())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s1051,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s4025,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getThirdPartyObjectLabel())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s4022,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s4026,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getThirdPartyObjectApiName())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s4022,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s4027,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getThirdPartyObjectLabel())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s4022,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s4026,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getThirdPartyFieldApiName())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s4022,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s1057,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getThirdPartyFieldType())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s4022,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s1058,
                            lang,
                            tenantId));
        } else if(StringUtils.isEmpty(data.getThirdPartyFieldLabel())) {
            errorMsg = String.format("%s-%s",
                    i18NStringManager.get(I18NStringEnum.s4022,
                            lang,
                            tenantId),
                    i18NStringManager.get(I18NStringEnum.s1056,
                            lang,
                            tenantId));
        } else if(StringUtils.equalsIgnoreCase(data.getThirdPartyFieldType(), FieldType.OBJECT_REFERENCE)
                || StringUtils.equalsIgnoreCase(data.getThirdPartyFieldType(),FieldType.OBJECT_REFERENCE_MANY)) {
            if(StringUtils.isEmpty(data.getThirdPartyFieldExtendInfo())) {
                errorMsg = String.format("%s-%s",
                        i18NStringManager.get(I18NStringEnum.s4022,
                                lang,
                                tenantId),
                        i18NStringManager.get(I18NStringEnum.s4028,
                                lang,
                                tenantId));
            }
        }

        return errorMsg;
    }

    private void dealBankData(ErpIntegrationStreamExcelVo data) {
        data.setCrmObjectApiName(StringUtils.trimToEmpty(data.getCrmObjectApiName()));
        data.setCrmFileApiName(StringUtils.trimToEmpty(data.getCrmFileApiName()));
        data.setThirdPartyObjectApiName(StringUtils.trimToEmpty(data.getThirdPartyObjectApiName()));
        data.setThirdPartyFieldApiName(StringUtils.trimToEmpty(data.getThirdPartyFieldApiName()));
    }

    private void dealResultData() {
        if (StringUtils.isNotEmpty(importResult.getPrintMsg())) {
            return;
        }

        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s772.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s772.getI18nValue(), importResult.getInvokedNum(), importResult.getInsertNum(), importResult.getDeleteNum(), importResult.getUpdateNum()),
                Lists.newArrayList(importResult.getInvokedNum()+"", importResult.getInsertNum()+"", importResult.getDeleteNum()+"", importResult.getUpdateNum()+""))).append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s773.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s773.getI18nValue(), importResult.getImportErrorRows().size()),
                Lists.newArrayList(importResult.getImportErrorRows().size()+""))).append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s775.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s775.getI18nValue(), importResult.getInvokeExceptionRows().size()),
                Lists.newArrayList(importResult.getInvokeExceptionRows().size()+""))).append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }
}
