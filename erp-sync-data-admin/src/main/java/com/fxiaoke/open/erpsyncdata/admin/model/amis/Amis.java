package com.fxiaoke.open.erpsyncdata.admin.model.amis;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ModifierUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.TypeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/3/31
 */
public class Amis {

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class Schema extends Dict {

        public static Schema of(String type) {
            Schema schema = new Schema();
            schema.put("type", type);
            return schema;
        }

        public static Schema tpl(String tpl) {
            Schema schema = of("tpl");
            schema.put("tpl", tpl);
            return schema;
        }

        public static Schema jsonEditorStatic(String json){
            Schema schema = of("editor");
            schema.put("language","json");
            schema.put("static",true);
            schema.put("value",json);
            return schema;
        }
    }

    @Data
    public static class BulkUpdateArg<T> {
        private String ids;
        private List<String> indexes;
        private List<T> rows;
        private List<T> rowsDiff;
        private List<T> rowsOrigin;
        private List<T> unModifiedItems;
    }

    @Data
    @Builder
    public static class Option {
        /**
         * id
         */
        private Object value;
        /**
         * 名称
         */
        private String label;

        private List<Option> children;

        public static Option of(Object value, String label) {
            Option option = Option.builder().value(value).label(label).build();
            return option;
        }

        public static List<Option> BoolOptions = ListUtil.of(Option.of(true, "是"), Option.of(false, "否"));   // ignoreI18n 实施和开发自用
    }

    public static class ColHelper<T> {
        private final LinkedHashMap<String, Col> colMap = new LinkedHashMap<>();
        private List<T> dataList;

        public void add(Col col) {
            colMap.put(col.name(), col);
        }

        public Col get(String name) {
            return colMap.get(name);
        }

        public ColHelper<T> filterable(String colName, Function<T, Object> func) {
            List<Object> values = dataList.stream().map(func).distinct().collect(Collectors.toList());
            get(colName).filterable2(values);
            return this;
        }

        public List<Col> getCols() {
            //排序后返回
            List<Col> cols = CollUtil.sort(colMap.values(), Comparator.comparingInt(v -> ObjectUtil.defaultIfNull(v.getInt("order"), Integer.MAX_VALUE)));
            return cols;
        }

        public Crud<T> getCrud() {
            return Crud.of(getCols(), dataList);
        }
        public static ColHelper<Dict> parseMap(List<Dict> dataList) {
            return parseMap(dataList, new HashMap<>());
        }

        public static ColHelper<Dict> parseMap(List<Dict> dataList,@NotNull Map<String, Col> colConfig) {
            ColHelper<Dict> colHelper = new ColHelper<>();
            colHelper.dataList = dataList;
            if (dataList.isEmpty()){
                return colHelper;
            }
            //获取字段最多的一条数据
            Dict maxFieldDict = dataList.stream().max(Comparator.comparingInt(Dict::size)).orElseGet(Dict::new);
            maxFieldDict.forEach((k,v)->{
                Col configCol = colConfig.get(k);
                Col col = Col.of(k);
                if (configCol != null) {
                    col.putAll(configCol);
                }
                //布尔值
                if (v instanceof Boolean) {
                    col.filterable(Option.BoolOptions);
                }
                colHelper.add(col);
            });
            return colHelper;
        }

        public static <T> ColHelper<T> parse(Class<?> tClass, List<T> dataList) {
            return parse(tClass, dataList, new Col().sortable(true));
        }

        public static <T> ColHelper<T> parse(Class<?> tClass, List<T> dataList,Col config) {
            ColHelper<T> colHelper = new ColHelper<>();
            colHelper.dataList = dataList;
            Field[] declaredFields = ClassUtil.getDeclaredFields(tClass);
            for (Field field : declaredFields) {
                if (ModifierUtil.isStatic(field)) {
                    continue;
                }
                //借用swagger注解解析
                ApiModelProperty apiModelProperty = AnnotationUtil.getAnnotation(field, ApiModelProperty.class);
                Col col = config.copy();
                col.label(field.getName()).name(field.getName());
                if (apiModelProperty != null) {
                    if (StrUtil.isNotBlank(apiModelProperty.value())) {
                        String[] split = apiModelProperty.value().split(";", 2);
                        col.label(split[0]);
                        if (split.length > 1) {
                            //可以使用;分割注释
                            col.remark(split[1]);
                        }
                    }
                    if (apiModelProperty.hidden()){
                        col.hidden();
                    }
                }

                Class<?> fieldClass = TypeUtil.getClass(field);
                //枚举类默认可以筛选
                if (ClassUtil.isEnum(fieldClass)) {
                    Enum<?>[] enums = (Enum<?>[]) fieldClass.getEnumConstants();
                    List<Option> options = Arrays.stream(enums).map(v -> Option.of(v.name(), v.name())).collect(Collectors.toList());
                    col.filterable(options);
                }
                //布尔值
                if (fieldClass == boolean.class || fieldClass == Boolean.class) {
                    col.filterable(Option.BoolOptions);
                }
                colHelper.add(col);
            }
            return colHelper;
        }
    }

    public static class Col extends Dict {

        public static Col of(String name, String label) {
            Col col = new Col()
                    .label(label)
                    .name(name);
            return col;
        }

        public static Col of(String labelAndName) {
            Col col = of(labelAndName, labelAndName);
            return col;
        }

        public Col copy(){
            Col col = new Col();
            col.putAll(this);
            return col;
        }

        public Col unixTimeStyle(){
            put("type","date");
            put("format","YYYY-MM-DD HH:mm:ss.SSS");
            put("valueFormat","unix");
            return this;
        }

        public Col type(String value) {
            put("type", value);
            return this;
        }

        public Col label(String label) {
            put("label", label);
            return this;
        }

        /**
         * 自定义的一个用于列排序的字段
         */
        public Col order(int order){
            put("order",order);
            return this;
        }

        public String name() {
            return getStr("name");
        }

        public Col name(String name) {
            put("name", name);
            return this;
        }

        public Col remark(String remark) {
            put("remark", remark);
            return this;
        }

        public Col sortable(boolean b) {
            put("sortable", b);
            return this;
        }

        public Col searchable() {
            put("searchable", true);
            return this;
        }

        public Col hidden(){
            put("hidden", true);
            return this;
        }

        /**
         * 默认显示列
         */
        public Col toggled(boolean toggled){
            put("toggled", toggled);
            return this;
        }

        /**
         * 固定列，值可以是left或者right
         */
        public Col fixed(String leftOrRight){
            put("fixed", leftOrRight);
            return this;
        }

        public Col filterable2(Collection<?> options) {
            return filterable2(options, false);
        }

        public Col filterable2(Collection<?> options,boolean multiple) {
            return filterable(options.stream().distinct().map(v -> Option.of(v, String.valueOf(v))).collect(Collectors.toList()), multiple);
        }
        public Col filterable(List<Option> options) {
            return filterable(options, false);
        }

        public Col filterable(List<Option> options, boolean multiple) {
            put("filterable", Dict.of("options", options,
                    "multiple", multiple));
            return this;
        }

        public Col multiple() {
            Dict filter = (Dict) computeIfAbsent("filterable", v -> Dict.of());
            filter.put("multiple", true);
            return this;
        }
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class Crud<T> {
        private Collection<Col> columns;
        private List<T> rows;
        private Integer total;

        public Crud(List<T> rows) {
            this.rows = rows;
            columns = null;
        }

        public Crud(List<T> rows, Collection<Col> cols) {
            this.rows = rows;
            this.columns = cols;
        }

        public Schema toSchema() {
            Schema schema = Schema.of("crud");
            Dict parse = Dict.parse(this);
            schema.putAll(parse);
            schema.put("data", rows);
            return schema;
        }

        public static <T> Crud<T> of(Collection<Col> columns, List<T> rows) {
            return new Crud<T>(rows, columns);
        }

        public static <T> Crud<T> of() {
            return new Crud<>();
        }

        public static <T> Crud<T> of(List<T> rows) {
            return new Crud<>(rows, null);
        }

        public static <T> Crud<T> parse(Class<?> tClass, List<T> data) {
            return ColHelper.parse(tClass, data).getCrud();
        }

        public static Crud<Dict> parseMap( List<Dict> data) {
            return ColHelper.parseMap(data).getCrud();
        }
    }
}
