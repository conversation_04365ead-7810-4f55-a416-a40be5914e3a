package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class ListSyncDataHistoryArg implements Serializable {
    @ApiModelProperty("集成流id")
    private String streamId;
    @ApiModelProperty("源企业id")
    private String sourceTenantId;
    @ApiModelProperty("源对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源数据id")
    private String sourceDataId;
    @ApiModelProperty("源数据主属性")
    private String sourceDataName;
    @ApiModelProperty("目标企业id")
    private String destTenantId;
    @ApiModelProperty("目标对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("日志开始时间")
    private Long startLogTime;
    @ApiModelProperty("日志结束时间")
    private Long endLogTime;
}
