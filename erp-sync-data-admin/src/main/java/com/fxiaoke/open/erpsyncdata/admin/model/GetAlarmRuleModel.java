package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PloyDetailModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RoleModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UserModel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetAlarmRuleModel implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 企业ei
     */
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 数据中心名称
     */
    private String dataCenterName;

    /**
     * 告警中的集成流列表
     *
     */
    private List<PloyDetailModel> ployDetailListInProgress;

    /**
     * 应用的集成流列表
     *
     */
    private List<PloyDetailModel> applyPloyDetailList;

    /**
     * 应用全部集成流
     *
     */
    private boolean applyAll;

    /**
     * 告警中/应用的集成流列表
     *
     */
    private String alarmInProgressAndAll;

    /**
     * 告警规则类型
     */
    private AlarmRuleType alarmRuleType;

    /**
     * 告警规则类型
     */
    private String alarmRuleName;

    /**
     * 告警类型
     */
    private AlarmType alarmType;

    /**
     * 告警条件
     */
    private String alarmCondition;

    /**
     * 告警等级
     */
    private AlarmLevel alarmLevel;

    /**
     * 告警阀值
     */
    private Integer threshold;

    /**
     * 通知类型
     *
     */
    private List<NotifyType> notifyType;

    /**
     * 通知人员列表
     * 比如：userId1;userId2
     *
     */
    private List<UserModel> userList;

    /**
     * 通知角色列表
     *
     */
    private List<RoleModel> roleList;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
