package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.QueryCountryAreasArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.service.CountryAreaService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.PaasGlobalDataService;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncDataMappingListByPloyDetailIdArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener.*;
import com.fxiaoke.open.erpsyncdata.admin.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.ImportProductCategoryManager;
import com.fxiaoke.open.erpsyncdata.admin.model.ImportIntegrationStreamMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.*;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.StoneFileManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingListByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.admin.utils.ExcelUtils;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmMetaManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CellStyleData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteHandler;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DBExcelUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDepartmentOrPersonnelService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {
    @Autowired
    private FileManager fileManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private EmployeeMappingService employeeMappingService;
    @Autowired
    private ErpConnectInfoDao connectInfoDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private FsObjectDataService fsObjectDataService;
    @Autowired
    private AdminSyncDataMappingManager adminSyncDataMappingManager;
    @Autowired
    private AdminSyncDataManager adminSyncDataManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private ImportProductCategoryManager importProductCategoryManager;
    @Autowired
    private PaasGlobalDataService paasGlobalDataService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CountryAreaService countryAreaService;
    @Autowired
    private SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;
    @Autowired
    private DBFileManager dbFileManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private CrmMetaManager crmMetaManager;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private StoneFileManager stoneFileManager;
    @Autowired
    private AdminNodeSettingService adminNodeSettingService;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;

    @Override
    public <R> Result<BuildExcelFile.Result> buildExcelFile(BuildExcelFile.Arg<R> buildExcelArg,String lang) {
        String ea = buildExcelArg.getEa();
        if (StringUtils.isBlank(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(buildExcelArg.getTenantId()));
        }
        List<R> dataList = buildExcelArg.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return new Result<>(ResultCodeEnum.LIST_EMPTY);
        }
        //写excel
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy styleStrategy = buildExcelArg.getStyle();
        if (styleStrategy == null) {
            styleStrategy = ExcelUtils.getDefaultStyle();
        }
        ExcelWriter excelWriter = EasyExcel.write(outputStream, dataList.get(0).getClass())
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .registerWriteHandler(new ExcelTemplateWriteHandler(buildExcelArg.getExcelType(),i18NStringManager,lang,buildExcelArg.getTenantId()))
                .build();
        final CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager, buildExcelArg.getTenantId(), lang);
        for (String sheetName : buildExcelArg.getSheetNames()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).registerWriteHandler(customCellWriteHandler).build();
            excelWriter.write(dataList, writeSheet);
        }
        excelWriter.finish();
        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, outputStream.toByteArray(),lang);
        BuildExcelFile.Result result = new BuildExcelFile.Result();
        String fileName = StringUtils.appendIfMissing(buildExcelArg.getFileName(), ".xlsx");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }

    private <E> List<E> readExcelToList(ReadExcel.Arg<E> arg) {
        BaseListener<E> listener = new BaseListener<E>() {
        };
        arg.setExcelListener(listener);
        fileManager.readExcel(arg);
        return listener.getDataList();
    }

    @Override
    public Result<BuildExcelFile.Result> buildExcelTemplate(String ea, ImportExcelFile.FieldDataMappingArg arg) {
        switch (arg.getExcelType()) {
            case FIELD_DATA_MAPPING:
                return buildFieldDataMappingTemplate(ea, arg.getDataType(),arg.getLang());
//            case EMPLOYEE_DATA_MAPPING:
//                return buildEmployeeDataMappingTemplate(ea);
            case OA_EMPLOYEE_DATA_MAPPING:
                return buildOAEmployeeDataMappingTemplate(ea,arg.getLang());
            case INTEGRATION_STREAM_DATA_MAPPING:
                return buildIntegrationStreamTemplate(ea, arg.getLang());
            default:
                throw new IllegalStateException("Unexpected excelType: " + arg.getExcelType());
        }
    }

    @Override
    public Result<List<CrmSpecialFieldExcelVo>> getCrmDistrictExcel(@NotNull Integer tenantId, @NotNull Integer userId,String lang) {
        HeaderObj headerObj = new HeaderObj(tenantId, userId);
        InnerResult<String> innerResult = paasGlobalDataService.areaList(headerObj);
        if (!innerResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, innerResult.getErrMessage());
        }
        JSONObject jsonObject = JSON.parseObject(innerResult.getResult());
        List<Pair<String, String>> types = ImmutableList.of(Pair.of(ErpFieldTypeEnum.country.name(), i18NStringManager.get(I18NStringEnum.s216,lang,tenantId+"")),
                Pair.of(ErpFieldTypeEnum.province.name(), i18NStringManager.get(I18NStringEnum.s217,lang,tenantId+"")),
                Pair.of(ErpFieldTypeEnum.city.name(), i18NStringManager.get(I18NStringEnum.s218,lang,tenantId+"")),
                Pair.of(ErpFieldTypeEnum.district.name(), i18NStringManager.get(I18NStringEnum.s219,lang,tenantId+"")));
        List<CrmSpecialFieldExcelVo> districtExcelVos = new ArrayList<>();
        for (Pair<String, String> type : types) {
            jsonObject.getJSONObject(type.getLeft()).getJSONArray("options").forEach(v -> {
                JSONObject j = (JSONObject) v;
                CrmSpecialFieldExcelVo build = CrmSpecialFieldExcelVo.builder()
                        .key(type.getKey())
                        .type(type.getRight())
                        .code(j.getString("value"))
                        .label(j.getString("label"))
                        .build();
                districtExcelVos.add(build);
            });
        }
        return Result.newSuccess(districtExcelVos);
    }

    @Override
    public Result<List<CrmSpecialFieldExcelVo>> queryCountryAreas(String tenantId, Integer userId, List<String> districtCodes, String lang) {
        HeaderObj headerObj = new HeaderObj(Integer.parseInt(tenantId), userId);
        // crm一次最多返回1500个
        return Result.newSuccess(ListUtils.partition(districtCodes, 100).stream()
                .flatMap(l -> countryAreaService.queryCountryAreas(headerObj, new QueryCountryAreasArg(tenantId, l)).values().stream())
                .flatMap(Collection::stream)
                .map(labelData -> CrmSpecialFieldExcelVo.builder()
                        .key(ErpFieldTypeEnum.town.name())
                        .type(i18NStringManager.get(I18NStringEnum.s331, lang, tenantId))
                        .code(labelData.getValue())
                        .label(labelData.getLabel())
                        .build())
                .distinct()
                .collect(Collectors.toList()));
    }

    @Override
    public Result<List<CrmSpecialFieldExcelVo>> getCrmDeptExcel(Integer tenantId, Integer userId,String lang) {
        HeaderObj headerObj = new HeaderObj(tenantId, userId);
        ControllerListArg listArg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        int offset = 0;
        searchQuery.setLimit(200);
        List<CrmSpecialFieldExcelVo> excelVos = new ArrayList<>();
        while (offset < 2000) {
            searchQuery.setOffset(offset);
            listArg.setSearchQuery(searchQuery);
            List<ObjectData> departments = metadataControllerService.list(headerObj, "DepartmentObj", listArg).getData().getDataList();
            for (ObjectData department : departments) {
                CrmSpecialFieldExcelVo crmSpecialFieldExcelVo = CrmSpecialFieldExcelVo.builder()
                        .key(ErpFieldTypeEnum.department.name())
                        .type(i18NStringManager.get(I18NStringEnum.s221,lang,tenantId+""))
                        .code(department.getString("dept_id"))
                        .label(department.getName()).build();
                excelVos.add(crmSpecialFieldExcelVo);
            }
            offset += 200;
        }
        return Result.newSuccess(excelVos);
    }

    @Override
    @LogLevel
    public Result<ImportExcelFile.Result> importExcelFile(ImportExcelFile.FieldDataMappingArg arg, String dataCenterId, String lang) throws IOException {
        Result<ImportExcelFile.Result> result;
        switch (arg.getExcelType()) {
            case FIELD_DATA_MAPPING:
                if (arg.getDataType() == ErpFieldTypeEnum.category) {
                    result = importCategoryFieldDataMapping(arg,lang);
                } else if (arg.getDataType() == ErpFieldTypeEnum.user) {
                    result = importUserFieldDataMapping(arg,lang);
                } else if (arg.getDataType() == ErpFieldTypeEnum.employee) {
                    result = importEmpFieldDataMapping(arg,lang);
                } else {
                    result = importFieldDataMapping(arg,lang);
                }
                break;
//            case EMPLOYEE_DATA_MAPPING:
//                result = importEmployeeDataMapping(arg, dataCenterId);
//                break;
            case OA_EMPLOYEE_DATA_MAPPING:
                result = importOAEmployeeDataMapping(arg,lang);
                break;
            default:
                throw new IllegalStateException("Unexpected excelType: " + arg.getExcelType());
        }
        if (!result.isSuccess()) {
            return result;
        }
        return result;
    }

    @Override
    @LogLevel
    public Result<ImportExcelFile.Result> importObjectDataMapping(ImportExcelFile.ObjectDataMappingArg arg,String lang) throws IOException {
        String tenantId = arg.getTenantId();
        String ployDetailId = arg.getPloyDetailId();
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        Result<SyncPloyDetailResult> ployDetail = adminSyncPloyDetailService.getById(tenantId, ployDetailId,lang);
        SyncPloyDetailResult ployDetailData = ployDetail.getData();
/*      List<SyncPloyDetailSnapshotEntity> syncPloyDetailSnapshotEntities = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listEnableSnapshotsBySyncPloyDetailsId(ployDetailId, 1);
        if (CollectionUtils.isEmpty(syncPloyDetailSnapshotEntities)) {
            syncPloyDetailSnapshotEntities = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listEnableSnapshotsBySyncPloyDetailsId(ployDetailId, 2);
            if (CollectionUtils.isEmpty(syncPloyDetailSnapshotEntities)) {
                Result result = Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
                result.setErrMsg(i18NStringManager.get(I18NStringEnum.s996, lang, tenantId));
                return result;
            }
        }*/
        List<ImportExcelFile.Result> results = Lists.newArrayList();
        ObjectDataMappingListener listener =
                new ObjectDataMappingListener(tenantId,ployDetailData, adminSyncDataMappingManager, adminSyncDataManager, syncDataMappingsDao,syncDepartmentOrPersonnelService,i18NStringManager,lang);
        //读取excel
        ReadExcel.Arg<ObjectDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(ObjectDataMappingExcelVo.class);

        try (ByteArrayInputStream inputStream = getByteArrayInputStream(arg)) {
            readExcelArg.setInputStream(inputStream);
            readExcelArg.setExcelType(excelType);

            listener.setApiName(ployDetailData.getSourceObjectApiName(), ployDetailData.getDestObjectApiName());
            boolean sourceIsCrm = TenantType.CRM.equals(ployDetailData.getSourceTenantType());
            if (sourceIsCrm) {
                readExcelArg.setSheetName(ExcelUtils.getSheetName(ployDetailData.getSourceObjectApiName()));
            } else {
                readExcelArg.setSheetName(ExcelUtils.getSheetName(ployDetailData.getDestObjectApiName()));
            }
            listener.setSheetName(readExcelArg.getSheetName());
            fileManager.readExcelBySheetName(readExcelArg);//主对象数据
            ImportExcelFile.Result result = listener.getImportResult();
            results.add(result);
            if (CollectionUtils.isNotEmpty(ployDetailData.getDetailObjectMappings())) {//明细数据
                for (SyncPloyDetailResult.ObjectMappingInfo detailObjectMappingData : ployDetailData.getDetailObjectMappings()) {
                    ObjectDataMappingListener listener1 =
                            new ObjectDataMappingListener(tenantId,ployDetailData, adminSyncDataMappingManager, adminSyncDataManager, syncDataMappingsDao,syncDepartmentOrPersonnelService,i18NStringManager,lang);
                    //读取excel
                    // 重置输入流位置
                    inputStream.reset();
                    ReadExcel.Arg<ObjectDataMappingExcelVo> readExcelArg1 = new ReadExcel.Arg<>();
                    readExcelArg1.setExcelListener(listener1);
                    readExcelArg1.setType(ObjectDataMappingExcelVo.class);
                    readExcelArg1.setInputStream(inputStream);
                    readExcelArg1.setExcelType(excelType);
                    listener1.setApiName(detailObjectMappingData.getSourceObjectApiName(), detailObjectMappingData.getDestObjectApiName());
                    if (sourceIsCrm) {
                        readExcelArg1.setSheetName(ExcelUtils.getSheetName(detailObjectMappingData.getSourceObjectApiName()));
                    } else {
                        readExcelArg1.setSheetName(ExcelUtils.getSheetName(detailObjectMappingData.getDestObjectApiName()));
                    }
                    listener1.setSheetName(readExcelArg1.getSheetName());
                    fileManager.readExcelBySheetName(readExcelArg1);//从对象数据
                    ImportExcelFile.Result detailResult = listener1.getImportResult();
                    results.add(detailResult);
                }
            }
            log.info("importObjectDataMapping arg:{},result:{}", arg, results);
            ImportExcelFile.Result allResult = new ImportExcelFile.Result();
            for (ImportExcelFile.Result res : results) {
                allResult.incrUpdate(res.getUpdateNum());
                allResult.incrInsert(res.getInsertNum());
                allResult.incrInvoke(res.getInvokedNum());
                allResult.setDeleteNum(res.getDeleteNum());
                if (allResult.getPrintMsg().isEmpty()) {
                    allResult.setPrintMsg(res.getPrintMsg());
                } else {
                    allResult.setPrintMsg(allResult.getPrintMsg() + "\n" + res.getPrintMsg());
                }
            }
            Result<ImportExcelFile.Result> resultResult = new Result<>(allResult);
            return resultResult;
        }
    }

    @Override
    public Result<ImportExcelFile.Result> importObjectDataMapping(String tenantId, Integer userId, String ployDetailId, ExcelTypeEnum excelType, InputStream inputStream, String fileType, String lang) {
        SyncPloyDetailResult ployDetailData = adminSyncPloyDetailService.getById(tenantId, ployDetailId, lang).getData();
        final boolean sourceIsCrm = TenantType.CRM.equals(ployDetailData.getSourceTenantType());

        final com.alibaba.excel.support.ExcelTypeEnum excelTypeEnum = com.alibaba.excel.support.ExcelTypeEnum.valueOf(fileType.toUpperCase());
        final ExcelReader build = EasyExcelFactory.read(inputStream).excelType(excelTypeEnum).build();
        List<ReadSheet> sheetList = build.excelExecutor().sheetList();
        final Map<String, ReadSheet> sheetMap = sheetList.stream().collect(Collectors.toMap(ReadSheet::getSheetName, Function.identity()));

        final List<ObjectDataMappingListener> allListener = Stream.concat(
                        ListUtils.emptyIfNull(ployDetailData.getDetailObjectMappings()).stream()
                                .map(detailObjectMappingData -> Pair.of(detailObjectMappingData.getSourceObjectApiName(), detailObjectMappingData.getDestObjectApiName())),
                        Stream.of(Pair.of(ployDetailData.getSourceObjectApiName(), ployDetailData.getDestObjectApiName()))
                ).map(pair -> {
                    final String sheetName = ExcelUtils.getSheetName(sourceIsCrm ? pair.getKey() : pair.getValue());
                    final ReadSheet readSheet = sheetMap.get(sheetName);
                    if (Objects.isNull(readSheet)) {
                        return null;
                    }
                    final ObjectDataMappingListener listener = new ObjectDataMappingListener(tenantId, ployDetailData, adminSyncDataMappingManager, adminSyncDataManager, syncDataMappingsDao, syncDepartmentOrPersonnelService, i18NStringManager, lang);
                    listener.setApiName(pair.getKey(), pair.getValue());
                    readSheet.setCustomReadListenerList(Collections.singletonList(listener));
                    readSheet.setClazz(ObjectDataMappingExcelVo.class);

                    return listener;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        build.readAll();

        ImportExcelFile.Result allResult = new ImportExcelFile.Result();
        for (ObjectDataMappingListener listener : allListener) {
            final ImportExcelFile.Result res = listener.getImportResult();
            allResult.incrUpdate(res.getUpdateNum());
            allResult.incrInsert(res.getInsertNum());
            allResult.incrInvoke(res.getInvokedNum());
            allResult.setDeleteNum(res.getDeleteNum());
            if (allResult.getPrintMsg().isEmpty()) {
                allResult.setPrintMsg(res.getPrintMsg());
            } else {
                allResult.setPrintMsg(allResult.getPrintMsg() + "\n" + res.getPrintMsg());
            }
        }

        return new Result<>(allResult);
    }

    private static ByteArrayInputStream getByteArrayInputStream(ImportExcelFile.ObjectDataMappingArg arg) throws IOException {
        //可能有从对象,会导致多次读流导致报错,先用ByteArrayInputStream,但是大文件可能导致oom
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            return new ByteArrayInputStream(baos.toByteArray());
        }
    }

    private void sendExportResult(String tenantId, String dataCenterId, String ployDetailId, Integer userId, String lang, String downloadUrl) {
        String msg = i18NStringManager.get2(I18NStringEnum.s214.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s214.getI18nValue(), downloadUrl),
                Lists.newArrayList(downloadUrl));
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setDataCenterId(dataCenterId);
        if(StringUtils.isNotEmpty(ployDetailId)) {
            sendTextNoticeArg.setPloyDetailId(ployDetailId);
        }
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(i18NStringManager.get2(I18NStringEnum.s215.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s215.getI18nValue(), LocalDateTime.now().toString()),
                Lists.newArrayList(LocalDateTime.now().toString())));

        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager, lang, tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }

    @Override
    public Result<ExportSyncDataMapping.Result> asyncExportSyncDataMappingData(ExportSyncDataMapping.ExportSyncDataMappingArg arg,
                                                                               String dataCenterId,
                                                                               String lang) {
        final String tenantId = arg.getTenantId();
        SyncPloyDetailEntity ployDetail = syncPloyDetailManager.getEntryById(tenantId, arg.getPloyDetailId());
        if (Objects.isNull(ployDetail)) {
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }

        SyncDataMappingListByPloyDetailIdArg detailIdArg= BeanUtil.copy(arg,SyncDataMappingListByPloyDetailIdArg.class);
        detailIdArg.setPageNumber(1);
        detailIdArg.setPageSize(1);
        Result<SyncDataMappingListByPloyDetailIdResult> mappingResult = adminSyncDataMappingService
                .listByPloyDetailId(tenantId, detailIdArg,lang);

        if (!mappingResult.isSuccess() || mappingResult.getData() == null || mappingResult.getData().getTotalCount() == 0) {
            return Result.newSystemError(I18NStringEnum.s211);
        }

        final String url = exportAndUpload(tenantId, lang, ployDetail, arg);

        sendExportResult(tenantId, arg.getDataCenterId(), arg.getPloyDetailId(), arg.getUserId(), lang, url);
        ExportSyncDataMapping.Result syncDataMappingResult = new ExportSyncDataMapping.Result();
        syncDataMappingResult.setDownloadUrl(url);
        syncDataMappingResult.setSuccess(true);
        final String printMsg = i18NStringManager.get(I18NStringEnum.s209, arg.getLang(), tenantId);
        syncDataMappingResult.setPrintMsg(printMsg);
        return new Result<>(syncDataMappingResult);
    }

    @SneakyThrows
    private String exportAndUpload(String tenantId, String lang, SyncPloyDetailEntity ployDetail, ExportSyncDataMapping.ExportSyncDataMappingArg arg) {
        SyncObjectAndTenantMappingData objMapping = SyncObjectAndTenantMappingData.newInstance(tenantId, ployDetail, arg.getSourceObjectApiName(), i18NStringManager);
        Map<Integer, String> statusMap = Arrays.stream(SyncStatusEnum.values())
                .map(SyncStatusEnum::getSyncDataStatuses)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toMap(Function.identity(), status -> SyncStatusEnum.getBySyncDataStatus(status).getNameByLang(i18NStringManager, lang, tenantId)));

        Map<Integer, String> statusTypeMap = Arrays.stream(EventTypeEnum.values())
                .collect(Collectors.toMap(
                        EventTypeEnum::getType,
                        eventType -> Optional.ofNullable(EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, eventType.getType()))
                                .orElse(eventType.getName()),
                        (existing, replacement) -> existing
                ));

        String erpObjApiName;
        String crmObjApiName;
        final boolean sourceCrm = Objects.equals(ployDetail.getSourceTenantType(), TenantType.CRM);
        if (sourceCrm) {
            erpObjApiName = objMapping.getDestObjectApiName();
            crmObjApiName = objMapping.getSourceObjectApiName();
        } else {
            erpObjApiName = objMapping.getSourceObjectApiName();
            crmObjApiName = objMapping.getDestObjectApiName();
        }
        String erpObjName = erpObjManager.getErpObjName(tenantId, erpObjApiName);
        String crmObjName = crmMetaManager.getObjectDescribe(tenantId, crmObjApiName).getDisplayName();

        String excelNamePrefix = i18NStringManager.get2(I18NStringEnum.s212,
                lang,
                tenantId,
                tenantId, LocalDateTime.now().toString());
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        String zipFileName = i18NStringManager.get(I18NStringEnum.s213, lang, tenantId);
        byte[] bytes;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(out)) {

            ExportSyncDataMapping.PageExcel pageExcel = new ExportSyncDataMapping.PageExcel();
            pageExcel.setNext(true);
            pageExcel.setUpdateTime(Objects.isNull(arg.getEndTime()) ? System.currentTimeMillis() : arg.getEndTime());
            pageExcel.setDataForExcel(new ArrayList<>());
            // 5W一个excel 最多100W
            for (int i = 0; i < 20 && pageExcel.isNext(); i++) {
                pageExcel = getNextExcel(tenantId, pageExcel, arg, objMapping, statusMap, erpObjName, crmObjName, sourceCrm, statusTypeMap);
                if (pageExcel.getDataForExcel().isEmpty()) {
                    break;
                }

                final String excelName = excelNamePrefix + "-" + i;
                final ExcelSheetArg sheetArg = new ExcelSheetArg();
                sheetArg.setSheetName("sheet1");
                sheetArg.setDataList(pageExcel.getDataForExcel());
                sheetArg.setClazz(ExportDataMappingExcelVo.class);
                ByteArrayOutputStream outputStream = dbFileManager.createExcelStream(tenantId, lang, excelName, Lists.newArrayList(sheetArg));

                if (i == 0 && !pageExcel.isNext()) {
                    // 如果只有一个文件，则直接返回
                    String npath = dbFileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, zipFileName, outputStream.toByteArray());
                    return String.format(userCenterService.getDownloadFilePath(tenantId), npath, URLEncoder.encode(zipFileName) + ".xlsx");
                }

                addIndexToZip(excelName, zos, outputStream);
            }

            zos.finish();
            bytes = out.toByteArray();
        }

        String npath = dbFileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, zipFileName, bytes);
        return String.format(userCenterService.getDownloadFilePath(tenantId), npath, URLEncoder.encode(zipFileName) + ".zip");
    }

    private ExportSyncDataMapping.PageExcel getNextExcel(String tenantId,
                                                         ExportSyncDataMapping.PageExcel page,
                                                         ExportSyncDataMapping.ExportSyncDataMappingArg ployDetailIdArg,
                                                         SyncObjectAndTenantMappingData objMapping,
                                                         Map<Integer, String> statusMap,
                                                         String erpObjName,
                                                         String crmObjName,
                                                         boolean sourceCrm,
                                                         Map<Integer, String> statusTypeMap) {
        List<ExportDataMappingExcelVo> dataForExcel = page.getDataForExcel();
        dataForExcel.clear();
        Integer excelFileSize = ConfigCenter.EXPORT_DATA_NUMBER;

        while (dataForExcel.size() < excelFileSize && page.isNext()) {
            final int limit = Math.min(100, excelFileSize - dataForExcel.size());
            Page<SyncDataMappingsEntity> entityPage = syncDataMappingManager.listMappingsAfterTimeAndId(tenantId, objMapping, limit, page.getUpdateTime(), page.getMaxId(), ployDetailIdArg.getStatus(), ployDetailIdArg.getSourceDataId(), ployDetailIdArg.getDestDataId(), ployDetailIdArg.getSourceDataName(), ployDetailIdArg.getDestDataName(), ployDetailIdArg.getRemark(), ployDetailIdArg.getStartTime(), false);
            List<SyncDataMappingsEntity> currentPageData = entityPage.getData();
            if (currentPageData.isEmpty()) {
                if (Objects.isNull(page.getMaxId())) {
                    page.setNext(false);
                    break;
                } else {
                    page.setMaxId(null);
                    continue;
                }
            }

            //在这里查询remark
            List<String> lastSyncDataIds = Lists.newArrayList();
            for (SyncDataMappingsEntity entity : currentPageData) {
                if (StringUtils.isNotBlank(entity.getLastSyncDataId())) {
                    lastSyncDataIds.add(entity.getLastSyncDataId());
                }
            }
            List<SyncDataEntity> dataEntityList = adminSyncDataDao.setTenantId(tenantId).listByIds(tenantId, lastSyncDataIds);
            Map<String, SyncDataEntity> id2DataEntity = dataEntityList.stream().collect(Collectors.toMap(SyncDataEntity::getId, obj -> obj));
            for (SyncDataMappingsEntity entity : currentPageData) {
                if (entity.getLastSyncDataId() != null && id2DataEntity.get(entity.getLastSyncDataId()) != null) {
                    SyncDataEntity syncDataEntity = id2DataEntity.get(entity.getLastSyncDataId());
                    entity.setRemark(syncDataEntity.getRemark());
                }
            }

            dataForExcel.addAll(ExportDataMappingExcelVo.convert2ExcelVo(currentPageData, sourceCrm, statusMap, erpObjName, crmObjName, id2DataEntity, statusTypeMap));
            final SyncDataMappingsEntity syncDataMappingsEntity = currentPageData.get(currentPageData.size() - 1);
            page.setMaxId(syncDataMappingsEntity.getId());
            page.setUpdateTime(syncDataMappingsEntity.getUpdateTime());
        }

        return page;
    }

    private void addIndexToZip(String name, ZipOutputStream zos, ByteArrayOutputStream excelOutputStream) throws IOException {
        zos.putNextEntry(new ZipEntry(name + ".xlsx"));
        zos.write(excelOutputStream.toByteArray());
        zos.closeEntry();
    }

    @Override
    public Result<BuildExcelFile.Result> buildDataVerificationTemplate(String tenantId, String dataCenterId,String lang) {
        DataVerificationIdExcelVo tempData = getDataVerificationIdExcelVoTempData(tenantId, lang);
        BuildExcelFile.Arg<DataVerificationIdExcelVo> arg = new BuildExcelFile.Arg<>();
        arg.setTenantId(tenantId);
        arg.setFileName(i18NStringManager.get(I18NStringEnum.s205,lang,tenantId));
        arg.setDataList(Collections.singletonList(tempData));
        arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s206,lang,tenantId)));
        return buildExcelFile(arg,lang);
    }

    public DataVerificationIdExcelVo getDataVerificationIdExcelVoTempData(String tenantId, String lang) {
        DataVerificationIdExcelVo vo = new DataVerificationIdExcelVo();
        vo.setId(i18NStringManager.get(I18NStringEnum.s3786, lang, tenantId));
        return vo;
    }

    @Override
    public Result<BuildExcelFile.Result> buildObjectDataMappingTemplate(String tenantId, String ployDetailId,String lang) {
        Result<SyncPloyDetailResult> ployDetail = adminSyncPloyDetailService.getById(tenantId, ployDetailId,lang);
        List<String> sheetNames = Lists.newArrayList();
        ObjectDataMappingExcelVo tempData = getObjectDataMappingExcelVoTempData(tenantId, lang);
        if (TenantType.CRM.equals(ployDetail.getData().getSourceTenantType())) {//crm->erp
            tempData.setSourceDataName(i18NStringManager.get(I18NStringEnum.s199,lang,tenantId));
            tempData.setSourceDataId(i18NStringManager.get(I18NStringEnum.s200,lang,tenantId));
            tempData.setDestDataName(i18NStringManager.get(I18NStringEnum.s201,lang,tenantId));
            tempData.setDestDataId(i18NStringManager.get(I18NStringEnum.s202,lang,tenantId));
            tempData.setMasterDataId(i18NStringManager.get(I18NStringEnum.s203,lang,tenantId));
            tempData.setRemark(i18NStringManager.get(I18NStringEnum.s204,lang,tenantId));
            sheetNames.add(ExcelUtils.getSheetName(ployDetail.getData().getSourceObjectApiName()));
            if (CollectionUtils.isNotEmpty(ployDetail.getData().getDetailObjectMappings())) {
                for (SyncPloyDetailResult.ObjectMappingInfo detail : ployDetail.getData().getDetailObjectMappings()) {
                    sheetNames.add(ExcelUtils.getSheetName(detail.getSourceObjectApiName()));
                }
            }
        } else {//erp->crm
            tempData.setSourceDataName(i18NStringManager.get(I18NStringEnum.s201,lang,tenantId));
            tempData.setSourceDataId(i18NStringManager.get(I18NStringEnum.s202,lang,tenantId));
            tempData.setDestDataName(i18NStringManager.get(I18NStringEnum.s199,lang,tenantId));
            tempData.setDestDataId(i18NStringManager.get(I18NStringEnum.s200,lang,tenantId));
            tempData.setMasterDataId(i18NStringManager.get(I18NStringEnum.s203,lang,tenantId));
            tempData.setRemark(i18NStringManager.get(I18NStringEnum.s204,lang,tenantId));
            sheetNames.add(ExcelUtils.getSheetName(ployDetail.getData().getDestObjectApiName()));
            if (CollectionUtils.isNotEmpty(ployDetail.getData().getDetailObjectMappings())) {
                for (SyncPloyDetailResult.ObjectMappingInfo detail : ployDetail.getData().getDetailObjectMappings()) {
                    sheetNames.add(ExcelUtils.getSheetName(detail.getDestObjectApiName()));
                }
            }
        }
        BuildExcelFile.Arg<ObjectDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
        arg.setTenantId(tenantId);
        arg.setFileName(i18NStringManager.get(I18NStringEnum.s198,lang,tenantId));
        arg.setDataList(Collections.singletonList(tempData));
        arg.setSheetNames(sheetNames);
        return buildExcelFile(arg,lang);
    }

    public ObjectDataMappingExcelVo getObjectDataMappingExcelVoTempData(String tenantId, String lang) {
        ObjectDataMappingExcelVo vo = new ObjectDataMappingExcelVo();
        vo.setSourceDataId("123");
        vo.setSourceDataName(i18NStringManager.get(I18NStringEnum.s3781, lang, tenantId));
        vo.setDestDataId("456");
        vo.setDestDataName(i18NStringManager.get(I18NStringEnum.s3780, lang, tenantId));
        vo.setMasterDataId(i18NStringManager.get(I18NStringEnum.s3787, lang, tenantId));
        vo.setRemark(i18NStringManager.get(I18NStringEnum.s3788, lang, tenantId));
        return vo;
    }

    @Override
    public Result<BuildExcelFile.Result> buildObjectFieldTemplate(String tenantId,String dataCenterId,String realObjectApiName, String lang) {
        List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByRealObjectApiName(tenantId,
                dataCenterId, realObjectApiName);

        final List<ErpObjectFieldExcelVo> dataList = relationshipEntityList.stream()
                .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                .map(ErpObjectFieldExcelVo::getTempData)
                .collect(Collectors.toList());
        BuildExcelFile.Arg<ErpObjectFieldExcelVo> arg = new BuildExcelFile.Arg<>();
        arg.setTenantId(tenantId);
        arg.setFileName(i18NStringManager.get(I18NStringEnum.s197,lang,tenantId));
        arg.setDataList(dataList);
        arg.setSheetNames(Lists.newArrayList(ExcelUtils.getSheetName(realObjectApiName)));
        arg.setExcelType(ExcelTypeEnum.OBJ_FIELD_DATA);
        return buildExcelFile(arg,lang);
    }

    private Result<BuildExcelFile.Result> buildFieldDataMappingTemplate(String ea, ErpFieldTypeEnum dataType, String lang) {
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        if (dataType == ErpFieldTypeEnum.category) {
            CategoryFieldDataMappingExcelVo cTempData = getCategoryFieldDataMappingExcelVoTempData(tenantId, lang);
            BuildExcelFile.Arg<CategoryFieldDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
            arg.setEa(ea);
            arg.setFileName(i18NStringManager.get(I18NStringEnum.s187,lang,tenantId));
            arg.setDataList(Collections.singletonList(cTempData));
            arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s188,lang,tenantId)));
            return buildExcelFile(arg,lang);
        } else if (dataType == ErpFieldTypeEnum.user) {
            UserFieldDataMappingExcelVo cTempData = getUserFieldDataMappingExcelVoTempData(tenantId, lang);
            BuildExcelFile.Arg<UserFieldDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
            arg.setEa(ea);
            arg.setFileName(i18NStringManager.get(I18NStringEnum.s189,lang,tenantId));
            arg.setDataList(Collections.singletonList(cTempData));
            arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s190,lang,tenantId)));
            return buildExcelFile(arg,lang);
        } else if (dataType == ErpFieldTypeEnum.employee) {
            EmpFieldDataMappingExcelVo tempData = getEmpFieldDataMappingExcelVoTempData(tenantId, lang);
            BuildExcelFile.Arg<EmpFieldDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
            arg.setEa(ea);
            arg.setFileName(i18NStringManager.get(I18NStringEnum.s191,lang,tenantId));
            arg.setDataList(Collections.singletonList(tempData));
            arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s192,lang,tenantId)));
            return buildExcelFile(arg,lang);
        } else {
            FieldDataMappingExcelVo tempData = FieldDataMappingExcelVo.getTempData(i18NStringManager, tenantId, lang);
            BuildExcelFile.Arg<FieldDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
            arg.setEa(ea);
            arg.setFileName(i18NStringManager.get(I18NStringEnum.s193,lang,tenantId));
            arg.setDataList(Collections.singletonList(tempData));
            arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s194,lang,tenantId)));
            return buildExcelFile(arg,lang);
        }
    }

    public EmpFieldDataMappingExcelVo getEmpFieldDataMappingExcelVoTempData(String tenantId, String lang) {
        EmpFieldDataMappingExcelVo vo = new EmpFieldDataMappingExcelVo();
        vo.setFsDataName(i18NStringManager.get(I18NStringEnum.s3780, lang, tenantId));
        vo.setErpDataName(i18NStringManager.get(I18NStringEnum.s3781, lang, tenantId));
        vo.setFsDataId("248");
        vo.setErpDataId("248");
        return vo;
    }

    public CategoryFieldDataMappingExcelVo getCategoryFieldDataMappingExcelVoTempData(String tenantId, String lang) {
        CategoryFieldDataMappingExcelVo vo = new CategoryFieldDataMappingExcelVo();
        vo.setFsDataName(i18NStringManager.get(I18NStringEnum.s3780, lang, tenantId));
        vo.setErpDataName(i18NStringManager.get(I18NStringEnum.s3781, lang, tenantId));
        vo.setFsCategoryCode("248");
        vo.setErpDataId("248");
        vo.setErpParentDataId("0");
        return vo;
    }

    public UserFieldDataMappingExcelVo getUserFieldDataMappingExcelVoTempData(String tenantId, String lang) {
        UserFieldDataMappingExcelVo vo = new UserFieldDataMappingExcelVo();
        vo.setFsDataName(i18NStringManager.get(I18NStringEnum.s3780, lang, tenantId));
        vo.setErpDataName(i18NStringManager.get(I18NStringEnum.s3781, lang, tenantId));
        vo.setFsDataId("248");
        return vo;
    }

    private Result<BuildExcelFile.Result> buildOAEmployeeDataMappingTemplate(String ea, String lang) {
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        OAEmployeeDataMappingExcelVo tempData = getOAEmployeeDataMappingExcelVoTempData(tenantId, lang);
        BuildExcelFile.Arg<OAEmployeeDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
        arg.setEa(ea);
        arg.setFileName(i18NStringManager.get(I18NStringEnum.s195,lang,tenantId));
        arg.setDataList(Collections.singletonList(tempData));
        arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s196,lang,tenantId)));
        return buildExcelFile(arg,lang);
    }

    public OAEmployeeDataMappingExcelVo getOAEmployeeDataMappingExcelVoTempData(String tenantId, String lang) {
        OAEmployeeDataMappingExcelVo vo = new OAEmployeeDataMappingExcelVo();
        vo.setOaEmployeeName(i18NStringManager.get(I18NStringEnum.s3778, lang, tenantId) + "xx");
        vo.setCrmEmployeeId(i18NStringManager.get(I18NStringEnum.s3778, lang, tenantId) + "10001");
        vo.setOaEmployeeId("gf2500");
        return vo;
    }

    private <T> Result<ImportExcelFile.Result> importFieldDataMapping(ImportExcelFile.FieldDataMappingArg arg,String lang) throws IOException {
        String tenantId = arg.getTenantId();
        String dataCenterId = arg.getDataCenterId();
        ErpFieldTypeEnum dataType = arg.getDataType();
        ErpConnectInfoEntity connectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dataCenterId);
        FieldDataMappingListener listener =
                new FieldDataMappingListener(tenantId,
                        connectInfo.getChannel(),
                        dataType,
                        erpFieldDataMappingDao,
                        idGenerator,
                        arg.getDataCenterId(),
                        i18NStringManager,
                        lang);
        //读取excel
        ReadExcel.Arg<FieldDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(FieldDataMappingExcelVo.class);
        InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
        readExcelArg.setInputStream(inputStream);
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        readExcelArg.setExcelType(excelType);
        fileManager.readExcel(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importFieldDataMapping arg:{},result:{}", arg, result);
        return new Result<>(result);
    }

    private <T> Result<ImportExcelFile.Result> importUserFieldDataMapping(ImportExcelFile.FieldDataMappingArg arg,String lang) throws IOException {
        String tenantId = arg.getTenantId();
        String dataCenterId = arg.getDataCenterId();
        ErpFieldTypeEnum dataType = arg.getDataType();
        ErpConnectInfoEntity connectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dataCenterId);
        UserFieldDataMappingListener listener =
                new UserFieldDataMappingListener(tenantId,
                        connectInfo.getChannel(),
                        dataType,
                        erpFieldDataMappingDao,
                        idGenerator,
                        arg.getDataCenterId(),
                        employeeMappingService,
                        i18NStringManager,
                        lang);
        //读取excel
        ReadExcel.Arg<UserFieldDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(UserFieldDataMappingExcelVo.class);
        InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
        readExcelArg.setInputStream(inputStream);
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        readExcelArg.setExcelType(excelType);
        fileManager.readExcel(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importUserFieldDataMapping arg:{},result:{}", arg, result);
        return new Result<>(result);
    }

    private <T> Result<ImportExcelFile.Result> importEmpFieldDataMapping(ImportExcelFile.FieldDataMappingArg arg,String lang) throws IOException {
        String tenantId = arg.getTenantId();
        String dataCenterId = arg.getDataCenterId();
        ErpFieldTypeEnum dataType = arg.getDataType();
        ErpConnectInfoEntity connectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dataCenterId);
        EmpFieldDataMappingListener listener =
                new EmpFieldDataMappingListener(tenantId,
                        connectInfo.getChannel(),
                        dataType,
                        erpFieldDataMappingDao,
                        employeeMappingService,
                        arg.getDataCenterId(),
                        lang,
                        i18NStringManager);
        //读取excel
        ReadExcel.Arg<EmpFieldDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(EmpFieldDataMappingExcelVo.class);
        InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
        readExcelArg.setInputStream(inputStream);
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        readExcelArg.setExcelType(excelType);
        fileManager.readExcel(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importUserFieldDataMapping arg:{},result:{}", arg, result);
        return new Result<>(result);
    }

    /**
     * 分类特殊处理，会进行导入CRM操作
     *
     * @param arg
     * @return
     * @throws IOException
     */
    private Result<ImportExcelFile.Result> importCategoryFieldDataMapping(ImportExcelFile.FieldDataMappingArg arg,String lang) throws IOException {
        ReadExcel.Arg<CategoryFieldDataMappingExcelVo> readArg = new ReadExcel.Arg<>();
        readArg.setType(CategoryFieldDataMappingExcelVo.class);
        InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
        readArg.setInputStream(inputStream);
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        readArg.setExcelType(excelType);
        List<CategoryFieldDataMappingExcelVo> importList = readExcelToList(readArg);
        String tenantId = arg.getTenantId();
        String dataCenterId = arg.getDataCenterId();
        boolean b = importProductCategoryManager.importProductCateGory2Crm(tenantId, arg.getUserId(), dataCenterId, importList,lang);
        ImportExcelFile.Result result = new ImportExcelFile.Result();
        if (b) {
            //全量导入成功
            result.setPrintMsg(i18NStringManager.get2(I18NStringEnum.s207.getI18nKey(),
                    arg.getLang(),
                    arg.getTenantId(),
                    String.format(I18NStringEnum.s207.getI18nValue(), importList.size()),
                    Lists.newArrayList(importList.size()+"")));
        } else {
            result.setPrintMsg(i18NStringManager.get(I18NStringEnum.s207,arg.getLang(),arg.getTenantId()));
        }
        return Result.newSuccess(result);
    }


//    private Result<ImportExcelFile.Result> importEmployeeDataMapping(ImportExcelFile.FieldDataMappingArg arg, String dataCenterId) throws IOException {
//        String tenantId = arg.getTenantId();
//        ErpFieldTypeEnum dataType = ErpFieldTypeEnum.employee;
//        ErpConnectInfoEntity connectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
//        Result<List<EmployeeDto>> fsEmployeeResult = fsObjectDataService.getEmployeeFs(Integer.valueOf(tenantId));
//        Map<String, EmployeeDto> tel2FsEmployeeMap = fsEmployeeResult.getData().stream()
//                //筛选有手机号的员工
//                .filter(v -> StringUtils.isNotBlank(v.getMobile()))
//                .collect(Collectors.toMap(EmployeeDto::getMobile, employee -> employee, (u, v) -> u));
//        EmployeeDataMappingListener listener = new EmployeeDataMappingListener(tenantId, connectInfo.getChannel(), dataType,
//                tel2FsEmployeeMap, employeeMappingService, idGenerator, dataCenterId);
//        //读取excel
//        ReadExcel.Arg<EmployeeDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
//        readExcelArg.setExcelListener(listener);
//        readExcelArg.setType(EmployeeDataMappingExcelVo.class);
//        readExcelArg.setInputStream(arg.getFile().getInputStream());
//        fileManager.readExcel(readExcelArg);
//        ImportExcelFile.Result result = listener.getImportResult();
//        log.info("importFieldDataMapping arg:{},result:{}", arg, result);
//        return new Result<>(result);
//    }

    private Result<ImportExcelFile.Result> importOAEmployeeDataMapping(ImportExcelFile.FieldDataMappingArg arg,String lang) throws IOException {
        String tenantId = arg.getTenantId();
        ErpFieldTypeEnum dataType = ErpFieldTypeEnum.employee_oa;
        OAEmployeeDataMappingListener listener = new OAEmployeeDataMappingListener(tenantId, ErpChannelEnum.OA, dataType, employeeMappingService,
                idGenerator,i18NStringManager,lang);
        //读取excel
        ReadExcel.Arg<OAEmployeeDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(OAEmployeeDataMappingExcelVo.class);
        InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
        readExcelArg.setInputStream(inputStream);
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        readExcelArg.setExcelType(excelType);
        fileManager.readExcel(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importFieldDataMapping arg:{},result:{}", arg, result);
        return new Result<>(result);
    }

    @Override
    public Result<BuildExcelFile.Result> buildIntegrationStreamTemplate(String ea, String lang) {
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(ea));
        //多表插入
        WriteTable writeTable0 = EasyExcel.writerTable(0)
                .needHead(Boolean.TRUE)
                .head(ErpIntegrationStreamExcelVo2.class)
                .build();
        WriteTable writeTable1 = EasyExcel.writerTable(1)
                .needHead(Boolean.TRUE)
                .head(ErpIntegrationStreamExcelVo.class)
                .build();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                DBExcelUtils.getDefaultStyle();
        //样式调整
        Map<String, CellStyleData> styleDataList = new HashMap<>();
        CellStyleData data = new CellStyleData();
        data.setFillPatternType(FillPatternType.NO_FILL);
        styleDataList.put(i18NStringManager.get(I18NStringEnum.s3779, lang, tenantId), data);

        final CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager, tenantId, lang, styleDataList);

        ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .registerWriteHandler(customCellWriteHandler)
                .registerWriteHandler(new ExcelTemplateWriteHandler(ExcelTypeEnum.INTEGRATION_STREAM_DATA_MAPPING, i18NStringManager, lang, tenantId))
                .build();

        WriteSheet writeSheet = EasyExcel.writerSheet(i18NStringManager.get(I18NStringEnum.s1098, lang, tenantId)).build();
        excelWriter.write(null, writeSheet, writeTable0);
        excelWriter.write(getErpIntegrationStreamExcelVoTemplateData(tenantId, lang), writeSheet, writeTable1);
        excelWriter.finish();


        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, outputStream.toByteArray(), lang);
        BuildExcelFile.Result result = new BuildExcelFile.Result();
        String fileName = StringUtils.appendIfMissing(i18NStringManager.get(I18NStringEnum.s4029, lang, tenantId), ".xlsx");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);

        return new Result<>(result);
    }

    public List<ErpIntegrationStreamExcelVo> getErpIntegrationStreamExcelVoTemplateData(String tenantId, String lang) {
        List<ErpIntegrationStreamExcelVo> erpIntegrationStreamExcelVos = new LinkedList<>();
        ErpIntegrationStreamExcelVo erpIntegrationStreamExcelVo = new ErpIntegrationStreamExcelVo();
        erpIntegrationStreamExcelVo.setCrmObjectLabel(i18NStringManager.get(I18NStringEnum.s3782, lang, tenantId));
        erpIntegrationStreamExcelVo.setCrmObjectApiName(i18NStringManager.get(I18NStringEnum.s3783, lang, tenantId));
        erpIntegrationStreamExcelVo.setThirdPartyObjectLabel(i18NStringManager.get(I18NStringEnum.s3782, lang, tenantId));
        erpIntegrationStreamExcelVo.setThirdPartyObjectApiName(i18NStringManager.get(I18NStringEnum.s3783, lang, tenantId));
        erpIntegrationStreamExcelVo.setThirdPartyFieldType("text");
        erpIntegrationStreamExcelVo.setThirdPartyFieldRequired(false);
        erpIntegrationStreamExcelVos.add(erpIntegrationStreamExcelVo);
        ErpIntegrationStreamExcelVo erpIntegrationStreamExcelVo2 = new ErpIntegrationStreamExcelVo();
        erpIntegrationStreamExcelVo2.setCrmObjectLabel(i18NStringManager.get(I18NStringEnum.s3784, lang, tenantId));
        erpIntegrationStreamExcelVo2.setCrmObjectApiName(i18NStringManager.get(I18NStringEnum.s3785, lang, tenantId));
        erpIntegrationStreamExcelVo2.setThirdPartyObjectLabel(i18NStringManager.get(I18NStringEnum.s3784, lang, tenantId));
        erpIntegrationStreamExcelVo2.setThirdPartyObjectApiName(i18NStringManager.get(I18NStringEnum.s3785, lang, tenantId));
        erpIntegrationStreamExcelVo2.setThirdPartyFieldType("text");
        erpIntegrationStreamExcelVo2.setThirdPartyFieldRequired(false);
        erpIntegrationStreamExcelVos.add(erpIntegrationStreamExcelVo2);
        return erpIntegrationStreamExcelVos;
    }

    @Override
    public Result<ImportIntegrationStreamMapping.Result> batchImportErpIntegrationStreams(ImportIntegrationStreamMapping.Arg arg, String lang) {
        //测试耗时
        if (ConfigCenter.isCostTimes) {
            try {
                Thread.sleep(1000 * 10L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        //解析npath，获取stream
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDataCenterId());
        try (InputStream inputStream = stoneFileManager.downloadByPath(arg.getTenantId(), arg.getNpath(), com.alibaba.excel.support.ExcelTypeEnum.XLSX.getValue())) {
            //解析
//            ReadExcel.Arg<ErpIntegrationStreamExcelVo> excelVoArg = new ReadExcel.Arg<>();
//            IntegrationStreamMultiSheetListener listen = new IntegrationStreamMultiSheetListener(arg.getTenantId(),
//                    connectInfo.getChannel(),
//                    arg.getDataCenterId(),
//                    i18NStringManager,
//                    lang,
//                    erpObjectDao,
//                    erpObjectRelationshipDao,
//                    arg.getUserId(),
//                    erpObjectFieldsService,
//                    erpConnectInfoDao,
//                    integrationStreamService,
//                    adminNodeSettingService,
//                    erpObjectService);
//
//            excelVoArg.setExcelListener(listen);
//            excelVoArg.setType(ErpIntegrationStreamExcelVo.class);
//            excelVoArg.setInputStream(inputStream);
//            fileManager.readExcelByAll(excelVoArg);
//
//            //获取解析结果

            final ExcelReader build = EasyExcelFactory.read(inputStream).excelType(com.alibaba.excel.support.ExcelTypeEnum.XLSX).build();
            List<ReadSheet> sheetList = build.excelExecutor().sheetList();

            List<Pair<String, IntegrationStreamMultiSheetListener>> collect = sheetList.stream()
                    .map(readSheet -> {
                        final String sheetName = readSheet.getSheetName();
                        if (StringUtils.isEmpty(sheetName) || "hideSheet".equalsIgnoreCase(sheetName)) {
                            return null;
                        }
                        readSheet.setClazz(ErpIntegrationStreamExcelVo.class);
                        final IntegrationStreamMultiSheetListener listener = new IntegrationStreamMultiSheetListener(arg.getTenantId(),
                                connectInfo.getChannel(),
                                arg.getDataCenterId(),
                                i18NStringManager,
                                lang,
                                erpObjectDao,
                                erpObjectRelationshipDao,
                                arg.getUserId(),
                                erpObjectFieldsService,
                                erpConnectInfoDao,
                                integrationStreamService,
                                adminNodeSettingService,
                                erpObjectService);
                        readSheet.setCustomReadListenerList(Collections.singletonList(listener));
                        return Pair.of(sheetName, listener);
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            build.readAll();

            final List<ImportIntegrationStreamMapping.SheetResult> sheetResults = collect.stream()
                    .map(pair -> new ImportIntegrationStreamMapping.SheetResult(pair.getKey(), pair.getValue().getImportResult()))
                    .collect(Collectors.toList());

            final String collectResult = sheetResults.stream()
                    .map(sheetResult -> sheetResult.getSheetName() + i18NStringManager.get2(I18NStringEnum.s225, lang, arg.getTenantId(), sheetResult.getResult().getPrintMsg()))
                    .map(msg -> msg.replace("\n", "\n\t"))
                    .collect(Collectors.joining("\n"));

            return Result.newSuccess(new ImportIntegrationStreamMapping.Result(sheetResults, collectResult));
        } catch (Exception e) {
            log.error("batchImportErpIntegrationStreams error, tenantId:{} npath:{}", arg.getTenantId(), arg.getNpath(), e);
            return Result.newError(i18NStringManager.get(I18NStringEnum.s2072, lang, arg.getTenantId()) + e.getMessage());
        }
    }
}
