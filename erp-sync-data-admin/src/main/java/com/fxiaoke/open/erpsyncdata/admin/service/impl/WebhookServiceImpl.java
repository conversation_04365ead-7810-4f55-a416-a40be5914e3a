package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.admin.service.WebhookService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.Webhook;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
public class WebhookServiceImpl implements WebhookService {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;


    @Override
    public Result<Webhook.Result> webhook(String tenantId, String dcId, Webhook.Arg arg) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if (connectInfo == null) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_CONNECTOR);
        }
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
        Result<Webhook.Result> result = erpDataManager.webhook(arg, connectInfo);
        //数据处理
        if (!result.isSuccess()) {
            return result.error();
        }
        Webhook.Result webhookRes = result.getData();
        if (webhookRes.getDataMap() != null) {
            webhookRes.getDataMap().forEach(((eventType, dataList) -> {
                erpObjDataPushManager.push2TempAsync(tenantId, dcId, eventType, dataList);
            }));
        }
        return Result.newSuccess(webhookRes);
    }
}
