package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.model.RedoSyncRecord;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingGetDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingListByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingNumByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

public interface AdminSyncDataMappingService {

    Result<SyncDataMappingGetDetailResult> getDetail(String tenantId, String id, String syncDataId);

    Result<SyncDataMappingGetDetailResult> getSyncDataDetail(String tenantId, String dcId, String id, String syncDataId);

    Result<SyncDataMappingListByPloyDetailIdResult> listByPloyDetailId(String tenantId,SyncDataMappingListByPloyDetailIdArg detailIdArg,String lang);

    /**
     * 重新拉取最新数据走一遍mq
     * @param tenantId
     * @param ids
     * @return
     */
    Result<Void> redoSyncData(String tenantId, List<String> ids,String sourceObjectApiName,String sourceDataId,String ployDetailId);

    /**
     * 重新拉取最新数据走一遍mq
     * erp->crm
     * @param erpIdArg
     * @return
     */
    Result<Void> syncSingletonData(ErpIdArg erpIdArg,String destObjApiName);

    Result<Void> deleteBySyncDataMappingIds(Integer tenantId, String dcId, Integer userId, List<String> ids, String ployDetailId, String lang);

    Result<String> updateSyncDataMappingBySourceDataId(Integer tenantId,SyncDataMappingResult arg,String lang);

    Result<String> updateSyncDataMapping2(String tenantId,SyncDataMappingResult arg,String lang);

    Result<SyncDataMappingsEntity> createSyncDataMapping(Integer tenantId, CreateSyncDataMappingArg arg,String lang);

    Result<Void> adminCreateSyncDataMapping(String tenantId, Integer userId, String dcId, String ployDetailId, String sourceObjectApiName, String sourceDataId, String sourceDataName, String destObjectApiName, String destDataId, String destDataName, String masterDataId, String lang);

    Result<Void> adminUpdateSyncDataMapping(String tenantId, Integer userId, String dcId, String ployDetailId, String dataId, String sourceDataId, String sourceDataName, String destDataId, String destDataName, String masterDataId, String lang);

    Result<Map<String,SyncDataMappingResult>> getSyncDataMappingBySourceDataIdOrDestDataId(Integer tenantId, QuerySyncDataMappingArg arg,String lang);

    Result<Void> deleteSyncDataAndSyncDataMapping(DeleteSyncDataAndSyncDataMappingArg arg);

    Result<Void> deleteSyncDataMappingByPloyDetailId(Integer tenantId,
                                                     String dcId,
                                                     Integer userId,
                                                     String phone,
                                                     DeleteMappingByPloyDetailIdArg arg,
                                                     String lang);

    Result<RedoSyncRecord> redoSyncDataByPloyDetailId(String tenantId, String dcId, SyncDataMappingListByPloyDetailIdArg arg, String lang);

    Result<List<Result>> initDataMapping(String tenantId,InitDataMappingArg arg,String lang);

    Result<SyncDataMappingNumByPloyDetailIdResult> getSyncDataMappingNumByPloyDetailId(String tenantId,
                                                                                       String dataCenterId,
                                                                                       Integer userId,
                                                                                       String taskId,
                                                                                       SyncDataMappingListByPloyDetailIdArg arg,
                                                                                       String lang);

    Result<SyncDataMappingNumByPloyDetailIdResult> getTotalByTaskId(String tenantId, String dataCenterId, Integer userId, String taskId);
}
