package com.fxiaoke.open.erpsyncdata.admin.preset;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PresetSalesOrderObjectCRM2ERP extends AbstractPresetObject {
    @Override
    protected List<String> getFormId() {
        return Lists.newArrayList(K3CloudForm.SAL_SaleOrder);
    }

    @Override
    public List<String> getFieldMappingJson() {
        String json = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"SalesOrderObj\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"fieldMappings\":[{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"编辑了订单\",\"optionMappings\":[],\"destApiName\":\"FChangeReason\",\"destType\":\"text\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1\",\"optionMappings\":[],\"destApiName\":\"FSaleOrderFinance.FExchangeRate\",\"destType\":\"number\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"XSDD01_SYS\",\"optionMappings\":[],\"destApiName\":\"FBillTypeID.FNumber\",\"destType\":\"select_one\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"AccountObj\",\"destApiName\":\"FCustId.FNumber\",\"destType\":\"object_reference\",\"destTargetApiName\":\"BD_Customer.BillHead\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":2,\"value\":\"owner\",\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceType\":\"employee\",\"destApiName\":\"FSalerId.FNumber\",\"destType\":\"employee\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_f4801__c\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"ErpOrganizationObj\",\"destApiName\":\"FSaleOrgId.FNumber\",\"destType\":\"object_reference\",\"destTargetApiName\":\"ORG_Organizations.BillHead\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_time\",\"sourceType\":\"date\",\"destApiName\":\"FDate\",\"destType\":\"date\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceType\":\"long_text\",\"destApiName\":\"FNote\",\"destType\":\"long_text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_amount\",\"sourceType\":\"currency\",\"destApiName\":\"FSaleOrderFinance.FBillAllAmount\",\"destType\":\"currency\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"data_own_department\",\"sourceType\":\"department\",\"destApiName\":\"FSaleDeptId.FNumber\",\"destType\":\"department\"}]},\"detailObjectMappings\":[{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldMappings\":[{\"mappingType\":1,\"sourceApiName\":\"order_id\",\"sourceType\":\"master_detail\",\"sourceTargetApiName\":\"SalesOrderObj\",\"destApiName\":\"fake_master_detail\",\"destType\":\"master_detail\",\"destTargetApiName\":\"SAL_SaleOrder.BillHead\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"Pcs\",\"optionMappings\":[],\"destApiName\":\"FUnitID.FNumber\",\"destType\":\"select_one\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"product_id\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"ProductObj\",\"destApiName\":\"FMaterialId.FNumber\",\"destType\":\"object_reference\",\"destTargetApiName\":\"BD_MATERIAL.BillHead\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"quantity\",\"sourceType\":\"number\",\"destApiName\":\"FQty\",\"destType\":\"number\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"sales_price\",\"sourceType\":\"currency\",\"destApiName\":\"FTaxPrice\",\"destType\":\"currency\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceType\":\"long_text\",\"destApiName\":\"FEntryNote\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"subtotal\",\"sourceType\":\"number\",\"destApiName\":\"FAllAmount\",\"destType\":\"currency\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"100\",\"optionMappings\":[],\"destApiName\":\"FDiscountRate\",\"destType\":\"text\"}]}]}";   // ignoreI18n
        return Lists.newArrayList(json);
    }


    @Override
    public List<SyncPloyDetailCreateArg> getSyncPloyDetailCreateArg() {
        SyncPloyDetailCreateArg arg = new SyncPloyDetailCreateArg();
        arg.setPloyId(tenantId);
        arg.setSourceTenantIds(Lists.newArrayList(tenantId));
        arg.setSourceTenantType(TenantType.CRM);
        arg.setSourceObjectApiName("SalesOrderObj");
        arg.setDestTenantIds(Lists.newArrayList(tenantId));
        arg.setDestTenantType(TenantType.ERP);
        arg.setDestObjectApiName("SAL_SaleOrder.BillHead");

        SyncPloyDetailCreateArg.DetailObjectMappingCreateArg detailObjectMappingCreateArg
                = new SyncPloyDetailCreateArg.DetailObjectMappingCreateArg();
        detailObjectMappingCreateArg.setSourceObjectApiName("SalesOrderProductObj");
        detailObjectMappingCreateArg.setDestObjectApiName("SAL_SaleOrder.SaleOrderEntry");
        arg.setDetailObjectMappings(Lists.newArrayList(
                detailObjectMappingCreateArg
        ));
        arg.setDcId(dataCenterId);
        return Lists.newArrayList(arg);
    }
}
