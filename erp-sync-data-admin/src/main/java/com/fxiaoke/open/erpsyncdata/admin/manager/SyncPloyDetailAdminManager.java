package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.admin.data.ObjectMappingData;
import com.fxiaoke.open.erpsyncdata.admin.remote.ErRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.common.constant.CheckAndUpdatePloyValidErrorTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionIllegalOperateTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailValidEnum;
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailQueryObject2SyncDataMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExtraEventTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionResult;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.REDIS_KEY_EXCEPTION_PLOY_DETAIL;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/6.
 */
@Component
@Slf4j
public class SyncPloyDetailAdminManager {
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private CustomFunctionService customFunctionService;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErRemoteManager erRemoteManager;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private PloyBreakManager ployBreakManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    public Set<CheckAndUpdatePloyValidStatusDetailData> checkAndUpdatePloyValidStatus(String tenantId, String id,String lang) {

        Result<SyncPloyDetailResult> syncPloyDetailResultResult = adminSyncPloyDetailService.getById(tenantId, id, lang);
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        SyncPloyDetailResult syncPloyDetailResult = syncPloyDetailResultResult.getData();
        if (syncPloyDetailResult == null) {
            throw new ErpSyncDataException(i18NStringManager.get(I18NStringEnum.s175,lang,tenantId), null, null);
        }

        return checkAndUpdatePloyValidStatus(tenantId, syncPloyDetailEntity,lang, Boolean.FALSE);
    }

    @NotNull
    public Set<CheckAndUpdatePloyValidStatusDetailData> checkAndUpdatePloyValidStatus(String tenantId, SyncPloyDetailEntity syncPloyDetailEntity, String lang, Boolean updateInEnabled) {
        Set<CheckAndUpdatePloyValidStatusDetailData> allCheckData = Sets.newHashSet();
        String id = syncPloyDetailEntity.getId();
        SyncPloyDetailResult syncPloyDetailResult = SyncPloyDetailResult.buildSyncPloyDetailResultByEntity(i18NStringManager,lang,tenantId,syncPloyDetailEntity);

        if (syncPloyDetailEntity.getIntegrationStreamNodes() != null) {
            //检查回写crm组件
            if (syncPloyDetailEntity.getIntegrationStreamNodes().getReverseWriteNode() != null) {
                List<CheckAndUpdatePloyValidStatusDetailData> reverseWriteResult = this.verifyReverseWrite(tenantId, syncPloyDetailEntity,lang);
                if (CollectionUtils.isNotEmpty(reverseWriteResult)) {
                    allCheckData.addAll(reverseWriteResult);
                }
            }
            if (syncPloyDetailEntity.getIntegrationStreamNodes().getCheckSyncDataMappingNode() != null) {
                List<CheckAndUpdatePloyValidStatusDetailData> checkSyncDataMappingNodeResult = this.verifyCheckSyncDataMappingNode(tenantId, syncPloyDetailEntity,lang);
                if (CollectionUtils.isNotEmpty(checkSyncDataMappingNodeResult)) {
                    allCheckData.addAll(checkSyncDataMappingNodeResult);
                }
            }
        }
        List<ObjectMappingData> objectMappingDataList = Lists.newArrayList();
        //将主从的对象塞到list
        objectMappingDataList.add(
                ObjectMappingData.newData(syncPloyDetailResult.getSourceObjectApiName(),
                        syncPloyDetailResult.getDestObjectApiName(),
                        syncPloyDetailResult.getFieldMappings()));
        //校验主数据错误数量
//        List<CheckAndUpdatePloyValidStatusDetailData> checkFailedMapping=this.checkIntegrationStreamFailedMapping(tenantId,syncPloyDetailEntity);
//        allCheckData.addAll(checkFailedMapping);
        objectMappingDataList.addAll(syncPloyDetailResult.getDetailObjectMappings().stream()
                .map(val -> ObjectMappingData.newData(val.getSourceObjectApiName(), val.getDestObjectApiName(), val.getFieldMappings())).collect(Collectors.toList()));
        //检查字段映射是否为空
        objectMappingDataList.stream()
                .filter(v -> CollectionUtils.isEmpty(v.getFieldMappings()))
                .forEach(objectMappingData -> {
                    CheckAndUpdatePloyValidStatusDetailData data = CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.MAPPING_NOT_EXISTS.getType(),
                            CheckAndUpdatePloyValidErrorTypeEnum.MAPPING_NOT_EXISTS.indexedFormat(objectMappingData.getSourceObjectApiName(), objectMappingData.getDestObjectApiName()));
                    allCheckData.add(data);
                });
        //同步规则是否为空
        if (syncPloyDetailEntity.getSyncRules() == null) {
            CheckAndUpdatePloyValidStatusDetailData data = CheckAndUpdatePloyValidStatusDetailData
                    .newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.SYNC_RULES_NOT_EXISTS.getType(), CheckAndUpdatePloyValidErrorTypeEnum.SYNC_RULES_NOT_EXISTS.getNameByTraceLocale());
            allCheckData.add(data);
        }
        //校验函数
        List<CheckAndUpdatePloyValidStatusDetailData> checkBeforeFunctionCheckAndUpdatePloyValidStatusDetailDatas = this.checkFunction(tenantId, syncPloyDetailResult.getBeforeFuncApiName(), syncPloyDetailResult.getFieldMappings(),lang);
        List<CheckAndUpdatePloyValidStatusDetailData> checkDuringFunctionCheckAndUpdatePloyValidStatusDetailDatas = this.checkFunction(tenantId, syncPloyDetailResult.getDuringFuncApiName(), syncPloyDetailResult.getFieldMappings(),lang);
        List<CheckAndUpdatePloyValidStatusDetailData> checkAfterFunctionCheckAndUpdatePloyValidStatusDetailDatas = this.checkFunction(tenantId, syncPloyDetailResult.getAfterFuncApiName(), syncPloyDetailResult.getFieldMappings(),lang);
        allCheckData.addAll(checkBeforeFunctionCheckAndUpdatePloyValidStatusDetailDatas);
        allCheckData.addAll(checkDuringFunctionCheckAndUpdatePloyValidStatusDetailDatas);
        allCheckData.addAll(checkAfterFunctionCheckAndUpdatePloyValidStatusDetailDatas);

        // 启用状态修改集成流,不校验熔断信息
        if (!updateInEnabled) {
            //熔断异常，每次获取后就会删除
            String key = String.format(REDIS_KEY_EXCEPTION_PLOY_DETAIL, tenantId, id);
            String exceptionMsgCache = redisCacheManager.getCache(key, this.getClass().getSimpleName());
            if (StringUtils.isNotBlank(exceptionMsgCache)) {
                CheckAndUpdatePloyValidStatusDetailData ployBreak = CheckAndUpdatePloyValidStatusDetailData.newData(tenantId,
                        CheckAndUpdatePloyValidErrorTypeEnum.PLOY_BREAK_BY_SYSTEM.getType(),
                        CheckAndUpdatePloyValidErrorTypeEnum.PLOY_BREAK_BY_SYSTEM.indexedFormat(exceptionMsgCache));
                allCheckData.add(ployBreak);
            }
        }
        if (CollectionUtils.isNotEmpty(allCheckData)) {
            adminSyncPloyDetailService.updateValid(tenantId, id, SyncPloyDetailValidEnum.IN_VALID.getValid());
        } else {
            adminSyncPloyDetailService.updateValid(tenantId, id, SyncPloyDetailValidEnum.VALID.getValid());
        }
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenantId));
        allCheckData.forEach(val -> {
            if (StringUtils.isNotEmpty(val.getTenantData().getTenantId())) {
                val.getTenantData().setTenantName(tenantIdToNameMap.get(tenantId));
            }
        });
        return allCheckData;
    }

    /**
     * 检查id映射组件
     * 当id映射组件有数据时
     * 检查id映射组件的从对象是否全部都配置了
     * erp -> crm ,id 映射组件的源对象为crm,目标对象为erp
     * 所以 集成流的源从对象 - id组件的从对象的目标对象
     */
    private List<CheckAndUpdatePloyValidStatusDetailData> verifyCheckSyncDataMappingNode(String tenantId,
                                                                                         SyncPloyDetailEntity syncPloyDetailEntity,
                                                                                         String lang) {
        List<CheckAndUpdatePloyValidStatusDetailData> results = Lists.newArrayList();
        IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = syncPloyDetailEntity.getIntegrationStreamNodes().getCheckSyncDataMappingNode();
        if (CollectionUtils.isNotEmpty(syncPloyDetailEntity.getDetailObjectMappings()) && checkSyncDataMappingNode.getQueryObjectMappingData() != null
                && StringUtils.isNotBlank(checkSyncDataMappingNode.getQueryObjectMappingData().getSourceObjectApiName())) {//有从对象映射,有中间表映射组件
            List<String> detailObjApiNames = Lists.newArrayList();
            for (DetailObjectMappingsData.DetailObjectMappingData data : syncPloyDetailEntity.getDetailObjectMappings()) {
                detailObjApiNames.add(data.getSourceObjectApiName());
            }
            if (checkSyncDataMappingNode.getDetailCheckSyncDataMappingData() != null) {
                for (DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData data : checkSyncDataMappingNode.getDetailCheckSyncDataMappingData()) {
                    if (data.getQueryObjectMappingData() != null && data.getQueryObjectMappingData().getDestObjectApiName() != null) {
                        detailObjApiNames.remove(data.getQueryObjectMappingData().getDestObjectApiName());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(detailObjApiNames)) {
                String msg = i18NStringManager.get2(I18NStringEnum.s811.getI18nKey(),
                        lang,
                        tenantId,
                        String.format(I18NStringEnum.s811.getI18nValue(), detailObjApiNames),
                        Lists.newArrayList(detailObjApiNames));

                CheckAndUpdatePloyValidStatusDetailData result = CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, 2, msg);
                results.add(result);
            }
        }
        return results;
    }

    private List<CheckAndUpdatePloyValidStatusDetailData> verifyReverseWrite(String tenantId, SyncPloyDetailEntity entity,String lang) {
        List<CheckAndUpdatePloyValidStatusDetailData> fieldResult = Lists.newArrayList();
        //主对象反写字段
        if (CollectionUtils.isNotEmpty(entity.getIntegrationStreamNodes().getReverseWriteNode().getFieldMappings())) {
            //主对象，已设置字段同步映射的源对象字段
            List<String> masterObjFieldApiNames = entity.getFieldMappings().stream().map(com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData::getSourceApiName).collect(Collectors.toList());
            List<String> fields = Lists.newArrayList();
            for (com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData fieldMappingData : entity.getIntegrationStreamNodes().getReverseWriteNode().getFieldMappings()) {
                if (masterObjFieldApiNames.contains(fieldMappingData.getDestApiName())) {
                    fields.add(fieldMappingData.getDestApiName());
                }
            }
            if (CollectionUtils.isNotEmpty(fields)) {
                String msg = i18NStringManager.get2(I18NStringEnum.s812.getI18nKey(),
                        lang,
                        tenantId,
                        String.format(I18NStringEnum.s812.getI18nValue(), entity.getIntegrationStreamNodes().getReverseWriteNode().getDestObjectApiName(),fields.toString()),
                        Lists.newArrayList(entity.getIntegrationStreamNodes().getReverseWriteNode().getDestObjectApiName(),fields.toString()));

                CheckAndUpdatePloyValidStatusDetailData result = CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, 2, msg);
                fieldResult.add(result);
            }
        }
        //明细对象反写字段
        if (CollectionUtils.isNotEmpty(entity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings())) {
            //明细对象，已设置字段同步映射的源对象字段
            Map<String, List<String>> detailObjFieldApiNames = Maps.newHashMap();
            for (DetailObjectMappingsData.DetailObjectMappingData detail : entity.getDetailObjectMappings()) {
                if (CollectionUtils.isNotEmpty(detail.getFieldMappings())) {
                    List<String> fieldApiNameList = detail.getFieldMappings().stream().map(com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData::getSourceApiName).collect(Collectors.toList());
                    detailObjFieldApiNames.put(detail.getSourceObjectApiName(), fieldApiNameList);
                }
            }
            for (DetailObjectMappingsData.DetailObjectMappingData detail : entity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings()) {
                List<String> fields = Lists.newArrayList();
                for (com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData fieldMappingData : detail.getFieldMappings()) {
                    if (detailObjFieldApiNames.get(detail.getDestObjectApiName()).contains(fieldMappingData.getDestApiName())) {
                        fields.add(fieldMappingData.getDestApiName());
                    }
                }
                if (CollectionUtils.isNotEmpty(fields)) {
                    String msg = i18NStringManager.get2(I18NStringEnum.s812.getI18nKey(),
                            lang,
                            tenantId,
                            String.format(I18NStringEnum.s812.getI18nValue(), detail.getDestObjectApiName(),fields.toString()),
                            Lists.newArrayList(detail.getDestObjectApiName(),fields.toString()));

                    CheckAndUpdatePloyValidStatusDetailData result = CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, 2, msg);
                    fieldResult.add(result);
                }
            }
        }

        return fieldResult;
    }


    public Result<Void> checkQueryInterfaceStatus(String tenantId, String ployDetailId,String lang) {
        List<SyncPloyDetailSnapshotEntity> snapshotEntities = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listEnableSnapshotsBySyncPloyDetailsId(tenantId, ployDetailId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(snapshotEntities)) {
            return Result.newSuccess();
        }
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = snapshotEntities.get(0);
        final String snapshotEntityId = syncPloyDetailSnapshotEntity.getId();
        SyncPloyDetailEntity ployDetailData = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, ployDetailId);
        return checkQueryInterfaceStatus(tenantId, snapshotEntityId, ployDetailData,lang);
    }

    @NotNull
    public Result<Void> checkQueryInterfaceStatus(String tenantId, String snapshotEntityId, SyncPloyDetailEntity ployDetailData,String lang) {
        //源为erp，方式为轮询
        Result<Void> result = Result.newSuccess();
        if (TenantType.ERP.equals(ployDetailData.getSourceTenantType())
                && ployDetailData.getSyncRules() != null
                && ployDetailData.getSyncRules().getSyncTypeList() != null
                && ployDetailData.getSyncRules().getSyncTypeList().contains(SyncTypeEnum.get.name())) {
            List<Integer> events = ployDetailData.getSyncRules().getEvents();
            TimeFilterArg timeFilterArg = new TimeFilterArg();
            timeFilterArg.setTenantId(tenantId);
            timeFilterArg.setObjAPIName(ployDetailData.getSourceObjectApiName());
            //一天前
            timeFilterArg.setStartTime(System.currentTimeMillis() - 1000 * 60 * 60 * 24L);
            timeFilterArg.setEndTime(System.currentTimeMillis());
            timeFilterArg.setSnapshotId(snapshotEntityId);
            timeFilterArg.setOffset(0);
            timeFilterArg.setLimit(1);
            if (events.contains(ExtraEventTypeEnum.ADD.getExtraType()) ||
                    events.contains(ExtraEventTypeEnum.UPDATE.getExtraType())) {
                //轮询正常数据
                timeFilterArg.setOperationType(ExtraEventTypeEnum.UPDATE.getExtraType());
                Result<ListErpObjDataResult> listErpObjDataResultResult = erpDataPreprocessService.listErpObjDataByTime(timeFilterArg);
                if (!listErpObjDataResultResult.isSuccess()) {
                    String message = i18NStringManager.get(I18NStringEnum.s813,lang,tenantId) + i18NStringManager.get2(listErpObjDataResultResult.getI18nKey(),lang,tenantId,listErpObjDataResultResult.getErrMsg(),listErpObjDataResultResult.getI18nExtra());
                    result = Result.newError(message);
                }
            }
            if (events.contains(ExtraEventTypeEnum.INVALID.getExtraType())) {
                //轮询作废数据
                timeFilterArg.setOperationType(ExtraEventTypeEnum.INVALID.getExtraType());
                Result<ListErpObjDataResult> listErpObjDataResultResult = erpDataPreprocessService.listErpObjDataByTime(timeFilterArg);
                if (!listErpObjDataResultResult.isSuccess()) {
                    String message = i18NStringManager.get(I18NStringEnum.s814,lang,tenantId) + i18NStringManager.get2(listErpObjDataResultResult.getI18nKey(),lang,tenantId,listErpObjDataResultResult.getErrMsg(),listErpObjDataResultResult.getI18nExtra());
                    result = Result.newError(message);
                }
            }
        }

        if (!result.isSuccess()) {
            String key = String.format(REDIS_KEY_EXCEPTION_PLOY_DETAIL, tenantId, ployDetailData.getId());
            boolean setCache = redisCacheManager.setCache(key, result.getErrMsg(), TimeUnit.DAYS.toSeconds(1L), this.getClass().getSimpleName());
            log.info("list erp obj data by time failed,result:{},{}", result, setCache);
        }
        return result;
    }

    /**
     * 获取对应企业校验自定义函数的结果集合
     * <p>
     * 同步后函数校验。
     * 背景： 同步后函数，如果用户通过自定义函数改动CRM上的对接字段，
     * 可能会引起 CRM< -  >ERP 循环同步。
     * <p>
     * 所以这里要检查，不能update 对接字段。
     * 当前没有通过语法分析去检查 update了"对接字段"，而是检查update和"对接字段"同时存在，所以当前的实现可能存在误伤的情况。
     * 去除校验
     *
     * @param tenantId    企业id
     * @param funcApiName 自定义函数apiName
     */
    public List<CheckAndUpdatePloyValidStatusDetailData> checkFunction(String tenantId, String funcApiName, List<FieldMappingData> fieldMappings,String lang) {
        List<CheckAndUpdatePloyValidStatusDetailData> datas = Lists.newArrayList();
        if (StringUtils.isBlank(funcApiName)) {
            return datas;
        }
        FunctionServiceFindArg functionServiceFindArg = new FunctionServiceFindArg();
        functionServiceFindArg.setApiName(funcApiName);
        functionServiceFindArg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), SuperUserConstants.USER_ID);
        Result2<FunctionResult<FunctionServiceFindResult>> findResult = null;
        try {
            findResult = customFunctionService.find(headerObj, functionServiceFindArg);
        } catch (Exception e) {
            log.warn("checkFunction customFunctionService.find ,find is exection, funcApiName={}, e={},", funcApiName, e.getMessage(), e);
        }
        if (findResult == null || findResult.getData() == null || findResult.getData().getResult() == null || findResult.getData().getResult().getFunction() == null) {
            return datas;
        }
        String oldFuncBody = findResult.getData().getResult().getFunction().getBody();
        String funcBody = new String();
        if (oldFuncBody != null) {//去掉换行和空格
            funcBody = oldFuncBody.replaceAll("\n", "").replaceAll(" ", "");
        }
        Boolean isContainOperate = false;
        String operateTypeStr = "";
        String operateFieldStr = "";
        for (CustomFunctionIllegalOperateTypeEnum operateType : CustomFunctionIllegalOperateTypeEnum.values()) {
            if (CustomFunctionIllegalOperateTypeEnum.REMOVE.name().equals(operateType.name())) {//remove操作合法
                continue;
            }
            if (funcBody != null && funcBody.contains(operateType.getType())) {
                operateTypeStr = operateType.getType();
                isContainOperate = true;
                break;
            }
        }
        if (isContainOperate) {
            isContainOperate = false;
            for (FieldMappingData fieldMappingData : fieldMappings) {
                if (funcBody != null && fieldMappingData.getSourceApiName() != null && funcBody.contains(fieldMappingData.getSourceApiName())) {
                    operateFieldStr = fieldMappingData.getSourceApiName();
                    isContainOperate = true;
                    break;
                }
            }
        }
        if (isContainOperate) {
            CheckAndUpdatePloyValidStatusDetailData data = CheckAndUpdatePloyValidStatusDetailData
                    .newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.FUNCTION_NOT_VALID.getType(), CheckAndUpdatePloyValidErrorTypeEnum.FUNCTION_NOT_VALID.indexedFormat(funcApiName));
            data.setErrorMessage(data.getErrorMessage() + i18NStringManager.get2(I18NStringEnum.s815.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s815.getI18nValue(), operateTypeStr,operateFieldStr),
                    Lists.newArrayList(operateTypeStr,operateFieldStr)));
            log.info("contains update and mapping field,data:{}", data);

            //去除校验
//            datas.add(data);
        }
        return datas;
    }


    /**
     * 根据CRM对象查询ERP真实对象
     * 有缓存
     *
     * 错误用法，不兼容多数据中心，请不要在新代码使用！
     *
     * @param tenantId
     * @param crmObjApiName
     * @return
     */
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    @Deprecated
    public String getErpRealObjApiNameByCrmObjApiName(String tenantId, String crmObjApiName) {
        //erp->crm
        List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByDestTenantTypeAndObjApiName(tenantId, TenantType.CRM, crmObjApiName);
        if (CollectionUtils.isNotEmpty(ployDetailEntities)) {
            String fakeProductObjApiName = ployDetailEntities.get(0).getSourceObjectApiName();
            ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, fakeProductObjApiName);
            return relation.getErpRealObjectApiname();
        }
        return null;
    }
}
