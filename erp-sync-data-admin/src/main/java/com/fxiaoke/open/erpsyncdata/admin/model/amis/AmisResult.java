package com.fxiaoke.open.erpsyncdata.admin.model.amis;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/8/1
 */
public class AmisResult extends Result<Dict> {
    private static final long serialVersionUID = 294372440321536243L;

    public AmisResult() {
        super(new Dict());
    }

    public void set(String key, Object value) {
        this.data.put(key, value);
    }

    public static AmisResult of(Object obj) {
        AmisResult result = new AmisResult();
        result.set("a", obj);
        return result;
    }

    public static AmisResult list(List<?> objs) {
        String a = Joiner.on("<br />").join(objs);
        return of(a);
    }



}
