package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.EmpFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ////////////////以下注释看起来是copy的，不准确。////////////////////////////////////////////////////////
 * 以纷享侧数据Id作为唯一id。导入数据按顺序执行。
 * 1. 如纷享数据Id已存在，ERP数据Id不为空则修改原映射关系。
 * 2. 如纷享数据Id已存在，ERP数据Id为空则删除记录。
 * 3. 如纷享数据Id为空，ERP数据Id不为空则删除已有数据
 * 4. 如纷享数据Id不为空，ERP数据Id如果已存在则报错
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/2
 */
@Slf4j
@Getter
public class EmpFieldDataMappingListener extends BaseListener<EmpFieldDataMappingExcelVo> {

    private final ErpFieldDataMappingDao erpFieldDataMappingDao;
    private final EmployeeMappingService employeeMappingService;

    private final ErpFieldTypeEnum datatype;

    private final String tenantId;

    private final String dataCenterId;

    private final String lang;

    private final I18NStringManager i18NStringManager;

    private final ErpChannelEnum channel;

    private final ImportExcelFile.Result importResult;

    public EmpFieldDataMappingListener(String tenantId,
                                       ErpChannelEnum channel,
                                       ErpFieldTypeEnum datatype,
                                       ErpFieldDataMappingDao erpFieldDataMappingDao,
                                       EmployeeMappingService employeeMappingService,
                                       String dataCenterId,
                                       String lang,
                                       I18NStringManager i18NStringManager) {
        this.erpFieldDataMappingDao = erpFieldDataMappingDao;
        this.employeeMappingService = employeeMappingService;
        this.datatype = datatype;
        this.tenantId = tenantId;
        this.channel = channel;
        this.dataCenterId = dataCenterId;
        this.importResult = new ImportExcelFile.Result();
        this.lang = lang;
        this.i18NStringManager = i18NStringManager;
    }

    @Override
    public void invoke(EmpFieldDataMappingExcelVo data, AnalysisContext context) {
        super.invoke(data, context);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();
        //根据FsId修改数据
        upsertDataByFsDataId(data, rowNo);
    }


    private void upsertDataByFsDataId(EmpFieldDataMappingExcelVo data, Integer rowNo) {
        if (StringUtils.isEmpty(data.getFsDataId())){
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s380,lang,tenantId));
            return;
        }

        if (StringUtils.isEmpty(data.getErpDataId())){
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s381,lang,tenantId));
            return;
        }
        List<ErpFieldDataMappingEntity> exitsErp = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNoSearch2(tenantId, dataCenterId, datatype, null, data.getErpDataId());
        if (!exitsErp.isEmpty()) {
            if (exitsErp.stream().anyMatch(v -> !data.getFsDataId().equals(v.getFsDataId()))) {
                importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s382,lang,tenantId));
                return;
            }
        }
        List<ErpFieldDataMappingEntity> exits = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNoSearch2(tenantId, dataCenterId, datatype, data.getFsDataId(), null);
        if (CollectionUtils.isEmpty(exits)) {
            //新增数据
            Long now = System.currentTimeMillis();
            ErpFieldDataMappingEntity entity = new ErpFieldDataMappingEntity();
            entity.setTenantId(tenantId);
            entity.setDataCenterId(dataCenterId);
            entity.setChannel(channel);
            entity.setDataType(datatype);
            entity.setFsDataId(data.getFsDataId());
            entity.setFsDataName(data.getFsDataName());
            entity.setErpDataId(data.getErpDataId());
            entity.setErpDataName(data.getErpDataName());
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            Result<String> result = employeeMappingService.updateEmpOrUserMapping(tenantId, -10000, entity,lang);
            if (!result.isSuccess()) {
                importResult.addImportError(rowNo, i18NStringManager.get2(I18NStringEnum.s383,
                        lang,
                        tenantId,
                        result.getErrMsg()));
            } else {
                importResult.incrInsert(1);
            }
        } else {
            //修改数据
            for (ErpFieldDataMappingEntity exit : exits) {
                exit.setErpDataId(data.getErpDataId());
                exit.setErpDataName(data.getErpDataName());
                exit.setFsDataName(data.getFsDataName());
                Result<String> result = employeeMappingService.updateEmpOrUserMapping(tenantId, -10000, exit,lang);
                if (!result.isSuccess()) {
                    importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s384,lang,tenantId));
                } else {
                    importResult.incrUpdate(1);
                }
            }

        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s772.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s772.getI18nValue(), importResult.getInvokedNum(), importResult.getInsertNum(), importResult.getDeleteNum(), importResult.getUpdateNum()),
                Lists.newArrayList(importResult.getInvokedNum()+"", importResult.getInsertNum()+"", importResult.getDeleteNum()+"", importResult.getUpdateNum()+""))).append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s773.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s773.getI18nValue(), importResult.getImportErrorRows().size()),
                Lists.newArrayList(importResult.getImportErrorRows().size()+""))).append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s775.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s775.getI18nValue(), importResult.getInvokeExceptionRows().size()),
                Lists.newArrayList(importResult.getInvokeExceptionRows().size()+""))).append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    /**
     * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
     * entire read will terminate.
     *
     * @param exception
     * @param context
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
