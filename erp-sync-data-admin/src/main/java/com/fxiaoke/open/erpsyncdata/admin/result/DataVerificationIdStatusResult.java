package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 18:00 2022/11/9
 * @Desc:
 */
@Data
@ApiModel
public class DataVerificationIdStatusResult implements Serializable {

    @ApiModelProperty(value = "唯一标识")
    private String id;

    @ApiModelProperty(value = "企业id")
    private String tenantId;

    @ApiModelProperty(value = "核对任务id")
    private String dataVerificationTaskId;

    @ApiModelProperty(value = "数据id")
    private String dataId;

    @ApiModelProperty(value = "日志id")
    private String syncLogId;

    @ApiModelProperty(value = "数据同步状态")
    private IdSyncStatus status;

    @ApiModelProperty(value = "数据状态原因")
    private String statusReason;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
