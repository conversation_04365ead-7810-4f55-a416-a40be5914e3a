package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.arg.IdListArg;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.AsyncGetTotalResult;
import com.fxiaoke.open.erpsyncdata.admin.service.CheckCodeService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpTempDataMonitorService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.TriggerPollingMongoManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ErpTempDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.DeleteErpTempDataMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetTempDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpHistoryTaskNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpTempDataMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpTempDataMonitorSimpleArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.RefreshTempDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 17:08 2021/8/11
 * @Desc:
 */
@Slf4j
@Service
public class ErpTempDataMonitorServiceImpl implements ErpTempDataMonitorService {
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Autowired
    private CheckCodeService checkCodeService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private TriggerPollingMongoManager triggerPollingMongoManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<QueryResult<List<ErpTempDataMonitorResult>>> queryTempDataMonitorList(String tenantId,
                                                                                        String dataCenterId,
                                                                                        Integer userId,
                                                                                        QueryErpTempDataMonitorArg arg,
                                                                                        String lang) {
        if (StringUtils.isBlank(arg.getErpFakeObjectApiName())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, arg.getErpFakeObjectApiName());
        if (relationshipEntity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        QueryResult<List<ErpTempDataMonitorResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        //查询所有策略的
        String ployDetailId = null;
        Integer tempDataSyncStatus = null;
        if (arg.getPloyDetailId() != null && arg.getTempDataSyncStatus() != null) {
            //筛选状态
            ployDetailId = arg.getPloyDetailId();
            tempDataSyncStatus = arg.getTempDataSyncStatus();
        }
        int count = erpTempDataManager.countErpTempDataLimit1000(tenantId, dataCenterId, relationshipEntity.getErpRealObjectApiname(),ployDetailId, arg.getStartTime(), arg.getEndTime(),
                arg.getSourceDataStatus(), tempDataSyncStatus, arg.getTaskNum(), arg.getIdOrNum(), arg.getSearchType(), arg.getPageNum(), arg.getPageSize());
        queryResult.setTotal(count);
        if (count == 0) {
            return Result.newSuccess(queryResult);
        }
        Result<List<ErpTempData>> erpTempDataList = erpTempDataManager.listErpTempData(tenantId, dataCenterId, relationshipEntity.getErpRealObjectApiname(),ployDetailId, arg.getStartTime(), arg.getEndTime(),
                arg.getSourceDataStatus(), arg.getTempDataSyncStatus(), arg.getTaskNum(), arg.getIdOrNum(), arg.getSearchType(), arg.getPageNum(), arg.getPageSize());
        if (erpTempDataList == null || !erpTempDataList.isSuccess() || CollectionUtils.isEmpty(erpTempDataList.getData())) {
            return Result.copy(erpTempDataList);
        }
        List<ErpTempDataMonitorResult> erpTempDataMonitorResults = Lists.newArrayList();
        for (ErpTempData erpTempData : erpTempDataList.getData()) {
            Boolean isLarge2M=false;
            if(erpTempData.getDataBody()!=null&&erpTempData.getDataBody().length()>1024*1024*2){
                isLarge2M=true;
            }
            erpTempData.setDataBody(null);
            ErpTempDataMonitorResult copy = BeanUtil.copy(erpTempData, ErpTempDataMonitorResult.class);
            copy.setDataBodyLarge2M(isLarge2M);
            copy.setId(erpTempData.getId().toString());
            if (erpTempData.getStatus() != null) {
                copy.setStatusDesc(ErpTempDataStatusEnum.getErpTempDataStatus(erpTempData.getStatus()).getDesc(i18NStringManager,lang,tenantId));
            }
            if (erpTempData.getSyncStatusMap() != null) {
                Map<String,Integer> syncStatus=erpTempData.getSyncStatusMap();
                if(syncStatus.get(arg.getPloyDetailId())!=null){
                    copy.setSyncStatus(syncStatus.get(arg.getPloyDetailId()));
                    copy.setSyncStatusDesc(ErpTempDataStatusEnum.getErpTempDataStatus(syncStatus.get(arg.getPloyDetailId())).getDesc(i18NStringManager,lang,tenantId));
                }else {
                    //未触发该策略
                    copy.setSyncStatus(ErpTempDataStatusEnum.STATUS_OTHER.getStatus());
                    copy.setSyncStatusDesc(i18NStringManager.get(I18NStringEnum.s396,lang,tenantId));
                }
            }
            erpTempDataMonitorResults.add(copy);
        }
        queryResult.setDataList(erpTempDataMonitorResults);
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<QueryResult<List<ErpTempDataMonitorSimpleMsgResult>>> queryAllTempDataMonitorList(String tenantId, String dataCenterId, Integer userId, QueryErpTempDataMonitorSimpleArg arg) {
        List<SyncPloyDetailEntity> detailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantIdAndDestTenantId(tenantId, tenantId);
        //筛除目标不是crm的策略明细
        detailEntities = detailEntities.stream().filter(entity -> TenantType.CRM.equals(entity.getDestTenantType())).collect(Collectors.toList());
        List<String> crmApiNames = Lists.newArrayList();

        for (SyncPloyDetailEntity entity : detailEntities) {
            if (TenantType.CRM.equals(entity.getDestTenantType())) {
                //2crm
                crmApiNames.add(entity.getDestObjectApiName());
                if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                        crmApiNames.add(detailObjectMappingData.getDestObjectApiName());
                    }
                }
            }
        }
        Map<String, String> crmObjApiNameToNameMap = crmRemoteManager.listObjectNamesByApiNames(tenantId, crmApiNames);
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        Map<String, String> erpObjApiName2NameMap = erpObjectEntities.stream().collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getErpObjectName));
        //筛除不是指定数据中心的策略明细
        detailEntities = detailEntities.stream().filter(entity -> erpObjApiName2NameMap.containsKey(entity.getSourceObjectApiName())).collect(Collectors.toList());
        List<ErpTempDataMonitorSimpleMsgResult> resultList = Lists.newArrayList();
        for (int i = 0; i < detailEntities.size(); i++) {
            if (i < (arg.getPageNum() - 1) * arg.getPageSize() || resultList.size() == arg.getPageSize()) {
                continue;
            }
            SyncPloyDetailEntity entity = detailEntities.get(i);
            ErpTempDataMonitorSimpleMsgResult monitorSimpleMsg = new ErpTempDataMonitorSimpleMsgResult();
            monitorSimpleMsg.setRemark(entity.getRemark());
            monitorSimpleMsg.setPloyDetailId(entity.getId());
            //2crm
            if (StringUtils.isNotBlank(arg.getObjectApiName())) {
                if (!arg.getObjectApiName().equals(entity.getDestObjectApiName())) {
                    continue;
                }
            }
            ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName crmMasterObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
            crmMasterObj.setObjApiName(entity.getDestObjectApiName());
            crmMasterObj.setObjName(crmObjApiNameToNameMap.get(entity.getDestObjectApiName()));
            monitorSimpleMsg.setCrmMasterObj(crmMasterObj);
            monitorSimpleMsg.getAllCrmObj().add(crmMasterObj);

            ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpMasterObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
            erpMasterObj.setObjApiName(entity.getSourceObjectApiName());
            erpMasterObj.setObjName(erpObjApiName2NameMap.get(entity.getSourceObjectApiName()));
            monitorSimpleMsg.setErpMasterObj(erpMasterObj);
            monitorSimpleMsg.getAllErpObj().add(erpMasterObj);

            ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getSourceObjectApiName());
            ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpRealObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
            erpRealObj.setObjApiName(relationshipEntity.getErpRealObjectApiname());
            erpRealObj.setObjName(erpObjApiName2NameMap.get(relationshipEntity.getErpRealObjectApiname()));
            monitorSimpleMsg.setErpRealObj(erpRealObj);

            monitorSimpleMsg.setSourceObj(monitorSimpleMsg.getErpRealObj());
            resultList.add(monitorSimpleMsg);
            if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                    ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName crmDetailObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                    crmDetailObj.setObjApiName(detailObjectMappingData.getDestObjectApiName());
                    crmDetailObj.setObjName(crmObjApiNameToNameMap.get(detailObjectMappingData.getDestObjectApiName()));
                    monitorSimpleMsg.getAllCrmObj().add(crmDetailObj);

                    ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpDetailObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                    erpDetailObj.setObjApiName(detailObjectMappingData.getSourceObjectApiName());
                    erpDetailObj.setObjName(erpObjApiName2NameMap.get(detailObjectMappingData.getSourceObjectApiName()));
                    monitorSimpleMsg.getAllErpObj().add(erpDetailObj);
                }
            }
        }
        QueryResult<List<ErpTempDataMonitorSimpleMsgResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        queryResult.setTotal(detailEntities.size());
        if (StringUtils.isNotBlank(arg.getObjectApiName())) {
            long count = detailEntities.stream().filter(entity -> arg.getObjectApiName().equals(entity.getDestObjectApiName())).count();
            queryResult.setTotal((int) count);
        }
        queryResult.setDataList(resultList);
        return Result.newSuccess(queryResult);
    }

    public Result<QueryResult<List<ErpTempDataMonitorSimpleMsgResult>>> queryAllObjList(String tenantId, String dataCenterId, Integer userId, QueryErpTempDataMonitorSimpleArg arg) {
        List<SyncPloyDetailEntity> detailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantIdAndDestTenantId(tenantId, tenantId);
        List<String> crmApiNames = Lists.newArrayList();
        for (SyncPloyDetailEntity entity : detailEntities) {
            if (TenantType.CRM.equals(entity.getDestTenantType())) {//2crm
                crmApiNames.add(entity.getDestObjectApiName());
                if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                        crmApiNames.add(detailObjectMappingData.getDestObjectApiName());
                    }
                }
            } else {//2erp
                crmApiNames.add(entity.getSourceObjectApiName());
                if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                        crmApiNames.add(detailObjectMappingData.getSourceObjectApiName());
                    }
                }
            }
        }
        Map<String, String> crmObjApiNameToNameMap = crmRemoteManager.listObjectNamesByApiNames(tenantId, crmApiNames);
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        Map<String, String> erpObjApiName2NameMap = erpObjectEntities.stream().collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getErpObjectName));
        List<ErpTempDataMonitorSimpleMsgResult> resultList = Lists.newArrayList();
        for (int i = 0; i < detailEntities.size(); i++) {
            if (i < (arg.getPageNum() - 1) * arg.getPageSize() || resultList.size() == arg.getPageSize()) {
                continue;
            }
            SyncPloyDetailEntity entity = detailEntities.get(i);
            ErpTempDataMonitorSimpleMsgResult monitorSimpleMsg = new ErpTempDataMonitorSimpleMsgResult();
            monitorSimpleMsg.setRemark(entity.getRemark());
            List<ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName> allErpObj = Lists.newArrayList();
            List<ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName> allCrmObj = Lists.newArrayList();
            if (TenantType.CRM.equals(entity.getDestTenantType())) {//2crm
                if (StringUtils.isNotBlank(arg.getObjectApiName())) {
                    if (!arg.getObjectApiName().equals(entity.getDestObjectApiName())) {
                        continue;
                    }
                }
                ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName crmMasterObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                crmMasterObj.setObjApiName(entity.getDestObjectApiName());
                crmMasterObj.setObjName(crmObjApiNameToNameMap.get(entity.getDestObjectApiName()));
                monitorSimpleMsg.setCrmMasterObj(crmMasterObj);
                monitorSimpleMsg.getAllCrmObj().add(crmMasterObj);

                ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpMasterObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                erpMasterObj.setObjApiName(entity.getSourceObjectApiName());
                erpMasterObj.setObjName(erpObjApiName2NameMap.get(entity.getSourceObjectApiName()));
                monitorSimpleMsg.setErpMasterObj(erpMasterObj);
                monitorSimpleMsg.getAllErpObj().add(erpMasterObj);

                ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getSourceObjectApiName());
                ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpRealObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                erpRealObj.setObjApiName(relationshipEntity.getErpRealObjectApiname());
                erpRealObj.setObjName(erpObjApiName2NameMap.get(relationshipEntity.getErpRealObjectApiname()));
                monitorSimpleMsg.setErpRealObj(erpRealObj);

                monitorSimpleMsg.setSourceObj(monitorSimpleMsg.getErpRealObj());
                resultList.add(monitorSimpleMsg);
                if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                        ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName crmDetailObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                        crmDetailObj.setObjApiName(detailObjectMappingData.getDestObjectApiName());
                        crmDetailObj.setObjName(crmObjApiNameToNameMap.get(detailObjectMappingData.getDestObjectApiName()));
                        monitorSimpleMsg.getAllCrmObj().add(crmDetailObj);

                        ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpDetailObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                        erpDetailObj.setObjApiName(detailObjectMappingData.getSourceObjectApiName());
                        erpDetailObj.setObjName(erpObjApiName2NameMap.get(detailObjectMappingData.getSourceObjectApiName()));
                        monitorSimpleMsg.getAllErpObj().add(erpDetailObj);
                    }
                }
            } else {//2erp
                if (StringUtils.isNotBlank(arg.getObjectApiName())) {
                    if (!arg.getObjectApiName().equals(entity.getSourceObjectApiName())) {
                        continue;
                    }
                }
                ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName crmMasterObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                crmMasterObj.setObjApiName(entity.getSourceObjectApiName());
                crmMasterObj.setObjName(crmObjApiNameToNameMap.get(entity.getSourceObjectApiName()));
                monitorSimpleMsg.setCrmMasterObj(crmMasterObj);
                monitorSimpleMsg.getAllCrmObj().add(crmMasterObj);

                ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpMasterObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                erpMasterObj.setObjApiName(entity.getDestObjectApiName());
                erpMasterObj.setObjName(erpObjApiName2NameMap.get(entity.getDestObjectApiName()));
                monitorSimpleMsg.setErpMasterObj(erpMasterObj);
                monitorSimpleMsg.getAllErpObj().add(erpMasterObj);

                ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getDestObjectApiName());
                ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpRealObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                erpRealObj.setObjApiName(relationshipEntity.getErpRealObjectApiname());
                erpRealObj.setObjName(erpObjApiName2NameMap.get(relationshipEntity.getErpRealObjectApiname()));
                monitorSimpleMsg.setErpRealObj(erpRealObj);

                monitorSimpleMsg.setSourceObj(monitorSimpleMsg.getCrmMasterObj());
                resultList.add(monitorSimpleMsg);
                if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                        ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName crmDetailObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                        crmDetailObj.setObjApiName(detailObjectMappingData.getSourceObjectApiName());
                        crmDetailObj.setObjName(crmObjApiNameToNameMap.get(detailObjectMappingData.getSourceObjectApiName()));
                        monitorSimpleMsg.getAllCrmObj().add(crmDetailObj);

                        ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName erpDetailObj = new ErpTempDataMonitorSimpleMsgResult.ObjApiNameAndName();
                        erpDetailObj.setObjApiName(detailObjectMappingData.getDestObjectApiName());
                        erpDetailObj.setObjName(erpObjApiName2NameMap.get(detailObjectMappingData.getDestObjectApiName()));
                        monitorSimpleMsg.getAllErpObj().add(erpDetailObj);
                    }
                }
            }
        }
        QueryResult<List<ErpTempDataMonitorSimpleMsgResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        queryResult.setTotal(detailEntities.size());
        if (StringUtils.isNotBlank(arg.getObjectApiName())) {
            long count = detailEntities.stream().filter(entity -> arg.getObjectApiName().equals(entity.getSourceObjectApiName()) || arg.getObjectApiName().equals(entity.getDestObjectApiName())).count();
            queryResult.setTotal((int) count);
        }
        queryResult.setDataList(resultList);
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<ErpTempDataMonitorResult> getTempDataMonitor(String tenantId, Integer userId, GetTempDataArg idArg, String lang) {
        Result<ErpTempData> tempDataResult = erpTempDataManager.getErpTempData(tenantId, idArg.getId());
        if (tempDataResult == null || !tempDataResult.isSuccess() || tempDataResult.getData() == null) {
            Result.copy(tempDataResult);
        }
        ErpTempDataMonitorResult erpTempDataMonitorResult = BeanUtil.copy(tempDataResult.getData(), ErpTempDataMonitorResult.class);
        if (tempDataResult.getData().getStatus() != null) {
            erpTempDataMonitorResult.setStatusDesc(ErpTempDataStatusEnum.getErpTempDataStatus(tempDataResult.getData().getStatus()).getDesc(i18NStringManager,lang,tenantId));
        }
        if (tempDataResult.getData().getSyncStatusMap() != null) {
            Map<String,Integer> syncStatus=tempDataResult.getData().getSyncStatusMap();
            if(idArg.getPloyDetailId()!=null&&syncStatus.get(idArg.getPloyDetailId())!=null){
                erpTempDataMonitorResult.setSyncStatus(syncStatus.get(idArg.getPloyDetailId()));
                erpTempDataMonitorResult.setSyncStatusDesc(ErpTempDataStatusEnum.getErpTempDataStatus(syncStatus.get(idArg.getPloyDetailId())).getDesc(i18NStringManager,lang,tenantId));
            }

        }
        return Result.newSuccess(erpTempDataMonitorResult);
    }

    @Override
    public Result<List<ErpHistoryTaskNameResult>> queryAllHistoryTask(String tenantId,
                                                                      String dataCenterId,
                                                                      Integer userId,
                                                                      QueryErpHistoryTaskNameArg arg,
                                                                      String lang) {
        List<ErpHistoryTaskNameResult> resultList = Lists.newArrayList();
        ErpHistoryTaskNameResult autoQuery = new ErpHistoryTaskNameResult();
        autoQuery.setTaskName(i18NStringManager.get(I18NStringEnum.s398,lang,tenantId));
        autoQuery.setTaskNum("ploy_auto_query");
        resultList.add(autoQuery);
        Result<List<ErpHistoryDataTaskResult>> taskResult = erpHistoryDataTaskService.queryErpHistoryDataTaskByFakeObj(tenantId, dataCenterId, userId, arg.getErpFakeObjectApiName(),lang);
        //多个taskNum只返回一个筛选
        Set<String> taskNumSet = new HashSet<>();
        if (taskResult != null && taskResult.isSuccess() && CollectionUtils.isNotEmpty(taskResult.getData())) {
            for (ErpHistoryDataTaskResult task : taskResult.getData()) {
                if (!taskNumSet.add(task.getTaskNum())||StringUtils.isAnyEmpty(task.getTaskName(),task.getTaskNum())){
                    continue;
                }
                ErpHistoryTaskNameResult taskNameResult = new ErpHistoryTaskNameResult();
                taskNameResult.setTaskName(task.getTaskName());
                taskNameResult.setTaskNum(task.getTaskNum());
                resultList.add(taskNameResult);
            }
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<Long> removeTempDataMonitorByFilter(String tenantId, String dataCenterId, Integer userId,String phone, DeleteErpTempDataMonitorArg arg) {
        Result<Boolean> checkCode = checkCodeService.checkCode(tenantId, phone, arg.getCheckingCode());
        if (!checkCode.isSuccess()) {
            return Result.copy(checkCode);
        } else if (!checkCode.getData()) {
            return Result.newError(ResultCodeEnum.CODE_CHECKED_INVALID);
        }
        if(StringUtils.isBlank(arg.getErpRealObjectApiName())){
            ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, arg.getErpFakeObjectApiName());
            if(relationshipEntity!=null){
                arg.setErpRealObjectApiName(relationshipEntity.getErpRealObjectApiname());
            }
        }
        //删除所有策略的
        return erpTempDataManager.removeErpObjData(tenantId,dataCenterId,arg.getErpRealObjectApiName(),null,arg.getStartTime(),arg.getEndTime(),
                arg.getSourceDataStatus(),arg.getTempDataSyncStatus(),arg.getTaskNum(),arg.getIdOrNum());
    }

    @Override
    public Result<ErpRealAndFakeApiNameResult> queryErpTrueAndFakeApiName(String tenantId, String dataCenterId, String streamId) {
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndId(tenantId, Lists.newArrayList(streamId));
        if(CollectionUtils.isEmpty(syncPloyDetailEntities)){
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }
        SyncPloyDetailEntity syncPloyDetailEntity=syncPloyDetailEntities.get(0);
        if (Objects.equals(TenantType.CRM, syncPloyDetailEntity.getSourceTenantType())) {
            // crm->erp没有临时库
            return Result.newError(ResultCodeEnum.VALID_ERROR);
        }
        ErpRealAndFakeApiNameResult erpRealAndFakeApiNameResult=new ErpRealAndFakeApiNameResult();
        if(syncPloyDetailEntity.getSourceTenantType().equals(TenantType.ERP)){
            ErpRealAndFakeApiNameResult.ObjApiNameAndName erpFakeObj=new ErpRealAndFakeApiNameResult.ObjApiNameAndName();
            erpFakeObj.setObjApiName(syncPloyDetailEntity.getSourceObjectApiName());
            ErpRealAndFakeApiNameResult.ObjApiNameAndName erpTrueObj=new ErpRealAndFakeApiNameResult.ObjApiNameAndName();
            ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, syncPloyDetailEntity.getSourceObjectApiName());
            erpTrueObj.setObjApiName(relationshipEntity.getErpRealObjectApiname());

            erpRealAndFakeApiNameResult.setErpFakeObj(erpFakeObj);
            erpRealAndFakeApiNameResult.setErpRealObj(erpTrueObj);
            return Result.newSuccess(erpRealAndFakeApiNameResult);
        }

        return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
    }

    @Override
    public Result<AsyncGetTotalResult> getTempDataMonitorListTotalSize(String tenantId,
                                                                       String dataCenterId,
                                                                       Integer userId,
                                                                       String taskId,
                                                                       QueryErpTempDataMonitorArg arg,
                                                                       String lang) {
        if (StringUtils.isBlank(arg.getErpFakeObjectApiName())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, arg.getErpFakeObjectApiName());
        if (relationshipEntity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        QueryResult<List<ErpTempDataMonitorResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        //查询所有策略的
        String ployDetailId = null;
        Integer tempDataSyncStatus = null;
        if (arg.getPloyDetailId() != null && arg.getTempDataSyncStatus() != null) {
            //筛选状态
            ployDetailId = arg.getPloyDetailId();
            tempDataSyncStatus = arg.getTempDataSyncStatus();
        }
        int totalCount = erpTempDataManager.countErpTempData(tenantId, dataCenterId, relationshipEntity.getErpRealObjectApiname(),ployDetailId, arg.getStartTime(), arg.getEndTime(),
                arg.getSourceDataStatus(), tempDataSyncStatus, arg.getTaskNum(), arg.getIdOrNum(), arg.getSearchType(), arg.getPageNum(), arg.getPageSize());

        //缓存
        redisDataSource.get(this.getClass().getSimpleName()).setex(taskId, 60L, new Gson().toJson(totalCount));
        AsyncGetTotalResult numResult = new AsyncGetTotalResult();
        numResult.setIsSuccess(true);
        numResult.setTotalCount(totalCount);
        Result<AsyncGetTotalResult> result = Result.newSuccess(numResult);
        result.setErrMsg(i18NStringManager.get2(I18NStringEnum.s397.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s397.getI18nValue(), totalCount),
                Lists.newArrayList(totalCount+"")));
        return result;
    }

    @Override
    public Result<AsyncGetTotalResult> getTotalByTaskId(String tenantId, String dataCenterId, Integer userId, String taskId) {
        String redisValue = redisDataSource.get(this.getClass().getSimpleName()).get(taskId);
        if (StringUtils.isNotBlank(redisValue)) {
            AsyncGetTotalResult numResult = new AsyncGetTotalResult();
            numResult.setIsSuccess(true);
            numResult.setTotalCount(Integer.valueOf(redisValue));
            return Result.newSuccess(numResult);
        }
        AsyncGetTotalResult numResult = new AsyncGetTotalResult();
        numResult.setIsSuccess(false);
        numResult.setTaskId(taskId);
        return Result.newSuccess(numResult);
    }


    @Override
    public Result<Long> removeTempDataMonitorByIdList(String tenantId, String dataCenterId, Integer userId, IdListArg arg) {
        List<ObjectId> objectIds=Lists.newArrayList();
        for(String id :arg.getIds()){
            objectIds.add(new ObjectId(id));
        }
        return erpTempDataManager.removeErpObjDataByIdLists(tenantId,objectIds);
    }


    @Override
    public Result<Void> refreshTempDataByIds(String tenantId, String dcId, RefreshTempDataArg arg) {
        //更新时间
        long l = erpTempDataManager.refreshTimeByIds(tenantId,dcId, arg.getRealObjApiName(), arg.getDataIds());
        log.info("refreshTime,inputCount:{},modCount:{},ids:{}", arg.getDataIds().size(), l, arg.getDataIds());
        triggerPollingMongoManager.triggerPolling(tenantId, Sets.newHashSet(arg.getSplitObjApiName()));
        return Result.newSuccess();
    }
}
