package com.fxiaoke.open.erpsyncdata.admin.manager;


import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.SyncDataErrLog;
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;

import com.fxiaoke.ps.ProtostuffUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AdminSyncDataManager {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private CHSyncDataManager chSyncDataManager;
    @Autowired
    private PloyBreakManager ployBreakManager;


    public void updateToError(String tenantId, String syncDataId, Integer newStatus, Integer errCode, String errMsg, String snapId) {
        int success = chSyncDataManager.updateStatus(tenantId, syncDataId, newStatus, errMsg);

//        adminSyncDataMappingsDao.setTenantId(tenantId).batchUpdateBySyncDataIdAdmin(tenantId, Lists.newArrayList(syncDataId), newStatus, null, errMsg,System.currentTimeMillis());
        ployBreakManager.incrFailedSyncDataNum(tenantId, syncDataId);

        try {//上报bizlog
            SyncDataErrLog dumpLog = SyncDataErrLog.builder().tenantId(tenantId).appName("erpdss").objectApiName("").build();
            if (SyncDataStatusEnum.isFailed(newStatus)) {
                dumpLog.setErrType(0);
            } else {
                dumpLog.setErrType(2);
                dumpLog.setSyncErrMsg(errMsg);
            }
            BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
        } catch (Exception e) {}

    }
}
