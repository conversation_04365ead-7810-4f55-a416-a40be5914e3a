package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.arg.K3CloudStockArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.admin.service.K3CStockService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class K3CStockServiceImpl implements K3CStockService {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;

    private static final String InventoryDetailsObj = "InventoryDetailsObj";

    @Override
    public Result<Void> syncStockData(K3CloudStockArg arg) {
        String tenantId = arg.getTenantId();
        Result<SyncPloyDetailEntity> result = getErpConnectInfoOfInventoryDetailsObj(arg.getTenantId());
        if(!result.isSuccess())
            return Result.newError(result.getErrCode(),result.getErrMsg());
        String dataCenterId = result.getData().getSourceDataCenterId();
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.getByIdAndTenantId(tenantId,dataCenterId);

        Result result2 = null;

        for (int index = 0; index < arg.getStockNumberList().size(); index++) {
            String stockNumber = arg.getStockNumberList().get(index);
            String materialNumber = arg.getMaterialNumberList().get(index);

            Result<List<K3Model>> totalDataListResult = getAllDataList(arg.getTenantId(),
                    dataCenterId,
                    stockNumber,
                    materialNumber);
            if(!totalDataListResult.isSuccess()) {
                result2 = totalDataListResult;
                break;
            }
            for(K3Model k3Model : totalDataListResult.getData()) {
                String id = k3Model.getString("FID");
                ErpIdArg erpIdArg = new ErpIdArg();
                erpIdArg.setTenantId(arg.getTenantId());
                erpIdArg.setDataId(id);
                erpIdArg.setObjAPIName(K3CloudForm.STK_Inventory);
                Result<StandardData> erpObjData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
                log.info("syncStockData,erpObjData={}",erpObjData);
                if(!erpObjData.isSuccess()) {
                    result2 = erpObjData;
                    break;
                }
                erpObjData.getData().setObjAPIName(K3CloudForm.STK_Inventory);

                String erpVisualApiName = erpObjDataPushManager.validateErpObjApiName(arg.getTenantId(),
                        K3CloudForm.STK_Inventory,
                        dataCenterId);
                Result<Object> pushDataToDss = erpObjDataPushManager.erpPushDataToDss(arg.getTenantId(),
                        K3CloudForm.STK_Inventory,
                        erpVisualApiName,
                        null,
                        JacksonUtil.toJson(erpObjData.getData()),
                        null,
                        id,
                        dataCenterId,
                        true,
                        InventoryDetailsObj,
                        null, null);
                log.info("syncStockData,pushDataToDss={}",pushDataToDss);
                if(!pushDataToDss.isSuccess()) {
                    result2 = pushDataToDss;
                    break;
                }
            }
        }

        return result2==null ? Result.newSuccess() : result2;
    }

    private Result<SyncPloyDetailEntity> getErpConnectInfoOfInventoryDetailsObj(String tenantId) {
        List<ErpConnectInfoEntity> connectInfoEntityList = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getListDcByTenantId(tenantId, ErpChannelEnum.ERP_K3CLOUD);
        log.info("getErpConnectInfoOfInventoryDetailsObj,connectInfoEntityList={}",connectInfoEntityList);

        if(CollectionUtils.isEmpty(connectInfoEntityList))
            return Result.newError(ResultCodeEnum.NOT_K3C_DATA_CENTER);

        SyncPloyDetailEntity ployDetailEntity = null;
        List<SyncPloyDetailEntity> ployDetailEntityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByDestTenantTypeAndObjApiName(tenantId, TenantType.CRM, InventoryDetailsObj);
        for(SyncPloyDetailEntity detailEntity : ployDetailEntityList) {
            if(detailEntity.getStatus() == SyncPloyDetailStatusEnum.DISABLE.getStatus()) continue;
            if(StringUtils.startsWithIgnoreCase(detailEntity.getSourceObjectApiName(),K3CloudForm.STK_Inventory)) {
                ployDetailEntity = detailEntity;
                break;
            }
        }
        if(ployDetailEntity == null) {
            return Result.newError(ResultCodeEnum.NO_CRM_INVERTORY_DETAILS_OBJ);
        }
        log.info("getErpConnectInfoOfInventoryDetailsObj,ployDetailEntity={}",ployDetailEntity);

        return Result.newSuccess(ployDetailEntity);
    }

    private Result<List<K3Model>> getAllDataList(String tenantId, String dataCenterId, String stockNumber, String materialNumber) {
        Result result2 = null;
        List<K3Model> totalDataList = new ArrayList<>();
        int limit = 2000;
        for (int i = 0; ; i += limit) {
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId(K3CloudForm.STK_Inventory);
            queryArg.setFieldKeys("FID,FStockId.FNumber,FMaterialId.FNumber,FBaseQty");
            queryArg.appendEqualFilter("FStockId.FNumber", stockNumber);
            queryArg.appendEqualFilter("FMaterialId.FNumber", materialNumber);
            queryArg.setLimit(limit);
            queryArg.setStartRow(i);
            Result<List<K3Model>> result = k3DataManager.queryK3ObjData(tenantId,
                    dataCenterId,
                    queryArg);
            log.info("getAllDataList,queryArg={},result={}",queryArg,result);
            if (result.isSuccess()) {
                totalDataList.addAll(result.getData());
                if (result.getData().size() < limit) {
                    break;
                }
            } else {
                result2 = result;
                break;
            }
        }

        return result2==null ? Result.newSuccess(totalDataList) : result2;
    }
}
