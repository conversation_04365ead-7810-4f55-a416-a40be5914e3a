package com.fxiaoke.open.erpsyncdata.admin.service;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.Template;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * 模板中心
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/3/28
 */
public interface TemplateService {

    /**
     * 获取筛选条件
     *
     * @return
     */
    Result<Template.Filters> getFilters();


    /**
     * 获取基本信息列表,该接口不需要企业
     *
     * @param arg
     * @return
     */
    Result<List<Template.TemplateInfo>> listBaseInfo(Template.QueryBaseInfoArg arg);

    /**
     * 获取详情信息
     *
     * @param id
     * @return
     */
    Result<Template.TemplateInfo> getAllInfo(String id);

    /**
     * 检查条件
     *
     * @param tenantId
     * @param dcId
     * @param arg
     * @return
     */
    Result<List<Template.Precondition>> preCheckCondition(String tenantId, String dcId, Template.PreCheckArg arg);

    /**
     * 检查对象
     *
     * @param tenantId
     * @param dcId
     * @param arg
     * @return
     */
    Result<List<Template.TemplateErpObjInfo>> preCheckErpObj(String tenantId, String dcId, Template.PreCheckArg arg,String lang);

    /**
     * 创建模板集成流
     *
     * @param tenantId
     * @param dcId
     * @param arg
     * @return
     */
    Result<List<Template.StreamInfo>> createTemplateIntegrationStreams(String tenantId, String dcId, Template.ListStreamsArg arg,String lang);

    /**
     * 新增或更新模板
     *
     */
    Result<Void> upsertTemplate(Template.TemplateInfo info, String oldVersion);

    Result<List<Template.OptionGroup>> listStreams(String tenantId);
    Result<Template.Detail> parseTemplateFromStreams(String tenantId, List<String> streamIds);

    Result<List<Template.Option>> listOptions(String optionsType);
    Result<Void> addOptions(String optionsType, Template.Option options);
    Result<Void> deleteOptions(String optionType, Template.Option option);

    Result<Void> saveOrders(List<String> ids,String type);


    /**
     * 导出所有数据
     * @return
     */
    Result<Dict> downloadALlData();

}