package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncTimeService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:29 2021/7/5
 * @Desc:
 */
@Service
@Data
@Slf4j
public class AdminSyncTimeServiceImpl implements AdminSyncTimeService {
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;

    @Override
    public Result<Void> updateLastSyncTime(String tenantId, String erpObjApiName, Integer eventType,Long lastSyncTime) {
        ErpSyncTimeEntity arg=new ErpSyncTimeEntity();
        arg.setTenantId(tenantId);
        arg.setObjectApiName(erpObjApiName);
        arg.setOperationType(eventType);
        List<ErpSyncTimeEntity> erpSyncTimeEntities = erpSyncTimeDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        if(CollectionUtils.isEmpty(erpSyncTimeEntities)){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        erpSyncTimeEntities.get(0).setLastSyncTime(lastSyncTime);
        int update = erpSyncTimeDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpSyncTimeEntities.get(0));
        return Result.newSuccess();
    }
}
