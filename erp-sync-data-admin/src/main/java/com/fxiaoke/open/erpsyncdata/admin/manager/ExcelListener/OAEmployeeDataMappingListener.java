package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.OAEmployeeDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 以erp侧数据Id作为唯一id。导入数据按顺序执行。
 *
 * <AUTHOR>
 * @Desc:
 */
@Slf4j
@Getter
public class OAEmployeeDataMappingListener extends BaseListener<OAEmployeeDataMappingExcelVo> {

    private final EmployeeMappingService employeeMappingService;

    private final IdGenerator idGenerator;

    private final ErpFieldTypeEnum datatype;

    private final String tenantId;

    private final ErpChannelEnum channel;

    private final ImportExcelFile.Result importResult;

    private final I18NStringManager i18NStringManager;

    private final String lang;

    public OAEmployeeDataMappingListener(String tenantId,
                                         ErpChannelEnum channel,
                                         ErpFieldTypeEnum datatype,
                                         EmployeeMappingService employeeMappingService,
                                         IdGenerator idGenerator,
                                         I18NStringManager i18NStringManager,
                                         String lang) {
        this.employeeMappingService = employeeMappingService;
        this.datatype = datatype;
        this.tenantId = tenantId;
        this.channel = channel;
        this.idGenerator = idGenerator;
        this.importResult = new ImportExcelFile.Result();
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
    }

    @Override
    public void invoke(OAEmployeeDataMappingExcelVo data, AnalysisContext context) {
        super.invoke(data, context);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();
        dealWithErpEmployee(data, rowNo);
    }

    private void dealWithErpEmployee(OAEmployeeDataMappingExcelVo data, Integer rowNo) {
        if (StringUtils.isEmpty(data.getCrmEmployeeId()) || StringUtils.isEmpty(data.getOaEmployeeId()) || StringUtils.isEmpty(data.getOaEmployeeName())) {
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s787,lang,tenantId));
        } else {
            EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
            employeeMappingResult.setChannel(channel);
            employeeMappingResult.setErpEmployeeId(data.getOaEmployeeId());
            employeeMappingResult.setErpEmployeeName(data.getOaEmployeeName());
            employeeMappingResult.setFsEmployeeId(Integer.valueOf(data.getCrmEmployeeId()));
            employeeMappingResult.setFsEmployeeName(data.getOaEmployeeName());
            Result<String> updateResult = employeeMappingService.updateOAEmployeeMappingByErpId(tenantId, employeeMappingResult);
            if (updateResult.isSuccess()) {
                if ("add".equals(updateResult.getData())) {
                    importResult.incrInsert(1);
                } else if ("update".equals(updateResult.getData())) {
                    importResult.incrUpdate(1);
                }
            } else {
                importResult.addImportError(rowNo, i18NStringManager.get2(I18NStringEnum.s383,
                        lang,
                        tenantId,
                        updateResult.getErrMsg()));
            }
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s772.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s772.getI18nValue(), importResult.getInvokedNum(), importResult.getInsertNum(), importResult.getDeleteNum(), importResult.getUpdateNum()),
                Lists.newArrayList(importResult.getInvokedNum()+"", importResult.getInsertNum()+"", importResult.getDeleteNum()+"", importResult.getUpdateNum()+""))).append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s773.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s773.getI18nValue(), importResult.getImportErrorRows().size()),
                Lists.newArrayList(importResult.getImportErrorRows().size()+""))).append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s775.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s775.getI18nValue(), importResult.getInvokeExceptionRows().size()),
                Lists.newArrayList(importResult.getInvokeExceptionRows().size()+""))).append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    /**
     * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
     * entire read will terminate.
     *
     * @param exception
     * @param context
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
