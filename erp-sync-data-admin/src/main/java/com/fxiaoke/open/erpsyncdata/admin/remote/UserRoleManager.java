package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.paas.auth.common.constant.AuthConstant;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.RolePojo;
import com.facishare.paas.auth.model.params.response.RoleUserPageInfoReponse;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/18 19:37:44
 */
@Component
@Slf4j
public class UserRoleManager {
    @Autowired
    private RoleClient roleClient;

    // 管理权限的crm管理员角色的code为31, 业务权限的crm管理员角色的code为AuthConstant.ROLE_CODE.CRM_MANAGER
    private static final String CRM_MANAGER_ROLE_CODE = "31";

    public static final String ERPDSS_MANAGER_ROLE_CODE = "erpdss_manager";

    public boolean checkAdminAuth(final String tenantId, final String employeeId) {
        // 临时校验角色信息(crm管理员/系统管理员),后续需要改为cep调用接口,使用cep鉴权
        try {
            final Set<String> roles = roleClient.queryRoleCodeByUserId(getAuthContext(tenantId, employeeId));
            return roles.contains(CRM_MANAGER_ROLE_CODE) || roles.contains(AuthConstant.ROLE_CODE.SYSTEM_ROLE);
        } catch (Exception e) {
            log.warn("checkAdminAuth error, tenantId:{}, employeeId:{}", tenantId, employeeId, e);
            return true;
        }
    }

    /**
     * 获取该企业缺少的角色
     */
    public List<String> getLackRoles(String tenantId, Collection<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return new ArrayList<>();
        }
        final HashSet<String> roleCodes = Sets.newHashSet(roles);
        final List<RolePojo> rolePojos = roleClient.queryAllRoleInfo(getAuthContext(tenantId), roleCodes, false);
        rolePojos.stream().map(RolePojo::getRoleCode).forEach(roleCodes::remove);

        return Lists.newArrayList(roleCodes);
    }

    public Map<String, String> queryRoleNameByRoleCode(String tenantId, List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return new HashMap<>();
        }
        final List<RolePojo> rolePojos = roleClient.queryAllRoleInfo(getAuthContext(tenantId), Sets.newHashSet(roles), true);
        return rolePojos.stream().collect(Collectors.toMap(RolePojo::getRoleCode, RolePojo::getRoleName, (o1, o2) -> o1));
    }

    /**
     * 查询有某个管理功能权限的员工
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public List<Integer> getUserIdsBySystemRoleCode(String tenantId, String roleCode) {
        final RoleUserPageInfoReponse roleUserPageInfoReponse = roleClient.queryUserIdByRole(getSystemAuthContext(tenantId), roleCode, null);
        return roleUserPageInfoReponse.getUsers().stream()
                .filter(StringUtils::isNumeric)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    @NotNull
    private static AuthContext getAuthContext(String tenantId) {
        return getAuthContext(tenantId, AuthConstant.SYSTEM_USERID);
    }

    private static AuthContext getAuthContext(String tenantId, String employeeId) {
        return getAuthContext(tenantId, employeeId, AuthConstant.AppId.ALL);
    }

    /**
     * 查询管理功能权限的authContext
     */
    @NotNull
    private static AuthContext getSystemAuthContext(String tenantId) {
        return getAuthContext(tenantId, AuthConstant.SYSTEM_USERID, AuthConstant.AppId.SYSTEM);
    }

    @NotNull
    private static AuthContext getAuthContext(String tenantId, String employeeId, String appId) {
        // 临时校验角色信息(crm管理员/系统管理员),后续需要改为cep调用接口,使用cep鉴权
        final AuthContext authContext = new AuthContext();
        authContext.setTenantId(tenantId);
        authContext.setAppId(appId);
        authContext.setUserId(employeeId);
        return authContext;
    }
}
