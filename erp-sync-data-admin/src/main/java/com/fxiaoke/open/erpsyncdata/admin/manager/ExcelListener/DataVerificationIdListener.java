package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.DataVerificationIdExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.result.DataVerificationResult;
import com.fxiaoke.open.erpsyncdata.admin.service.DataVerificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 14:40 2022/11/9
 * @Desc:
 */
@Slf4j
@Getter
public class DataVerificationIdListener extends BaseListener<DataVerificationIdExcelVo> {
    private final String dataVerificationTaskId;
    private final String tenantId;
    private final String historyTaskNum;
    private final String sourceObjectApiName;
    private final String destObjectApiName;
    private final String sourceDcId;
    private final String erpRealObjectApiname;
    private final DataVerificationService dataVerificationService;
    private final DataVerificationResult dataVerificationResult;
    private final String dealType;

    public DataVerificationIdListener(String dataVerificationTaskId, String tenantId, String historyTaskNum, String sourceObjectApiName, String destObjectApiName, String sourceDcId, String erpRealObjectApiname, DataVerificationService dataVerificationService, String dealType) {
        this.dataVerificationTaskId=dataVerificationTaskId;
        this.tenantId = tenantId;
        this.historyTaskNum = historyTaskNum;
        this.sourceObjectApiName = sourceObjectApiName;
        this.destObjectApiName = destObjectApiName;
        this.sourceDcId = sourceDcId;
        this.erpRealObjectApiname = erpRealObjectApiname;
        this.dataVerificationService = dataVerificationService;
        this.dataVerificationResult = new DataVerificationResult();
        this.dealType=dealType;
        this.dataVerificationResult.setPollingListSize(0);
        this.dataVerificationResult.setAllIdListSize(0);
        this.dataVerificationResult.setNotTempIdListSize(0);
        this.dataVerificationResult.setNotMappingIdListSize(0);
        this.dataVerificationResult.setNotCreatedIdListSize(0);
    }

    @Override
    public void invoke(DataVerificationIdExcelVo data, AnalysisContext context) {
        super.invoke(data, context);
        if (dataList.size() >= 500) {
            dealWithDataVerificationId(dataList);
            dataList.clear();
        }
    }

    private void dealWithDataVerificationId(List<DataVerificationIdExcelVo> dataList) {
        log.info("dealWithDataVerificationId dataList size={}",dataList.size());
        List<String> collect = dataList.stream().map(DataVerificationIdExcelVo::getId).collect(Collectors.toList());
        Result<DataVerificationResult> result;
        if(StringUtils.isNotBlank(dealType)&&"historyDataId".equals(dealType)){
            result=dataVerificationService.verifyHistoryDataId(dataVerificationTaskId,tenantId,historyTaskNum,sourceDcId,erpRealObjectApiname,collect);
        }else{
            result = dataVerificationService.verifyDataIdByIdList(dataVerificationTaskId,tenantId, historyTaskNum, sourceObjectApiName, destObjectApiName, sourceDcId, erpRealObjectApiname, collect);
        }
        if(result!=null&&result.isSuccess()&&result.getData()!=null){
            dataVerificationResult.setNeedStop(result.getData().getNeedStop());
            dataVerificationResult.setPollingListSize(dataVerificationResult.getPollingListSize() + result.getData().getPollingListSize());
            dataVerificationResult.setAllIdListSize(dataVerificationResult.getAllIdListSize()+result.getData().getAllIdListSize());
            dataVerificationResult.setNotTempIdListSize(dataVerificationResult.getNotTempIdListSize()+result.getData().getNotTempIdListSize());
            dataVerificationResult.setNotMappingIdListSize(dataVerificationResult.getNotMappingIdListSize()+result.getData().getNotMappingIdListSize());
            dataVerificationResult.setNotCreatedIdListSize(dataVerificationResult.getNotCreatedIdListSize()+result.getData().getNotCreatedIdListSize());
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        if (CollectionUtils.isNotEmpty(dataList)) {
            dealWithDataVerificationId(dataList);
        }
    }

    /**
     * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
     * entire read will terminate.
     *
     * @param exception
     * @param context
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("DataVerificationIdListener onException");
    }
}
