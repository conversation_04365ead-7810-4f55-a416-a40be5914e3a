package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncDataMappingListByPloyDetailIdArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.StreamStatManager;
import com.fxiaoke.open.erpsyncdata.admin.model.RedoSyncRecord;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.EnterpriseRelationManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.LicenseService;
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjCustomFunctionService;
import com.fxiaoke.open.erpsyncdata.admin.service.RelationErpShardService;
import com.fxiaoke.open.erpsyncdata.admin.task.AggDownstreamNotify2TemplateScheduleService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationManageGroupDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpAlarmRuleManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.RelationErpShardManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamStatProgress;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.GroupStreamStatDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ManageGroupAddDownstreamFailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ManageGroupAddTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplatePloyDetailChangeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.CheckResultEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ManageGroupAddDownstreamFailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ManageGroupAddTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplatePloyDetailChangeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.CollectionsUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.GetDownstreamEnterprise;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/3/6 16:04:39
 */
@Service
@Slf4j
public class RelationErpShardServiceImpl implements RelationErpShardService {
    @Autowired
    private RelationErpShardDao relationErpShardDao;
    @Autowired
    private RelationManageGroupDao relationManageGroupDao;
    @Autowired
    private RelationErpShardManager relationErpShardManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private TemplatePloyDetailChangeDao templatePloyDetailChangeDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private ManageGroupAddTaskDao manageGroupAddTaskDao;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ManageGroupAddDownstreamFailDao manageGroupAddDownstreamFailDao;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private CustomFunctionService customFunctionService;
    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpAlarmRuleManager erpAlarmRuleManager;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private StreamStatManager streamStatManager;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private GroupStreamStatDao groupStreamStatDao;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private AggDownstreamNotify2TemplateScheduleService aggDownstreamNotify2TemplateScheduleService;

    @Override
    public Result<Void> checkAddDownstreamIds(String tenantId, List<String> checkIds, String templateId) {
        // 模板企业自己加入报错
        if (checkIds.contains(templateId)) {
            return Result.newError(ResultCodeEnum.TEMPLATE_CAN_NOT_ADD_DOWNSTREAM);
        }

        // 检查是否是下游企业
        final List<String> noDownstreamIds = checkIds.stream().filter(ei -> {
            final Result2<Boolean> downstream = enterpriseRelationManager.isDownstream(tenantId, ei);
            // 错误降级,按正常处理
            return downstream.isSuccess() && BooleanUtils.isNotTrue(downstream.getData());
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noDownstreamIds)) {
            return Result.newErrorExtra(ResultCodeEnum.NOT_DOWNSTREAM_ENTERPRISE_IN_PARAMETERS, String.join(",", noDownstreamIds));
        }

        // 校验下游是否为代管模式
        final List<String> noManagedIds = checkIds.stream()
                .filter(ei -> !configCenterConfig.isManagedEnterprise(ei, true))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noManagedIds)) {
            return Result.newErrorExtra(ResultCodeEnum.NOT_MANAGED_ENTERPRISE_IN_PARAMETERS, String.join(",", noManagedIds));
        }

        // 检查是否已经有模板企业,当前逻辑不支持一个下游对应多个模版企业
        final List<String> otherTemplateIds = checkIds.stream().filter(ei -> {
            final RelationErpShardDto relationErpShardDto = relationErpShardDao.queryFirstNormalByDownstreamId(ei);
            if (!Objects.nonNull(relationErpShardDto)) return false;
            return !Objects.equals(relationErpShardDto.getTemplateId(), templateId);
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherTemplateIds)) {
            return Result.newErrorExtra(ResultCodeEnum.DOWNSTREAM_HAS_OTHER_TEMPLATE, String.join(",", otherTemplateIds));
        }

        // 检查配额,企业下游数量限制
        final List<String> downstreamIds = relationErpShardDao.getAllDownstreamIdsByUpstreamId(tenantId);
        final Integer quota = licenseService.getManagedDownstreamQuota(tenantId);
        final long count = Stream.concat(downstreamIds.stream(), checkIds.stream()).distinct().count();
        if (count > quota) {
            return Result.newErrorExtra(ResultCodeEnum.MANAGED_DOWNSTREAM_QUOTA_LIMIT, String.valueOf(count), String.valueOf(quota));
        }

        return Result.newSuccess();
    }

    @Override
    public Pair<List<String>, List<String>> addDownstream(String tenantId, String groupId, String templateId, String dcId, List<String> downstreamIds) {
        String taskId = IdGenerator.get();
        manageGroupAddTaskDao.create(new ManageGroupAddTaskEntity(taskId, tenantId, groupId, ManageGroupAddTaskEntity.STATUS_INIT, downstreamIds, System.currentTimeMillis(), System.currentTimeMillis()));

        // 获取所有集成流
        final Result<List<SyncPloyDetailData>> ployDetailsResult = adminSyncPloyDetailService.getTop1000ByDcId(templateId, dcId);
        final List<SyncPloyDetailData> ployDetails = ployDetailsResult.getData();

        // 获取需要校验的对象/字段/选项值
        // key:对象 value-key:字段  value-value:选项值
        final Map<String, Map<String, Set<Object>>> objectAndFieldApiName = adminSyncPloyDetailService.getAllObjectAndFieldApiNameByPloy(ployDetails).getData();
        // 需要校验的函数
        Set<String> functions = getNeedCheckFunctions(templateId, dcId, ployDetails);
        // 获取需要校验的角色
        Set<String> roles = getNeedCheckRoles(templateId, dcId, ployDetails);

        List<String> success = new ArrayList<>();
        List<String> fail = new ArrayList<>();
        downstreamIds.forEach(downstreamId -> {
            final CheckResultEntity checkResultEntity = checkDownstream(downstreamId, objectAndFieldApiName, functions, roles);
            if (CollectionsUtils.anyNotEmpty(checkResultEntity.getLackObject(), checkResultEntity.getLackField(), checkResultEntity.getLackOption(), checkResultEntity.getLackRole(), checkResultEntity.getLackFunc())) {
                fail.add(downstreamId);
                final String downstreamName = userCenterService.getEnterpriseName(downstreamId);
                manageGroupAddDownstreamFailDao.create(new ManageGroupAddDownstreamFailEntity(null, taskId, downstreamId, downstreamName, checkResultEntity, System.currentTimeMillis()));
            } else {
                success.add(downstreamId);
                createRelationErpShardEntity(groupId, downstreamId);
            }
        });
        //刷新分组更新时间
        relationManageGroupDao.refreshUpdateTime(templateId, groupId);

        // 发送mq消息,初始化
        notifyDownstreamInit(templateId, dcId, success);

        manageGroupAddTaskDao.finishTask(tenantId, taskId);
        return Pair.of(success, fail);
    }

    private RelationErpShardEntity createRelationErpShardEntity(String groupId, String downstreamId) {
        final RelationErpShardEntity relationErpShardEntity = new RelationErpShardEntity();
        relationErpShardEntity.setId(IdGenerator.get());
        relationErpShardEntity.setGroupId(groupId);
        relationErpShardEntity.setDownstreamId(downstreamId);
        relationErpShardEntity.setStatus(RelationErpShardStatusEnum.normal.getStatus());
        relationErpShardEntity.setCreateTime(System.currentTimeMillis());
        relationErpShardEntity.setUpdateTime(System.currentTimeMillis());

        relationErpShardDao.insert(relationErpShardEntity);

        return relationErpShardEntity;
    }

    private CheckResultEntity checkDownstream(String downstreamId, Map<String, Map<String, Set<Object>>> objectAndFieldApiName, Set<String> functions, Set<String> roles) {
        final CheckResultEntity checkResultEntity = new CheckResultEntity();
        checkObjectAndField(downstreamId, objectAndFieldApiName, checkResultEntity);

        final List<String> lackFunctions = customFunctionService.getLackFunctions(downstreamId, functions);
        checkResultEntity.setLackFunc(lackFunctions);

        final List<String> lackRoles = userRoleManager.getLackRoles(downstreamId, roles);
        checkResultEntity.setLackRole(lackRoles);

        return checkResultEntity;
    }

    private void checkObjectAndField(String downstreamId, Map<String, Map<String, Set<Object>>> objectAndFieldApiName, CheckResultEntity checkResultEntity) {
        if (MapUtils.isEmpty(objectAndFieldApiName)) {
            return;
        }
        final List<String> objectApiNames = Lists.newArrayList(objectAndFieldApiName.keySet());
        final List<ObjectDescribe> objectDescribes = crmRemoteManager.listObjectAndFieldByApiNames(downstreamId, objectApiNames);
        final Map<String, Map<String, FieldDescribe>> describeMap = objectDescribes.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getFields));
        objectAndFieldApiName.forEach((apiName, fields) -> {
            final Map<String, FieldDescribe> fieldMap = describeMap.get(apiName);
            if (Objects.isNull(fieldMap)) {
                checkResultEntity.getLackObject().add(apiName);
                return;
            }

            fields.forEach((fieldName, options) -> {
                if (!fieldMap.containsKey(fieldName)) {
                    checkResultEntity.getLackField().add(new CheckResultEntity.Field(apiName, fieldName));
                } else if (CollectionUtils.isNotEmpty(options)) {
                    final FieldDescribe fieldDescribe = fieldMap.get(fieldName);
                    final Set<Object> fieldOptions = fieldDescribe.getOptions().stream().map(fieldOption -> fieldOption.get("value")).collect(Collectors.toSet());
                    options.stream()
                            .filter(option -> !fieldOptions.contains(option))
                            .map(option -> new CheckResultEntity.Option(apiName, fieldName, option))
                            .forEach(checkResultEntity.getLackOption()::add);
                }
            });
        });
    }

    private Set<String> getNeedCheckRoles(String templateId, String dcId, List<SyncPloyDetailData> ployDetails) {
        // 集成流通知节点
        final Set<String> roles = ployDetails.stream()
                .map(detail -> Optional.ofNullable(detail.getIntegrationStreamNodes())
                        .map(IntegrationStreamNodesData::getNotifyComplementNode)
                        .map(IntegrationStreamNodesData.NotifyComplementNode::getNotifyRoles)
                        .orElse(null)
                ).filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        // 告警规则通知角色
        final List<ErpAlarmRuleEntity> data = erpAlarmRuleManager.findData(templateId);
        data.stream()
                .map(ErpAlarmRuleEntity::getRoleIds)
                .filter(StringUtils::isNotBlank)
                .forEach(roles::add);

        return roles;
    }

    private Set<String> getNeedCheckFunctions(String templateId, String dcId, List<SyncPloyDetailData> ployDetails) {
        // 集成流前中后函数
        final Set<String> functions = ployDetails.stream().flatMap(detail -> Stream.of(detail.getBeforeFuncApiName(), detail.getDuringFuncApiName(), detail.getAfterFuncApiName())).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        // 对象api函数
        final List<String> erpObjectApiNames = ployDetails.stream().map(detail -> Objects.equals(detail.getSourceTenantType(), TenantTypeEnum.CRM.getType()) ? detail.getDestObjectApiName() : detail.getSourceObjectApiName()).distinct().collect(Collectors.toList());
        final List<String> strings = erpObjCustomFunctionService.queryAllFunctionApiNameByObjectApiName(templateId, dcId, erpObjectApiNames);
        functions.addAll(strings);

        // 连接器函数
        final ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(templateId, dcId);
        if (Objects.equals(connectInfo.getChannel(), ErpChannelEnum.STANDARD_CHANNEL) && StringUtils.isNotBlank(connectInfo.getConnectParams())) {
            StandardConnectParam standardConnectParam = connectInfo.getChannel().getAndCheckConnectParam(templateId, connectInfo.getConnectParams());
            Optional.ofNullable(standardConnectParam.getHeaderFunctionName()).filter(StringUtils::isNotBlank).ifPresent(functions::add);
        }
        return functions;
    }


    @Override
    public void deleteAllDownstreamEnterprise(String groupId, String templateId, String dcId) {
        final List<String> allDownstreamIdsByTemplateIdAndDcId = relationErpShardDao.getAllDownstreamIdsByTemplateIdAndDcId(templateId, dcId);
        deleteDownstreamEnterprise(groupId, templateId, dcId, allDownstreamIdsByTemplateIdAndDcId);
    }

    @Override
    public void deleteDownstreamEnterprise(String groupId, String templateId, String dcId, List<String> downstreamIds) {
        // 获取所有模板企业ErpObject,用于删除下游企业的sync_time
        final Result<List<SyncPloyDetailData>> ployDetailsResult = adminSyncPloyDetailService.getTop1000ByDcId(templateId, dcId);
        final List<String> erpObjects = ployDetailsResult.getData().stream().filter(ployDetail -> Objects.equals(ployDetail.getSourceTenantType(), TenantTypeEnum.ERP.getType())).map(SyncPloyDetailData::getSourceObjectApiName).distinct().collect(Collectors.toList());
        final List<String> ployDetailIds = ployDetailsResult.getData().stream().map(SyncPloyDetailData::getId).distinct().collect(Collectors.toList());

        final List<RelationErpShardEntity> relationErpShardEntities = relationErpShardDao.queryByGroupIdAndDownstreamIds(groupId, downstreamIds, null);
        final Set<String> normalDownStreamIds = relationErpShardEntities.stream()
                .filter(entity -> Objects.equals(entity.getStatus(), RelationErpShardStatusEnum.normal.getStatus()))
                .map(RelationErpShardEntity::getDownstreamId)
                .collect(Collectors.toSet());

        final List<String> notifyDownStreamIds = downstreamIds.stream()
                .filter(downstreamId -> {
                    final int i = relationErpShardManager.deleteDownstreamEnterprise(groupId, downstreamId);
                    relationErpShardManager.invalidSimpleCache(downstreamId, dcId);
                    return i > 0;
                })
                .filter(normalDownStreamIds::contains)
                .collect(Collectors.toList());


        TemplatePloyDetailChangeEntity.DeletedDownstreamEvent event = new TemplatePloyDetailChangeEntity.DeletedDownstreamEvent(groupId, erpObjects, ployDetailIds);
        saveTemplatePloyChange(templateId, notifyDownStreamIds, TemplatePloyDetailChangeEntity.Reason.DELETED_DOWNSTREAM, event);
        //刷新分组更新时间
        relationManageGroupDao.refreshUpdateTime(templateId, groupId);
    }

    private void saveTemplatePloyChange(String tenantId, String dcId, TemplatePloyDetailChangeEntity.Reason reason, Object event) {
        final List<String> allDownstreamIds = relationErpShardDao.getAllDownstreamIdsByTemplateIdAndDcId(tenantId, dcId);
        if (CollectionUtils.isEmpty(allDownstreamIds)) {
            return;
        }

        saveTemplatePloyChange(tenantId, allDownstreamIds, reason, event);
    }

    private void saveTemplatePloyChange(String tenantId, List<String> allDownstreamIds, TemplatePloyDetailChangeEntity.Reason reason, Object event) {
        final String upStreamId = relationManageGroupDao.getUpStreamIdByTemplateId(tenantId);
        final String event1 = JSON.toJSONString(event);

        final List<TemplatePloyDetailChangeEntity> entities = allDownstreamIds.stream()
                .map(downstreamId -> {
                    final TemplatePloyDetailChangeEntity entity = new TemplatePloyDetailChangeEntity();
                    entity.setDownstreamId(downstreamId);
                    entity.setId(IdGenerator.get());
                    entity.setTenantId(upStreamId);
                    entity.setTemplateId(tenantId);
                    entity.setReason(reason.getValue());
                    entity.setStatus(TemplatePloyDetailChangeEntity.STATUS_INIT);
                    entity.setTraceId(TraceUtil.get());
                    entity.setUpdateTime(System.currentTimeMillis());
                    entity.setCreateTime(System.currentTimeMillis());
                    entity.setTryTime(0);
                    entity.setEvent(event1);
                    return entity;
                })
                .collect(Collectors.toList());
        Lists.partition(entities, 100).forEach(templatePloyDetailChangeDao::create);
    }

    @Override
    public void notifyDownstreamInit(String tenantId, String dcId, List<String> downstreamIds) {
        // 初始化企业连接器和数据库表
        saveTemplatePloyChange(tenantId, downstreamIds, TemplatePloyDetailChangeEntity.Reason.INIT, new TemplatePloyDetailChangeEntity.InitEvent());

        // 所有erp->crm集成流需要创建sync_time
        List<SyncPloyDetailEntity> listResult = adminSyncPloyDetailDao.listStreamByDCIDAndObjApiName(tenantId, null, null, dcId, null, null);
        listResult.forEach(ployDetail -> {
            final TemplatePloyDetailChangeEntity.UpdatedPloyDetailEvent event = new TemplatePloyDetailChangeEntity.UpdatedPloyDetailEvent(dcId, ployDetail.getSourceObjectApiName(), ployDetail.getSyncRules());

            saveTemplatePloyChange(tenantId, downstreamIds, TemplatePloyDetailChangeEntity.Reason.UPDATED, event);
        });
    }

    @Override
    public void notifyTemplatePloyStatusChange(String tenantId, String ployDetailId, TemplatePloyDetailChangeEntity.Reason change) {
        final SyncPloyDetailEntity ployDetail = adminSyncPloyDetailDao.getById(tenantId, ployDetailId);
        if (Objects.isNull(ployDetail) || !Objects.equals(ployDetail.getSourceTenantType(), TenantType.ERP)) {
            return;
        }

        Object event;
        switch (change) {
            case ENABLED:
                SyncPloyDetailResult ployDetailResult = SyncPloyDetailResult.buildSyncPloyDetailResultByEntity(i18NStringManager, null, tenantId, ployDetail);
                event = new TemplatePloyDetailChangeEntity.EnabledPloyDetailEvent(true, JSON.toJSONString(ployDetailResult));
                break;
            case DISABLED:
                event = new TemplatePloyDetailChangeEntity.DisabledPloyDetailEvent(ployDetailId, ployDetail.getSourceObjectApiName());
                break;
            case DELETED_PLOY_DETAIL:
            default:
                event = new TemplatePloyDetailChangeEntity.DeletedPloyDetailEvent(ployDetailId);
                break;
        }

        saveTemplatePloyChange(tenantId, ployDetail.getErpDataCenterId(), change, event);
    }

    @Override
    public void notifyTemplateSyncTimeChange(String tenantId, String objectApiName) {
        final List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantIdAndSourceObjs(tenantId, Lists.newArrayList(objectApiName), null);
        if (CollectionUtils.isEmpty(syncPloyDetailEntities)) {
            return;
        }
        // objectApiName绑定一个dcId
        final String dcId = syncPloyDetailEntities.stream().findFirst().map(SyncPloyDetailEntity::getErpDataCenterId).orElse(null);

        final SyncRulesData syncRulesData = syncPloyDetailEntities.stream().findFirst().map(SyncPloyDetailEntity::getSyncRules).orElse(null);

        final TemplatePloyDetailChangeEntity.UpdatedPloyDetailEvent event = new TemplatePloyDetailChangeEntity.UpdatedPloyDetailEvent(dcId, objectApiName, syncRulesData);
        saveTemplatePloyChange(tenantId, dcId, TemplatePloyDetailChangeEntity.Reason.UPDATED, event);
    }

    @Override
    public Result<StreamStatProgress> getDownstreamStatProgress(String tenantId, String groupId) {
        StreamStatProgress cache = getDownstreamStatProgressByCache(tenantId, groupId, null);
        if (cache == null) {
            return Result.newError("not found but task is in progress");
        }
        return Result.newSuccess(cache);
    }

    @Nullable
    private StreamStatProgress getDownstreamStatProgressByCache(String tenantId, String groupId,@Nullable Long minTime) {
        if (minTime == null) {
            RelationManageGroupEntity groupEntity = relationManageGroupDao.getById(tenantId, groupId);
            if (groupEntity == null) {
                throw new ErpSyncDataException(ResultCodeEnum.NOT_FOUND_GROUP);
            }
            minTime = ObjectUtil.defaultIfNull(groupEntity.getUpdateTime(), 0L);
        }
        String cacheKey = CommonConstant.REDIS_CACHE_DOWN_STREAM_STAT_TRACE + tenantId + "." + groupId;
        //获取锁失败
        String cache = redisCacheManager.getCache(cacheKey);
        if (cache != null) {
            StreamStatProgress parse = StreamStatProgress.parse(cache);
            if (parse.getBeginTime() > minTime) {
                return parse;
            }
        }
        return null;
    }

    @Override
    public Result<StreamStatProgress> refreshDownstreamStatTask(String tenantId, String groupId) {
        //生成唯一trace
        TraceUtil.initTraceWithFormat(tenantId);
        TraceUtil.addChildTrace(IdGenerator.get());
        RelationManageGroupEntity groupEntity = relationManageGroupDao.getById(tenantId, groupId);
        //加锁
        RLock lock = redissonClient.getLock(CommonConstant.REDIS_LOCK_DOWN_STREAM_STAT + tenantId + "." + groupId);
        String cacheKey = CommonConstant.REDIS_CACHE_DOWN_STREAM_STAT_TRACE + tenantId + "." + groupId;
        if (lock.tryLock()) {
            try {
                //更新缓存的最近任务Id
                StreamStatProgress streamStatProgress = new StreamStatProgress();
                String dcId = groupEntity.getDcId();
                List<String> downstreamIds = relationErpShardDao.getAllDownstreamIdsByGroupId(groupId, null);
                streamStatProgress.setTraceId(TraceUtil.get());
                streamStatProgress.setTotal(downstreamIds.size());
                streamStatProgress.setBeginTime(System.currentTimeMillis());
                streamStatProgress.setLastUpdateTime(streamStatProgress.getBeginTime());
                long expireTime = 60 * 60L;
                redisCacheManager.setCache(cacheKey, streamStatProgress.toString(), expireTime);
                try {
                    for (String downstreamId : downstreamIds) {
                        streamStatManager.groupStatByDc(tenantId, downstreamId, dcId);
                        streamStatProgress.setCompletedNum(streamStatProgress.getCompletedNum() + 1);
                        streamStatProgress.setLastUpdateTime(System.currentTimeMillis());
                        redisCacheManager.setCache(cacheKey, streamStatProgress.toString(), expireTime);
                    }
                    return Result.newSuccess(streamStatProgress);
                } catch (Exception e) {
                    //任务异常
                    streamStatProgress.setExceptionMsg(I18NStringEnum.s3314.getNameByTraceLocale() + ExceptionUtil.getMessage(e));
                    redisCacheManager.setCache(cacheKey, streamStatProgress.toString(), expireTime);
                    return Result.newSuccess(streamStatProgress);
                }
            } finally {
                lock.unlock();
            }
        } else {
            //获取锁失败时，返回进行中的结果
            StreamStatProgress cache = getDownstreamStatProgressByCache(tenantId, groupId, groupEntity.getUpdateTime());
            if (cache == null) {
                return Result.newError("not found but task is in progressing");
            }
            return Result.newSuccess(cache);
        }
    }

    @Override
    public Result<UpGroupStreamStat> calStatGroupByDcLatest(String tenantId, String templateId, String dcId, String id) {
        UpGroupStreamStat upGroupStreamStat = groupStreamStatDao.calStatGroupByDcLatest(tenantId, dcId);
        StreamStatProgress cache = getDownstreamStatProgressByCache(tenantId, id, null);
        if (cache != null && Objects.equals(upGroupStreamStat.getLastStatTime(), 0L)) {
            //当有任务缓存，取任务缓存的值
            upGroupStreamStat.setLastStatTime(cache.getLastUpdateTime());
        }
        //集成流数据实时查询
        int enableStreamCount = adminSyncPloyDetailDao.countEnableByDcId(templateId, dcId);
        int allStreamCount = adminSyncPloyDetailDao.countByDcId(templateId, dcId);
        upGroupStreamStat.setStreamCount(allStreamCount);
        upGroupStreamStat.setEnableStreamCount(enableStreamCount);
        return new Result<>(upGroupStreamStat);
    }

    @Override
    public Result<RedoSyncRecord> retryFailedData(String tenantId, String groupId, @Nullable List<String> downstreamIds) {
        //加锁
        String lockKey = CommonConstant.REDIS_LOCK_DOWN_STREAM_RETRY + tenantId + ":" + groupId;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.tryLock()) {
            try {
                if (CollUtil.isEmpty(downstreamIds)) {
                    GetDownstreamEnterprise.StatArg statArg = new GetDownstreamEnterprise.StatArg();
                    statArg.setId(groupId);
                    statArg.setPageSize(10000);
                    statArg.setSyncDataStatus(2);
                    statArg.setFillEnterpriseInfo(false);
                    GetDownstreamEnterprise.StatResult statResult = getDownstreamEnterpriseStat(tenantId, statArg).safeData();
                    downstreamIds = statResult.getDataList().stream().map(v -> v.getTenantId()).distinct().collect(Collectors.toList());
                }
                RelationManageGroupEntity groupEntity = relationManageGroupDao.getById(tenantId, groupId);
                String dcId = groupEntity.getDcId();
                String groupName = groupEntity.getName();
                String firstTenant = downstreamIds.get(0);
                List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantIdAndStatus(firstTenant, SyncPloyDetailStatusEnum.ENABLE.getStatus());
                String lastErrorResult = null;
                int successCount = 0, failedCount = 0;
                for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
                    List<String> sourceObjs = new ArrayList<>();
                    sourceObjs.add(syncPloyDetailEntity.getSourceObjectApiName());
                    if (syncPloyDetailEntity.getDetailObjectMappings() != null) {
                        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailEntity.getDetailObjectMappings()) {
                            sourceObjs.add(detailObjectMapping.getSourceObjectApiName());
                        }
                    }
                    for (String sourceObj : sourceObjs) {
                        //逐个企业处理
                        for (String downstreamId : downstreamIds) {
                            try {
                                SyncDataMappingListByPloyDetailIdArg arg = new SyncDataMappingListByPloyDetailIdArg();
                                arg.setPloyDetailId(syncPloyDetailEntity.getId());
                                arg.setSourceObjectApiName(sourceObj);
                                arg.setStatus(2);//只重试失败的
                                Result<RedoSyncRecord> result = adminSyncDataMappingService.redoSyncDataByPloyDetailId(downstreamId, dcId, arg, I18nUtil.getLocaleFromTrace());
                                log.info("redo sync data result:{}", result);
                                if (result.isSuccess()) {
                                    RedoSyncRecord redoSyncRecord = result.getData();
                                    successCount += redoSyncRecord.getSuccessCount();
                                    failedCount += redoSyncRecord.getFailedCount();

                                } else {
                                    lastErrorResult = result.getErrMsg();
                                }
                            } catch (Exception e) {
                                log.error("redo sync data error", e);
                                lastErrorResult = ExceptionUtil.getMessage(e);
                            }
                        }
                    }
                }
                String title = I18NStringEnum.kjcpttz.indexedFormat();
                String msg = I18NStringEnum.kdgqyzplcs.indexedFormat(groupName);
                aggDownstreamNotify2TemplateScheduleService.sendMsg2ErpDssRole(tenantId, title, msg, AlarmLevel.IMPORTANT);
                RedoSyncRecord redoSyncRecord = new RedoSyncRecord();
                redoSyncRecord.setTenantCount(downstreamIds.size());
                redoSyncRecord.setSuccessCount(successCount);
                redoSyncRecord.setFailedCount(failedCount);
                redoSyncRecord.setLastError(lastErrorResult);
                return Result.newSuccess(redoSyncRecord);
            } finally {
                lock.unlock();
            }
        } else {
            String title = I18NStringEnum.kjcpttz.indexedFormat();
            String msg = I18NStringEnum.kscplcswwc.indexedFormat();
            aggDownstreamNotify2TemplateScheduleService.sendMsg2ErpDssRole(tenantId, title, msg, AlarmLevel.IMPORTANT);
            return Result.newError(msg);
        }
    }

    @Override
    public Result<GetDownstreamEnterprise.StatResult> getDownstreamEnterpriseStat(String tenantId, GetDownstreamEnterprise.StatArg arg) {
        GetDownstreamEnterprise.StatResult statResult = new GetDownstreamEnterprise.StatResult();

        String groupId = arg.getId();
        RelationManageGroupEntity groupEntity = relationManageGroupDao.getById(tenantId, groupId);
        if (groupEntity == null) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }
        String dcId = groupEntity.getDcId();

        //统计的数据，必须在组更新之后。
        long time7DaysAge = DateUtil.date().offset(DateField.DAY_OF_YEAR, -7).getTime();
        long updateTime = ObjectUtil.defaultIfNull(groupEntity.getUpdateTime(), 0L);
        Date minStatDate = new Date(Long.max(time7DaysAge, updateTime));

        //获取进行中任务
        StreamStatProgress cache = getDownstreamStatProgressByCache(tenantId, groupId, updateTime);
        String traceId;
        //缓存的时间得在分组更新时间之后
        if (cache != null && cache.getBeginTime() > minStatDate.getTime()) {
            //有任务缓存时，取进行中任务的traceId
            //上次任务异常了，则返回异常信息
            if (StrUtil.isNotBlank(cache.getExceptionMsg())) {
                statResult.setExceptionMsg(cache.getExceptionMsg());
                //已完成
                statResult.setStatStatus(1);
            }
            traceId = cache.getTraceId();
            statResult.setLastStatTime(cache.getLastUpdateTime());
            statResult.setExpectTotal(cache.getTotal());
        } else {
            //没有任务缓存，则取最新的任务
            GroupStreamStat streamStat = groupStreamStatDao.getLatestTraceIdAndTime(tenantId, dcId, minStatDate);
            //没有最新的，为空
            if (streamStat == null) {
                //无数据
                statResult.setStatStatus(3);
                return Result.newSuccess(statResult);
            }
            traceId = streamStat.getTraceId();
            statResult.setLastStatTime(streamStat.getCreateTime().getTime());
        }
        Set<String> filterTenantIds = null;
        //查询名称
        if (StringUtils.isNotBlank(arg.getQueryStr())) {
            //先查出企业Id
            List<String> allDownstreamIds = relationErpShardDao.getAllDownstreamIdsByGroupId(groupId, RelationErpShardStatusEnum.normal.getStatus());
            final Map<String, String> enterpriseNameMap = userCenterService.batchGetEnterpriseName(allDownstreamIds);
            filterTenantIds = enterpriseNameMap.entrySet().stream()
                    .filter(entry -> entry.getValue().contains(arg.getQueryStr()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            if (filterTenantIds.isEmpty()) {
                //无数据
                statResult.setStatStatus(3);
                return Result.newSuccess(statResult);
            }
        }
        //根据traceId获取该次结果，支持分页、排序，根据企业聚合
        List<OrderBy> orderBys = arg.getOrderByList();
        //默认值
        if (CollUtil.isEmpty(orderBys)) {
            orderBys = ListUtil.of(OrderBy.of(GroupStreamStat.Fields.alertingStreamCount, false));
        }
        Page<GroupStreamStat> groupStreamStatsPage = groupStreamStatDao.calDownstreamStat(tenantId, dcId, traceId, minStatDate, orderBys, arg.getPageSize(), arg.calOffset(), arg.getAlertStatus(), arg.getSyncDataStatus(), filterTenantIds);

        List<DownstreamRelationManage> downstreamRelationManages = groupStreamStatsPage.getData().stream()
                .map(v -> DownstreamRelationManage.builder()
                        .tenantId(v.getDownstreamId())
                        .groupStreamStat(v).build()).collect(Collectors.toList());
        int total = (int) groupStreamStatsPage.getTotalNum();
        if (cache == null) {
            //没有进行中任务，表里是多少就是多少
            statResult.setExpectTotal(total);
        }
        if (StrUtil.isBlank(statResult.getExceptionMsg()) && total < statResult.getExpectTotal()) {
            //未完成
            statResult.setStatStatus(2);
        }
        statResult.setTotal(total);
        statResult.setDataList(downstreamRelationManages);
        if (arg.isFillEnterpriseInfo()) {
            if (!downstreamRelationManages.isEmpty()) {
                //填充企业名称
                final List<String> collect = downstreamRelationManages.stream()
                        .map(DownstreamRelationManage::getTenantId)
                        .distinct()
                        .collect(Collectors.toList());
                final Map<String, SimpleEnterpriseData> enterpriseMap = userCenterService.batchGetSimpleEnterprise(collect);
                for (DownstreamRelationManage downstreamRelationManage : downstreamRelationManages) {
                    final SimpleEnterpriseData simpleEnterpriseData = enterpriseMap.get(downstreamRelationManage.getTenantId());
                    downstreamRelationManage.setEnterpriseAccount(simpleEnterpriseData.getEnterpriseAccount());
                    downstreamRelationManage.setEnterpriseName(simpleEnterpriseData.getEnterpriseName());
                }
            }
        }
        return new Result<>(statResult);
    }


}
