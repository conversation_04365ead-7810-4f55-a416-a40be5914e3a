package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 11:21 2022/5/13
 * @Desc:后端all环境
 */
@AllArgsConstructor
@Getter
public enum ErpSyncDataBackStageEnvironmentEnum {

    //vip环境
    VIP("vip","Vip"),
    //jacoco环境
    JACOCO("jacoco","Jacoco"),
    //gray环境
    GRAY("gray","Historydata"),
    //normal环境
    NORMAL("normal",null),
    ;
    /**
     * 环境名称
     */
    private String environment;
    /**
     * dubbo后缀
     */
    private String environmentSuffix;

    public static ErpSyncDataBackStageEnvironmentEnum getEnvironmentEnum(String environment){
        for (ErpSyncDataBackStageEnvironmentEnum theEnv : ErpSyncDataBackStageEnvironmentEnum.values()) {
            if (theEnv.getEnvironment().equals(environment)) {
                return theEnv;
            }
        }
        return null;
    }
}
