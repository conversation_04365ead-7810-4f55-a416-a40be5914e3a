package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 14:32 2020/10/10
 * @Desc:
 */
@Data
@ApiModel
public class QueryResult<T> implements Serializable {
    @ApiModelProperty("实际总记录数")
    public Integer total=0;
    @ApiModelProperty("当前页码")
    private Integer pageNum=1;
    @ApiModelProperty("每页数量")
    private Integer pageSize;
    @ApiModelProperty("数据")
    public T dataList;
}
