package com.fxiaoke.open.erpsyncdata.preprocess.model.node;

import cn.hutool.core.collection.CollUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.NodeDataStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@SuppressWarnings("unchecked")
@Getter
@Setter
@ToString
@Slf4j
@Accessors(chain = true)
public abstract class NodeContext<T> {
    private String tenantId;
    private String dcId;
    //允许设置多个集成流
    private List<String> streamIds;

    public abstract Integer getSourceTenantType();

    @ToString.Exclude
    private List<NodeStatusRecord> records;

    /**
     * 起始时间
     */
    private Long startTime;

    public int getAllCount() {
        return records == null ? 1 : records.stream().mapToInt(v -> v.getCount()).sum();
    }

    public T setBasic(String tenantId, String dcId) {
        //继承类可实现set方法
        setTenantId(tenantId);
        setDcId(dcId);
        return (T) this;
    }

    public T setStreamId(String streamId) {
        setStreamIds(Collections.singletonList(streamId));
        return (T) this;
    }

    public List<String> getStreamIds() {
        if (streamIds == null) {
            return Collections.emptyList();
        }
        return streamIds;
    }

    public String getStreamId() {
        return CollUtil.getLast(getStreamIds());
    }

    public void addStatusRecord(NodeDataStatus status, String id, Integer count) {
        addStatusRecord(status, id, count, null);
    }

    public void addStatusRecord(NodeDataStatus status, String id, Integer count, Long tpm) {
        if (records == null) {
            records = new ArrayList<>();
        }
        if (records.size() > 10000) {
            //这应该出问题了
            log.error("records is too big,{}", this);
            return;
        }
        NodeStatusRecord nodeStatusRecord = new NodeStatusRecord()
                .setStatus(status)
                .setMainDataId(id)
                .setCount(count == null ? 1 : count)
                .setLimitTpm(tpm == null ? 0 : tpm);
        records.add(nodeStatusRecord);
    }
}
