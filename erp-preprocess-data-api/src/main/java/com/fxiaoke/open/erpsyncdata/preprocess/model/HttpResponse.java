package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.Data;
import okhttp3.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
@Data
public class HttpResponse {

    private int code;

    private String message;

    private String body;

    public HttpResponse(Response response) throws IOException {
        this.code = response.code();
        this.message = response.message();
        this.body = response.body() != null ? response.body().string() : "";
    }
}
