package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;

/**
 * <AUTHOR>
 * @Date: 15:06 2023/4/19
 * @Desc: 附件
 */
public interface AttachmentsService {
    Result2<AttachmentsResult> downAndUploadAttachments(@ContextEi("?.getTenantId()") AttachmentsArg attachmentsArg);
}
