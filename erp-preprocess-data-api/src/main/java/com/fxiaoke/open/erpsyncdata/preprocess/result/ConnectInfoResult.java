package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 19:51 2020/8/17
 * @Desc:
 */
@Data
@ApiModel
public class ConnectInfoResult implements Serializable {
    @ApiModelProperty("是否存在绑定")
    public boolean isBind = false;
    @ApiModelProperty("数据id")
    public String id;
    //    @ApiModelProperty("企业id")
//    public String tenantId; //
    @ApiModelProperty("数据中心名称")
    public String dataCenterName;
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8,MARKETING_ZHIHU")
    public ErpChannelEnum channel;
    @ApiModelProperty("企业名称")
    public String enterpriseName;
    @ApiModelProperty("连接参数")
    public ConnectParams connectParams;
    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;
    @ApiModelProperty("连接器名称,当不为空时，优先使用这个")
    private String connectorName;
    @ApiModelProperty("连接器编码,企业内唯一健")
    private Integer number;
    /**
     * 授权异常信息
     */
    private String bindExceptionMsg;

    //默认是channel，apiName有值时是apiName
    private String connectorKey;

    /**
     * 防止前端漏传
     */
    public void setConnectorKey(String connectorKey) {
        this.connectorKey = connectorKey;
        if (channel == null && connectorKey != null) {
            channel = AllConnectorUtil.getByConnectorKey(connectorKey).getChannel();
        }
    }

    /**
     * 防止前端漏传
     */
    public void setChannel(ErpChannelEnum channel) {
        this.channel = channel;
        if (connectorKey == null && channel != null) {
            connectorKey = channel.name();
        }
    }

    public void setConnectorNameIfAbsent(String connectorName) {
        if (this.connectorName == null) {
            this.connectorName = connectorName;
        }
    }

    public BaseConnectParam getBaseConnectParam() {
        return channel.getConnectParam(connectParams);
    }

    @Data
    @ApiModel
    public static class ConnectParams implements Serializable {
        @ApiModelProperty("u8连接参数")
        public U8ConnectParam u8;
        @ApiModelProperty("sap连接参数")
        public SapConnectParam sap;
        @ApiModelProperty("k3Cloud连接参数")
        public K3CloudConnectParam k3Cloud;
        @ApiModelProperty("k3Cloud旗舰版连接参数")
        public K3UltimateConnectParam k3CloudUltimate;
        @ApiModelProperty("标准渠道连接参数")
        public StandardConnectParam standard;
        @ApiModelProperty("U8EAI连接参数")
        public U8EaiConnectParam u8Eai;
        @ApiModelProperty("DB连接代理连接参数")
        public DBProxyConnectParam dbProxy;
        @ApiModelProperty("营销通知乎连接参数")
        public ZhiHuConnectParam zhiHu;
        @ApiModelProperty("营销通推送通用连接参数")
        public YxtCommonConnectParam yxtCommon;
        @ApiModelProperty("云星辰连接参数")
        public JdyConnectParam jdy;
        @ApiModelProperty("Facebook账号信息(不包含token)")
        public Oauth2ConnectParam oauthInfo;
        @ApiModelProperty("企微连接参数")
        public QYWXConnectParam qywx;
        @ApiModelProperty("飞书连接参数")
        public FeiShuConnectParam feiShu;
    }
}
