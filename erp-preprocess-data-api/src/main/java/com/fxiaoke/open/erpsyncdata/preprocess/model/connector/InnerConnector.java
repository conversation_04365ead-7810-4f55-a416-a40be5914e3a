package com.fxiaoke.open.erpsyncdata.preprocess.model.connector;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import lombok.Value;

/**
 * <AUTHOR> (^_−)☆
 */
@Value
public class InnerConnector implements Connector{
     ErpChannelEnum channel;

    @Override
    public String getDefaultName() {
        return getChannel().getDefaultName();
    }

    @Override
    public String getNameI18nKey() {
        return getChannel().getI18nKey();
    }

    @Override
    public String getKey() {
        return getChannel().name();
    }

    @Override
    public String getModuleCode() {
        return channel.getModuleCode();
    }

    @Override
    public Integer getConnectorId() {
        return getChannel().getChannelId();
    }

    @Override
    public ConnectorHandlerType getConnectorHandlerType() {
        return ConnectorHandlerType.INNER;
    }
}
