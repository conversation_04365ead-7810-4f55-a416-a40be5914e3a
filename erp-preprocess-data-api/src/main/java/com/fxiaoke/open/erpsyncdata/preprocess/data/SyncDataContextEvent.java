package com.fxiaoke.open.erpsyncdata.preprocess.data;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.BaseResult;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.data.CompleteDataWriteMqData.OUT_ERROR_CODE;
import static com.fxiaoke.open.erpsyncdata.preprocess.data.CompleteDataWriteMqData.SUCCESS_CODE;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class SyncDataContextEvent extends NodeContext<SyncDataContextEvent> implements RAMEstimable {

    protected int errCode = ResultCodeEnum.SUCCESS.getErrCode();
    protected String errMsg = ResultCodeEnum.SUCCESS.getErrMsg();

    public boolean isSuccess() {
        return this.errCode == ResultCodeEnum.SUCCESS.getErrCode();
    }


    /**
     * erp数据结构
     */
    /**
     * 源数据时间类型，主从对象数据共用
     */
    private Integer sourceEventType;
    /**
     * 源数据,会变化
     */
    private ObjectData sourceData;
    /**
     * 明细数据
     * key: 从对象entry(erp_object中可以查到绑定的erp拆分对象名)， value：明细数据
     */
    private Map<String, List<ObjectData>> detailData = new HashMap<>();//源对象的erpObjectDetail

    private String syncLogId;

    private Long dataVersion;

    private Map<String,List<String>> needReturnField;//需要返回的字段
    /**
     * 链路记录的apiname。不能修改
     */
    private String skyWalkingApiName;
    /**
     * 数据接收方式
     *
     * @see DataReceiveTypeEnum
     */
    private Integer dataReceiveType=DataReceiveTypeEnum.OTHER.getType();

    /**
     * sourceObjectData中为null的字段名称
     * 因为传输过程中会丢失，所以需要记录
     */
    private List<String> cleanFields = new ArrayList<>();

    private String sourceContextUserId;//用户id

    /**
     * 多语
     */
    private String locale;
    /**
     * 数据优先级
     */
    private Integer priority;

    /**
     * 数据分发后，主流程同步-------------------------------------------------------
     * 可能是null
     */
    private String ployDetailSnapshotId;

    private Integer sourceTenantType;

    private Boolean syncDependForce;

    private List<Long> dataVersionList;//聚合的所有版本

    private Long delayDispatcherTime;

    private String sourceDataCenterId;

    private String destDataCenterId;

    /**
     * 不太敢随便删字段了，，，怕有aop反射取值。。。
     */
    private String syncDataId;
    private String destTenantId;

    /**
     * 目标数据名称，写后赋值
     */
    private String destDataNameAfterWrite;

    public SyncDataContextEvent setTenantId(String tenantId) {
        super.setTenantId(tenantId);
        this.destTenantId = tenantId;
        this.sourceTenantId = tenantId;
        return this;
    }

    public void setSourceTenantId(String tenantId) {
        setTenantId(tenantId);
    }

    public void setDestTenantId(String tenantId) {
        setTenantId(tenantId);
    }

    private Integer destTenantType;
    //主对象更新事件
    private Boolean masterUpdateEvent;
    private String sourceMasterId;

    /**
     * @return 主数据Id，当存在的时候，否则返回当前数据Id
     */
    public String getMainIdIfExist() {
        return sourceMasterId == null ? getDataId() : sourceMasterId;
    }

    /**
     * 中间对象apiName
     */
    private String destObjectApiName;
    private ObjectData updateData;
    private Boolean isMatchUpdateData;
    //可能为null
    private String syncPloyDetailSnapshotId;
    private String mainObjApiName;//源主对象apiName

    public boolean isMainObj() {
        //是否主对象同步
        return Objects.equals(mainObjApiName, objectApiName);
    }

    @Override
    public SyncDataContextEvent setStreamId(String streamId) {
        super.setStreamId(streamId);
        this.syncPloyDetailId = streamId;
        return this;
    }

    //crm对象apiName
    private String crmObjApiName;
    //ERP真实apiName(主对象)
    private String outSideObjApiName;
    private MasterMappingsData masterMappingsData;
    private Map<String, List<ObjectData>> detailObjectDatasMap = Maps.newHashMap();


    /**
     * 同步字段转换处理-----------------------------------------------------
     */
    private SyncDataData syncDataData;
    private Map<String, SyncDataData> detailSyncDataDataMap;

    /**
     * --------------------------------------------------
     * 完成字段转换->同步中函数
     */
    private String sourceTenantId;
    private Integer destEventType;
    private String destDataId;
    private ObjectData destData;
    /**
     * key:中间表映射id
     * value: 从对象数据
     */
    private LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
    private LinkedHashMap<String, String> destDetailObjMasterDetailFieldApiName;//目标明细对象对应的主从字段:<对象apiName,主从字段apiName>

    /**
     * 终止向后继续执行
     */
    private Boolean stop = false;

    /**
     * stop的时候不记录日志
     */
    private boolean ignoreLogWhenStop = false;

    /**
     * 开始写目标系统-------------------------------------------------
     */
    /**
     * requestId
     */
    private String requestId;
    /**
     * key:syncDataId
     */
    private Map<String, SimpleSyncData> syncDataMap;

    /**
     * 批量写时使用，序列化成string传输syncData
     */
    private String syncDataEntityStr;

    /**
     * 批量写时使用
     */
    private SyncLogBaseInfo syncLogBaseInfo;
    private String objectApiName;//源对象apiName
    private String dataId;//源数据id
    private Long version;//源数据版本
    private String syncPloyDetailId;//集成流id

    /**
     * 回写节点-------------------------------------------------------------------------------------------
     * 同步后函数
     */

    private WriteResult writeResult;
    private List<WriteResult> detailWriteResults = new ArrayList<>();

    /**
     * 同步状态
     */
    private boolean finish;
    private String msg;

    private Boolean debugRecordIfDetailCauseMaterSync = false;

    private DataNodeNameEnum currentDataNodeName;

    /**
     * 匹配的字段，仅记录一个就停止记录
     */
    private String matchField;

    /**
     * 所有对象数据计数，仅ERP来源有值
     */
    private Integer allObjCount;

    public int getAllObjCount() {
        return allObjCount == null ? 1 : allObjCount;
    }

    public String tryGetSnapshotId() {
        if (getPloyDetailSnapshotId() != null) {
            return getPloyDetailSnapshotId();
        }
        //这两个都是null就不知道咋整了。
        return getSyncPloyDetailSnapshotId();
    }

    public static SyncDataContextEvent convertByCrmObject(com.fxiaoke.crmrestapi.common.data.ObjectData crmObjData, EventTypeEnum eventType, String syncLogId, String ployDetailSnapshotId) {
        ObjectData objectData = ObjectData.convert(crmObjData);
        return convertByCrmObject(objectData, eventType, syncLogId, ployDetailSnapshotId);
    }

    public static SyncDataContextEvent convertByCrmObject(ObjectData crmObjData, EventTypeEnum eventType, String syncLogId, String ployDetailSnapshotId) {
        SyncDataContextEvent eventData = new SyncDataContextEvent();
        eventData.setSourceData(crmObjData);
        eventData.setSourceEventType(eventType.getType());
        eventData.setSourceTenantType(TenantType.CRM);
        eventData.setSyncLogId(syncLogId);
        eventData.setDataReceiveType(DataReceiveTypeEnum.PAAS_META_EVENT.getType());
        eventData.setPloyDetailSnapshotId(ployDetailSnapshotId);

        eventData.fillCrmCleanFields();
        return eventData;
    }

    public void fillCrmCleanFields() {
        setCleanFields(crmCleanFields());
    }

    public List<String> crmCleanFields() {
        return MapUtils.emptyIfNull(sourceData).entrySet().stream()
                .filter(entry -> Objects.isNull(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    public void addCrmCleanFields() {
        if (Objects.isNull(sourceData) || CollectionUtils.isEmpty(getCleanFields())) {
            return;
        }

        // 将null的字段加回来
        getCleanFields().forEach(field -> getSourceData().put(field, null));
        getCleanFields().clear();
    }

    @Override
    public long ramBytesUsed(int depth) {
        // 暂时只在批量写的时候使用,所以没有计算写后的字段
        return RamUsageEstimateUtil.sizeOfObject(sourceData, depth) +
                RamUsageEstimateUtil.sizeOfObject(detailData, depth) +
                RamUsageEstimateUtil.sizeOfObject(updateData, depth) +
                RamUsageEstimateUtil.sizeOfObject(detailObjectDatasMap, depth) +
                RamUsageEstimateUtil.sizeOfObject(destDetailSyncDataIdAndDestDataMap, depth) +
                RamUsageEstimateUtil.sizeOfObject(destData, depth) +
                RamUsageEstimateUtil.sizeOfObject(syncDataData, depth) +
                RamUsageEstimateUtil.sizeOfObject(detailSyncDataDataMap, depth) +
                RamUsageEstimateUtil.sizeOfObject(syncDataEntityStr, depth);
    }

    // 序列化忽略的大数据字段
    private static PropertyFilter bigDataPropertyfilter = (source, name, value) ->
            !StrUtil.equalsAny(name,
                    Fields.sourceData,
                    Fields.detailData,
                    Fields.updateData,
                    Fields.detailObjectDatasMap,
                    Fields.destDetailSyncDataIdAndDestDataMap,
                    Fields.destData,
                    Fields.syncDataEntityStr,
                    Fields.syncDataData,
                    Fields.detailSyncDataDataMap);
    /**
     * 不会复制大内存字段
     */
    public SyncDataContextEvent deepCopyIgnoreBigData() {
        String json = JSON.toJSONString(this, bigDataPropertyfilter);
        SyncDataContextEvent newObj = JSON.parseObject(json, SyncDataContextEvent.class,
                JSON.DEFAULT_PARSER_FEATURE & ~Feature.UseBigDecimal.getMask());
        return newObj;
    }


    @Data
    public static class WriteResult extends BaseResult {
        /**
         * 请不要get该属性做查表操作，保留是因为上层还无法一一去除。
         */
        private String syncDataId;
        private SimpleSyncData simpleSyncData;
        private String destDataId;
        private Map<String,Object> destReturnData;
        /**
         * 写成功才有值
         */
        private Map<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
    }

    /**
     * remark 涉及ObjectData
     * private ObjectData sourceData;
     * private ObjectData updateData;
     * private Map<String, List<ObjectData>> detailObjectDatasMap = Maps.newHashMap();
     * private LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
     * private Map<String,SimpleSyncData> syncDataMap;
     *ObjectData destData;
     *
     */

    /**
     * 各个节点状态的更新
     */
    public void start() {
        this.setStop(false);
    }

    /**
     * 继续后续节点
     */
    public SyncDataContextEvent next(){
        this.setStop(false);
        return this;
    }
    public SyncDataContextEvent nextButNotSetStop2False(){
        return this;
    }

    /**
     * 正常结束任务
     *
     * @param
     * @param stopMsg
     * @return
     */
    public SyncDataContextEvent finish(String stopMsg) {
        this.setStop(false);
        this.setFinish(true);
        this.setErrMsg(stopMsg);
        return this;
    }

    /**
     * 停止同步，并记录停止信息
     * 暂时只传入代码行号，代码会变动所以行号其实不准
     *
     * @param
     * @param stopMsg
     * @return
     */
    public SyncDataContextEvent stop(String stopMsg) {
        this.setStop(true);
        this.setMsg(stopMsg);
        return this;
    }

    public SyncDataContextEvent newSuccess(Integer destEventType, String syncDataId, String destDataId) {
        WriteResult writeResult = new WriteResult();
        writeResult.setErrCode(SUCCESS_CODE);
        writeResult.setErrMsg("success");
        writeResult.setDestDataId(destDataId);
        writeResult.setSyncDataId(syncDataId);
        this.setWriteResult(writeResult);
        return this;
    }

    public SyncDataContextEvent newError(Integer destEventType, String syncDataId, String errMsg) {

        WriteResult writeResult = new WriteResult();
        writeResult.setSyncDataId(syncDataId);
        writeResult.setErrCode(OUT_ERROR_CODE);
        writeResult.setErrMsg(errMsg);
        this.setWriteResult(writeResult);
        return this;
    }

    public SyncDataContextEvent newError(Integer destEventType, String syncDataId, Integer errCode, String errMsg) {

        WriteResult writeResult = new WriteResult();
        writeResult.setSyncDataId(syncDataId);
        writeResult.setErrCode(errCode);
        writeResult.setErrMsg(errMsg);
        this.setWriteResult(writeResult);
        return this;
    }

    public SyncDataContextEvent newError2(Integer errCode, String errMsg) {

        WriteResult writeResult = new WriteResult();
        writeResult.setSyncDataId(syncDataId);
        writeResult.setErrCode(errCode);
        writeResult.setErrMsg(errMsg);

        SimpleSyncData simpleSyncData = new SimpleSyncData();
        simpleSyncData.setSyncDataId(syncDataId);
        if(sourceData!=null) {
            simpleSyncData.setSourceObjectApiName(sourceData.getApiName());
            simpleSyncData.setSourceDataId(sourceData.getId());
        }
        if(StringUtils.isNotEmpty(destObjectApiName)) {
            simpleSyncData.setDestObjectApiName(destObjectApiName);
        }
        writeResult.setSimpleSyncData(simpleSyncData);

        this.setWriteResult(writeResult);
        return this;
    }

    public SyncDataContextEvent newError(WriteResult writeResult, List<WriteResult> detailWriteResults, String syncDataId, Collection<String> detailSyncDataIds, Integer errCode, String errMsg) {
        writeResult.setSyncDataId(syncDataId);
        writeResult.setErrCode(errCode);
        writeResult.setErrMsg(errMsg);
        if (detailSyncDataIds != null) {
            for (String detailSyncDataId : detailSyncDataIds) {
                WriteResult detailResult = new WriteResult();
                detailResult.setSyncDataId(detailSyncDataId);
                detailResult.setErrCode(errCode);
                detailResult.setErrMsg(errMsg);
                detailWriteResults.add(detailResult);
            }
        }
        this.setWriteResult(writeResult);
        this.setDetailWriteResults(detailWriteResults);
        return this;
    }

    @Override
    public String toString() { //toString太耗费cpu了, 精简一下
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"super\":\"")
                .append(super.toString()).append('\"');
        sb.append(",\"destObjectApiName\":\"")
                .append(destObjectApiName).append('\"');
        sb.append(",\"stop\":")
                .append(stop);
        sb.append(",\"objectApiName\":\"")
                .append(objectApiName).append('\"');
        sb.append(",\"dataId\":\"")
                .append(dataId).append('\"');
        sb.append(",\"writeResult\":")
                .append(writeResult);
        sb.append(",\"needReturnField\":")
                .append(needReturnField);
        sb.append('}');
        return sb.toString();
    }
}
