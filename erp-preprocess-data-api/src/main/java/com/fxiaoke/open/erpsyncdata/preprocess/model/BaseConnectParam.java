package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 10:08:59
 */
public interface BaseConnectParam extends Serializable {

    default String getBaseUrl() {
        return "";
    }

    default void setBaseUrl(String s) {

    }

    /**
     * 如果不为空 将覆盖连接器名称
     */
    default String getConnectorName(){
        return null;
    }


    default ConnectorHandlerType getConnectorHandlerType(){
        return ConnectorHandlerType.INNER;
    }

    default SystemParams getSystemParams() {
        return new SystemParams();
    }

    default void setSystemParams(SystemParams systemParams) {
        //不执行操作
    }

    default void setSystemName(String systemName) {
        //不执行操作
    }
    default void setApiName(String apiName) {
        //不执行操作
    }

    default String getApiName() {
        return null;
    }

    List<String> getPushDataApiNames();

    default String getSystemName() {
        //null的，在外层使用channel的名称
        return null;
    }

    void setPushDataApiNames(List<String> pushDataApiNames);

    default String getIconUrl(){
        return null;
    }
}
