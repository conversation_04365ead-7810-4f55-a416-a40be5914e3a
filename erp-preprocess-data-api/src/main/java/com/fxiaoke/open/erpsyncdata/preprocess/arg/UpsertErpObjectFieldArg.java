package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 更新新增字段参数
 * <AUTHOR> (^_−)☆
 * @date 2023/3/3
 */
@Data
@ApiModel
@Accessors(chain = true)
public class UpsertErpObjectFieldArg {
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel; //渠道，k3,sap,u8，其他
    @ApiModelProperty("对象apiName")
    public String erpObjectApiName;
    @ApiModelProperty("字段列表")
    public List<ErpObjectFieldResult> erpObjectFields;
}
