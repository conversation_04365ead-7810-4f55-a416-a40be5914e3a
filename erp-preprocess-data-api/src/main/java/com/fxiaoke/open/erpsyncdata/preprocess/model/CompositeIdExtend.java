package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.gson.Gson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/17
 */
@Data
public class CompositeIdExtend implements Serializable {
    private static final long serialVersionUID = 3987323644801681316L;

    /**
     * 设置这个字段是希望能不影响不使用复合Id情况的同步
     */
    @ApiModelProperty(value = "是否复合ID")
    private boolean composite = false;

    @ApiModelProperty(value = "复合字段列表，当composite=true时，该列表不可为空")
    private List<String> compositeFields;

    @ApiModelProperty(value = "字段值分隔符,默认值为#号：'#'")
    private String separator = "#";

    @ApiModelProperty(value = "目标apiName，查找关联字段使用")
    private String targetApiName;

    @ApiModelProperty(value = "调用ERP查看接口时使用的id字段，没有则取第一个")
    private String viewIdField;

    @ApiModelProperty(value = "调用ERP保存接口时使用的id字段，没有则取第一个")
    private String saveIdField;

    public int getSaveIdIndex() {
        if (StringUtils.isNotBlank(saveIdField) && compositeFields.contains(saveIdField)) {
            return compositeFields.indexOf(saveIdField);
        }
        return 0;
    }

    /**
     * id字段获取复合id
     *
     * @param extendValue
     * @return
     */
    public static CompositeIdExtend getByIdField(String extendValue) {
        if (StringUtils.isBlank(extendValue)) {
            return new CompositeIdExtend();
        }
        return new Gson().fromJson(extendValue, CompositeIdExtend.class);
    }

    /**
     * 查找关联字段字段获取复合id
     *
     * @param extendValue
     * @return
     */
    public static CompositeIdExtend getByReferenceField(String tenantId,String extendValue) {
        if (StringUtils.isBlank(extendValue)) {
            throw new ErpSyncDataException(I18NStringEnum.s226,tenantId);
        }
        if (extendValue.startsWith("{")) {
            return new Gson().fromJson(extendValue, CompositeIdExtend.class);
        }
        CompositeIdExtend compositeId = new CompositeIdExtend();
        compositeId.setTargetApiName(extendValue);
        return compositeId;
    }

    public List<String> splitIds(String id){
        return Splitter.on(this.getSeparator()).splitToList(id);
    }

    public String getSaveId(String id){
        if (this.isComposite()){
            List<String> splitIds = splitIds(id);
            //兼容直接传真实Id的情况   原来代码中柯南增加，，，
            if (splitIds.size() > 1) {
                return splitIds.get(this.getSaveIdIndex());
            }
        }
        return id;
    }

    /**
     * 组合Id
     */
    public String composId(Map<String,Object> objectData) {
        Joiner joiner = Joiner.on(this.getSeparator());
        List<Object> values = this.getCompositeFields().stream()
                .map(objectData::get).collect(Collectors.toList());
        String compositeId = joiner.skipNulls().join(values);
        return compositeId;
    }


    public String getMasterId(String mainId) {
        if (!composite) {
            return mainId;
        }
        //???
        if (StringUtils.isBlank(viewIdField)) {
            return mainId;
        }
        return getSaveId(mainId);
    }
}
