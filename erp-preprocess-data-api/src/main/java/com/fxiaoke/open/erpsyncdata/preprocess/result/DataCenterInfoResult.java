package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/6
 */
@Data
@ApiModel
public class DataCenterInfoResult {
    @ApiModelProperty("数据id")
    public String id;
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel;
    @ApiModelProperty("数据中心名称")
    public String dataCenterName;
    @ApiModelProperty("是否当前选中的数据中心")
    @Deprecated
    public boolean checked = false;
    @ApiModelProperty("是否配置连接信息")
    public boolean hasConnect = false;
    @ApiModelProperty("是否购买")
    public boolean hasPurchased = true;
    @ApiModelProperty("是否已初始化")
    public boolean hasInited;
    @ApiModelProperty("是否是erp渠道")
    public boolean hasErpChannel = true;
    @ApiModelProperty("连接器类型枚举")
    public ConnectorTypeEnum connectorType;
    @ApiModelProperty("连接器名称")
    public String connectorName;
    //默认是channel，aplClassName有值时是aplClassName
    public String connectorKey;
    /**
     * 图标url，可能只是一个path
     */
    public String iconUrl;
    /**
     * 应用市场id
     */
    private String applicationMarketId;

    public void setChannel(ErpChannelEnum channel) {
        this.channel = channel;
        if (connectorKey == null) {
            connectorKey = channel.name();
        }
    }
}
