package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 13:36 2020/8/19
 * @Desc:
 */
@Getter
public enum ErpObjInterfaceUrlEnum {
    create("新建", false, I18NStringEnum.s796.getI18nKey(),"create"),//创建
    createDetail("新建明细", false, I18NStringEnum.s873.getI18nKey(),"createDetail"),//创建明细
    update("编辑", false, I18NStringEnum.s797.getI18nKey(),"update"),//更新
    updateDetail("编辑明细", false, I18NStringEnum.s874.getI18nKey(),"updateDetail"),//更新明细
    queryMasterBatch("批量查询修改", true, I18NStringEnum.s875.getI18nKey(),"queryMasterBatch"),//批量查询
    queryMasterById("通过id查询", true, I18NStringEnum.s876.getI18nKey(),"queryMasterById"),//通过id查询
    push("推送(同步或异步)", true, I18NStringEnum.s877.getI18nKey(),null),//推送
    invalid("作废", false, I18NStringEnum.s365.getI18nKey(),"invalid"),
    invalidDetail("作废明细", false, I18NStringEnum.s878.getI18nKey(),"invalidDetail"),//作废明细
    subscribeEvent("事件订阅", false, I18NStringEnum.s2214.getI18nKey(),"subscribeEvent"),//事件订阅
    crmQuery(true),
    crmCreate(false),
    crmUpdate(false),
    crmInvalid(false),
    reverseWrite2Crm("回写crm", false, I18NStringEnum.s879.getI18nKey(),null),
    queryInvalid("批量查询作废", true, I18NStringEnum.s880.getI18nKey(), "queryInvalid"),//批量查询作废
    recover("恢复", false, I18NStringEnum.s879.getI18nKey(),"recover"),//恢复

    // =========== k3 start ========
    draft(false), //暂存
    submit(false), //提交
    unSubmit(false), //反提交
    audit(false), //审核
    unAudit(false), //反审核
    cancelAssign(false), //撤销
    delete("删除",false, I18NStringEnum.s376.getI18nKey(),"delete"), //删除
    enable(false), //启用
    disable(false), //禁用
    view(true),
    businessInfo(true),
    createXOrder(false),
    distribute(false),
    unDistribute(false),
    // =========== k3 end ========
    //登录接口
    login(true),
    //检查类接口
    check(true),
    //函数调用
    execute(true),

    listObjects("获取对象列表", false, I18NStringEnum.s1085.getI18nKey(), "listObjects"),
    getObjectTree("获取对象树", false, I18NStringEnum.s3301.getI18nKey(), "getObjectTree"),
    getObjectDesc("获取对象描述", false, I18NStringEnum.s1086.getI18nKey(), "getObjectDesc"),
    getConnectorIntro("获取连接器介绍", false, I18NStringEnum.s1087.getI18nKey(), "getConnectorIntro"),
    processUserInputSystemParams("加工连接器系统参数", false, I18NStringEnum.s3302.getI18nKey(), "processUserInputSystemParams"),
    getOAuth2AuthUrl("获取OAuth2授权URL", false, I18NStringEnum.s3300.getI18nKey(), "getOAuth2AuthUrl"),
    checkAuthStatus("检查授权状态",false,I18NStringEnum.s3305.getI18nKey(),"checkAuthStatus"),
    getAuthTypeList("获取授权方式列表",false,I18NStringEnum.s3307.getI18nKey(),"getAuthTypeList"),
    processPushData("处理推送数据",false,I18NStringEnum.s3308.getI18nKey(),"processPushData"),
    checkPushAuth("推送验证",false,I18NStringEnum.s3309.getI18nKey(),"checkPushAuth"),
    getObjsNeedPreset("获取需要预置的对象",false,I18NStringEnum.s3310.getI18nKey(),"getObjsNeedPreset"),
    getStreamInfoNeedPreset("获取需要预置的集成流",false,I18NStringEnum.s3311.getI18nKey(),"getStreamInfoNeedPreset"),
    executeGetToken("jdy获取token",false,I18NStringEnum.s3311.getI18nKey(),"executeGetToken"),
    webhook("webhook",false,I18NStringEnum.kwebhook.getI18nKey(),"webhook"),
    processChangeEmployeeMapping("修改人员绑定后处理",false,I18NStringEnum.kxgrybd.getI18nKey(),"processChangeEmployeeMapping"),
    queryList("指定字段查询云星辰对象列表",false,"queryList"),
    checkEnableStream("启用集成流时检查", true, "checkEnableStream"),

    ;
    private String NameDesc = "default";
    /**
     * true: read接口
     * false: write接口
     */
    private boolean read;

    private String i18nKey;

    /**
     * apl方法名称
     */
    private String methodName = "none";

    ErpObjInterfaceUrlEnum(final boolean read) {
        this.read = read;
    }

    ErpObjInterfaceUrlEnum(final String nameDesc, final boolean read, String methodName) {
        NameDesc = nameDesc;
        this.read = read;
        this.methodName = methodName;
    }

    ErpObjInterfaceUrlEnum(final String nameDesc, final boolean read, final String i18nKey,String methodName) {
        NameDesc = nameDesc;
        this.read = read;
        this.i18nKey = i18nKey;
        this.methodName = methodName;
    }

    public static final List<ErpObjInterfaceUrlEnum> erpObjApiTypes = Lists.newArrayList(create, update, push, queryMasterBatch, queryInvalid, queryMasterById, invalid, delete, invalidDetail);

    public static final Set<ErpObjInterfaceUrlEnum> crmTypes = ImmutableSet.of(crmQuery, crmCreate, crmUpdate, crmInvalid, reverseWrite2Crm);

    @Getter
    private static final List<String> readInterface = Arrays.stream(ErpObjInterfaceUrlEnum.values())
            .filter(ErpObjInterfaceUrlEnum::isRead)
            .map(ErpObjInterfaceUrlEnum::name)
            .collect(Collectors.toList());

    private static final List<String> writeInterfaceByCrm = Arrays.stream(ErpObjInterfaceUrlEnum.values())
            .filter(erpObjInterfaceUrlEnum -> !erpObjInterfaceUrlEnum.isRead())
            .map(ErpObjInterfaceUrlEnum::name)
            .collect(Collectors.toList());

    //            ERP没有回写节点
    private static final List<String> writeInterfaceByErp = Arrays.stream(ErpObjInterfaceUrlEnum.values())
            .filter(erpObjInterfaceUrlEnum -> !erpObjInterfaceUrlEnum.isRead() && !Objects.equals(erpObjInterfaceUrlEnum, reverseWrite2Crm))
            .map(ErpObjInterfaceUrlEnum::name)
            .collect(Collectors.toList());

    public static final Set<ErpObjInterfaceUrlEnum> k3cInterfaceList = Sets.newHashSet(ErpObjInterfaceUrlEnum.create,
            ErpObjInterfaceUrlEnum.update,
            ErpObjInterfaceUrlEnum.queryMasterBatch,
            ErpObjInterfaceUrlEnum.queryInvalid,
            ErpObjInterfaceUrlEnum.queryMasterById,
            ErpObjInterfaceUrlEnum.invalid,
            ErpObjInterfaceUrlEnum.delete);

    public static List<String> getWriteInterface(Integer sourceTenantType) {
        return Objects.equals(sourceTenantType, TenantTypeEnum.CRM.getType()) ?  writeInterfaceByCrm : writeInterfaceByErp;
    }

    public static String getNameDesc(ErpObjInterfaceUrlEnum url, I18NStringManager i18NStringManager, String lang, String tenantId) {
        if (url == null) {
            return null;
        }

        return url.getNameDesc();
    }
}
