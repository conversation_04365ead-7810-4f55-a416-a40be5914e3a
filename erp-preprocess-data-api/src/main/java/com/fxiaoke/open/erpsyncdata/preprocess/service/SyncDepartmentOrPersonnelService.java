package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AfterWritePersonnelMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AutoBindEmployeeMapping;

/**
 * <AUTHOR>
 * @Date: 10:35 2020/8/18
 * @Desc:
 */
public interface SyncDepartmentOrPersonnelService {
    /**
     * 写CRM人员部门数据后置动作
     * @param message
     * @return
     */
    void afterWriteDepartmentOrPersonnel2Crm(SyncDataContextEvent message);

    void autoBindEmployeeMapping(AutoBindEmployeeMapping.Arg arg);

    void afterWritePersonnelMapping(AfterWritePersonnelMapping.Arg arg);
    
    void allEmployeeAutoBindFields(String tenantId, String dcId, String erpUserIdField, String erpUserNameFiled);
}
