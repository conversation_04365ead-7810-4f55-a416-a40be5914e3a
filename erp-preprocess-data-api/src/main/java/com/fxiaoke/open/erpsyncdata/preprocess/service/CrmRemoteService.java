package com.fxiaoke.open.erpsyncdata.preprocess.service;


import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmRequestBaseParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.TriggerFlowConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchCreateObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.BatchUpdateObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.CrmObjectDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.gson.JsonObject;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
public interface CrmRemoteService {

    Result<List<ObjectData>>listByIdSelective(String tenantId, String objectApiName, Collection<String> ids, Collection<String> fields);

    /**
     * 创建对象
     * 
     * @param baseParam
     * @param objectDataMap
     * @param extParamMap
     * @return
     */
    Result<CrmObjectDataResult> createObjectData(CrmRequestBaseParam baseParam, Map<String, Object> objectDataMap,
                                         Map<String, Object> extParamMap);

    /**
     * 更新对象
     *
     * @param baseParam
     * @param dataMap
     * @param extParamMap
     * @return
     */
    Result<CrmObjectDataResult> updateObjectData(CrmRequestBaseParam baseParam, Map<String, Object> dataMap,
                                         Map<String, Object> extParamMap);

    Result<JsonObject> BOMObjTreeRelatedListV1(CrmRequestBaseParam baseParam, Map<String, Object> dataMap,
                                               Map<String, Object> extParamMap);

    Result<JsonObject> BomDeploy(CrmRequestBaseParam baseParam, Map<String, Object> dataMap,
                                               Map<String, Object> extParamMap);

    Result<JSONObject> checkModuleStatus(String tenantId, String key);
    /**
     * 批量创建对象
     *
     * @param baseParam
     * @param dataList
     * @param triggerFlowConfig
     * @return
     */
    BatchCreateObjectResult batchCreateObject(CrmRequestBaseParam baseParam, List<ObjectData> dataList, TriggerFlowConfig triggerFlowConfig);
    /**
     * 批量更新对象
     *
     * @param baseParam
     * @param dataList
     * @param triggerFlowConfig
     * @return
     */
    BatchUpdateObjectResult batchUpdateObject(CrmRequestBaseParam baseParam, List<ObjectData> dataList, TriggerFlowConfig triggerFlowConfig);

    Result2<List<ObjectData>> batchGetObjectData(String tenantId, String objectApiName, List<String> objectDataIds);
}
