package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/28 10:08:59
 */
public interface AfterWritePersonnelMapping {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg implements Serializable {
        private String tenantId;
        private String employeeObjectId;

        private String erpSplitObjectApiName;
        private String erpEmployeeId;
        public String erpEmployeeName;
    }
}
