package com.fxiaoke.open.erpsyncdata.preprocess.result;


import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpTaskStatusArgEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:35 2021/8/10
 * @Desc:
 */
@Data
@ApiModel
public class ErpHistoryDataTaskResult implements Serializable {
    @ApiModelProperty("数据id")
    public String id;
    /**
     * @see ErpHistoryDataTaskTypeEnum
     */
    @ApiModelProperty("任务类型:1.按时间，2.按ids, 3.k3自定义条件 4.crm数据范围")
    public Integer taskType;
    @ApiModelProperty("数据中心id")
    public String dataCenterId;
    @ApiModelProperty("渠道")
    public ErpChannelEnum channel;
    @ApiModelProperty("真实渠道")
    public String connectorKey;
    @ApiModelProperty("iconUrl")
    public String iconUrl;
    @ApiModelProperty("连接器名字")
    public String dataCenterName;
    @ApiModelProperty("任务编码")
    public String taskNum; //
    @ApiModelProperty("任务名称")
    public String taskName; //
    @ApiModelProperty("对象apiName")
    public String objApiName; //
    @ApiModelProperty("对象名称")
    public String objName; //
    @ApiModelProperty("真实对象apiName")
    public String realObjApiName; //
    @ApiModelProperty("真实对象名称")
    public String realObjName; //
    @ApiModelProperty("数据ids")
    public List<String> dataIds; //
    @ApiModelProperty("K3自定义条件")
    public String filterString;
    @ApiModelProperty("crm条件同步")
    public List<CrmFilters> crmFilters;
    @ApiModelProperty("开始时间")
    @Deprecated
    public Long startTime; //
    @ApiModelProperty("最后轮询开始时间")
    public Long lastQueryStartTime; //
    @ApiModelProperty("结束时间")
    @Deprecated
    public Long endTime; //
    /**
     * 时间段列表，key：开始时间，value，结束时间
     */
    @ApiModelProperty("时间段")
    public List<TimePeriod> timePeriods;
    /**
     * 在这个时间后才执行
     */
    @ApiModelProperty("执行时间")
    public Long executeTime;
    @ApiModelProperty("是否中断")
    public Boolean needStop; //
    @ApiModelProperty("每次获取条数")
    public Long limit; //
    @ApiModelProperty("偏移量")
    public Long offset; //
    @ApiModelProperty("任务状态(1.创建，2.开启，3执行中，4.异常，5.中断，6.结束（成功）)")
    public ErpHistoryDataTaskStatusEnum taskStatusEnum; //
    @ApiModelProperty("任务状态描述")
    public String taskStatusDesc; //
    @ApiModelProperty("累计轮询数据")
    public Long totalDataSize; //
    @ApiModelProperty("累计耗时（格式化）")
    public String totalCostTime; //
    @ApiModelProperty("备注")
    public String remark; //
    @ApiModelProperty("任务关联的集成流")
    public List<IntegrationSimpleViewInfoResult> integrationResults;

    @ApiModelProperty("任务筛选的状态列表项")
    /**
     * @see ErpTaskStatusArgEnum
     */
    public ErpTaskStatusArgEnum erpTaskStatusArgEnum; //
    @ApiModelProperty("traceId")
    public String traceId; //
    @ApiModelProperty("创建时间")
    public Long createTime; //
    @ApiModelProperty("更新时间")
    public Long updateTime; //
    @ApiModelProperty("创建人")
    public Integer creator; //
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorName;
    @ApiModelProperty("分片间隔（ms）")
    private Long splittingIntervalMs;
    @ApiModelProperty("数据优先级")
    private Integer priority;

    @AllArgsConstructor
    @Getter
    @Setter
    @ToString
    @NoArgsConstructor
    public static class TimePeriod{
        @ApiModelProperty("开始时间")
        private Long startTime;
        @ApiModelProperty("结束时间")
        private Long endTime;
    }

    @Data
    public static class CrmFilters {
        @ApiModelProperty("对象apiName")
        private String objectApiName;
        @ApiModelProperty("过滤条件")
        private List<List<FilterData>> filters;
    }
}
