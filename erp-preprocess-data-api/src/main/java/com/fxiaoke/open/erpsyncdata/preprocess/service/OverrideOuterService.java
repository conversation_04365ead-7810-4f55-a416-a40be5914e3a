package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.SyncDataDependData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.CompleteDataWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;

import java.util.List;
import java.util.Map;

/**
 * 对外提供重写方法
 */
public interface OverrideOuterService {
    /**
     * 根据主对象ID与过滤条件获取从对象
     *
     * @param sourceTenantId 源企业id
     * @param destTenantId 目标企业id
     * @param tenantType 企业类型
     * @param objectApiName 对象apiName（主、从）
     * @param masterFieldApiName 主对象FieldApiName
     * @param masterObjectId 主对象id
     * @param detailSyncConditionsData 从对象数据范围
     */
    Result2<List<ObjectData>> listDetailDatasByIdAndFilter(String sourceTenantId,
                                                           String destTenantId,
                                                           Integer tenantType,
                                                           String objectApiName,
                                                           String masterFieldApiName,
                                                           String masterObjectId,
                                                           SyncConditionsData detailSyncConditionsData,
                                                           Integer limit,
                                                           Integer offset,
                                                           String dataCenterId);

    /**
     * 是否需要处理该企业的paas mq
     * 用于控制灰度
     *
     * @param sourceTenantId 源企业
     * @return
     */
    Result2<Boolean> needConsumePaasObjectDataMq(String sourceTenantId);

    /**
     * 写入完成后更新写入状态
     * @param message
     * @return
     */
    Result2<Void> completeWriteUpdateStatus(CompleteDataWriteMqData message);

    /**
     *更新mongo中的erpTempData的状态和备注
     * @param tenantId
     * @param ployDetailId
     * @param mongoId
     * @param status
     * @param remark
     * @return
     */
    Result2<Void> updateErpTempDataByIds(String tenantId, String ployDetailId, String mongoId, Integer status, String remark);



    Result2<Void> saveInterfaceLog(String remark, SyncDataContextEvent syncDataContextEvent, Object arg, Object result, long callTime, long returnTime, int status);

    Result2<Void> saveDependData(String tenantId, SyncDataDependData syncDataDependData);

    Map<String,String> getObjectMainAttribute(String tenantId);

}
