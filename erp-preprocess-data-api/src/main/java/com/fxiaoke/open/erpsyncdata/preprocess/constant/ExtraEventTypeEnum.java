package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

/**
 * 在平台的EventTypeEnum扩展一些特殊类型
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/12/8
 * @see EventTypeEnum
 */
@AllArgsConstructor
@Getter
public enum ExtraEventTypeEnum {
    /*  以下复写原类型*/
    ADD(1, 1, "新增", I18NStringEnum.s652.getI18nKey()),
    UPDATE(2, 2, "更新", I18NStringEnum.s364.getI18nKey()),
    INVALID(3, 3, "作废", I18NStringEnum.s365.getI18nKey()),
    SYNC_MENU(4, 4, "手动同步", I18NStringEnum.s675.getI18nKey()),
    DELETE_DIRECT(5, 5, "直接删除", I18NStringEnum.s676.getI18nKey()),
    WAITTING(100, 100, "等待", I18NStringEnum.s680.getI18nKey()),
    INIT(101, 101, "初始化", I18NStringEnum.s681.getI18nKey()),
    DEPEND(102, 102, "依赖处理", I18NStringEnum.s682.getI18nKey()),

    /**
     * 手动新增，即不会主动轮询，需要手动调用轮询接口
     */
    PUSH_ADD(201, 1, "手动新增", I18NStringEnum.s886.getI18nKey()),
    /**
     * 手动更新，即不会主动轮询，需要手动调用轮询接口
     */
    PUSH_UPDATE(202, 2, "手动更新", I18NStringEnum.s887.getI18nKey()),
    /**
     * 手动作废，即不会主动轮询，需要手动调用轮询接口
     */
    PUSH_INVALID(203, 3, "手动作废", I18NStringEnum.s888.getI18nKey()),

    ;
    /**
     * 扩展类型
     */
    private int extraType;
    /**
     * 真实类型
     */
    private int realType;

    /**
     * 类型名称
     */
    private String name;

    private String i18nKey;

    /**
     * 只有新增、修改、作废、推送可以轮询
     */
    public static Set<Integer> permittedType = ImmutableSet.of(1,2,3,201,202,203);

    public static Map<Integer, Integer> TRANS_MAP =
            ImmutableMap.of(PUSH_ADD.extraType, PUSH_ADD.realType,
                    PUSH_UPDATE.extraType, PUSH_UPDATE.realType,
                    PUSH_INVALID.extraType,PUSH_INVALID.realType);
}
