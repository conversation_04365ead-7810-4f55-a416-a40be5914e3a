package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.CompleteEventTriggerArg;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 同步过程的结果类
 * 统一使用一个，方便做aop处理
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/12/28
 */
@Builder
@Getter
@Setter
public class SyncStepData {
    /**
     * 终止同步
     */
    private boolean stop;
    /**
     * 记录信息
     */
    private String msg;

    private CompleteEventTriggerArg completeEventTriggerArg;

    //参数共用DoProcessMqData
    private boolean checkSyncDataMapping;

    private boolean doProcess;
    private DoProcessMqData doProcessMqData;

    //参数共用DoProcessMqData、CompleteDataProcessMqData
    private boolean queryCrmObject2DestNode;

    private boolean doWrite;
    private DoWriteMqData doWriteMqData;

    private boolean completeDataProcess;
    private CompleteDataProcessMqData completeDataProcessMqData;

    //参数共用completeDataWriteMqData
    private boolean reverseWrite2Crm;

    private boolean completeDataWrite;
    private CompleteDataWriteMqData completeDataWriteMqData;

    private boolean isFinish;

    public static SyncStepData start(CompleteEventTriggerArg arg) {
        return SyncStepData.builder()
                .stop(false)
                .completeEventTriggerArg(arg)
                .build();
    }

    /**
     * 停止同步，并记录停止信息
     * 暂时只传入代码行号，代码会变动所以行号其实不准
     * @param stopMsg
     * @return
     */
    public static SyncStepData stop(String stopMsg) {
        return SyncStepData.builder()
                .stop(true)
                .msg(stopMsg)
                .build();
    }

    /**
     * 正常结束任务
     * @param stopMsg
     * @return
     */
    public static SyncStepData finish(String stopMsg) {
        return SyncStepData.builder()
                .stop(false)
                .isFinish(true)
                .msg(stopMsg)
                .build();
    }

    public static SyncStepData doCheckSyncDataMapping(DoProcessMqData doProcessMqData) {
        return SyncStepData.builder()
                .stop(false)
                .checkSyncDataMapping(true)
                .doProcessMqData(doProcessMqData).build();
    }
    public static SyncStepData doProcess(DoProcessMqData doProcessMqData) {
        return SyncStepData.builder()
                .stop(false)
                .doProcess(true)
                .doProcessMqData(doProcessMqData).build();
    }
    public static SyncStepData doQueryCrmObject2DestBySource(DoProcessMqData doProcessMqData,CompleteDataProcessMqData data) {
        return SyncStepData.builder()
                .stop(false)
                .queryCrmObject2DestNode(true)
                .doProcessMqData(doProcessMqData)
                .completeDataProcessMqData(data).build();
    }
    public static SyncStepData doWrite(DoWriteMqData doWriteMqData) {
        return SyncStepData.builder()
                .stop(false)
                .doWrite(true)
                .doWriteMqData(doWriteMqData).build();
    }

    public static SyncStepData completeDataProcess(CompleteDataProcessMqData data) {
        return SyncStepData.builder()
                .stop(false)
                .completeDataProcess(true)
                .completeDataProcessMqData(data).build();
    }
    public static SyncStepData completeDataWrite(CompleteDataWriteMqData data) {
        return SyncStepData.builder()
                .stop(false)
                .completeDataWrite(true)
                .completeDataWriteMqData(data).build();
    }
}
