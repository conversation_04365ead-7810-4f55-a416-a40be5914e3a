package com.fxiaoke.open.erpsyncdata.preprocess.model.connector;

import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

/**
 * 外部连接器，独立应用,不可变
 *
 * <AUTHOR> (^_−)☆
 */
@Data
@Jacksonized
@Builder
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor
public class OuterConnector implements Connector {

    /**
     * 唯一键
     */
    private String apiName;

    /**
     * 默认名称，i18n名称从apiName获取
     */
    private String defaultName;

    /**
     * 应用模块编码，用于与产品绑定
     */
    private String moduleCode;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 图标url
     */
    private String iconUrl;

    /**
     * 用于生成连接信息Num
     * jar包的使用10000+,
     */
    private Integer connectorId;

    /**
     * 安装码
     * @deprecated 改为使用applicationMarketId安装
     */
    @Deprecated
    private String installId;

    /**
     * 是否需要安装
     */
    private boolean needInstall;
    /**
     * 应用市场id，应用市场(应用)的数据ID
     */
    private String applicationMarketId;

    /**
     * 连接器处理类型
     */
    @ApiModelProperty(hidden = true)
    private ConnectorHandlerType connectorHandlerType;

    @Override
    public String getNameI18nKey() {
        return I18nUtil.buildKey("outconnector", apiName);
    }

    @Override
    public ErpChannelEnum getChannel() {
        return ErpChannelEnum.STANDARD_CHANNEL;
    }

    @Override
    public String getKey() {
        return getApiName();
    }
}
