package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
public class BatchSendEventDataArg implements Serializable {
    private List<EventData> eventDatas;
    private List<String> destTenantIds;

    public BatchSendEventDataArg (List<EventData> eventDatas) {
        this.eventDatas = eventDatas;
    }

    @Data
    public static class EventData implements Serializable {
        private String ployDetailSnapshotId;
        private Integer sourceEventType;
        private Integer sourceTenantType;
        private ObjectData sourceData;
        private Boolean syncDependForce;
        private String syncLogId;
        private List<Long> dataVersionList;//聚合的所有版本
        private Long dataVersion;//最后的版本
        private Long delayDispatcherTime;
        /**
         * @see com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum
         */
        private Integer dataReceiveType;//数据来源类型
        private String sourceContextUserId;//用户id
        /**
         * sourceObjectData中为null的字段名称
         * 因为传输过程中会丢失，所以需要记录
         */
        private List<String> cleanFields = new ArrayList<>();

        public static EventData newAddEventData(String sourceTenantId, String sourceObjectApiName, String sourceObjectId) {
            EventData eventData = new EventData();
            eventData.setSourceEventType(EventTypeEnum.ADD.getType());
            ObjectData souceData = new ObjectData();
            souceData.putApiName(sourceObjectApiName);
            souceData.putId(sourceObjectId);
            souceData.putTenantId(sourceTenantId);
            eventData.setSourceData(souceData);
            return eventData;
        }

        /**
         * 使用成熟的封装方法打印。自己写的方法不区分类型太不友好了。
         */
        @Override
        public String toString() {
            return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
                    .append("ployDetailSnapshotId", ployDetailSnapshotId)
                    .append("sourceEventType", sourceEventType)
                    .append("sourceTenantType", sourceTenantType)
                    .append("sourceData", sourceData)
                    .append("syncDependForce", syncDependForce)
                    .append("syncLogId", syncLogId)
                    .append("dataVersionList", dataVersionList)
                    .append("dataVersion", dataVersion)
                    .append("delayDispatcherTime", delayDispatcherTime)
                    .append("dataReceiveType", dataReceiveType)
                    .toString();
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"eventDatas\":")
                .append(eventDatas);
        sb.append(",\"destTenantIds\":")
                .append(destTenantIds);
        sb.append('}');
        return sb.toString();
    }
}
