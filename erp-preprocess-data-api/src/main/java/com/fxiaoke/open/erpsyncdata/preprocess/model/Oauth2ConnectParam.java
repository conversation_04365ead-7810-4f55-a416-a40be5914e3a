package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class Oauth2ConnectParam extends BaseLangConnectParam {
    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "连接参数")
    @SecurityField
    private String connectParam;

    @ApiModelProperty(value = "用户名")
    private Long expireTime;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

    @ApiModelProperty("facebook账号类型，1：个人账号，2：广告账号")
    private Integer type;

    @Override
    public String getBaseUrl() {
        return "";
    }
}
