package com.fxiaoke.open.erpsyncdata.preprocess.model.connector;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Jacksonized
@Builder
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor
public class HubInfo {

    private String name;

    private String baseUrl;

    private List<OuterConnector> outerConnectors;

    /**
     * 路由的企业Id，默认的hub为空
     */
    private Set<String> tenantIds;

    public boolean isDefault() {
        return Objects.equals(name, "default");
    }
}
