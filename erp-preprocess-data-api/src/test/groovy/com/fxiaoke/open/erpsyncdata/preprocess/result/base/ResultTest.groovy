package com.fxiaoke.open.erpsyncdata.preprocess.result.base


import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import spock.lang.Specification
import spock.lang.Unroll

import java.util.function.Function
import java.util.function.Supplier

/**
 * <AUTHOR> 
 * @date 2024/7/24 16:37:39
 */
class ResultTest extends Specification {

    void setup() {
        //mock i18n
        I18nClient i18nClient = Mock(I18nClient)
        I18nUtil._setI18ClientOnlySpring(i18nClient)
        i18nClient.get(_ as String, 0, _ as String, _ as String) >> { i18nKey, _, locale, defaultValue ->
            return "i18n"
        }
    }

    @Unroll
    def "测试ResultCode"() {
        when:
        def data = Result.newError(i18nEnum)

        then:
        data.getErrCode() == errorCode
        data.getErrMsg() == i18nEnum.getText()

        where:
        i18nEnum             || errorCode
        I18NStringEnum.s1    || "s306240001"
        I18NStringEnum.s12   || "s306240012"
        I18NStringEnum.s112  || "s306240112"
        I18NStringEnum.s1111 || "s306241111"

        I18NStringEnum.md1   || "s306240000"
    }


    @Unroll
    def "test 构造成功结果-#data"(def data, Function<Object, Result> dataFunc) {
        def newRes = dataFunc.apply(data)
        println(newRes)
        expect:
        ResultCodeEnum.SUCCESS.getErrCode() == newRes.getErrCode()
        ResultCodeEnum.SUCCESS.getText() == newRes.getErrMsg()
        data == newRes.getData()

        where:
        data                       | dataFunc
        null                       | { new Result<>() }
        null                       | { Result.newSuccess() }
        "string data"              | { new Result<>(it) }
        ["k": 1, "mapData": "lll"] | { new Result<>(it) }
        ["k": 1, "mapData": "lll"] | { Result.newSuccess(it) }
    }


    @Unroll
    def "test 构造失败结果-enum-#codeEnum"(ResultCodeEnum codeEnum, Supplier<Result> sup) {
        def newRes = sup.get()
        println(newRes)
        expect:
        codeEnum.getErrCode() == newRes.getErrCode()
        "i18n" == newRes.getErrMsg()// 都按i18n转换了
        null == newRes.getData()

        where:
        codeEnum                 | sup
        ResultCodeEnum.ERROR_MSG | { new Result<>(ResultCodeEnum.ERROR_MSG) }
        ResultCodeEnum.ERROR_MSG | { Result.newError(ResultCodeEnum.ERROR_MSG) }
    }


    @Unroll
    def "test 构造失败结果-带data-#codeEnum"(ResultCodeEnum codeEnum, def data, Supplier<Result> sup) {
        def newRes = sup.get()
        println(newRes)
        expect:
        codeEnum.getErrCode() == newRes.getErrCode()
        "i18n" == newRes.getErrMsg()// 都按i18n转换了
        data == newRes.getData()

        where:
        codeEnum                 | data   | sup
        ResultCodeEnum.ERROR_MSG | "data" | { Result.newErrorWithData(ResultCodeEnum.ERROR_MSG, "data") }
        ResultCodeEnum.ERROR_MSG | "data" | { Result.newErrorWithData(ResultCodeEnum.ERROR_MSG.getErrCode(), I18NStringEnum.s7, "data") }
    }
}
