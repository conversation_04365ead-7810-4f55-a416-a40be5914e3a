package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RetryDataEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RetrySendMqDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TimeUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.probe.service.RetryFailDataService;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @create 2024/5/20 19:52
 * @desc
 */
@Component
@Slf4j
public class RetryFailDataServiceImpl implements RetryFailDataService {

    @Autowired
    private AsyncReTryIfFailedManager asyncReTryIfFailedManager;
    @Autowired
    private RetrySendMqDao retrySendMqDao;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    private static ExecutorService executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("RetryFailDataService-%d").build();
        executorService = new ThreadPoolExecutor(2, 2, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), workerFactory);
    }

    @Override
    public Result<Integer> retryDataService(String tenantId,String retryType,Long startTime,Long endTime,List<Integer> status,Integer limit,Integer offset ) {
        executorService.submit( () ->{doReTrySendMq(tenantId,retryType,startTime,endTime,Lists.newArrayList(0,2),limit,offset);});
        return Result.newSuccess();
    }

    @Override
    public Result<Void> retryDataServiceHandler() {
        doReTrySendMq(null,null,null,null,Lists.newArrayList(0,2),null,null);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> retryDataServiceHandlerData(List<ReTrySendMq> reTrySendMqs) {
        try {
            retryDataDetail(reTrySendMqs);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Result<Integer> retryDataByIds(List<ObjectId> ids) {
        List<ReTrySendMq> sendMqs = retrySendMqDao.listByRetryIds(ids);
        boolean result = this.retryDataDetail(sendMqs);
        return Result.newSuccess(ids.size());
    }

    public Result2<Void> doReTrySendMq(String ei, String retryType, Long startTime, Long endTime, List<Integer> status, Integer limit, Integer offset) {
        boolean getEiLock = false;
        String lockName = CommonConstant.lockMongoReTryMq;
        try {
            //抢锁不等待。没抢到就等下一次触发
            getEiLock = redissonClient.getLock(lockName).tryLock();
        } catch (Exception e) {
            log.info("get lockMongoReTryMq lock  exception", e);
        }
        if (!getEiLock) {
            return Result2.newSuccess();
        }
        if(ObjectUtils.isEmpty(startTime)){
            startTime= 0L;
        }
        if(ObjectUtils.isEmpty(endTime)){
            endTime = System.currentTimeMillis() - 1000 * 60 * 9L;//;
        }
        if(ObjectUtils.isEmpty(limit)){
            limit=200;
        }
        if(ObjectUtils.isEmpty(offset)){
            offset= 0;
        }
        try {
            //避免重试的时候，依然会失败，重试10条数据都失败，那就跳过此任务。
            boolean retryData=true;

            for (int i = 0; i < 500&&retryData; i++) {
                List<ReTrySendMq> reTrySendMqs = retrySendMqDao.listRetrySendMqIdList(ei, retryType,startTime, endTime, status, limit,offset);
                log.info("reTryFromMongo start,ei={},startTime={},endTime={},status={},size:{}", ei, startTime, endTime, status,reTrySendMqs.size());
                ErpTenantConfigurationEntity configurationEntity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOne("all", "all", "all", TenantConfigurationTypeEnum.STOP_RETRY_DATA.name());
                if(ObjectUtils.isNotEmpty(configurationEntity)){
                    retryData= JSONObject.parseObject(configurationEntity.getConfiguration(),Boolean.class);
                }
                retryData=retryDataDetail(reTrySendMqs);
                if (reTrySendMqs.size() < limit) {
                    break;
                }
                if (i > 10) {
                    log.info("reTryFromMongo too many i={} pageSize={}", i, limit);
                }
            }
            log.info("reTryFromMongo end");
        } catch (
                Throwable t) {
            log.error("reTryFromMongo Throwable", t);
        } finally {
            log.info("lockMongoReTryMq relese lock");
            redissonClient.getLock(lockName).unlock();
        }
        return Result2.newSuccess();
    }
    public boolean retryDataDetail(List<ReTrySendMq> reTrySendMqs )  {
        boolean continueFail = false;//重试任务的数据发送，依然失败，那就不继续发送，等待下一次触发
        List<ObjectId> needUpdateSuccess = Lists.newArrayList();
        List<ObjectId> needUpdateFail = Lists.newArrayList();
        for (ReTrySendMq reTrySendMq : reTrySendMqs) {
            //根据类型type。匹配对应的方法执行。
            //        TraceUtil.initTrace(String.format("J-E.doReTrySendMq.-10000-erp%s", TimeUtil.hms()));
            //traceId是有一定规则的,不然一些组件解析会有问题
        TraceUtil.initTraceWithUser(reTrySendMq.getTenantId(), -10000, "J-E.%s.-10000-doReTrySendMq" + TimeUtil.hms());
            try {
                RetryDataEnum retryDataEnum = RetryDataEnum.valueOf(reTrySendMq.getRetryDataEnum());
                Object result = invokeMethod(retryDataEnum.getHandlerMethod(), reTrySendMq.getMqMsg());
                //不报错就更新成状态为1
                needUpdateSuccess.add(reTrySendMq.getId());
            } catch (Exception e) {
                needUpdateFail.add(reTrySendMq.getId());
                e.printStackTrace();
            }
        }
            if (CollectionUtils.isNotEmpty(needUpdateSuccess)) {
                retrySendMqDao.updateStatusById(needUpdateSuccess,1);
                needUpdateSuccess.clear();
            }
            if (CollectionUtils.isNotEmpty(needUpdateFail)) {
                continueFail = needUpdateFail.size() > 10;
                retrySendMqDao.updateStatusById(needUpdateFail, 2);
                needUpdateFail.clear();
            }
            return continueFail;
    }

    public Object invokeMethod(String fullMethodName, String msgData) throws Exception {
        // 分割类名和方法名
        int lastIndex = fullMethodName.lastIndexOf(".");
        String className = fullMethodName.substring(0, lastIndex);
        String methodName = fullMethodName.substring(lastIndex + 1);
        // 加载类
        Class<?> clazz = Class.forName(className);
        // 获取Spring管理的bean
        Object bean = applicationContext.getBean(clazz);
        // 获取方法，这里假设方法没有重载，即方法名唯一
        Method method = findMethod(clazz, methodName);
        //获取参数的类型
        Object result=null;
        Type listType = method.getGenericParameterTypes()[0];  // 获取方法参数的类型

        if (listType instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) listType;
            if (((Class<?>) parameterizedType.getRawType()).isAssignableFrom(List.class)){
                // 处理List类型的参数
                Type actualType = parameterizedType.getActualTypeArguments()[0];
                List<?> args = JSONArray.parseArray(msgData, (Class<?>) actualType);
                // 调用方法
                result = method.invoke(bean, args);
            }
        }else{
            Object args = JSONObject.parseObject(msgData, listType);
            // 调用方法
            result = method.invoke(bean, args);
        }
        return result ;
    }
    private Method findMethod(Class<?> clazz, String methodName) throws NoSuchMethodException {
        for (Method method : clazz.getMethods()) {
            if (method.getName().equals(methodName)) {
                return method;
            }
        }
        throw new NoSuchMethodException("Method " + methodName + " with "  + clazz);
    }
    @Override
    public Result<List<ReTrySendMq>> queryTryData(String tenantId,String retryType,Long startTime,Long endTime,List<Integer> status,Integer limit,Integer offset) {
        List<ReTrySendMq> reTrySendMqs = retrySendMqDao.listRetrySendMqIdList(tenantId, retryType, startTime, endTime,
                status, limit, offset);

        return Result.newSuccess(reTrySendMqs);
    }
}
