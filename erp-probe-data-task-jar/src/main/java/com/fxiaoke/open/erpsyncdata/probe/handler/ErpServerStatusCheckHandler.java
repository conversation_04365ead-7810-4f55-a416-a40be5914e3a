package com.fxiaoke.open.erpsyncdata.probe.handler;


import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.probe.service.ErpServerStatusCheckService;
import com.fxiaoke.open.erpsyncdata.probe.service.ProbeDataTaskService;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Component
@JobHander(value = "erpServerStatusCheckHandler")
public class ErpServerStatusCheckHandler extends IJobHandler {

    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;

    @Autowired
    private ErpServerStatusCheckService erpServerStatusCheckService;

    private static ExecutorService executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
          .setNameFormat("ErpServerStatucCheckJob-%d").build();
        executorService = new ThreadPoolExecutor(5, 10, 60, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(300), workerFactory);
    }

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        executeJob(params);
        return ReturnT.SUCCESS;
    }

    public void executeJob(TriggerParam params) throws Exception {


        Set<String> tenantIds = Sets.newHashSet();

        try {
            tenantIds = configCenterConfig.getErpServerStatusCheckTenants();
        }catch (Exception e){
            //服务器发布可能导致异常
            log.warn("获取企业异常，暂停一分钟探测",e);
            return;
        }

        for (String tenantId : tenantIds) {
            //每个企业在单独线程运行
            executorService.submit(()->{
                try {
                    erpServerStatusCheckService.executeCheckErpServerStatus(tenantId);
                }catch (Exception e){
                    log.error("探测服务线程执行异常：",e);
                }
            });
        }
    }

}
