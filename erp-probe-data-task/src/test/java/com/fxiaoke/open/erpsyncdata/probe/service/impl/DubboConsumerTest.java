package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.probe.service.ProbeDataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/2
 */
@Ignore
@Slf4j
public class DubboConsumerTest extends BaseTest {
    @Autowired
    private ProbeDataTaskService probeDataTaskService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Test
    public void testRoute(){
        probeDataTaskService.executeProbeErpDataJob("83952",null);
    }

//    @Test
//    public void testDubbo() {
//        for (int i = 0; i < 100; i++) {
//            testTrace(i);
//        }
//    }
//
//    private void testTrace(int i) {
//        log.info("trace test,1,{}",i);
//        TraceUtil.initTrace("J-E.83952.-10000-xjytest"+i);
//        log.info("trace test,2,{}",i);
//        Set<String> stageSet = tenantConfigurationManager.getStageTenantIds();
//        log.info("trace test,3,{}",i);
//        //灰度环境
//        Set<String> GRAY_TENANTS = configCenterConfig.getGrayConfig();
//        log.info("trace test,4,{}",i);
//    }
}
